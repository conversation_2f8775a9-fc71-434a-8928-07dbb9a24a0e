package com.cbkj.diagnosis.sysBeans;

import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/2 15:20
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class CustomPageResEntity<T> {
    private boolean status = true;
    private int code = 0;
    private String message;
    private boolean isHasNextPage;
    private long count;
    private Object data;

    public CustomPageResEntity(Page<T> page){

        this.count = page.getTotal();
        this.data = page.getRecords();
        this.isHasNextPage  = page.hasNext();
    }
}
