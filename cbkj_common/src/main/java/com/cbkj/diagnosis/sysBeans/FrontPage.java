package com.cbkj.diagnosis.sysBeans;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
//import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/2 15:29
 * @Version 1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class FrontPage<T> {
//    @Schema(description =  "行数", example = "10")
    private int limit = 10;
//    @Schema(description =  "页码", example = "1")
    private int page = 1;


//    @Schema(hidden = true)
    public Page<T> getPagePlus() {
        Page<T> pagePlus = new Page<T>();
        pagePlus.setCurrent(this.page);
        pagePlus.setSize(this.limit);
        return pagePlus;
    }
}
