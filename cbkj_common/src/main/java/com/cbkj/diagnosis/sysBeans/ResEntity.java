package com.cbkj.diagnosis.sysBeans;


import com.cbkj.diagnosis.common.utils.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应实体
 */
@Schema(description = "响应实体")
@Data
public class ResEntity<T> implements Serializable {

    private static final long serialVersionUID = -1439573222637546375L;

    @Schema(description = "操作状态（true=成功）", example = "true")
    private boolean status = true;

    @Schema(description = "状态码", example = "0")
    private int code = 0;

    @Schema(description = "错误信息", example = "账号已锁定")
    private String message;

    @Schema(description = "业务数据负载")
    private T data;

    public ResEntity() {

    }

    public ResEntity(boolean status, T data) {
        this.status = status;
        this.data = data;
    }


    public ResEntity(boolean status, String message, T data) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    public ResEntity(boolean status, int code, String message, T data) {
        this.status = status;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static ResEntity entity(boolean status, String message, Object data) {
        return new ResEntity(status, message, data);
    }

    public static ResEntity success(Object data) {
        return new ResEntity(true, Constant.SUCCESS_DX, data);
    }
    public static ResEntity success() {
        return new ResEntity(true, Constant.SUCCESS_DX, null);
    }

    public static ResEntity error(String message) {
        return new ResEntity(false, message, null);
    }

    public static ResEntity error(int code, String message) {
        return new ResEntity(false, code, message, null);
    }

    public boolean getStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}

