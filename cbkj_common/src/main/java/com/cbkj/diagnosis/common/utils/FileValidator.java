package com.cbkj.diagnosis.common.utils;

import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartException;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;

import javax.imageio.ImageIO;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

public class FileValidator {

    private static final List<String> ALLOWED_IMAGE_EXTENSIONS = Arrays.asList("jpg", "jpeg", "png", "gif");
    private static final List<String> ALLOWED_VIDEO_EXTENSIONS = Arrays.asList("mp4", "avi", "mkv");

    private static final byte[] JPEG_MAGIC_NUMBER = { (byte) 0xFF, (byte) 0xD8 };
    private static final byte[] PNG_MAGIC_NUMBER = { (byte) 0x89, 'P', 'N', 'G', '\r', '\n', (byte) 0x1A, '\n' };
    private static final byte[] GIF_MAGIC_NUMBER = { 'G', 'I', 'F', '8' };

    public static boolean validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            return false;
        }

        String fileName = file.getOriginalFilename();
        String fileExtension = FilenameUtils.getExtension(fileName);

        // 校验文件扩展名
        if (fileExtension != null && !isValidExtension(fileExtension)) {
            return false;
        }

        // 校验文件MIME类型
        if (!isValidMimeType(file)) {
            return false;
        }

        // 限制文件目录的执行权限
        if (!isValidDirectory(file)) {
            return false;
        }

        // 校验文件内容是否是图片或视频
        return validatImageFile2(file) || isValidVideoContent(file);
    }
    public static boolean validatImageFile2(MultipartFile file) {
        try {
            ImageIO.read(file.getInputStream());
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    public static boolean validatImageFile(MultipartFile file) {
        if (file.isEmpty()) {
            return false;
        }

        String fileName = file.getOriginalFilename();
        String fileExtension = FilenameUtils.getExtension(fileName);

        // 校验文件扩展名
        if (fileExtension != null && !isValidExtension(fileExtension)) {
            return false;
        }

        // 校验文件MIME类型
        if (!isValidMimeType(file)) {
            return false;
        }

        // 限制文件目录的执行权限
        if (!isValidDirectory(file)) {
            return false;
        }

        // 校验文件内容是否是图片
        return isValidImageContent(file) ;
    }

    private static boolean isValidExtension(String extension) {
        return ALLOWED_IMAGE_EXTENSIONS.contains(extension.toLowerCase()) || ALLOWED_VIDEO_EXTENSIONS.contains(extension.toLowerCase());
    }

    private static boolean isValidMimeType(MultipartFile file) {
        // 在实际项目中，可以根据文件内容判断MIME类型
        // 这里简化为根据文件扩展名判断
        String fileExtension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (fileExtension != null) {
            return isValidExtension(fileExtension);
        }
        return false;
    }

    private static boolean isValidDirectory(MultipartFile file) {
        try {
            // 获取文件路径
            String filePath = file.getOriginalFilename();
            // Decode the filename using UTF-8

            String decodedFilename = null;
            if (filePath != null) {

                // 获取文件路径
                String sanitizedFilename = FilenameUtils.normalize(filePath);


                // 先将文件名转换为ISO-8859-1编码
                decodedFilename = new String(sanitizedFilename.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
                Path path = Paths.get(decodedFilename);


                // 检查文件是否具有写入和执行权限
                if (Files.isWritable(path) || Files.isExecutable(path)) {
                    return false;
                }

                return true;
            }

            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    private static boolean isValidImageContent(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            // 获取文件内容的前几个字节
            byte[] fileContentHeader = IOUtils.toByteArray(inputStream, 8);
            // 检查是否为图片的魔数
            if (isImage(fileContentHeader)) {
                return true;
            }
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }
    private static boolean isValidVideoContent(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            // 获取文件内容的前几个字节
            byte[] fileContentHeader = IOUtils.toByteArray(inputStream, 8);
            // 检查是否为视频的魔数
            if (isVideo(fileContentHeader)) {
                return true;
            }
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }



    private static boolean isImage(byte[] fileContentHeader) {
        return Arrays.equals(fileContentHeader, JPEG_MAGIC_NUMBER)
                || Arrays.equals(Arrays.copyOfRange(fileContentHeader, 0, PNG_MAGIC_NUMBER.length), PNG_MAGIC_NUMBER)
                || Arrays.equals(Arrays.copyOfRange(fileContentHeader, 0, GIF_MAGIC_NUMBER.length), GIF_MAGIC_NUMBER);
    }

    private static boolean isVideo(byte[] fileContentHeader) {
        // AVI 魔数
        byte[] aviMagicNumber = { 'R', 'I', 'F', 'F', 0, 0, 0, 0 };
        // MKV 魔数
        byte[] mkvMagicNumber = { 0x1A, 0x45, (byte)0xDF, (byte)0xA3, (byte)0x93, 0x42, (byte)0x82, (byte)0x88};
        // MP4 魔数
        byte[] mp4MagicNumber = { 0, 0, 0, 32, 'f', 't', 'y', 'p' };
        // 检查是否为MP4的魔数
        boolean isMp4 = compareMagicNumber(fileContentHeader, 0, mp4MagicNumber);
        boolean isAvi = compareMagicNumber(fileContentHeader, 0, aviMagicNumber);
        boolean isMkv = compareMagicNumber(fileContentHeader, 0, mkvMagicNumber);
        return isMp4 || isAvi || isMkv;
    }

    private static boolean compareMagicNumber(byte[] fileContentHeader, int offset, byte[] magicNumber) {


        for (int i = 0; i < magicNumber.length; i++) {
            if (fileContentHeader[i + offset] != magicNumber[i]) {
                return false;
            }
        }
        return true;
    }

}


