//package com.cbkj.diagnosis.common.config;
//
//import com.github.xiaoymin.swaggerbootstrapui.annotations.EnableSwaggerBootstrapUI;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.handler.MappedInterceptor;
//import springfox.documentation.builders.*;
//import springfox.documentation.service.*;
//import springfox.documentation.spi.DocumentationType;
//import springfox.documentation.spi.service.contexts.SecurityContext;
//import springfox.documentation.spring.web.plugins.Docket;
//import springfox.documentation.swagger2.annotations.EnableSwagger2;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@Configuration
//@EnableSwagger2
//@EnableSwaggerBootstrapUI
//public class Swagger2Config {
//
//    @Bean
//    public Docket createRestApi() {
//        return new Docket(DocumentationType.SWAGGER_2)
//                .apiInfo(apiInfo())
//                .select()
//                .apis(RequestHandlerSelectors.basePackage("com.cbkj.diagnosis.controller"))
//                .paths(PathSelectors.any())
//                .build()
//                .securitySchemes(securitySchemes())
//                .securityContexts(securityContexts());
//    }
//
//    private ApiInfo apiInfo() {
//        return new ApiInfoBuilder()
//                .title("接口API")
//                .description("接口API文档")
//                .termsOfServiceUrl("")
//                .version("1.0")
//                .build();
//    }
//
//    private List<ApiKey> securitySchemes() {
//        List<ApiKey> apiKeys = new ArrayList<>();
//        apiKeys.add(new ApiKey("Authorization", "Authorization", "header"));
//        return apiKeys;
//    }
//
//    private List<SecurityContext> securityContexts() {
//        List<SecurityContext> securityContexts = new ArrayList<>();
//        securityContexts.add(SecurityContext.builder()
//                .securityReferences(defaultAuth())
//                .forPaths(PathSelectors.regex("^(?!auth).*$")).build());
//        return securityContexts;
//    }
//
//    private List<SecurityReference> defaultAuth() {
//        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
//        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
//        authorizationScopes[0] = authorizationScope;
//        List<SecurityReference> securityReferences = new ArrayList<>();
//        securityReferences.add(new SecurityReference("Authorization", authorizationScopes));
//        return securityReferences;
//    }
//
//    @Value("${swagger.basic.username:admin}")
//    private String username;
//    @Value("${swagger.basic.password:admin}")
//    private String password;
//    /* 必须在此处配置拦截器,要不然拦不到swagger的静态资源 */
//    @Bean
//    @ConditionalOnProperty(name = "swagger.basic.enable", havingValue = "true")
//    public MappedInterceptor getMappedInterceptor() {
//        return new MappedInterceptor(new String[]{"/doc.html", "/webjars/**"},
//                new SwaggerInterceptor(username, password));
//    }
//
//}