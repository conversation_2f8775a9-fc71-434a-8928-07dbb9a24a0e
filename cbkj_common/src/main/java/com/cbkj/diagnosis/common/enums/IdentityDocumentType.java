package com.cbkj.diagnosis.common.enums;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/12 09:47
 * @Version 1.0
 */
public enum IdentityDocumentType {
    // 枚举常量定义
    RESIDENT_IDENTITY_CARD("01", "居民身份证"),
    RESIDENT_HOUSEHOLD_REGISTER("02", "居民户口簿"),
    PASSPORT("03", "护照"),
    MILITARY_ID("04", "军官证（士兵证）"),
    DRIVING_LICENSE("05", "驾驶执照"),
    MAINLAND_TRAVEL_PERMIT_FOR_HONGKONG_AND_MACAO_RESIDENTS("06", "港澳居民来往内地通行证"),
    MAINLAND_TRAVEL_PERMIT_FOR_TAIWAN_RESIDENTS("07", "台湾居民来往内地通行证"),
    OTHER("99", "其他");

    // 枚举的属性
    private String code;
    private String description;

    // 枚举的私有构造函数
    IdentityDocumentType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 获取枚举的Code
    public String getCode() {
        return code;
    }

    // 获取枚举的描述
    public String getDescription() {
        return description;
    }

    // 根据Code获取枚举实例，如果不存在则返回null
    public static IdentityDocumentType fromCode(String code) {
        for (IdentityDocumentType type : IdentityDocumentType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        // 如果没有找到对应的枚举实例，则返回null
        return null;
    }

    // 可选：提供一个检查是否存在的方法
    public static boolean exists(String code) {
        return fromCode(code) != null;
    }
}
