package com.cbkj.diagnosis.common.utils;

import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//@Schema(description =  "分页")
@Schema(description = "分页")
public class Page implements Serializable {

    private static final long serialVersionUID = 2843053623355369576L;
//    @Schema(description =  "行数", example = "10")
    private Integer limit = 10;
//    @Schema(description =  "页码", example = "1")
    private Integer page = 1;

    public Page() {
    }

    ;

    public Page(int rows, int page) {
        this.page = page;
        this.limit = rows;
    }

    public int getCurrentRow() {
        return this.page <= 0 ? 0 : this.limit * (this.page - 1);
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    /**
     * layui 数据格式
     *
     * @param code  状态码 0成功 1失败
     * @param msg   消息
     * @param count 数据总数量
     * @param data  数据
     * @return
     */
    public static Object getLayuiData(boolean code, String msg, long count, boolean isHasNextPage, Object data) {
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("status", code);
        result.put("message", msg);
        result.put("isHasNextPage", isHasNextPage);
        result.put("count", count);
        result.put("data", data);
        return result;
    }

    /**
     * 数据格式
     *
     * @param count 数据总数量
     * @param data  数据
     */
    public static Object getPageDataSuccess(long count, Object data) {
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("status", true);
        result.put("message", Constant.SUCCESS);
        result.put("count", count);
        result.put("data", data);
        return result;
    }

    public static <T> Object getLayUiTablePageData(List<T> lis) {
        Object result;
        try {
            PageInfo<T> pageInfo = new PageInfo<>(lis);
            boolean hasNextPage = pageInfo.isHasNextPage();
            result = getLayuiData(true, Constant.SUCCESS, pageInfo.getTotal(), pageInfo.isHasNextPage(), lis);
        } catch (Exception e) {
            result = getLayuiData(false, "数据服务异常", 0, false, null);
        }
        return result;
    }

    public static <T> Object getResEntityPageData(List<T> lis) {
        Object result;


        try {
            if (lis == null){
                //PageInfo<T> pageInfo = new PageInfo<>(lis);
                //PageInfo<T> pageInfo = new PageInfo<>(lis);
                Map<String, Object> resultMap = new HashMap<String, Object>();
                resultMap.put("status", true);
                resultMap.put("message", Constant.SUCCESS_DX);
                resultMap.put("data", null);
                resultMap.put("count", 0);
                resultMap.put("isHasNextPage", false);
                result = resultMap;
            }else {
                PageInfo<T> pageInfo = new PageInfo<>(lis);
                Map<String, Object> resultMap = new HashMap<String, Object>();
                resultMap.put("status", true);
                resultMap.put("message", Constant.SUCCESS_DX);
                resultMap.put("data", lis);
                resultMap.put("count", pageInfo.getTotal());
                resultMap.put("isHasNextPage", pageInfo.isHasNextPage());
                result = resultMap;
            }

        } catch (Exception e) {
            result = ResEntity.entity(false, "数据服务异常", null);
        }
        return result;
    }
}