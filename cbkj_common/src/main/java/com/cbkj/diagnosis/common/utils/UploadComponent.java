package com.cbkj.diagnosis.common.utils;


import com.cbkj.diagnosis.sysBeans.ResEntity;
import lombok.extern.slf4j.Slf4j;
//import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * UploadService
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2021/4/23 11:10
 */
@Service
@Slf4j
public class UploadComponent {


    @Value("${file.address}")
    private String location;

    @Value("${root.preview}")
    private String preview;

    @Value("${root.upload.relative}")
    private String relative;

    private static final String BASE64_IMAGES_PNG = "data:image/png;base64,";


    /**
     * 上传文件到当前服务器
     *
     * @param file file
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/4/23
     */

    public ResEntity uploadMultipartFile(MultipartFile file) {

        if (file == null || file.isEmpty()) {
            return new ResEntity(false, "未选择文件", null);
        }
        boolean b = FileValidator.validateFile(file);
        if (!b){
            return ResEntity.error("请上传合法文件");
        }
        String fileName = file.getOriginalFilename();
        long size = file.getSize();
        String suffix = null;
        if (fileName != null) {
            suffix = fileName.substring(fileName.lastIndexOf("."));
        }
        StringBuilder filePath = new StringBuilder(relative)
                .append(DateUtil.getDateFormats(DateUtil.YYYYMMDD, null))
                .append("/")
                .append(UUID.randomUUID().toString())
                .append(suffix);
        File dest = new File(String.format("%s%s", location, filePath.toString()));
        //判断文件父目录是否存在
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }
        try {
            //保存文件
            file.transferTo(dest);
//            Thumbnails.of(file.getInputStream())
//                    .scale(0.5f) // 压缩比例为原始图像的一半
//                    .outputQuality(0.9f) // 输出图像质量为原始图像的80%
//                    .toFile(dest); // 输出到指定文件
            return new ResEntity(true, Constant.SUCCESS_DX, preview + filePath.toString());
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
            return new ResEntity(false, "服务异常", null);
        }
    }

    public ResEntity uploadImageOnly(MultipartFile file) {

        if (file == null || file.isEmpty()) {
            return new ResEntity(false, "未选择文件", null);
        }
        boolean b = FileValidator.validatImageFile2(file);
        if (!b){
            return ResEntity.error("请上传合法文件");
        }
        String fileName = file.getOriginalFilename();
        long size = file.getSize();
        String suffix = null;
        if (fileName != null) {
            suffix = fileName.substring(fileName.lastIndexOf("."));
        }
        StringBuilder filePath = new StringBuilder(relative)
                .append(DateUtil.getDateFormats(DateUtil.YYYYMMDD, null))
                .append("/")
                .append(UUID.randomUUID().toString())
                .append(suffix);
        File dest = new File(String.format("%s%s", location, filePath.toString()));
        //判断文件父目录是否存在
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }
        try {
//            File tempFile = File.createTempFile("upload", null);
//            file.transferTo(tempFile);
//            Thumbnails.of(tempFile)
//                    .scale(1f) // 压缩比例为原始图像的一半
//                    .outputQuality(0.5f) // 输出图像质量为原始图像的80%
//                    .toFile(dest); // 输出到指定文件
            //保存文件
            file.transferTo(dest);
            return new ResEntity(true, Constant.SUCCESS_DX, preview + filePath.toString());
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
            return new ResEntity(false, "服务异常", null);
        }
    }
    public void compressImage(String filePath) {

    }

    public boolean judgeFile(MultipartFile file){
        String contentType = file.getContentType();
        // 获取文件的原始名称
        String originalFilename = file.getOriginalFilename();
        // 如果没有文件或文件名为空，则返回false
        if (originalFilename == null || originalFilename.isEmpty()) {
            return false;
        }

        // 检查文件扩展名
        String[] parts = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).trim().toLowerCase().split("\\s+");
        String extension = parts[0]; // 假设第一个部分是扩展名

        // 检查文件扩展名是否在允许的列表中
        // 根据实际需要修改allowedExtensions
        String[] allowedExtensions = { "jpg", "jpeg", "png", "gif", "txt" };
        if (!Arrays.asList(allowedExtensions).contains(extension)) {
            return false;
        }

        // 图片格式
        String[] imageTypes = { "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp" };
        // 视频格式
        String[] videoTypes = { "video/mp4", "video/quicktime", "video/flv", "video/x-msvideo" };

        // 判断是否为图片或视频格式
        return Arrays.asList(imageTypes).contains(contentType) || Arrays.asList(videoTypes).contains(contentType);


    }




    /**
     * 上传base64文件到当前服务器
     *
     * @param baseStr baseStr
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/4/23
     */
    public ResEntity uploadBase64PngImage(String baseStr) {

        if (StringUtils.isBlank(baseStr)) {
            return ResEntity.error("base64不能为空");
        }

        if (!baseStr.startsWith(BASE64_IMAGES_PNG)) {
            return ResEntity.error("base64内容不是png图片");
        }

        baseStr = baseStr.replace(BASE64_IMAGES_PNG, "");
        byte[] bytes;
        try {

            bytes = Base64.getDecoder().decode(baseStr);
        } catch (Exception e) {
            e.printStackTrace();
            return ResEntity.error("base64无法转文件");
        }

        StringBuilder sb = new StringBuilder(relative)
                .append(DateUtil.getDateFormats(DateUtil.YYYYMMDD, null))
                .append("/")
                .append(UUID.randomUUID().toString())
                .append(".png");
        File file = new File(String.format("%s%s", location, sb.toString()));
        //判断文件父目录是否存在
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }

        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
            return ResEntity.success(preview + sb.toString());
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
            return ResEntity.error("服务异常");
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


}
