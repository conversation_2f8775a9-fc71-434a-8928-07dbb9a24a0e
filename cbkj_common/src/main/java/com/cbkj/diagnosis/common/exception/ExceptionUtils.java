package com.cbkj.diagnosis.common.exception;


import lombok.extern.slf4j.Slf4j;

/**
 * ExceptionUtils
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/7/8
 */
@Slf4j
public class ExceptionUtils {


    /**
     * 构造 参数配置错误
     */
    public static void throwErrorParamsException(String code, String message) {
        String errorMsg = "【参数配置错误】：参数代码=" + code + "，参数名称=" + message;
        log.error(errorMsg);
        throw new CustomRuntimeException(ExceptionEnum.ERROR.getCode(), errorMsg);
    }

    /**
     * 构造 自定异常
     */
    public static void throwErrorCustomRuntimeException(String message) {
        throw new CustomRuntimeException(ExceptionEnum.ERROR.getCode(), message);
    }

    /**
     * 构造 自定异常
     */
    public static void throwCustomRuntimeException(ExceptionEnum exceptionEnum) {
        log.error("接口错误：code={},message={}", exceptionEnum.getCode(), exceptionEnum.getMessage());
        throw new CustomRuntimeException(exceptionEnum.getCode(), exceptionEnum.getMessage());
    }

    /**
     * 构造 自定异常
     */
    public static void throwCustomRuntimeException(ExceptionEnum exceptionEnum, Exception ex) {
        log.error("接口错误：code={},message={},error={}", exceptionEnum.getCode(), exceptionEnum.getMessage(), ex.getMessage());
        throw new CustomRuntimeException(exceptionEnum.getCode(), exceptionEnum.getMessage());
    }

    /**
     * 构造 自定异常
     */
    public static void throwCustomRuntimeException(ExceptionEnum exceptionEnum, String message) {
        log.error("接口错误：code={},message={}", exceptionEnum.getCode(), message);
        throw new CustomRuntimeException(exceptionEnum.getCode(), message);
    }

    /**
     * 构造 自定异常
     */
    public static void throwCustomRuntimeException(ExceptionEnum exceptionEnum, String message, Exception ex) {
        log.error("接口错误：code={},message={},error={}", exceptionEnum.getCode(), message, ex.getMessage());
        throw new CustomRuntimeException(exceptionEnum.getCode(), message);
    }

}
