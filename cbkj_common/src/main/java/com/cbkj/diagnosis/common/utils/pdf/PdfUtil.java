//package com.jiuzhekan.cbkj.common.utils.pdf;
//
//import com.itextpdf.text.Document;
//import com.itextpdf.text.DocumentException;
//import com.itextpdf.text.Image;
//import com.itextpdf.text.Rectangle;
//import com.itextpdf.text.pdf.*;
//import com.jiuzhekan.cbkj.beans.business.TSysParam;
//import com.jiuzhekan.cbkj.common.exception.ExceptionUtils;
//import com.jiuzhekan.cbkj.common.utils.BarcodeUtil;
//import com.jiuzhekan.cbkj.common.utils.Constant;
//import com.jiuzhekan.cbkj.common.utils.DateUtil;
//import com.jiuzhekan.cbkj.common.utils.ZXingCode;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.io.ByteArrayOutputStream;
//import java.io.File;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.math.BigDecimal;
//import java.util.List;
//
///**
// * PDFUtil
// * <p>
// * 杭州聪宝科技有限公司
// * <p>
// *
// * <AUTHOR>
// * @date 2020/9/24 16:04
// */
//@Slf4j
//@Component
//public class PdfUtil {
//
//
//    @Value("${template.address}")
//    private String templateAddress;
//
//    @Value("${file.address}")
//    private String location;
//
//    @Value("${root.preview}")
//    private String preview;
//
//    @Value("${root.upload.relative}")
//    private String relative;
//
//    @Value("${address.mobile.decoct}pre/common/toInfo?preNo=")
//    private String decoctUrl;
//
//    private static final String PRE_PDF_FOLDER = "prescriptionPDF/";
//
//    private static final String PRESCRIPTION_TEMPLATE = "prescriptionTemplate.pdf";
//    private static final String PRESCRIPTION_TEMPLATE2 = "prescriptionTemplate2.pdf";
//    private static final String PREPARATION_TEMPLATE = "preparationTemplate.pdf";
//    private static final String RECORD_TEMPLATE_A4 = "recordTemplateA4.pdf";
//    private static final String RECORD_TEMPLATE_A5 = "recordTemplateA5.pdf";
//    private static final String SPAN = "　";
//    private static final String NEW_LINE = "\r\n";
//
//    private TSysParamService tSysParamService;
//
//    @Autowired
//    public PdfUtil(TSysParamService tSysParamService) {
//        this.tSysParamService = tSysParamService;
//    }
//
//    private String getTemplatePath(String template) {
//        String templatePath = templateAddress + template;
//        if (new File(templatePath).exists()) {
//            return templatePath;
//        }
//        return "templates/" + template;
//    }
//
//    /**
//     * 根据病历生产处方笺PDF
//     *
//     * @param pdfName pdf名称
//     * @param record  病历
//     * @return String pdf路径
//     * <AUTHOR>
//     * @date 2020/10/27
//     */
//    public String createRecordPdf(String pdfName, TRecord record) {
//        PdfReader reader = null;
//        AcroFields fields = null;
//        PdfStamper stamper = null;
//        ByteArrayOutputStream outStream = null;
//
//        String dateFolder = DateUtil.getDateFormats(DateUtil.date5, null);
//        String realFolder = location + relative + PRE_PDF_FOLDER + dateFolder;
//        String fileFolder = preview + relative + PRE_PDF_FOLDER + dateFolder;
//        //创建上传文件目录
//        File folder = new File(realFolder);
//        if (!folder.exists()) {
//            folder.mkdirs();
//        }
//        //设置文件名
//        String fileName = pdfName + "_" + DateUtil.getDateFormats(DateUtil.date7, null) + ".pdf";
//        //文件访问路径
//        String filePath = fileFolder + "/" + fileName;
//        //文件存储路径
//        String realPath = realFolder + "/" + fileName;
//
//        if (record != null && record.getPrescriptionList() != null && record.getPrescriptionList().size() > 0) {
//
//            try {
//
//                FileOutputStream fos = new FileOutputStream(realPath);
//                Document document = new Document();
//                PdfCopy copy = new PdfCopy(document, fos);
//                document.open();
//
//                for (TPrescription prescription : record.getPrescriptionList()) {
//
//                    try {
//                        //制剂模板
//                        if (Constant.BASIC_STRING_FIVE.equals(prescription.getPreType())) {
//                            reader = new PdfReader(getTemplatePath(PREPARATION_TEMPLATE));
//                        } else if (prescription.getItemList() != null && prescription.getItemList().size() > 28
//                                || prescription.getAcuItemList() != null && prescription.getAcuItemList().size() > 28) {
//                            //普通两页模板
//                            reader = new PdfReader(getTemplatePath(PRESCRIPTION_TEMPLATE2));
//                        } else {
//                            //普通一页模板
//                            reader = new PdfReader(getTemplatePath(PRESCRIPTION_TEMPLATE));
//                        }
//                    } catch (Exception e) {
//                        ExceptionUtils.throwErrorCustomRuntimeException("处方模板错误，请联系管理员维护模板文件");
//                    }
//
//                    outStream = new ByteArrayOutputStream();
//                    stamper = new PdfStamper(reader, outStream);
//                    fields = stamper.getAcroFields();
//
//                    setTextFont(fields);
//
//                    setTextField(fields, record, prescription);
//
//                    setBarCode(stamper, prescription);
//
//                    setQRCode(stamper, prescription);
//                    // 如果为false那么生成的PDF文件还能编辑，一定要设为true
//                    stamper.setFormFlattening(true);
//                    stamper.close();
//
//                    PdfReader pdfReader = new PdfReader(outStream.toByteArray());
//                    //合并多页
//                    for (int i = 1; i <= pdfReader.getNumberOfPages(); i++) {
//                        document.newPage();
//                        PdfImportedPage page = copy.getImportedPage(pdfReader, i);
//                        copy.addPage(page);
//                    }
//
//                    outStream.close();
//                    reader.close();
//                }
//
//                document.close();
//                copy.flush();
//                copy.close();
//                fos.flush();
//                fos.close();
//
//            } catch (IOException | DocumentException e) {
//                log.error("读取文件异常。" + e.getMessage());
//                e.printStackTrace();
//                return "";
//            }
//        }
//        return filePath;
//    }
//
//    public String createRecordPdf2(String pdfName, TRecord record) {
//        PdfReader reader = null;
//        AcroFields fields = null;
//        PdfStamper stamper = null;
//        ByteArrayOutputStream outStream = null;
//
//        String dateFolder = DateUtil.getDateFormats(DateUtil.date5, null);
//        String realFolder = location + relative + PRE_PDF_FOLDER + dateFolder;
//        String fileFolder = preview + relative + PRE_PDF_FOLDER + dateFolder;
//        //创建上传文件目录
//        File folder = new File(realFolder);
//        if (!folder.exists()) {
//            folder.mkdirs();
//        }
//        //设置文件名
//        String fileName = pdfName + "_" + DateUtil.getDateFormats(DateUtil.date7, null) + ".pdf";
//        //文件访问路径
//        String filePath = fileFolder + "/" + fileName;
//        //文件存储路径
//        String realPath = realFolder + "/" + fileName;
//        TSysParam sysParam = tSysParamService.getSysParam(Constant.REC_PDF_TEMPLATE);
//        if (record != null && record.getPrescriptionList() != null && record.getPrescriptionList().size() > 0) {
//
//            try {
//
//                FileOutputStream fos = new FileOutputStream(realPath);
//                Document document = new Document();
//                PdfCopy copy = new PdfCopy(document, fos);
//                document.open();
//
//                // for (TPrescription prescription : record.getPrescriptionList()) {
//
//                try {
//                    //A5模板
//                    if (Constant.BASIC_STRING_TWO.equals(sysParam.getParValues())) {
//                        reader = new PdfReader(getTemplatePath(RECORD_TEMPLATE_A5));
//                    } else {
//                        //A4模板
//                        reader = new PdfReader(getTemplatePath(RECORD_TEMPLATE_A4));
//                    }
//                } catch (Exception e) {
//                    ExceptionUtils.throwErrorCustomRuntimeException("病历模板错误，请联系管理员维护模板文件");
//                }
//
//                outStream = new ByteArrayOutputStream();
//                stamper = new PdfStamper(reader, outStream);
//                fields = stamper.getAcroFields();
//
//                setTextFont(fields);
//
//                setTextField2(fields, record, record.getPrescriptionList(), sysParam.getParValues() == null ? 1 + "" : sysParam.getParValues());
//
//                //setBarCode(stamper, prescription);
//
//                //setQRCode(stamper, prescription);
//                // 如果为false那么生成的PDF文件还能编辑，一定要设为true
//                stamper.setFormFlattening(true);
//                stamper.close();
//
//                PdfReader pdfReader = new PdfReader(outStream.toByteArray());
//                //合并多页
//                for (int i = 1; i <= pdfReader.getNumberOfPages(); i++) {
//                    document.newPage();
//                    PdfImportedPage page = copy.getImportedPage(pdfReader, i);
//                    copy.addPage(page);
//                }
//
//                outStream.close();
//                reader.close();
//                //}
//
//                document.close();
//                copy.flush();
//                copy.close();
//                fos.flush();
//                fos.close();
//
//            } catch (IOException | DocumentException e) {
//                log.error("读取文件异常。" + e.getMessage());
//                e.printStackTrace();
//                return "";
//            }
//        }
//        return filePath;
//    }
//
//    /**
//     * 设置文字域字体
//     *
//     * @param fields 文字域
//     * <AUTHOR>
//     * @date 2020/10/27
//     */
//    private void setTextFont(AcroFields fields) throws IOException, DocumentException {
//
//        String propertyName = "textfont";
//        // 设置中文显示
//        BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
////        Font bfChinese = FontFactory.getFont("/usr/share/fonts/simsun.ttf");
//
//        fields.setFieldProperty("incName", propertyName, bfChinese, null);
//        fields.setFieldProperty("recName", propertyName, bfChinese, null);
//        fields.setFieldProperty("payType", propertyName, bfChinese, null);
//        fields.setFieldProperty("recGender", propertyName, bfChinese, null);
//        fields.setFieldProperty("preNo", propertyName, bfChinese, null);
//        fields.setFieldProperty("deptName", propertyName, bfChinese, null);
//        fields.setFieldProperty("recAge", propertyName, bfChinese, null);
//        fields.setFieldProperty("recTime", propertyName, bfChinese, null);
//        fields.setFieldProperty("disName", propertyName, bfChinese, null);
//        fields.setFieldProperty("symName", propertyName, bfChinese, null);
//        fields.setFieldProperty("preNum", propertyName, bfChinese, null);
//        fields.setFieldProperty("preFre", propertyName, bfChinese, null);
//        fields.setFieldProperty("preUse", propertyName, bfChinese, null);
//        fields.setFieldProperty("preDes", propertyName, bfChinese, null);
//        fields.setFieldProperty("docName", propertyName, bfChinese, null);
//        fields.setFieldProperty("matMoney", propertyName, bfChinese, null);
//        fields.setFieldProperty("checkName", propertyName, bfChinese, null);
//        fields.setFieldProperty("sendName", propertyName, bfChinese, null);
//        fields.setFieldProperty("tell", propertyName, bfChinese, null);
//        fields.setFieldProperty("recTreTime", propertyName, bfChinese, null);
//        fields.setFieldProperty("visitNo", propertyName, bfChinese, null);
//
//    }
//
//
//    /**
//     * 填充文字域
//     *
//     * @param fields       文字域
//     * @param record       病历
//     * @param prescription 处方
//     * <AUTHOR>
//     * @date 2020/10/27
//     */
//    private void setTextField(AcroFields fields, TRecord record, TPrescription prescription) throws IOException, DocumentException {
//
//        String gender = "";
//        if ("M".equals(record.getRecGender())) {
//            gender = "男";
//        } else if ("F".equals(record.getRecGender())) {
//            gender = "女";
//        }
//        String age = "";
//        if (record.getRecAge1() != null) {
//            age += record.getRecAge1() + record.getRecAgeunit1();
//        }
//        if (record.getRecAge2() != null) {
//            age += record.getRecAge2() + record.getRecAgeunit2();
//        }
//
//        //设置文字域
//        fields.setField("incName", record.getInsName() + "处方笺");
//        fields.setField("recName", record.getRecName());
//        fields.setField("payType", "");
//        fields.setField("recGender", gender);
//        fields.setField("preNo", prescription.getPreNo());
//        fields.setField("deptName", record.getDeptName());
//        fields.setField("recAge", age);
//        fields.setField("recTime", DateUtil.getDateFormats(DateUtil.date1, prescription.getPreTime()));
//        fields.setField("disName", record.getDisName());
//        fields.setField("symName", record.getSymName());
//        fields.setField("preNum", prescription.getPreNum() == null ? "" : prescription.getPreNum().toString());
//        fields.setField("preFre", prescription.getPreFrequency());
//        fields.setField("preUse", prescription.getPreUsetimeDes());
//        fields.setField("preDes", prescription.getPreDescription());
//        fields.setField("docName", prescription.getPreDoctorname());
//        if (prescription.getMatTolMoney() != null) {
//            fields.setField("matMoney", prescription.getMatTolMoney().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//        }
//        if (prescription.getPreProductionFee() != null) {
//            fields.setField("preProduction", prescription.getPreProductionFee().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//        }
//        if (prescription.getPreDecoctionFee() != null) {
//            fields.setField("preDecoction", prescription.getPreDecoctionFee().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//        }
//        if (prescription.getPreExpressFee() != null) {
//            fields.setField("preExpress", prescription.getPreExpressFee().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//        }
//        if (prescription.getPreTolMoney() != null) {
//            fields.setField("tolMoney", prescription.getPreTolMoney().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//        }
//        fields.setField("checkName", "");
//        fields.setField("sendName", "");
//        fields.setField("tell", prescription.getPreAdvice());
//
//        if (prescription.getItemList() != null && prescription.getItemList().size() > 0) {
//            String jf = "煎服";
//            for (int i = 0; i < prescription.getItemList().size(); i++) {
//                TPrescriptionItem item = prescription.getItemList().get(i);
//                StringBuilder sb = new StringBuilder();
//                sb.append(item.getYpmcCenter()).append(SPAN);
//                if (!Constant.UNIT_LIST_G.contains(item.getBzdwHis())) {
//                    sb.append(item.getYpggCenter()).append(SPAN);
//                } else {
//                    sb.append(SPAN).append(SPAN);
//                }
//                sb.append(item.getMatDose().stripTrailingZeros().toPlainString()).append(SPAN);
//                sb.append(item.getBzdwHis()).append(SPAN);
//                if (StringUtils.isNotBlank(item.getYfmcCenter()) && !jf.equals(item.getYfmcCenter())) {
//                    sb.append("（").append(item.getYfmcCenter()).append("）").append(SPAN);
//                }
//
//                fields.setField("mat" + (i + 1), sb.toString());
//            }
//        } else if (prescription.getAcuItemList() != null && prescription.getAcuItemList().size() > 0) {
//            for (int i = 0; i < prescription.getAcuItemList().size(); i++) {
//                TPrescriptionAcuItem item = prescription.getAcuItemList().get(i);
//                String sb = item.getAcuName() + SPAN + item.getAcuNum() + SPAN;
//                fields.setField("mat" + (i + 1), sb);
//            }
//        } else if (prescription.getPreparationItemList() != null && prescription.getPreparationItemList().size() > 0) {
//            for (int i = 0; i < prescription.getPreparationItemList().size(); i++) {
//                TPrescriptionPreparationItem item = prescription.getPreparationItemList().get(i);
//                fields.setField("ypmc" + (i + 1), item.getYpmcCenter());
//                fields.setField("zl" + (i + 1), "总量：" + item.getMatNum().stripTrailingZeros().toPlainString() + item.getBzdwHis());
//                fields.setField("ff" + (i + 1), item.getUsage());
//                fields.setField("yl" + (i + 1), "一次" + item.getMatDose().stripTrailingZeros().toPlainString() + item.getMatDoseunit());
//                fields.setField("pc" + (i + 1), item.getMatFrequency());
//                fields.setField("ts" + (i + 1), "天数：" + item.getMatDay().toString());
//                fields.setField("bz" + (i + 1), item.getRemark());
//            }
//        }
//    }
//
//    private void setTextField2(AcroFields fields, TRecord record, List<TPrescription> prescriptionList, String c) throws IOException, DocumentException {
//
//        String gender = "";
//        if ("M".equals(record.getRecGender())) {
//            gender = "男";
//        } else if ("F".equals(record.getRecGender())) {
//            gender = "女";
//        }
//        String age = "";
//        if (record.getRecAge1() != null) {
//            age += record.getRecAge1() + record.getRecAgeunit1();
//        }
//        if (record.getRecAge2() != null) {
//            age += record.getRecAge2() + record.getRecAgeunit2();
//        }
//
//        //设置文字域
//        //门诊病历
//        fields.setField("title", record.getInsName() + "门诊病历");
//        //就诊日期
//        fields.setField("recTreTime", DateUtil.getDateFormats(DateUtil.date1, record.getRecTreTime()));
//        //科室
//        fields.setField("deptName", record.getDeptName());
//        //姓名
//        fields.setField("recName", record.getRecName());
//        fields.setField("recGender", gender);
//        fields.setField("recAge", age);
//        //门诊号
//        fields.setField("visitNo", record.getVisitNo() == null ? prescriptionList.get(0).getPreNo() : record.getVisitNo());
//        //主    诉
//        fields.setField("patientContent", record.getPatientContent());
//        //现 病 史
//        fields.setField("nowDesc", record.getNowDesc());
//        //既 往 史
//        fields.setField("pastDesc", record.getPastDesc());
//        //中医四诊
//        fields.setField("fourDiagnosis", record.getFourDiagnosis());
//        //体格检查：physical
//        fields.setField("physical", record.getPhysical());
//        //辅助检查：auxiliaryExam
//        fields.setField("auxiliaryExam", record.getAuxiliaryExam());
//        //诊    断：chineseDisease = "中医诊断：" + disName + symName
//        //               westernDisease = "西医诊断：" + westernDisease
//        fields.setField("chineseDisease", "中医诊断：" + record.getDisName() + record.getSymName());
//        if (record.getWesternDisease() != null) {
//            fields.setField("westernDisease", "西医诊断：" + record.getWesternDisease());
//        }
//        //治    法
//        fields.setField("theNames", record.getTheNames());
//        //医生签名：docName
//        fields.setField("docName", record.getDocName());
//        //治疗意见：preInfo = "处方类型\r\n药品1  药品2  药品3\r\n贴数，服法，频次"
//        StringBuilder sb = new StringBuilder();
//        for (int i = 0; i < prescriptionList.size(); i++) {
//            TPrescription prescription = prescriptionList.get(i);
//            if (prescription.getItemList() != null && prescription.getItemList().size() > 0) {
//                int count = (c.equals(Constant.BASIC_STRING_TWO) ? 6 : 5);
//                int temp = count;
//                if (Constant.BASIC_STRING_ONE.equals(prescription.getPreType())) {
//                    sb.append("内服中草药方").append(NEW_LINE);
//                } else {
//                    sb.append("外用中草药方").append(NEW_LINE);
//                }
//                for (int jj = 0; jj < prescription.getItemList().size(); jj++) {
//                    TPrescriptionItem item = prescription.getItemList().get(jj);
//                    if (count == 0) {
//                        sb.append(NEW_LINE);
//                        count = temp;
//                    }
//                    sb.append(item.getYpmcCenter()).append(SPAN).append(item.getYpggCenter());
//                    if (!Constant.UNIT_LIST_G.contains(item.getBzdwHis())) {
//                        sb.append(item.getYpggCenter()).append(SPAN);
//                    } else {
//                        sb.append(SPAN).append(SPAN);
//                    }
//                    count--;
//                }
//                sb.append(NEW_LINE);
//                if (Constant.BASIC_STRING_TWO.equals(prescription.getPreType())) {
//                    if (Constant.BASIC_STRING_ONE.equals(prescription.getIsHospitalTreatment())) {
//                        sb.append("院内").append(SPAN);
//                    } else {
//                        sb.append("院外").append(SPAN);
//                    }
//                    sb.append(prescription.getPreSmokeType()).append(SPAN);
//                    sb.append("仪器模式：").append(prescription.getPreSmokeInstrument());
//                    sb.append(NEW_LINE);
//                    sb.append("操作指南：").append(prescription.getAcuOperation()).append(NEW_LINE);
//                }
//                //共7贴，水煎服，一日两次，每次150ml  饭后服
//                sb.append("共").append(prescription.getPreNum()).append("贴，");
//                if (!StringUtils.isBlank(prescription.getPreDescription())) {
//                    sb.append(prescription.getPreDescription()).append("，");
//                }
//                sb.append(prescription.getPreFrequency());
//                if (prescription.getPreNMl() != null) {
//                    sb.append("，").append("每次").append(prescription.getPreNMl()).append("ml");
//                }
//                if (!StringUtils.isBlank(prescription.getPreUsetimeDes())) {
//                    sb.append("，").append(prescription.getPreUsetimeDes());
//                }
//                if (!StringUtils.isBlank(prescription.getPreAdvice())) {
//                    sb.append(NEW_LINE).append("医嘱：").append(prescription.getPreAdvice());
//                }
//                sb.append(NEW_LINE).append(NEW_LINE);
//
//            } else if (prescription.getAcuItemList() != null && prescription.getAcuItemList().size() > 0) {
//                sb.append("适宜技术方\r\n");
//                sb.append("类型：");
//                if (!StringUtils.isBlank(prescription.getAcuType())) {
//                    sb.append(prescription.getAcuType()).append(NEW_LINE);
//                } else {
//                    sb.append(NEW_LINE);
//                }
//                sb.append("穴位：");
//                int tempAcuNum = 0;
//                for (int j = 0; j < prescription.getAcuItemList().size(); j++) {
//                    TPrescriptionAcuItem item = prescription.getAcuItemList().get(j);
//                    tempAcuNum += item.getAcuNum();
//                    sb.append(item.getAcuName()).append(SPAN);
//                }
//                sb.append(NEW_LINE).append("针数：").append(tempAcuNum).append("针").append(SPAN);
//                if (!StringUtils.isBlank(prescription.getPreAdvice())) {
//                    sb.append(NEW_LINE).append("医嘱：").append(prescription.getPreAdvice());
//                }
//                sb.append(NEW_LINE).append(NEW_LINE);
//            } else if (prescription.getPreparationItemList() != null && prescription.getPreparationItemList().size() > 0) {
//                sb.append("中药制剂\r\n");
//                for (int a = 0; a < prescription.getPreparationItemList().size(); a++) {
//                    TPrescriptionPreparationItem item = prescription.getPreparationItemList().get(a);
//                    sb.append(a + 1).append("、").append(item.getYpmcCenter()).append(item.getYpggCenter()).append(SPAN).append(SPAN).append("总量：").append(item.getMatNum().stripTrailingZeros().toPlainString()).append(item.getBzdwHis()).append(NEW_LINE);
//                    sb.append(item.getUsage()).append(SPAN).append(SPAN).append(item.getMatDose().stripTrailingZeros().toPlainString())
//                            .append(item.getBzdwHis()).append(SPAN).append(SPAN)
//                            .append(item.getMatFrequency()).append(SPAN)
//                            .append("总天数：").append(item.getMatDay()).append(NEW_LINE)
//                    ;
//                }
//                if (!StringUtils.isBlank(prescription.getPreAdvice())) {
//                    sb.append(NEW_LINE).append("医嘱：").append(prescription.getPreAdvice()).append(NEW_LINE);
//                }
//                sb.append(NEW_LINE);
//            }
//        }
//        fields.setField("preInfo", sb.toString());
//    }
//
//    /**
//     * 设置条形码
//     *
//     * @param stamper      PdfStamper
//     * @param prescription TPrescription
//     * <AUTHOR>
//     * @date 2020/10/27
//     */
//    private void setBarCode(PdfStamper stamper, TPrescription prescription) throws IOException, DocumentException {
//
//        Image img = Image.getInstance(BarcodeUtil.generate(prescription.getPreNo()));
//
//        for (int i = 1; i <= stamper.getReader().getNumberOfPages(); i++) {
//            PdfContentByte contentByte = stamper.getOverContent(i);
//            Rectangle pageSize = stamper.getReader().getPageSize(i);
//            float height = pageSize.getHeight();
////        FontProgram fontProgram = FontProgramFactory.createFont("/usr/share/fonts/ttf-dejavu/simsun.ttf");
//
//            //居中显示
//            img.setAlignment(1);
//            //显示位置，根据需要调整
//            img.setAbsolutePosition(40, height - 110);
//            //显示为原条形码图片大小的比例，百分比
//            img.scalePercent(60);
//
//            contentByte.addImage(img);
//        }
//    }
//
//    /**
//     * 设置二维码
//     *
//     * @param stamper
//     * @param prescription
//     * @throws IOException
//     * @throws DocumentException
//     */
//    private void setQRCode(PdfStamper stamper, TPrescription prescription) throws IOException, DocumentException {
//        TSysParam sysParam = tSysParamService.getSysParam(Constant.PRINT_PRESCRIPTION_SHOW_QRCODE);
//        //sysParam值为1则打印出二维码
//        if (sysParam != null && Constant.BASIC_STRING_ONE.equals(sysParam.getParValues())) {
//            if (!StringUtils.isBlank(prescription.getPreNo())) {
//                byte[] logoQRCodeForZip = ZXingCode.getLogoQRCodeForZip(decoctUrl + prescription.getPreNo(), null, null);
//                if (null != logoQRCodeForZip) {
//                    Image img = Image.getInstance(logoQRCodeForZip);
//                    for (int i = 1; i <= stamper.getReader().getNumberOfPages(); i++) {
//                        PdfContentByte contentByte = stamper.getOverContent(i);
//                        Rectangle pageSize = stamper.getReader().getPageSize(i);
//                        float height = pageSize.getHeight();
//                        float width = pageSize.getWidth();
//                        //居中显示
//                        img.setAlignment(1);
//                        //显示位置，根据需要调整
//                        img.setAbsolutePosition(width - 85, height - 110);
//                        //显示为原图片大小的比例，百分比
//                        img.scalePercent(45);
//                        contentByte.addImage(img);
//                    }
//                }
//            }
//        }
//    }
//
//}
