package com.cbkj.diagnosis.common.utils;

/**
 * <AUTHOR>
 * @Description 自定义一个MultipartFile的实现类，用来把文件转成MultipartFile
 * @Date 2025/9/10 16:45
 * @Version 1.0
 */
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;

public class LocalMultipartFile implements MultipartFile {

    private final String name;              // 表单字段名
    private final String originalFilename;  // 原始文件名
    private final String contentType;       // MIME 类型
    private final byte[] content;           // 文件内容

    /**
     * 使用 File 构造
     */
    public LocalMultipartFile(String name, File file) throws IOException {
        this.name = name;
        this.originalFilename = file.getName();
        String type = Files.probeContentType(file.toPath());
        this.contentType = (type != null) ? type : "application/octet-stream";
        this.content = Files.readAllBytes(file.toPath());
    }

    /**
     * 使用自定义参数构造
     */
    public LocalMultipartFile(String name, String originalFilename,
                              String contentType, byte[] content) {
        this.name = name;
        this.originalFilename = originalFilename;
        this.contentType = (contentType != null ? contentType : "application/octet-stream");
        this.content = (content != null ? content : new byte[0]);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public String getOriginalFilename() {
        return this.originalFilename;
    }

    @Override
    public String getContentType() {
        return this.contentType;
    }

    @Override
    public boolean isEmpty() {
        return this.content.length == 0;
    }

    @Override
    public long getSize() {
        return this.content.length;
    }

    @Override
    public byte[] getBytes() {
        return this.content.clone();
    }

    @Override
    public InputStream getInputStream() {
        return new ByteArrayInputStream(this.content);
    }

    @Override
    public void transferTo(File dest) throws IOException {
        // 确保目录存在
        File parent = dest.getParentFile();
        if (parent != null && !parent.exists()) {
            if (!parent.mkdirs()) {
                throw new IOException("Failed to create directory: " + parent.getAbsolutePath());
            }
        }
        try (FileOutputStream out = new FileOutputStream(dest)) {
            out.write(this.content);
        }
    }
}


