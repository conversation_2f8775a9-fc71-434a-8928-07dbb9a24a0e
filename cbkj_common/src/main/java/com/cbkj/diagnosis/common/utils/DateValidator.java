package com.cbkj.diagnosis.common.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/29 11:25
 * @Version 1.0
 */
public class DateValidator {
    private static final Pattern DATE_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");

    public static boolean isValidDate(String date) {
        // 先进行正则表达式的匹配检查
        if (!DATE_PATTERN.matcher(date).matches()) {
            return false;
        }

        try {
            // 尝试将日期字符串转换为LocalDate对象
            LocalDate.parse(date, DateTimeFormatter.ISO_DATE);
            // 如果转换成功，且也匹配正则表达式，则是有效日期
            return true;
        } catch (DateTimeParseException e) {
            // 如果转换失败，则不是有效日期
            return false;
        }
    }

    public static void main(String[] args) {
        String date = " ";
        boolean isValid = isValidDate(date);
        System.out.println("Is valid date: " + isValid);
    }
}
