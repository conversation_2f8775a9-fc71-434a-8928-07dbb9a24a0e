package com.cbkj.diagnosis.common.utils;

import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class Constant {
    //菜单键
    public static final String MENUS = "MENUS";

    public static final String SUCCESS = "success";

    public static final String SUCCESS_DX = "SUCCESS";

    public static final String ENCODED = "UTF-8";

    public static final String FAILMESSAGE = "服务异常，请稍后重试！";

    public static final String ERRORNOTTROW = "服务异常！！";

    public static final String NOTPARAMS = "缺少参数，请检查！";

    public static final String UNDEFINED = "未知错误，未定义！";

    public static final String YES = "yes";

    public static final String NO = "no";

    public static final String OK = "ok";

    public static final String PONIT = ".";

    public static final String YES_SHORT = "Y";

    public static final String NO_SHORT = "N";

    public static final String BLOCKED = "BLOCKED";

    public static final String BLOCKEDMSG = "请别着急，当前操作正在进行中！";

    public static final String REPEATMSG = "请不要频繁操作！";

    public static final String TOKENNAME = "token";

    public static final boolean TRUE = true;

    public static final boolean FALSE = false;

    public static final String AUTHYES = "1";

    public static final String AUTHNO = "0";

    public static final String WEBSERVICEFAIL = "webService暂时不可用！！";

    public static final String ERRORPAGE = "error/400";

    //系统管理员
    public static final String ADMIN = "admin";

    /**
     * 超级管理员角色(神级)
     **/
    public static final String ROLEGRADE = "70810c874405453b99c6c2cf72296fe5";

    /**
     * 超级管理员角色(非神级)
     **/
    public static final String MANAGER = "f85aa731d9d144d1bc1d72cf3e877a4d";

    public static final String ENGLISH_COMMA = ",";

    public static final BigDecimal BIG_DECIMAL_ZERO = new BigDecimal("0");

    /************************* 删除标记 ***************************/
    public static final String BASIC_DEL_YES = "1";
    public static final String BASIC_DEL_NO = "0";

    /************************* 对错标记 ***************************/
    public static final String BASIC_STRING_TRUE = "true";
    public static final String BASIC_STRING_FALSE = "false";
    public static final String BASIC_STRING_SPACE = "";

    /************************* 数字字符串 ***************************/
    public static final String BASIC_STRING_MINUS_ONE = "-1";
    public static final String BASIC_STRING_ZERO = "0";
    public static final String BASIC_STRING_ONE = "1";
    public static final String BASIC_STRING_TWO = "2";
    public static final String BASIC_STRING_THREE = "3";
    public static final String BASIC_STRING_FOUR = "4";
    public static final String BASIC_STRING_FIVE = "5";
    public static final String BASIC_STRING_SIX = "6";
    public static final String BASIC_STRING_SEVEN = "7";
    public static final String BASIC_STRING_EIGHT = "8";
    public static final String BASIC_STRING_NINE = "9";
    public static final String BASIC_STRING_TEN = "10";
    public static final String BASIC_STRING_ELEVEN = "11";
    public static final String BASIC_STRING_TWELVE = "12";

    /************************* 协定方固定文件夹 ***************************/
    public static final String FOLDER_INS = "ins";
    public static final String FOLDER_DEPT = "dept";
    public static final String FOLDER_SELF = "self";
    public static final String FOLDER_INS_OTHER = "ins-other";
    public static final String FOLDER_DEPT_OTHER = "dept-other";
    public static final String FOLDER_SELF_OTHER = "self-other";
    public static final String FOLDER_KNOW = "know";
    public static final String FOLDER_KNOW_ = "know-";
    public static final String FOLDER_FORMULA = "formula";

    /**
     * 开方访问知识库的ID
     **/
    public static final String KNOW_APP_ID = "100001";
    public static final String KNOW_APP_PWD = "100001";


    /************************* 所有医联体 ***************************/
    public static final String BASIC_APP_ID = "000000";

    /************************* 所有医疗机构 ***************************/
    public static final String BASIC_INS_CODE = "000000";

    /************************* 所有科室 ***************************/
    public static final String BASIC_DEPT_ID = "000000";
    /************************* 所有药房 ***************************/
    public static final String BASIC_PHA_ID = "000000";
    /************************* 用户扩展表代码 ***************************/
    public static final int ADMIN_EXT_ZERO = 0;
    public static final int ADMIN_EXT_SHURUMA = 1;
    public static final int ADMIN_EXT_INTRODUCTION = 2;
    public static final int ADMIN_EXT_PERSONAL_SHARE = 3;
    public static final int ADMIN_EXT_TEMPLATE_SHARE = 4;
    public static final int ADMIN_EXT_PRE_SHARE = 5;
    public static final int ADMIN_EXT_PRE_SIX = 6;
    public static final int ADMIN_EXT_PRE_ELEVEN = 7;
    public static final int ADMIN_EXT_PRE_EIGHT = 8;
    public static final int ADMIN_EXT_INDIVISIBLE_TIP = 9;
    public static final int ADMIN_EXT_INDIVISIBLE_TEN = 10;

    public static final long ADMIN_ZERO = 0;
    public static final long ADMIN_SHURUMA = 1;
    public static final long ADMIN_INTRODUCTION = 2;
    public static final long ADMIN_PERSONAL_SHARE = 3;
    public static final long ADMIN_TEMPLATE_SHARE = 4;
    public static final long ADMIN_PRE_SHARE = 5;
    public static final long ADMIN_PRE_SIX = 6;
    public static final long ADMIN_PRE_ELEVEN = 7;
    public static final long ADMIN_PRE_EIGHT = 8;
    public static final long ADMIN_INDIVISIBLE_TIP = 9;
    public static final long ADMIN_INDIVISIBLE_TEN = 10;

    /************************* 系统参数表代码 ***************************/
    //患者就诊范围（1医联体  2医疗机构）
    public static final String PATIENT_VISIT_RANGE = "PATIENT_VISIT_RANGE";
    //首个诊疗功能（1中医电子病历 2智能辨证 3智能中医处方）
    public static final String FIRST_DIAGNOSIS_FUNCTION = "FIRST_DIAGNOSIS_FUNCTION";
    //历史病历显示范围（1医联体 2医疗机构）
    public static final String HISTORY_RECORD = "HISTORY_RECORD";
    //中医病历是否必填（1是0否）
    public static final String RECORD_MUST_SAVE = "RECORD_MUST_SAVE";
    //参考医案-医案筛选默认类型（1我的医案 2名家医案）
    public static final String DEFAULT_VERIFY_TYPE = "DEFAULT_VERIFY_TYPE";
    //国医大师专病（zyjb12173骨痹病 zyjb12076头痛病 zyjb12624喉痹病 zyjb12056痰饮病 zyjb12171风湿痹病 zyjb12121感冒病 zyjb12003咳嗽病 zyjb12169痛风病 zyjb12060便秘病 zyjb12039胃痞病 zyjb12050泄泻病 zyjb12358月经过多 zyjb12024不寐病 zyjb12376围绝经期 zyjb12295粉刺病 zyjb12170消渴病 zyjb12361痛经病 zyjb12078眩晕病 zyjb12359月经过少 zyjb12615鼻鼽病 zyjb12002肺系病类）
    public static final String MASTER_DISEASE = "MASTER_DISEASE";
    //处方开立时选择药房或者中药类型（值：1药房（内容从药房表里取）。 2中药类型（后面拼药品类型））
    public static final String PRESCRIPTION_CHOSE = "PRESCRIPTION_CHOSE_DRUGSTORE_OR_DRUGTYPE";
    //有效的处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方）
    public static final String VALID_PRESCRIPTION_TYPE = "VALID_PRESCRIPTION_TYPE";
    //颗粒剂处方是否限制为同一产地（1是  0否）
    public static final String PRESCRIPTION_GRANULE_SAME_PLACE = "PRESCRIPTION_GRANULE_SAME_PLACE";
    //开方页面“医保”列显示数据来源，可配置数据来源“his接口数据”或“医保外配置”(1his接口数据 2医保外配置)
    public static final String PRESCRIPTION_MEDICAL_INSURANCE_SOURCE = "PRESCRIPTION_MEDICAL_INSURANCE_SOURCE";
    //安全用药特殊签字配置（1禁用药、2忌用药、3慎用药、4十八反、5十九畏、6不宜同用、7孕妇忌用、8孕妇慎用、9使用注意、10剂量超标、11毒、12病症禁忌、13饮食禁忌、14禁孕、15超规定用药量、16剂量偏低）
    public static final String SAFETY_EVALUATION_SIGN = "SAFETY_EVALUATION_SIGN";
    //处方剂量：超剂量提醒文字设置 (医保限制报销|强制保存|超医保规定用药量)
    public static final String PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS = "PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS";
    //中心药房同规格多个产地的药品，按策略映射到HIS同规格的药品（1随机 2零售价最高的 3药品代码最小的）
    public static final String CENTER_DRUG_MAPPING_STRATEGY = "CENTER_DRUG_MAPPING_STRATEGY";
    //外用处方院内治疗费用
    public static final String PRESCRIPTION_EXTERNAL_TREATMENT_FEE = "PRESCRIPTION_EXTERNAL_TREATMENT_FEE";
    //开方界面是否显示代煎贴数(0不显示 1显示)
    public static final String PRESCRIPTION_DECOCT_SHOW = "PRESCRIPTION_DECOCT_SHOW";

    //内服中药方显示项目（1序号 2图标 3药品名称 4规格 5剂量 6单位 7用法 8产地 9单价 10库存 11医保）
    public static final String PRESCRIPTION_INTERNAL_COLUMN = "PRESCRIPTION_INTERNAL_COLUMN";
    //外用中药方显示项目（1序号 2图标 3药品名称 4规格 5剂量 6单位 7用法 8产地 9单价 10库存 11医保）
    public static final String PRESCRIPTION_EXTERNAL_COLUMN = "PRESCRIPTION_EXTERNAL_COLUMN";
    //中成药处方显示项目（1序号 2图标 3药品名称 4规格 5剂量 6单位 7用法 8产地 9单价 10库存 11医保）
    public static final String PRESCRIPTION_PATENT_COLUMN = "PRESCRIPTION_PATENT_COLUMN";
    //适宜技术方显示项目（1序号 2穴位 3针数 ）
    public static final String PRESCRIPTION_ACUPOINT_COLUMN = "PRESCRIPTION_ACUPOINT_COLUMN";

    //内服中草药单帖最高限价金额（通用单帖金额|膏方单帖金额（0为无限制））
    public static final String PRESCRIPTION_INTERNAL_MAXIMUM_AMOUNT = "PRESCRIPTION_INTERNAL_MAXIMUM_AMOUNT";
    //外用中草药单帖最高限价金额（0则无限制）
    public static final String PRESCRIPTION_EXTERNAL_MAXIMUM_AMOUNT = "PRESCRIPTION_EXTERNAL_MAXIMUM_AMOUNT";

    //内服中药方处方金额限制最高价格（通用处方金额|膏方处方金额（0为无限制））
    public static final String PRESCRIPTION_INTERNAL_HIGHEST_MONEY = "PRESCRIPTION_INTERNAL_HIGHEST_MONEY";
    //内服中药方处方金额最低价格限制（0则无限制）
    public static final String PRESCRIPTION_INTERNAL_LOWEST_MONEY = "PRESCRIPTION_INTERNAL_LOWEST_MONEY";

    //内服中药方最高贴数限制（0则无限制）
    public static final String PRESCRIPTION_INTERNAL_HIGHEST_NUMBER = "PRESCRIPTION_INTERNAL_HIGHEST_NUMBER";
    //内服中药方最低贴数限制（0则无限制）
    public static final String PRESCRIPTION_INTERNAL_LOWEST_NUMBER = "PRESCRIPTION_INTERNAL_LOWEST_NUMBER";

    //内服中药方最高药味数（通用药味数|膏方药味数（0为无限制））
    public static final String PRESCRIPTION_INTERNAL_HIGHEST_MATS = "PRESCRIPTION_INTERNAL_HIGHEST_MATS";
    //内服中药方最低药味数（0则无限制）
    public static final String PRESCRIPTION_INTERNAL_LOWEST_MATS = "PRESCRIPTION_INTERNAL_LOWEST_MATS";

    //外用中药方处方金额最高价格限制（0则无限制）
    public static final String PRESCRIPTION_EXTERNAL_HIGHEST_MONEY = "PRESCRIPTION_EXTERNAL_HIGHEST_MONEY";
    //外用中药方处方金额最低价格限制（0则无限制）
    public static final String PRESCRIPTION_EXTERNAL_LOWEST_MONEY = "PRESCRIPTION_EXTERNAL_LOWEST_MONEY";
    //外用中药方最高贴数限制（0则无限制）
    public static final String PRESCRIPTION_EXTERNAL_HIGHEST_NUMBER = "PRESCRIPTION_EXTERNAL_HIGHEST_NUMBER";
    //外用中药方最低贴数限制（0则无限制）
    public static final String PRESCRIPTION_EXTERNAL_LOWEST_NUMBER = "PRESCRIPTION_EXTERNAL_LOWEST_NUMBER";
    //外用中药方最高药味数（0则无限制）
    public static final String PRESCRIPTION_EXTERNAL_HIGHEST_MATS = "PRESCRIPTION_EXTERNAL_HIGHEST_MATS";
    //外用中药方最低药味数（0则无限制）
    public static final String PRESCRIPTION_EXTERNAL_LOWEST_MATS = "PRESCRIPTION_EXTERNAL_LOWEST_MATS";

    //特病中药方最高药味数（0则无限制）
    public static final String PRESCRIPTION_SPECIAL_HIGHEST_MATS = "PRESCRIPTION_SPECIAL_HIGHEST_MATS";
    //特病中药方最低药味数（0则无限制）
    public static final String PRESCRIPTION_SPECIAL_LOWEST_MATS = "PRESCRIPTION_SPECIAL_LOWEST_MATS";


    /**
     * 需审核的处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方）
     */
    public static final String CHECK_PRESCRIPTION_TYPE = "CHECK_PRESCRIPTION_TYPE";

    /**
     * 控制超安全用药权限:
     * 可配置的值包括1禁用药、2忌用药、3慎用药、4十八反、5十九畏、6不宜同用、7孕妇禁用、8孕妇慎用、9孕妇忌用、10大毒、11有毒、12小毒，默认空。
     */
    public static final String SPECIAL_DRUGS_QUALIFICATIONS = "SPECIAL_DRUGS_QUALIFICATIONS";
    /**
     * 是否需要审核（1无中医资质需要审核，有中医资质直接过 2所有医生都需要审核 3所有医生不需要审核）
     */
    public static final String CHECK_OR_NOT = "CHECK_OR_NOT";
    /**
     * 是否可以系统审核（0.全部时间段不可以系统审核；1.全部时间段可以系统审核；2.审方工作时间内不可以系统审核，审方工作时间外可以系统审核；3、审方工作时间内不可以系统审核，审方工作时间外不审核；4、审方工作时间内可以系统审核，审方工作时间外不审核）
     */
    public static final String CHECK_OR_NOT_BY_SYS = "CHECK_OR_NOT_BY_SYS";
    /**
     * 知识库的方剂未做更改则自动审核通过（1开启 0关闭）
     */
    public static final String SYS_CHECK_KNOW_PRE = "SYS_CHECK_KNOW_PRE";
    /**
     * 通过院内方未做更改则自动审核通过（1开启 0关闭）
     */
    public static final String SYS_CHECK_PERSONAL_PRE = "SYS_CHECK_PERSONAL_PRE";
    /**
     * 处方未超出医保单帖剂量上限则自动审核通过（1开启 0关闭）
     */
    public static final String SYS_CHECK_DAILY_MAX_DOSE = "SYS_CHECK_DAILY_MAX_DOSE";
    /**
     * 配方自动审核通过（1开启 0关闭）
     */
    public static final String SYS_CHECK_FORMULA = "SYS_CHECK_FORMULA";
    /**
     * 审核工作时间（星期与时间段用中文分号：分割，多个时间段用|分割，多个星期用中文分号；分隔。不在审方时间段将自动审核通过，处方标记为‘下班自动通过’。默认空，不支持下班自动审核）
     */
    public static final String SYS_CHECK_WORK_TIME = "SYS_CHECK_WORK_TIME";
    /**
     * 系统审核人名称
     */
    public static final String SYS_CHECK_USER_NAME = "SYS_CHECK_USER_NAME";


    //协定方是否区分住院和门诊（1是 0否）
    public static final String PERSONAL_PRESCRIPTION_MZ_ZY = "PERSONAL_PRESCRIPTION_MZ_ZY";

    //是否使用接口项目（1使用 0不使用）
    public static final String PRE_INTERFACE = "PRE_INTERFACE";
    //知识库疾病证型显示类型（1：95标准，2：95修订）
    public static final String KNOWLEDGE_SHOW_TYPE = "KNOWLEDGE_SHOW_TYPE";
    /**
     * 保存生成处方号(new：生成新的，old：使用旧的)
     */
    public static final String SAVE_PRESCRIPTION_NO = "SAVE_PRESCRIPTION_NO";
    /**
     * 未缴费处方去修改使用的挂号ID(new：生成新的，old：使用旧的)
     */
    public static final String SAVE_REGISTER_ID = "SAVE_REGISTER_ID";
    /**
     * know：知识库；his：HIS；其他值不开启特殊用法
     */
    public static final String MAT_SPECIAL_USAGE_SOURCE = "MAT_SPECIAL_USAGE_SOURCE";
    /**
     * 特病从HIS获取治疗方案（新都门特血透从HIS获取治疗方案，参数值配置血透的疾病编码）
     */
    public static final String SPECIAL_DIS_FROM_HIS = "SPECIAL_DIS_FROM_HIS";
    /**
     * 特病能否选择 1是0否
     */
    public static final String SPECIAL_DIS_CAN_CHOOSE = "SPECIAL_DIS_CAN_CHOOSE";
    /**
     * 特病处方来源 his：接口his/hisSpecialPres；其他开空白方
     */
    public static final String SPECIAL_DIS_PRES_SOURCE = "SPECIAL_DIS_PRES_SOURCE";
    /**
     * 特病（内服处方）可修改列 3药品名称 4规格 5剂量 6单位 7用法 8产地 9单价 10库存 11医保
     */
    public static final String SPECIAL_DIS_MODIFY_COLUMN = "SPECIAL_DIS_MODIFY_COLUMN";
    /**
     * 处方自动作废的时间(值必须为整数，单位小时)
     */
    public static final String PRESCRIPTION_DEL_TIME = "PRESCRIPTION_DEL_TIME";
    /**
     * 医保支付条件限制的对象	0）不限制（所有人不提醒），1）门特病人和住院病人
     */
    public static final String INSURANCE_LIMIT_OBJECT = "INSURANCE_LIMIT_OBJECT";
    /**
     * 医保限制提醒弹框内容	1日最大剂量、2医保外药品、3基金不支付
     */
    public static final String INSURANCE_LIMIT_TIP = "INSURANCE_LIMIT_TIP";
    /**
     * 修改处方时是否删除原处方 1是 0否
     */
    public static final String UPDATE_PRE_DEL_OLD = "UPDATE_PRE_DEL_OLD";
    /**
     * 住院医嘱显示项
     */
    public static final String INPATIENT_ADVICE_DISPLAY = "INPATIENT_ADVICE_DISPLAY";
    /**
     * 搜索科室协定方是否过滤当前科室  0否（可以看所有授权的科室协定方） 1是（只能看当前科室的科室协定方）
     */
    public static final String PERSONAL_PRESCRIPTION_FILTER_DEPT = "PERSONAL_PRESCRIPTION_FILTER_DEPT";
    /**
     * 搜索个人协定方是否过滤医疗机构  0（不过滤，可以使用全部个人协定方）1（过滤，只能使用当前医疗机构的个人协定方）
     */
    public static final String PERSONAL_PRESCRIPTION_FILTER_SELF_BY_INS = "PERSONAL_PRESCRIPTION_FILTER_SELF_BY_INS";
    /**
     * 协定方添加配方选项  1：表示添加配方选项
     */
    public static final String APPOINT_AS_FORMULA = "APPOINT_AS_FORMULA";

    public static final String PRINT_PRESCRIPTION_SHOW_QRCODE = "PRINT_PRESCRIPTION_SHOW_QRCODE";
    /**
     * 中医电子病历打印模板参数控制
     */
    public static final String REC_PDF_TEMPLATE = "REC_PDF_TEMPLATE";
    /************************* 系统分类代码ID ***************************/
    //中药用法
    public static final String CODE_MAT_USEAGE = "1";
    //中药单位
    public static final String CODE_MAT_UNIT = "4";
    //内服处方服法
    public static final String CODE_PRE_INTERNAL_DESCRIPTION = "5";
    //内服处方频次
    public static final String CODE_PRE_INTERNAL_FREQUENCY = "6";
    //内服处方服药时间
    public static final String CODE_PRE_INTERNAL_USETIME = "7";
    //内服处方浓煎
    public static final String CODE_PRE_INTERNAL_ML = "8";
    //外用处方浓煎
    public static final String CODE_PRE_EXTERNAL_ML = "9";
    //外用处方频次
    public static final String CODE_PRE_EXTERNAL_FREQUENCY = "13";
    //外用方式
    public static final String CODE_PRE_EXTERNAL_TYPE = "11";
    //熏蒸仪选择
    public static final String CODE_PRE_EXTERNAL_INSTRUMENT = "12";
    //适宜技术方类型
    public static final String CODE_PRE_ACU_TYPE = "21";
    //针刺项目
    public static final String CODE_PRE_ACU_PROJECT = "22";
    //针刺项目
    public static final String CODE_ANALYSIS_RESULT = "29";
    //制剂药品用法
    public static final String CODE_PREPARATION_MAT_USEAGE = "30";
    //制剂药品频次
    public static final String CODE_PREPARATION_MAT_FREQUENCY = "31";
    //膏方有无糖
    public static final String CODE_PREPARATION_INTERNAL_PRODUCTION_TYPE = "33";

    //发送验证码模版
    public static final String SYS_TEMPLATE_SEND_CODE = "QnVR92";


    /*********************************** 计算处方金额时四舍五入保留位数 **************************************/
    // 销售价四舍五入保留小数
    public static final Integer TO_FIXED_XSJ = 6;
    // 剂量四舍五入保留小数
    public static final Integer TO_FIXED_DOSE = 2;
    // 单帖金额四舍五入保留小数
    public static final Integer TO_FIXED_SINGLE = 2;


    public static final String[] PERSONAL_PRESCRIPTION_EXPERT_TITLE = {"", "国医大师", "国家级名老中医", "省级名老中医", "名医"};
    public static final String[] PERSONAL_SHARE_TEXT = {"个人", "科室", "全院"};
    public static final String[] TEMPLATE_SHARE_TEXT = {"私有", "科室", "医疗机构", "医共体"};
    public static final String[] DEFAULT_PRE_TEXT = {"", "内服中药方", "外用中药方", "", "适宜技术方"};

    /*********************************** 今日病人 **************************************/

    public static final String REGISTER_TIME_ARANGE_1 = "1";
    public static final String REGISTER_TIME_ARANGE_2 = "2";

    public static final String REGISTER_TIME_ARANGE_AM = "上午";
    public static final String REGISTER_TIME_ARANGE_PM = "下午";

    public static final List<String> UNIT_LIST_G = Arrays.asList("g", "G", "克");

    public static final int ListSize = 2000;
    /**
     * 内服
     */
    public static final String PRESCRIPTION_INTERNAL_GRID = "PRESCRIPTION_INTERNAL_GRID";
    /**
     * 外用
     */
    public static final String PRESCRIPTION_EXTERNAL_GRID = "PRESCRIPTION_EXTERNAL_GRID";
    /**
     * 适宜技术
     */
    public static final String PRESCRIPTION_ACUPOINT_GRID = "PRESCRIPTION_ACUPOINT_GRID";
    /**
     * 输入
     */
    public static final String SHURUMA = "ADMIN_EXT_SHURUMA";

    /**
     * 是否审核后直接推送到药房
     */
    public static final String ISOLATED_POINTS_SEND_PHARMACY = "ISOLATED_POINTS_SEND_PHARMACY";
    /**
     * 是否使用隔离点的地址
     */
    public static final String ISOLATED_POINTS_ADDRESS = "ISOLATED_POINTS_ADDRESS";

    /**
     * 处方同种草药判定规则
     */
    public static final String PRESCRIPTION_MEDICINE_COMPARE_RULE = "PRESCRIPTION_MEDICINE_COMPARE_RULE";


    public static final String HIS_URL_TYPE_SQL = "1,2,3";
    public static final String HIS_URL_TYPE_HTTP = "4";
    public static final String HIS_URL_TYPE_WEB = "5";



    public static final String CACHE_PREFIX = "diagnosis-web-api";
    public static final String USER_NAME_OR_PASSWORD_ERROR ="用户名或密码错误";
    public static final String ACCOUNT_LOCK_PROMPT = "%s还剩下%s次尝试机会！";


}