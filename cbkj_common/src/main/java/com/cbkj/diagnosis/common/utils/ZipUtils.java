package com.cbkj.diagnosis.common.utils;

import lombok.extern.slf4j.Slf4j;


import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
public class ZipUtils {




    public static void exponseZip( HttpServletResponse response, List<byte[]> picBuff, String[] sname) {

            //文件的名称
            String downloadFilename = "已填问卷压缩包"+DateUtil.getDateFormats(DateUtil.YYYYMMDD, null)+".zip";
            //转换中文否则可能会产生乱码
        try {
            downloadFilename = URLEncoder.encode(downloadFilename, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        // 指明response的返回对象是文件流
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            // 设置在下载框默认显示的文件名
            response.setHeader("Content-Disposition", "attachment;filename=" + downloadFilename);
        try (
                ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());

        ) {
            //ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());

            for (int i = 0; i < picBuff.size(); i++) {
                try {
                    zos.putNextEntry(new ZipEntry(sname[i] + DateUtil.getDateFormats(DateUtil.YYYYMMDD, null)+ ".xlsx"));
                    zos.write(picBuff.get(i), 0, picBuff.get(i).length);
                } catch (IOException e) {
                    log.error(e.getLocalizedMessage());
                    log.error("Error writing file entry: {}", sname[i], e);
                }
                zos.flush();
            }
           // zos.close();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("Error exporting zip file: {}", e.getMessage(), e);
        }
    }
    public static void exposeZip(String zipname,HttpServletResponse response, List<byte[]> picBuff, String[] sname) {
        if (response == null || picBuff == null || sname == null || sname.length != picBuff.size()) {
            throw new IllegalArgumentException("HTTP response, picBuff, and sname must not be null or mismatched lengths.");
        }

        String downloadFilename = zipname + DateUtil.getDateFormats(DateUtil.YYYYMMDD, null) + ".zip";
        try {
             String downloadFilenameN = URLEncoder.encode(downloadFilename, "UTF-8");
             String downloadFilenameNew = "filename*=utf-8''" +
                    URLEncoder.encode(downloadFilename, "UTF-8")
                            .replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;" + downloadFilenameNew+";filename="+downloadFilenameN);

        } catch (UnsupportedEncodingException e) {
            log.error("Error encoding filename to UTF-8: {}", downloadFilename, e);
            // Consider logging this information, or rethrowing after logging
            throw new RuntimeException("Error encoding filename to UTF-8", e);
        }

        // Response setup
//        response.setContentType("application/octet-stream;charset=UTF-8");
        //Content-Type: application/zip
        response.setContentType("application/zip");
        response.setCharacterEncoding("UTF-8");
        //response.setHeader("Content-Disposition", "attachment;filename=" + downloadFilename);
        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            final String yyyyMMdd = DateUtil.getDateFormats(DateUtil.YYYYMMDD, null);

            for (int i = 0; i < picBuff.size(); i++) {
                String safeName = sname[i].replace("/", "") + yyyyMMdd + ".xlsx";
                ZipEntry entry = new ZipEntry(safeName); // 动态创建条目，避免数组越界
                try {
                    zos.putNextEntry(entry);
                    zos.write(picBuff.get(i));
                    zos.closeEntry(); // 每次写入后关闭
                } catch (IOException e) {
                    log.error("Error writing entry: {}", safeName, e);
                    throw new RuntimeException("Failed to write ZIP entry", e);
                }
            }
        } catch (IOException e) {
            log.error("ZIP generation failed", e);
            throw new RuntimeException("ZIP export error", e);
        }
//        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
//            final String yyyyMMdd = DateUtil.getDateFormats(DateUtil.YYYYMMDD, null); // Calculated once outside the loop
//
//            // Prepare zip entries
//            final ZipEntry[] zipEntries = new ZipEntry[sname.length];
//            for (int i = 0; i < sname.length; i++) {
//                String replace = sname[i].replace("/", "");
//                zipEntries[i] = new ZipEntry(replace + yyyyMMdd + ".xlsx");
//            }
//
//            // Write files to the zip
//            for (int i = 0; i < picBuff.size(); i++) {
//                try {
//                    zos.putNextEntry(zipEntries[i]);
//                    zos.write(picBuff.get(i), 0, picBuff.get(i).length);
//                    zos.closeEntry();
//                } catch (IOException e) {
//                    log.error("Error writing file entry: {}", sname[i], e);
//                    // Consider logging this information, or rethrowing after logging
//                }
////                zos.flush();
//            }
//            zos.flush();
//
//        } catch (IOException e) {
//            log.error("Error exporting zip file: {}", e.getMessage(), e);
//            // Consider logging this information, or rethrowing after logging
//        }
    }
}
