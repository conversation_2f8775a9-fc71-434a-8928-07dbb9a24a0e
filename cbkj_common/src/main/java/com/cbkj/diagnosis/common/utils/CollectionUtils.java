package com.cbkj.diagnosis.common.utils;

import java.util.*;
import java.util.function.Function;

/**
 * Created by zbh on 2024/6/18 15:18
 *
 * @description：集合工具类
 */
public class CollectionUtils {
    private static final int MAX_POWER_OF_TWO = 1073741824;

    public CollectionUtils() {
    }

    public static boolean isEmpty(Collection<?> coll) {
        return coll == null || coll.isEmpty();
    }

    public static boolean isNotEmpty(Collection<?> coll) {
        return !isEmpty(coll);
    }

    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }

    public static <K, V> HashMap<K, V> newHashMap() {
        return new HashMap();
    }

    public static <K, V> HashMap<K, V> newHashMapWithExpectedSize(int expectedSize) {
        return new HashMap(capacity(expectedSize));
    }

    public static <K, V> V computeIfAbsent(Map<K, V> concurrentHashMap, K key, Function<? super K, ? extends V> mappingFunction) {
        V v = concurrentHashMap.get(key);
        return v != null ? v : concurrentHashMap.computeIfAbsent(key, mappingFunction);
    }

    private static int capacity(int expectedSize) {
        if (expectedSize < 3) {
            if (expectedSize < 0) {
                throw new IllegalArgumentException("expectedSize cannot be negative but was: " + expectedSize);
            } else {
                return expectedSize + 1;
            }
        } else {
            return expectedSize < 1073741824 ? (int)((float)expectedSize / 0.75F + 1.0F) : Integer.MAX_VALUE;
        }
    }

    public static <K, V> List<V> getCollection(Map<K, V> map, Iterable<K> keys) {
        List<V> result = new ArrayList();
        if (map != null && !map.isEmpty() && keys != null) {
            keys.forEach((key) -> {
                Optional.ofNullable(map.get(key)).ifPresent(result::add);
            });
        }

        return result;
    }

    public static <K, V> List<V> getCollection(Map<K, V> map, Iterable<K> keys, Comparator<V> comparator) {
        Objects.requireNonNull(comparator);
        List<V> result = getCollection(map, keys);
        Collections.sort(result, comparator);
        return result;
    }

    /**
     * 判断List中是否存在null元素。
     *
     * @param list 要检查的List。
     * @return 如果List中存在至少一个null元素则返回true，否则返回false。
     */
    public static boolean listContainsNullElement(List<?> list) {
        return list != null && list.stream().anyMatch(Objects::isNull);
    }
}
