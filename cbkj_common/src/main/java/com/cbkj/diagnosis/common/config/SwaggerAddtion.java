//package com.jiuzhekan.cbkj.common.config;
//
//
//import com.fasterxml.classmate.TypeResolver;
//import com.google.common.collect.Sets;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.MediaType;
//import org.springframework.stereotype.Component;
//import springfox.documentation.builders.OperationBuilder;
//import springfox.documentation.builders.ParameterBuilder;
//import springfox.documentation.schema.ModelRef;
//import springfox.documentation.service.ApiDescription;
//import springfox.documentation.spi.DocumentationType;
//import springfox.documentation.spi.service.ApiListingScannerPlugin;
//import springfox.documentation.spi.service.contexts.DocumentationContext;
//import springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//public class SwaggerAddtion implements ApiListingScannerPlugin {
//    @Override
//    public List<ApiDescription> apply(DocumentationContext documentationContext) {
//        return new ArrayList<ApiDescription>(
//                Collections.singletonList(
//                        new ApiDescription(
//                                //url
//                                "/login",
//                                //描述
//                                "登录",
//                                Collections.singletonList(
//                                        new OperationBuilder(
//                                                new CachingOperationNameGenerator())
//                                                //http请求类型
//                                                .method(HttpMethod.POST)
////                                                .produces(Sets.newHashSet(MediaType.MULTIPART_FORM_DATA_VALUE))
//                                                .summary("登录")
//                                                //方法描述
//                                                .notes("登录")
//                                                //归类标签
//                                                .tags(Sets.newHashSet("登录"))
//                                                .parameters(
//                                                        Arrays.asList(
//                                                                new ParameterBuilder()
//                                                                        .description("用户名")
//                                                                        .type(new TypeResolver().resolve(String.class))
//                                                                        .name("name")
//                                                                        .parameterType("query")
//                                                                        .parameterAccess("access")
//                                                                        .required(true)
//                                                                        .defaultValue("admin")
//                                                                        .modelRef(new ModelRef("string"))
//                                                                        .build(),
//                                                                new ParameterBuilder()
//                                                                        .description("密码")
//                                                                        .type(new TypeResolver().resolve(String.class))
//                                                                        .name("pwd")
//                                                                        .parameterType("query")
//                                                                        .parameterAccess("access")
//                                                                        .required(true)
//                                                                        .defaultValue("SBHmAloiPE1GYTD8s/dIW8RMmCKcgVxWPdcQZHQ/hEZVh9RtL9Hu/vhYirx4lhWtUryWVbcnldwZ9W4id1QWYwgoTWeHNZnhrtkCV6dOy5az5wGZMvsebSwcdYz1AGg942ZTNXfU1IL5bNq1JbwZccDq1/4mV7u5xaDOHIHuMD0=")
//                                                                        .modelRef(new ModelRef("string"))
//                                                                        .build()
//                                                        ))
//                                                .build()),
//                                false)
//                ));
//    }
//
//    @Override
//    public boolean supports(DocumentationType documentationType) {
//        return DocumentationType.SWAGGER_2.equals(documentationType);
//    }
//
//}