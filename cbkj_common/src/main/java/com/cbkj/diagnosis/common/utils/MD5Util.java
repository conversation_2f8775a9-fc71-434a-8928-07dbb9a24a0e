package com.cbkj.diagnosis.common.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;


public class MD5Util {
	private MD5Util(){
	}

	public static byte[] md5Bytes(String text) {
		if (null == text || "".equals(text)) {
			return new byte[0];
		}

		MessageDigest msgDigest = null;
		try {
			msgDigest = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException e) {
			throw new IllegalStateException("System doesn't support MD5 algorithm.");
		}

		msgDigest.update(text.getBytes());
		return msgDigest.digest();
	}


	public static String md5(String text, boolean isReturnRaw) {
		if (null == text || "".equals(text)) {
			return text;
		}
		byte[] bytes = md5Bytes(text);
		if (isReturnRaw) {
			return new String(bytes);
		}

		StringBuilder md5Str = new StringBuilder();
		byte tb;
		char low;
		char high;
		char tmpChar;

		for (byte aByte : bytes) {
			tb = aByte;

			tmpChar = (char) ((tb >>> 4) & 0x000f);
			if (tmpChar >= 10) {
				high = (char) (('a' + tmpChar) - 10);
			} else {
				high = (char) ('0' + tmpChar);
			}
			md5Str.append(high);

			tmpChar = (char) (tb & 0x000f);
			if (tmpChar >= 10) {
				low = (char) (('a' + tmpChar) - 10);
			} else {
				low = (char) ('0' + tmpChar);
			}
			md5Str.append(low);
		}

		return md5Str.toString();
	}
	public static String encode(String password) {

		MessageDigest md5 = null;
		try {
			md5 = MessageDigest.getInstance("MD5");
		} catch (Exception e) {
			throw new RuntimeException(e);
		}

		byte[] byteArray = password.getBytes(StandardCharsets.UTF_8);

		byte[] md5Bytes = md5.digest(byteArray);
		StringBuilder hexValue = new StringBuilder();
		for (byte md5Byte : md5Bytes) {
			int val = ((int) md5Byte) & 0xff;
			if (val < 16) {
				hexValue.append("0");
			}

			hexValue.append(Integer.toHexString(val));
		}
		return hexValue.toString();
	}

	public static String md5(String text) {
		return md5(text, false);
	}


}