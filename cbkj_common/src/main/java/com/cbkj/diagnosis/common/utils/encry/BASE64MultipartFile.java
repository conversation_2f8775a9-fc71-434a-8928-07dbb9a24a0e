package com.cbkj.diagnosis.common.utils.encry;

import org.springframework.web.multipart.MultipartFile;

import java.io.*;

/**
 * Description
 * <p>
 * <PERSON><PERSON> loujian<PERSON>
 * <p>
 * Create 2019/1/22 11:35 AM
 * <p>
 * Company 杭州买吧信息科技有限公司
 * <p>
 * URL  www.mai8mall.com
 *
 * <AUTHOR>
public class BASE64MultipartFile implements MultipartFile {

    private final byte[] imgContent;

    private final String header;

    public BASE64MultipartFile(byte[] imgContent, String header) {
        this.imgContent = imgContent;
        this.header = header;
    }

    @Override
    public String getName() {
        String name = header.substring(header.lastIndexOf("/") + 1, header.lastIndexOf(";"));
        String suffix;
        if ("text".equals(name)) {
            suffix = "txt";
        } else if ("msword".equals(name)) {
            suffix = "doc";
        } else if ("vnd.openxmlformats-officedocument.wordprocessingml.document".equals(name)) {
            suffix = "docx";
        } else if ("vnd.ms-excel".equals(name)) {
            suffix = "xls";
        } else if ("vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(name)) {
            suffix = "xlsx";
        } else if ("pdf".equals(name)) {
            suffix = "pdf";
        } else if ("vnd.openxmlformats-officedocument.presentationml.presentation".equals(name)) {
            suffix = "pptx";
        } else if ("vnd.ms-powerpoint".equals(name)) {
            suffix = "ppt";
        } else if ("svg+xml".equals(name)) {
            suffix = "svg";
        } else if ("x-icon".equals(name)) {
            suffix = "ico";
        } else {
            suffix = name;
        }
        return suffix;
    }

    @Override
    public String getOriginalFilename() {


        return System.currentTimeMillis() + (int) (Math.random() * 10000) + "." + getName();
    }

    @Override
    public String getContentType() {
        return header.split(":")[1];
    }

    @Override
    public boolean isEmpty() {
        return imgContent == null || imgContent.length == 0;
    }

    @Override
    public long getSize() {
        return imgContent.length;
    }

    @Override
    public byte[] getBytes() {
        return imgContent;
    }

    @Override
    public InputStream getInputStream() {
        return new ByteArrayInputStream(imgContent);
    }

    @Override
    public void transferTo(File file) throws IOException, IllegalStateException {
        new FileOutputStream(file).write(imgContent);
    }
}


