package com.cbkj.diagnosis.common.utils;

import lombok.experimental.UtilityClass;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.StandardPBEByteEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

@UtilityClass
public class JasyptUtils {


    public  String encryptPwd(String password, String value) {
        PooledPBEStringEncryptor encryptOr = new PooledPBEStringEncryptor();
        encryptOr.setConfig(cryptOr(password));
        String result = encryptOr.encrypt(value);
        return result;
    }


    public  String decyptPwd(String password, String value) {
        PooledPBEStringEncryptor encryptOr = new PooledPBEStringEncryptor();
        encryptOr.setConfig(cryptOr(password));
        String result = encryptOr.decrypt(value);
        return result;
    }

    public  SimpleStringPBEConfig cryptOr(String password) {
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(password);
        config.setAlgorithm(StandardPBEByteEncryptor.DEFAULT_ALGORITHM);
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setStringOutputType("base64");
        return config;
    }

    public static void main(String[] args) {
        System.out.println(JasyptUtils.encryptPwd("::!qb#9467@","root@cbkj_tenant"));
        System.out.println(JasyptUtils.encryptPwd("::!qb#9467@","ZYzb$cbkj2024"));
        System.out.println(JasyptUtils.encryptPwd("::!qb#9467@","cbkj123!@#"));
        System.out.println(JasyptUtils.decyptPwd("::!qb#9467@","aclo8YgexnVjzKRQqc5AbtLVXARjJq1M/RquNN0ZIFM="));
        System.out.println(JasyptUtils.decyptPwd("::!qb#9467@","OPXCNabmBpi/FKayjb2kubBySH5LiQln"));
        System.out.println(JasyptUtils.decyptPwd("::!qb#9467@","qk0UBu3JMuh2VxsiRPcJ2cavr9LawxbE"));

        System.out.println(JasyptUtils.encryptPwd("::!qb#9467@","root"));
    }

}