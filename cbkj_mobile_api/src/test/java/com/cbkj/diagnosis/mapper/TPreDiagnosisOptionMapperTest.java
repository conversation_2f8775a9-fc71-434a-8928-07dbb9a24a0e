package com.cbkj.diagnosis.mapper;

import com.alibaba.fastjson2.JSON;
import com.cbkj.diagnosis.DiagnosisMobileApiApplication;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMapper;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/26 16:19
 * @Version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisMobileApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class TPreDiagnosisOptionMapperTest {
    @Autowired
    private TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;
    @Test
    void getDiagnosisEventCode() {
        System.out.println(JSON.toJSONString(tPreDiagnosisOptionMapper.getDiagnosisEventCode(new Integer[]{15124, 15129})));
    }
}