package com.cbkj.diagnosis.mapper;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.DiagnosisMobileApiApplication;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMutexMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/28 10:01
 * @Version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisMobileApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TPreDiagnosisOptionMutexMapperTest {

    @Resource
    private TPreDiagnosisOptionMutexMapper tPreDiagnosisOptionMutexMapper;

    @Test
    public void test_1() {
        List<String> stringListByOptionId = tPreDiagnosisOptionMutexMapper.getStringListByOptionId("1610");
        System.out.println(JSON.toJSONString(stringListByOptionId));
    }
}
