package com.cbkj.diagnosis.service;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.DiagnosisMobileApiApplication;
import com.cbkj.diagnosis.beans.business.TDisease;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.mapper.business.TDiseaseMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.service.common.strategy.record.impl.TDiseaseRes;
import com.cbkj.diagnosis.service.common.vo.NextQVo;
import com.cbkj.diagnosis.utils.AESPKCS7Util;
import com.cbkj.diagnosis.utils.SystemConstants;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisMobileApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TPreDiagnosisQuestionMapperTest extends TestCase {


    @Autowired
    private TDiseaseMapper tDiseaseMapper;

    @Autowired
    private TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;

    @Test
    public void testGetNextQ() {
        TDisease tDisease = new TDisease();
        tDisease.setDisType("1");
        tDisease.setDisName("nameWord");
        List<TDiseaseRes> listByTDisease = tDiseaseMapper.getListByTDisease(tDisease);


        ArrayList<Integer> questionIds = new ArrayList<>();
        questionIds.add(1);


        NextQVo nextQVo = new NextQVo();
        nextQVo.setQuestionIds(questionIds);
        nextQVo.setDiaId("1");
        List<TRecordDia> nextQ = tPreDiagnosisQuestionMapper.getNextQ(nextQVo);
        System.out.println(JSON.toJSONString(nextQ));


    }

    @Test
    public void test2() {
        String base64 = null;
        try {
            base64 = AESPKCS7Util.decrypt("=bdNf0KlySD8yz0+J/YuxqJ5M+tfkFscNzNxOmVxMZceAqZ7VuoTWs2jYCiC1sLk0EJR0qNIPivlwXwS/AGm+ut8akp8dz+PRcUAXuGxV6Y0ujTAJVFp7Ug7aKSmCrITVlmrLV/p5lpKopcBjgrOAlmr2sWxx6e0gZM0t1L4NzZM2fsnJFUwvBooC+EeSynkWxvDajQpzr/Moxaru5q/aRy518Es9tOBo0dcxtBC8geIuPBh1a1+y8AG1KVBk80FJ", SystemConstants.SYS_AES_KEY, "base64");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        System.out.println(base64);
    }
}