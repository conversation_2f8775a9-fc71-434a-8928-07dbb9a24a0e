package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.DiagnosisMobileApiApplication;
import com.cbkj.diagnosis.beans.AssociationUserInfo;
import com.cbkj.diagnosis.common.interceptor.advice.AdminInfoStrategy;
import com.cbkj.diagnosis.common.interceptor.advice.SM4Stategy;
import com.cbkj.diagnosis.controller.HisMobileController;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisMobileApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class MyRecordDetailServiceTest extends TestCase {
    @Resource
    private MyRecordDetailService myRecordDetailService;
    @Resource
    private TAdminInfoMapper tAdminInfoMapper;

    @Resource
    private HisMobileController hisMobileController;
    @Resource(name = "sm4Stategy")
    private SM4Stategy sm4Stategy;
    @Resource
    private AdminInfoStrategy adminInfoStrategy;
    @Test
    public void testGetPrePaper() {
//        ResEntity prePaper = myRecordDetailService.getPrePaper("9E7ZmIxjkZhHZtabbJGwb");
//        System.out.println(prePaper.getData());

        List<AssociationUserInfo> associationUserInfos = tAdminInfoMapper.getaAssociation("3a80b5a407e84fafa713018979ed775e");
        for (AssociationUserInfo associationUserInfo : associationUserInfos) {
            System.out.println(associationUserInfo.getUserName());
        }
    }

    @Test
    public void  test2(){
        //BFE08B01D984F0D5D95A13AD36A2B21C
       // hisMobileController.hisLogin("eaPlHbjYA8TrJXcieg1G6OMHzA71NYrNM1zqyvnd8KSAqZ7VuoTWs2jYCiC1sLk0QVVrptOKWICUtOwdg+mc9mSTGVy5o3ARbbZ8xDAT4pjT2NhPdMz7Xn//ahQu2/F2Siw70oeMcSVrc0ZqAOLVhyo93CS+bYhcMOGH8sSIqrJDLra+Hyz0FzG5hTncnEFxbc111F9DlKmCEpT3lchixvHC4TeGSWNgSxpkCPBnQwlZ1zGjbHtr6gkpSXVRGkLdLgXul69uADNLF/6EAzvdMw==");
        String zw = sm4Stategy.encrypt("胡77");
        System.out.println("加密"+zw);
        System.out.println("解密"+sm4Stategy.decrypt(zw));

        String s = adminInfoStrategy.encrypt("胡7");
        System.out.println("加密2"+s);
        System.out.println("解密2"+adminInfoStrategy.decrypt(s));
    }

}