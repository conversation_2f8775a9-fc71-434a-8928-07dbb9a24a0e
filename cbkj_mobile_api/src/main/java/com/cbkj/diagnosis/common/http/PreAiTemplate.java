package com.cbkj.diagnosis.common.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.common.http.XiaMenResEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.xml.soap.*;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * http请求组件
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PreAiTemplate extends RestTemplate {
    @Value("${pre.ai.url}")
    private String url;


    private final RestTemplate restTemplate;

    public PreAiTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }


    public XiaMenResEntity post(JSONObject params) {
        if ("-1".equals(url)){
            log.error("【云系统后端接口-发送患者预诊了】失败：未配置云系统");
            return XiaMenResEntity.error("没有云系统，不需要发送预诊信息到数字诊间");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);
        XiaMenResEntity xiaMenResEntity = null;
        try {
            xiaMenResEntity = restTemplate.postForObject(url+"/digitPad/preDiagnosis/form/done", request, XiaMenResEntity.class);
            log.info("【云系统后端接口-发送患者预诊了】成功 --- URL:{}, request:{}, response:{}", url+"/digitPad/preDiagnosis/form/done", request, JSON.toJSONString(xiaMenResEntity));
        } catch (Exception e) {
            log.error("【云系统后端接口-发送患者预诊了】异常 --- URL:{}, request:{}, error:{}", url+"/digitPad/preDiagnosis/form/done", request, e.getMessage());
        }
        return xiaMenResEntity;
    }







}