package com.cbkj.diagnosis.common.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.xml.soap.*;
import java.io.ByteArrayOutputStream;

/**
 * http请求组件
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RestWebTemplate extends RestTemplate {
//    @Value("${web.url}")
//    private String path;

    @Value("${diagnosis.interface.url}")
    private String diagnosisInterfaceUrl;


    private final RestTemplate restTemplate;

    public RestWebTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }


    public ResEntity sendPreDiagnosis( JSONObject params) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set("interface_token","123456");
        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);
        ResEntity xiaMenResEntity = null;
        try {
            xiaMenResEntity = restTemplate.postForObject(diagnosisInterfaceUrl+"/his/prediagnosis/push", request, ResEntity.class);
            log.info("【手机端调用接口后端发送患者预诊数据给his】成功 --- URL:{}, request:{}, response:{}", diagnosisInterfaceUrl+"/his/prediagnosis/push", request, JSON.toJSONString(xiaMenResEntity));
        } catch (Exception e) {
            log.error("【手机端调用接口后端发送患者预诊数据给his】异常 --- URL:{}, request:{}, error:{}", diagnosisInterfaceUrl+"/his/prediagnosis/push", request, e);
        }
        return xiaMenResEntity;
    }


}