package com.cbkj.diagnosis.utils;

import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.config.TokenBo;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by sang on 2017/12/30.
 */
@Slf4j
@UtilityClass
public class AdminUtils {

    /**
     * 获取当前登录管理员
     *
     * @return
     */
    public TAdminInfo getCurrentHr() {
        Logger logger = LoggerFactory.getLogger(AdminUtils.class);

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            logger.warn("Authentication is null");
            return null;
        }

        Object principal = authentication.getPrincipal();
        if (principal == null) {
            logger.warn("Principal is null");
            return null;
        }

        if (principal instanceof TokenBo) {
            return ((TokenBo) principal).getAdmin();
        } else if (principal instanceof TAdminInfo) {
            return (TAdminInfo) principal;
        } else {
            logger.warn("Unexpected principal type: {}", principal.getClass().getName());
            return null;
        }
    }



    /**
     * 获取真实IP
     *
     * @param request
     * @return
     */
    public String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

}