package com.cbkj.diagnosis.utils;

import com.github.pagehelper.util.StringUtil;

import java.util.regex.Pattern;

public class StringUtils {

    public static final char UNDERLINE = '_';

    private static Pattern humpPattern = Pattern.compile("[A-Z]");
    //匹配规则
    private static final String REGEX_OF_PHONE = "(^\\d{3})\\d.*(\\d{4}$)";
    //替换规则
    private static final String RRPLAGE_OF_PHONE = "$1****$2";

    public static String phoneMasking(String phone){
        if(!StringUtil.isEmpty(phone)){
            return phone.replaceAll(REGEX_OF_PHONE, RRPLAGE_OF_PHONE);
        }
        return null;
    }



}
