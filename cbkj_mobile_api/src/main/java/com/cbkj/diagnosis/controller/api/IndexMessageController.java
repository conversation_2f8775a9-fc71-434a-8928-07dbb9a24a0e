package com.cbkj.diagnosis.controller.api;

import com.cbkj.diagnosis.bean.GetFurtherConsultationList;
import com.cbkj.diagnosis.bean.bussines.GetHealthEducationDetails;
import com.cbkj.diagnosis.beans.AssociationUserInfo;
import com.cbkj.diagnosis.beans.business.TPropagandaEdu;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.ApiSRoadTaskPatientsService;
import com.cbkj.diagnosis.service.mobileapi.vo.MessageListVo;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Tag(name = "首页-消息列表接口", description = "首页-消息列表接口")
//@Api(value = "首页-消息列表接口", tags = "首页-消息列表接口")
@Controller
@RequestMapping("index/message")
public class IndexMessageController {

    private final ApiSRoadTaskPatientsService mobileSRoadTaskPatientsService;


    IndexMessageController(ApiSRoadTaskPatientsService mobileSRoadTaskPatientsService) {
        this.mobileSRoadTaskPatientsService = mobileSRoadTaskPatientsService;
    }

    @PostMapping(value = "getTypeNumber")
    //@ApiOperation(value = "获取复诊、健康宣教、医生随访未读数量", notes = "获取复诊、健康宣教、医生随访未读数量")
    @Operation(description = "获取复诊、健康宣教、医生随访未读数量", summary = "获取复诊、健康宣教、医生随访未读数量",responses = {
            @ApiResponse(description = "获取复诊、健康宣教、医生随访未读数量",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getMobileIndexNumber() {
        return ResEntity.success(mobileSRoadTaskPatientsService.getMobileIndexNumber());
    }


    @PostMapping(value = "/furtherConsultation/list")
    //@ApiOperation(value = "获取复诊消息列表", notes = "获取复诊列表", response = MessageListVo.class)
    @Operation(description = "获取复诊列表", summary = "获取复诊列表",responses = {
            @ApiResponse(description = "获取复诊列表",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = MessageListVo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getFurtherConsultationList(@RequestBody Page page) {
        return mobileSRoadTaskPatientsService.getFurtherConsultationList(page, Constant.BASIC_STRING_THREE);
    }

    @PostMapping(value = "/furtherConsultation/details")
    //@ApiOperation(value = "获取复诊详情", notes = "获取复诊详情", response = MessageListVo.class)
    @Operation(description = "获取复诊详情", summary = "获取复诊详情",responses = {
            @ApiResponse(description = "获取复诊详情",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = MessageListVo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getFurtherConsultationList(@RequestBody GetFurtherConsultationList getFurtherConsultationList) {
        return mobileSRoadTaskPatientsService.getFurtherConsultationDetails(getFurtherConsultationList.getTaskPatientsId());
    }

    @PostMapping(value = "/healthEducation/list")
    //@ApiOperation(value = "获取健康宣教消息列表", notes = "获取健康宣教列表")
    @Operation(description = "获取健康宣教消息列表", summary = "获取健康宣教消息列表",responses = {
            @ApiResponse(description = "获取健康宣教消息列表",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = MessageListVo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getHealthEducationList(@RequestBody Page page) {
        return mobileSRoadTaskPatientsService.getFurtherConsultationList(page, Constant.BASIC_STRING_ONE);
    }

    @PostMapping(value = "/healthEducation/details")
    //@ApiOperation(value = "获取健康宣教消息详情", notes = "获取健康宣教详情", response = TPropagandaEdu.class)
    @Operation(description = "获取健康宣教消息详情", summary = "获取健康宣教消息详情",responses = {
            @ApiResponse(description = "获取健康宣教消息详情",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TPropagandaEdu.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getHealthEducationDetails(@RequestBody GetHealthEducationDetails getHealthEducationDetails) {
        Object healthEducationDetails = mobileSRoadTaskPatientsService.getHealthEducationDetails(getHealthEducationDetails.getRoadExecuteEventContentId(),getHealthEducationDetails.getTaskPatientsId());
        mobileSRoadTaskPatientsService.updatePatientTaskStatus(getHealthEducationDetails.getTaskPatientsId());
        return ResEntity.success(healthEducationDetails);
    }

    /**
     * 从患者任务里面取，需要返回 task_patients_id
     *
     * @param page
     * @return
     */
//    @StatisticsDic
    @PostMapping(value = "/suiFang/list")
    //@ApiOperation(value = "首页获取随访消息列表", notes = "首页获取随访列表")
    @Operation(description = "首页获取随访消息列表", summary = "首页获取随访消息列表",responses = {
            @ApiResponse(description = "首页获取随访消息列表",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = MessageListVo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getSuiFangList(@RequestBody Page page) {
        return mobileSRoadTaskPatientsService.getSuiFangFurtherConsultationList(page, Constant.BASIC_STRING_TWO);
    }

}
