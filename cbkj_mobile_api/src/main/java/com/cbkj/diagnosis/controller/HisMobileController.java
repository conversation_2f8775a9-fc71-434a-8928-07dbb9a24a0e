package com.cbkj.diagnosis.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.TAdminInfo2;
import com.cbkj.diagnosis.beans.business.SysIns;
import com.cbkj.diagnosis.common.enums.IdentityDocumentType;
import com.cbkj.diagnosis.common.utils.IDUtil;
import com.cbkj.diagnosis.config.TokenBo;
import com.cbkj.diagnosis.config.TokenUtil;
import com.cbkj.diagnosis.config.annotaionUtil.LogAnnotaion;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.sys.SysInsService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AESPKCS7Util;
import com.cbkj.diagnosis.utils.CardUtil;
import com.cbkj.diagnosis.utils.SystemConstants;
import com.github.pagehelper.util.StringUtil;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

//@Api(value = "三方登录接口", tags = "三方登录接口")
@Controller
@RequestMapping(value = "/his")
@Slf4j
@Tag(name = "三方登录接口", description = "三方登录接口")
public class HisMobileController {
    @Value("${sys.aes.key}")
    private String key;
    private final TokenUtil tokenUtil;
    private final TAdminInfoMapper tAdminInfoMapper;
    private final SysInsService sysInsService;

    public HisMobileController(TokenUtil tokenUtil, TAdminInfoMapper tAdminInfoMapper, SysInsService sysInsService) {
        this.tokenUtil = tokenUtil;
        this.tAdminInfoMapper = tAdminInfoMapper;
        this.sysInsService = sysInsService;
    }

    //@ApiOperation(value = "三方登录接口", notes = "三方登录接口")
    @PostMapping(value = "/login")
    @ResponseBody
    @Transactional(rollbackFor = Exception.class)
    @LogAnnotaion(value = "移动端三方登录接口", isWrite = true)
    public Object hisLogin(@RequestBody Cbdata cbdata) {
        if (StringUtil.isNotEmpty(cbdata.getCbdata())) {
            String mak = cbdata.getCbdata();
            String content = null;
            try {
                content = AESPKCS7Util.decrypt(mak, key, "base64");
                log.info("content:{}", content);
            } catch (Exception e) {
                return ResEntity.error(e.getMessage());
            }
            if (StringUtil.isNotEmpty(content)) {
                JSONObject map = JSONObject.parseObject(content);

                //Map map = AESPKCS7Util.jsonToMap(content);
                String appId = (String) map.get("appId");
                String insCode = (String) map.get("insCode");
                String userName = (String) map.get("userName");
                log.info("userName:{}", userName);
                String mobile = (String) map.get("mobile");
                String cardNumber = (String) map.get("cardNumber");
                String insName = (String) map.get("insName");

                String patientCardType = (String) map.get("patientCardType");
                String healthCardNum = (String) map.get("healthCardNum");
                //挂号记录,回传预诊电子病历需要回传该字段,存到登录的缓存里好了，做完预诊单，直接存到预诊记录里面。
                String regPlanId = (String) map.get("regPlanId");
                log.info("==================================regPlanId:{}", regPlanId);
                if (StringUtils.isBlank(appId)) {
                    return ResEntity.error("缺少参数");
                }
                if (StringUtils.isBlank(insName)) {
                    return ResEntity.error("缺少参数");
                }
                if (StringUtils.isBlank(insCode)) {
                    return ResEntity.error("缺少参数");
                }
//                if (StringUtils.isBlank(healthCardNum)) {
//                    return ResEntity.error("缺少参数");
//                }
                if (StringUtils.isBlank(userName)) {
                    return ResEntity.error("缺少参数");
                }
                if (StringUtils.isBlank(mobile)) {
                    return ResEntity.error("缺少参数");
                }
                if (StringUtils.isBlank(patientCardType)) {
                    patientCardType = "01";
                } else {
                    if (!IdentityDocumentType.exists(patientCardType)) {
                        return ResEntity.error("患者证件类型代码错误。请检查文档规范要求");
                    }
                }
                //通过
                //检查机构是否存在。
                SysIns sysIns = sysInsService.checkAndInsertIns(insCode, insName, appId);
                TAdminInfo tAdminInfo = new TAdminInfo();
                tAdminInfo.setIdcard(cardNumber);
                tAdminInfo.setMobile(mobile);
                tAdminInfo.setName(userName);
                tAdminInfo.setPatientCardType(patientCardType);
                tAdminInfo.setHealthCardNum(healthCardNum);


                TAdminInfo2 tAdminInfo2 = new TAdminInfo2();
//                tAdminInfo2.setIdCard(cardNumber);
//                tAdminInfo2.setHealthCardNum(healthCardNum);
                //就根据手机号码，判断这个人。作为唯一依据
                tAdminInfo2.setMobile(mobile);
                TAdminInfo admin = tAdminInfoMapper.getHisLoginCheckUserInfo(tAdminInfo2);
                if (admin != null && StringUtils.isBlank(admin.getIdcard()) && StringUtils.isNotBlank(cardNumber)) {
                    if (admin.getUserName().equals(userName)) {
                        //更新
                        TAdminInfo tAdminInfo1 = new TAdminInfo();
                        //admin.setIdcard(cardNumber);
                        BeanUtils.copyProperties(admin, tAdminInfo1);
                        tAdminInfoMapper.updateByPrimaryKey(tAdminInfo1);
                        admin.setRegPlanId(regPlanId);
                        admin.setAppId(appId);
                        admin.setInsCode(insCode);
                        admin.setInsId(sysIns.getInsId());
                        admin.setInsName(sysIns.getInsName());
                        return getUserInfo(admin);
                    } else {

                        //新建
                        tAdminInfo.setRegPlanId(regPlanId);
                        tAdminInfo.setAppId(appId);
                        tAdminInfo.setInsCode(insCode);
                        tAdminInfo.setInsId(sysIns.getInsId());
                        tAdminInfo.setInsName(sysIns.getInsName());
                        return saveUserInfo(tAdminInfo, userName, mobile);
                    }
                }
                if (admin == null) {
                    tAdminInfo.setRegPlanId(regPlanId);
                    tAdminInfo.setAppId(appId);
                    tAdminInfo.setInsCode(insCode);
                    tAdminInfo.setInsId(sysIns.getInsId());
                    tAdminInfo.setInsName(sysIns.getInsName());

                    return saveUserInfo(tAdminInfo, userName, mobile);
                } else {
                    admin.setRegPlanId(regPlanId);
                    admin.setAppId(appId);
                    admin.setInsCode(insCode);
                    admin.setInsId(sysIns.getInsId());
                    admin.setInsName(sysIns.getInsName());
                    return getUserInfo(admin);
                }
            }
        }
        return ResEntity.error("登录失败");
    }

    public ResEntity saveUserInfo(TAdminInfo tAdminInfo, String userName, String mobile) {
        tAdminInfo.setUserId(IDUtil.getID());
        tAdminInfo.setCreateTime(new Date());
        if (StringUtil.isNotEmpty(tAdminInfo.getIdcard()) && (tAdminInfo.getIdcard().length() == 15 || tAdminInfo.getIdcard().length() == 18)) {
            tAdminInfo.setAge(CardUtil.getCarAge(tAdminInfo.getIdcard()));
            tAdminInfo.setSex(CardUtil.getCarSex(tAdminInfo.getIdcard()));
            tAdminInfo.setCardNumber(tAdminInfo.getIdcard());
        }
        if (StringUtil.isNotEmpty(tAdminInfo.getName())) {
            tAdminInfo.setWxNiceName(tAdminInfo.getName());
            tAdminInfo.setUserName(tAdminInfo.getName());
        }
        if (StringUtil.isNotEmpty(tAdminInfo.getMobile())) {
            tAdminInfo.setMobile(tAdminInfo.getMobile());
        }
        //因为加密，所以要重新搞个对象
        tAdminInfoMapper.insert(tAdminInfo);
//        TokenBo tokenBo = tokenUtil.createTokenBo(tAdminInfo.getUserId());
//        tokenBo.getAdmin().setTokenId(tokenBo.getTokenKey());
//        HashMap<String, String> map1 = new HashMap<>();
//        map1.put("token",tokenBo.getTokenKey());
//        map1.put("userName",userName);
//        map1.put("mobile",mobile);
//        map1.put("age",tAdminInfo.getAge());
//        map1.put("sex","M".equals(tAdminInfo.getSex())?"男":"女" );
//        return ResEntity.success(map1);
        return getUserInfo(tAdminInfo);
    }

    public ResEntity getUserInfo(TAdminInfo admin) {
        TokenBo tokenBo = tokenUtil.createTokenBo(admin, admin.getRegPlanId());
        TAdminInfo admin1 = tokenBo.getAdmin();
        admin1.setTokenId(tokenBo.getTokenKey());
        HashMap<String, String> map1 = new HashMap<>();
        map1.put("token", tokenBo.getTokenKey());
        map1.put("userName", admin1.getUserName());
        map1.put("mobile", admin1.getMobile());
        map1.put("age", admin1.getAge());
        map1.put("sex", admin1.getSex());
        return ResEntity.success(map1);
    }

}
