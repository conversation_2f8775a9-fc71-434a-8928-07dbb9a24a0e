package com.cbkj.diagnosis.controller.api;


import com.cbkj.diagnosis.bean.GetMobileDisease;
import com.cbkj.diagnosis.bean.GetTokenByP;
import com.cbkj.diagnosis.beans.AssociationUserInfo;
import com.cbkj.diagnosis.beans.ChangeUser;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.config.annotaionUtil.Timing;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsMapper;
import com.cbkj.diagnosis.service.SendDiagnosisForm;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.common.vo.QuestionMainRes;
import com.cbkj.diagnosis.service.statistics.StatisticsAdverse;
import com.cbkj.diagnosis.service.statistics.StatisticsDicEvent;
import com.cbkj.diagnosis.service.statistics.StatisticsDis;
import com.cbkj.diagnosis.service.statistics.StatisticsEffect;
import com.cbkj.diagnosis.utils.*;
import com.cbkj.diagnosis.config.TokenBo;
import com.cbkj.diagnosis.config.TokenUtil;
import com.cbkj.diagnosis.service.QuestionService;
import com.cbkj.diagnosis.service.mobileapi.business.MobileTDiseaseService;
import com.cbkj.diagnosis.service.common.vo.QuestionListVo;
import com.cbkj.diagnosis.service.TAdminInfoService;
import com.cbkj.diagnosis.service.webapi.business.WebTPreDiagnosisQuestionService;
import com.cbkj.diagnosis.service.common.vo.MobileDisease;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.github.pagehelper.util.StringUtil;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
//@Api(value = "疾病、题目、登录接口", tags = "疾病、题目、登录接口")
@Tag(name = "疾病、题目、登录接口", description = "疾病、题目、登录接口")
@Controller
public class ApiController {
    @Value("${sys.aes.key}")
    private String key ;

    private final MobileTDiseaseService mobileTDiseaseService;
    private final TokenUtil tokenUtil;
    private final TAdminInfoService tAdminInfoService;

    private final QuestionService questionService;
    private final SendDiagnosisForm sendDiagnosisForm;
    private final HttpServletRequest httpServletRequest;
    private final StatisticsDicEvent statisticsDicEvent;
    private final StatisticsDis statisticsDis;
    private final StatisticsEffect statisticsEffect;
    private final StatisticsAdverse statisticsAdverse;
    private final MedicalRecordsMapper medicalRecordsMapper;
    ApiController(MobileTDiseaseService mobileTDiseaseService,
                  TAdminInfoService tAdminInfoService, TokenUtil tokenUtil,
                  QuestionService questionService, SendDiagnosisForm sendDiagnosisForm, HttpServletRequest httpServletRequest, StatisticsDicEvent statisticsDicEvent, StatisticsDis statisticsDis, StatisticsEffect statisticsEffect, StatisticsAdverse statisticsAdverse, MedicalRecordsMapper medicalRecordsMapper) {
        this.mobileTDiseaseService = mobileTDiseaseService;

        this.tAdminInfoService = tAdminInfoService;
        this.tokenUtil = tokenUtil;
        this.questionService = questionService;
        this.sendDiagnosisForm = sendDiagnosisForm;
        this.httpServletRequest = httpServletRequest;
        this.statisticsDicEvent = statisticsDicEvent;
        this.statisticsDis = statisticsDis;
        this.statisticsEffect = statisticsEffect;
        this.statisticsAdverse = statisticsAdverse;
        this.medicalRecordsMapper = medicalRecordsMapper;
    }

    @PostMapping(value = "getDiseaseList")
    //@ApiOperation(value = "获取中西医疾病列表", notes = "获取中西医疾病列表", response = MobileDisease.class)
    @ResponseBody
    public Object getMobileDisease(@RequestBody GetMobileDisease getMobileDisease) {
        MobileDisease mobileDisease = mobileTDiseaseService.getMobileDisease(getMobileDisease.getNameWord());
        return ResEntity.success(mobileDisease);
    }


    @Timing(value = "移动端获取题目接口长耗时记录")
    @PostMapping(value = "setQuestions")
    //@ApiOperation(value = "随访/预诊 获取和保存手机端题目", notes = "随访/预诊  获取和保存手机端题目-后端判断是否最后一题并且最后一题时候保存",response = QuestionMain.class)
    @Operation(description = "随访/预诊  获取和保存手机端题目-后端判断是否最后一题并且最后一题时候保存", summary = "随访/预诊 获取和保存手机端题目",responses = {
            @ApiResponse(description = "随访/预诊 获取和保存手机端题目",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = QuestionMain.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object setQuestions(@RequestBody QuestionListVo questionListVo) {

        ResEntity resEntity = questionService.saveAndGetQuestions(questionListVo);
        if (resEntity.getStatus() && resEntity.getData() instanceof QuestionMainRes && ((QuestionMainRes) resEntity.getData()).isNoNextItem() ){
            QuestionMainRes data = (QuestionMainRes) resEntity.getData();
            if (StringUtil.isNotEmpty(data.getRecId())){
                TAdminInfo tokenBo = AdminUtils.getCurrentHr();
                if ("1".equals(questionListVo.getFormType())) {
                sendDiagnosisForm.sendPreAi(tokenBo.getCardNumber(), AdminUtils.getCurrentHr(), data.getRecId());
                }else {
                    log.error("【云系统后端接口-发送患者预诊了】失败：formType值{}", questionListVo.getFormType());
                }

                TAdminInfo currentHr = AdminUtils.getCurrentHr();
                MedicalRecords a = medicalRecordsMapper.getOneBypatientId(currentHr.getUserId());
                if (a != null){
                    if (org.apache.commons.lang3.StringUtils.isBlank(currentHr.getAppId())){
                        currentHr.setAppId(a.getAppId());
                    }
                    if (org.apache.commons.lang3.StringUtils.isBlank(currentHr.getInsCode())){
                        currentHr.setInsCode(a.getInsCode());
                    }
                    if (org.apache.commons.lang3.StringUtils.isBlank(currentHr.getInsId())){
                        currentHr.setInsId(a.getInsId());
                    }
                    if (org.apache.commons.lang3.StringUtils.isBlank(currentHr.getInsName())){
                        currentHr.setInsName(a.getInsName());
                    }
                }

                //统计选项的事件数据
                statisticsDicEvent.writeReadFromRedis(data.getRecId(), questionListVo.getQuestionMobile().getDiaId(),currentHr.getAppId(),currentHr.getInsCode(),currentHr.getInsId(),currentHr.getInsName(),currentHr.getUserId());
                //统计预诊、随访关联病种、有效病历统计
                statisticsDis.statisticsDis(data.getRecId(), questionListVo.getQuestionMobile().getDiaId(),currentHr.getAppId(),currentHr.getInsCode(),currentHr.getInsId(),currentHr.getInsName(),questionListVo.getFormType());
                //疗效评价
                statisticsEffect.writeReadFromRedis(data.getRecId(), questionListVo.getQuestionMobile().getDiaId(),currentHr.getAppId(),currentHr.getInsCode(),currentHr.getInsId(),currentHr.getInsName(),currentHr.getUserId() );
                //不良反应
                statisticsAdverse.writeReadFromRedis(data.getRecId(), questionListVo.getQuestionMobile().getDiaId(),currentHr.getAppId(),currentHr.getInsCode(),currentHr.getInsId(),currentHr.getInsName(),currentHr.getUserId() );

            }

        }


        return resEntity;
    }

    @PostMapping("getTokenByM")
    @ResponseBody
    //@ApiOperation(value = "获取token通过手机号", response = TAdminInfo.class)
    @Operation(description = "获取token通过手机号", summary = "获取token通过手机号",responses = {
            @ApiResponse(description = "获取token通过手机号",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TAdminInfo.class)
                    )
            )
    }
    )
    public Object getToken(@RequestBody GetTokenByP getTokenByP) {
        String phone = getTokenByP.getPhone();
        if (org.apache.commons.lang3.StringUtils.isBlank(phone)) {
            return ResEntity.error("缺少入参");
        }
        //通过手机号找到这个患者。
        TAdminInfo userByMobile = tAdminInfoService.getUserByMobile(phone);
        if (userByMobile == null) {
            return ResEntity.error("");
        }
        TokenBo tokenBo = tokenUtil.createTokenBo(userByMobile,null);
        tokenBo.getAdmin().setTokenId(tokenBo.getTokenKey());
        TAdminInfo admin = tokenBo.getAdmin();
//        if (StringUtil.isNotEmpty(admin.getMobile())) {
//            admin.setMobile(StringUtils.phoneMasking(admin.getMobile()));
//        }
        //admin.setTokenId(authorization);
        return new ResEntity(true, "SUCCESS", admin);
    }

//    @PostMapping("getToken")
//    @ResponseBody
//    //@ApiOperation(value = "获取token", response = TAdminInfo.class)
//    public Object getToken(@RequestBody TAdminInfo adminInfo) {
//        if (StringUtil.isNotEmpty(adminInfo.getUserId())) {
//            return new ResEntity(false, "服务异常！", null);
//        }
//
//        if (StringUtil.isEmpty(adminInfo.getUserId()) && StringUtil.isEmpty(adminInfo.getIdcard())) {
//
//            if (StringUtil.isNotEmpty(adminInfo.getMAK())) {
//                String mak = adminInfo.getMAK();
//                try {
//                    String content = AESUtils.decrypt(mak);
//
//                    if (StringUtil.isNotEmpty(content)) {
//                        String idcard = content.substring(content.indexOf("idcard=") + 7, content.indexOf("&name"));
//                        String name = content.substring(content.indexOf("name=") + 5, content.indexOf("&mobile"));
//                        String mobile = content.substring(content.indexOf("mobile=") + 7, content.length());
//                        if (StringUtil.isEmpty(idcard)) {
//                            return new ResEntity(false, "缺少参数！", null);
//                        }
//                        adminInfo.setIdcard(idcard);
//                        adminInfo.setMobile(mobile);
//                        adminInfo.setName(name);
//                    } else {
//                        return new ResEntity(false, "缺少参数！", null);
//                    }
//                } catch (Exception e) {
//                    return new ResEntity(false, "缺少参数！", null);
//                }
//            }
//
//            //扁鹊登录
//            if (StringUtil.isNotEmpty(adminInfo.getBqdata())) {
//                String mak = adminInfo.getBqdata();
//                try {
//                    String content = AESPKCS7Util.decrypt(mak, key, "base64");
//                    Map map = AESPKCS7Util.jsonToMap(content);
//
//                    if (StringUtil.isNotEmpty(content) && map != null) {
//                        String idcard = (String) map.get("idcardValue");
//                        String name = (String) map.get("userName");
//                        String mobile = (String) map.get("phone");
//                        if (StringUtil.isEmpty(idcard)) {
//                            return new ResEntity(false, "缺少参数！", null);
//                        }
//                        adminInfo.setIdcard(idcard);
//                        adminInfo.setMobile(mobile);
//                        adminInfo.setName(name);
//                    } else {
//                        return new ResEntity(false, "缺少参数！", null);
//                    }
//                } catch (Exception e) {
//                    return new ResEntity(false, "缺少参数！", null);
//                }
//            }
//
//        }
//        String userId = null;
//        if (StringUtil.isNotEmpty(adminInfo.getUserId())) {
//            userId = adminInfo.getUserId();
//        } else {
//            ResEntity res = tAdminInfoService.getUserIdOrInsertUser(adminInfo);
//            if (!res.getStatus()) {
//                return res;
//            }
//            userId = (String) res.getData();
//        }
//        TokenBo tokenBo = tokenUtil.createTokenBo(userId,null);
//        //String authorization = tokenUtil.generateToken(tokenBo.getTokenKey());
//        tokenBo.getAdmin().setTokenId(tokenBo.getTokenKey());
//        TAdminInfo admin = tokenBo.getAdmin();
//        if (StringUtil.isNotEmpty(admin.getMobile())) {
//            admin.setMobile(StringUtils.phoneMasking(admin.getMobile()));
//        }
//        //admin.setTokenId(authorization);
//        return new ResEntity(true, "SUCCESS", admin);
//    }

    @PostMapping(value = "/my/user/association")
    //@ApiOperation(value = "获取登录用户的关联用户信息-患者账号只能被一个账号绑定且底下有绑定的账号就不能被其他账号绑定", notes = "获取登录用户的关联用户信息-患者账号只能被一个账号绑定且底下有绑定的账号就不能被其他账号绑定")
    @Operation(description = "获取登录用户的关联用户信息-患者账号只能被一个账号绑定且底下有绑定的账号就不能被其他账号绑定", summary = "获取登录用户的关联用户信息-患者账号只能被一个账号绑定且底下有绑定的账号就不能被其他账号绑定",responses = {
            @ApiResponse(description = "获取登录用户的关联用户信息",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AssociationUserInfo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getAssociation() {
        List<AssociationUserInfo> associationUserInfos = tAdminInfoService.getaAssociation();

        return ResEntity.success(associationUserInfos);
    }

    @PostMapping(value = "/my/user/before")
    //@ApiOperation(value = "切换用户", notes = "切换用户")
    @Operation(description = "切换用户", summary = "切换用户",responses = {
            @ApiResponse(description = "切换用户",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AssociationUserInfo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object changeUser(@RequestBody ChangeUser changeUser) {

//        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//        if (null != authentication) {
//            Object obj = authentication.getPrincipal();
//            if (obj instanceof TokenBo) {
//                String tokenKey = ((TokenBo) obj).getTokenKey();
//                tokenUtil.deleteToken(tokenKey);
//            }
//        }

        TokenBo tokenBo = tokenUtil.createTokenBo(changeUser.getUserId(),null);
        tokenBo.getAdmin().setTokenId(tokenBo.getTokenKey());
        TAdminInfo admin = tokenBo.getAdmin();
        if (StringUtil.isNotEmpty(admin.getMobile())) {
            admin.setMobile(StringUtils.phoneMasking(admin.getMobile()));
        }
        return new ResEntity(true, "SUCCESS", admin);
    }


}
