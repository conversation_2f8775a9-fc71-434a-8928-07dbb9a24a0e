package com.cbkj.diagnosis.controller.api;

import com.cbkj.diagnosis.bean.GetSuiFangRecordDetails;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.ApiSRoadTaskPatientsService;
import com.cbkj.diagnosis.service.MyRecordDetailService;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.mobileapi.vo.FurtherConsultationRecordListVo;
import com.cbkj.diagnosis.service.mobileapi.vo.MessageListVo;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
//@Api(value = "我的", tags = "我的")
@Tag(name = "我的", description = "我的")
@Controller
@RequestMapping("/my")
public class MyController {

    private final ApiSRoadTaskPatientsService mobileSRoadTaskPatientsService;

    private final MyRecordDetailService myRecordDetailService;

    public MyController(ApiSRoadTaskPatientsService mobileSRoadTaskPatientsService, MyRecordDetailService myRecordDetailService) {
        this.mobileSRoadTaskPatientsService = mobileSRoadTaskPatientsService;
        this.myRecordDetailService = myRecordDetailService;
    }


//    @GetMapping(value = "read")
//    //@ApiOperation(value = "设置健康宣教、复诊提醒已读", notes = "设置健康宣教、复诊提醒已读")
//    @ResponseBody
//    public Object getMobileIndexNumber(String taskPatientsId) {
//        mobileSRoadTaskPatientsService.updatePatientTaskStatus(taskPatientsId);
//        return ResEntity.success();
//    }

    @PostMapping(value = "suiFang/record/list")
    //@ApiOperation(value = "随访记录列表", notes = "随访记录-直接取随访记录表 不从任务里面取",response = FurtherConsultationRecordListVo.class)
    @Operation(description = "随访记录列表", summary = "随访记录列表",responses = {
            @ApiResponse(description = "随访记录列表",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = FurtherConsultationRecordListVo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getSuiFangRecordList(@RequestBody Page page) {
        return mobileSRoadTaskPatientsService.getFurtherConsultationRecordList(page);

    }

    @PostMapping(value = "suiFang/record/details")
    //@ApiOperation(value = "随访记录详情", notes = "随访记录详情",response = QuestionMain.class)
    @Operation(description = "随访记录详情", summary = "随访记录详情",responses = {
            @ApiResponse(description = "随访记录详情",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = QuestionMain.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getSuiFangRecordDetails(@RequestBody GetSuiFangRecordDetails getSuiFangRecordDetails) {
        return myRecordDetailService.getPrePaper(getSuiFangRecordDetails.getRecId());
    }


    @PostMapping(value = "preDia/record/list")
    //@ApiOperation(value = "预诊记录列表分页", notes = "预诊记录李列表",response = FurtherConsultationRecordListVo.class)
    @Operation(description = "预诊记录列表分页", summary = "预诊记录列表分页",responses = {
            @ApiResponse(description = "预诊记录列表分页",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = FurtherConsultationRecordListVo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPreDiaRecordList(@RequestBody Page page) {
        return mobileSRoadTaskPatientsService.getPreDiaRecordList(page);
    }

    @PostMapping(value = "preDia/record/details")
    //@ApiOperation(value = "预诊记录详情", notes = "预诊记录详情",response = QuestionMain.class)
    @Operation(description = "预诊记录详情", summary = "预诊记录详情",responses = {
            @ApiResponse(description = "预诊记录详情",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = QuestionMain.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPreDiaRecordDetails(@RequestBody GetSuiFangRecordDetails getSuiFangRecordDetails) {
        return myRecordDetailService.getPrePaper(getSuiFangRecordDetails.getRecId());
    }
}
