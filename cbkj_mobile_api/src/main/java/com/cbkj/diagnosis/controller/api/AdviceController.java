package com.cbkj.diagnosis.controller.api;

import com.cbkj.diagnosis.beans.business.TBusinessProposal;
import com.cbkj.diagnosis.beans.business.adviceVo.AdviceSave;
import com.cbkj.diagnosis.beans.business.adviceVo.GetAdviceListRes;
import com.cbkj.diagnosis.beans.business.adviceVo.ImageUploadRes;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.AdviceMobileService;
import com.cbkj.diagnosis.service.business.AdviceFileUploadService;
import com.cbkj.diagnosis.service.business.impl.TBusinessProposalServiceImpl;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AdminUtils;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiImplicitParam;
//import io.swagger.annotations.ApiImplicitParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/2 10:56
 * @Version 1.0
 */
@Controller
//@Api(value = "意见建议", tags = "意见建议")
@Tag(name = "意见建议", description = "意见建议")
@RequestMapping("/advice")
public class AdviceController {
    private final AdviceFileUploadService adviceFileUploadService;
    private final AdviceMobileService adviceMobileService;
    private final TBusinessProposalServiceImpl proposalService;

    public AdviceController(AdviceFileUploadService adviceFileUploadService, AdviceMobileService adviceMobileService, TBusinessProposalServiceImpl proposalService) {
        this.adviceFileUploadService = adviceFileUploadService;
        this.adviceMobileService = adviceMobileService;
        this.proposalService = proposalService;
    }

    @RequestMapping(value = "/upload/image", method = RequestMethod.POST,consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    //@ApiOperation(value = "上传意见建议图片文件", notes = "上传意见建议图片文件", response = ImageUploadRes.class)
   // @ApiImplicitParams({
     //       @ApiImplicitParam(name = "annexType", value = "附件所属业务类型：1系统建议反馈,2系统帮助操作手册3.系统帮助操作视频", dataType = "String", required = true)})
    @Operation(description = "上传意见建议图片文件", summary = "上传意见建议图片文件",responses = {
            @ApiResponse(description = "上传意见建议图片文件",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    public Object uploadImage(@Parameter(
            description = "图片文件",
            required = true,
            content = @Content(mediaType = "application/octet-stream", schema = @Schema(type = "string", format = "binary"))  // 标准文件声明
    ) @RequestPart("file")MultipartFile file) {
        if (file == null){
            return ResEntity.error("请选择正确图片");
        }
        return adviceFileUploadService.uploadImage(file, "1", AdminUtils.getCurrentHr().getUserId());

    }

    @RequestMapping(value = "advice/my/list", method = RequestMethod.POST)
    @ResponseBody
    //@ApiOperation(value = "获取我的问题建议咨询列表", notes = "获取我的问题建议咨询列表", response = GetAdviceListRes.class)
    @Operation(description = "获取我的问题建议咨询列表", summary = "获取我的问题建议咨询列表",responses = {
            @ApiResponse(description = "获取我的问题建议咨询列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = GetAdviceListRes.class)
                    )
            )
    }
    )
    public Object getAdviceList(@RequestBody Page page) {
       return adviceMobileService.getAdviceList(page);

    }

    @RequestMapping(value = "advice/create", method = RequestMethod.POST)
    @ResponseBody
    //@ApiOperation(value = "患者创建问题建议咨询", notes = "患者创建问题建议咨询", response = ResEntity.class)
    @Operation(description = "患者创建问题建议咨询", summary = "患者创建问题建议咨询",responses = {
            @ApiResponse(description = "患者创建问题建议咨询",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    public ResEntity fixAdvice(@RequestBody AdviceSave adviceSave) {
        return proposalService.saveAdvice(adviceSave, AdminUtils.getCurrentHr().getUserId(), AdminUtils.getCurrentHr().getUserName(), 2);
    }

}
