package com.cbkj.diagnosis.bean.bussines;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cbkj.diagnosis.beans.business.adviceVo.ImageUploadRes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业务管理建议问题咨询表-患者端保存
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Getter
@Setter
public class BusinessProposalSave implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description="标题限制20字")
    private String proposalTitle;

    @Schema(description="建议内容限制500字")
    private String proposalContent;

    @Schema(description="联系方式")
    private String proposalLiaison;

    @Schema(description="反馈类型：1建议，2问题 3.咨询")
    private Integer proposalType;


    @Schema(description="建议图片")
    private List<ImageUploadRes> adviceImages;


}
