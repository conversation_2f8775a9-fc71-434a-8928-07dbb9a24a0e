package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.common.annotaionUtil.MainTransaction;
import com.cbkj.diagnosis.service.common.vo.QuestionListVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/28 16:00
 * @Version 1.0
 */
@Service
public class LargeFixFormMainService {
    private final LargeFixFormService largeFixFormService;

    public LargeFixFormMainService(LargeFixFormService largeFixFormService) {
        this.largeFixFormService = largeFixFormService;
    }
//    @Transactional(rollbackFor = Exception.class)
//    @MainTransaction(1)
    public void transQuestion(QuestionListVo questionListVo, String recId, TAdminInfo tokenBo) {
        largeFixFormService.transQuestion(questionListVo, recId,tokenBo);
    }
}
