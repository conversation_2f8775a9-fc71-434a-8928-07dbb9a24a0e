package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.SysIns;
import com.cbkj.diagnosis.beans.business.TPropagandaEdu;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.his.HealthEducationDetails;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.*;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategyFactory;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.mobileapi.vo.FurtherConsultationRecordListVo;
import com.cbkj.diagnosis.service.mobileapi.vo.GetFurtherConsultationList;
import com.cbkj.diagnosis.service.mobileapi.vo.GetMobileIndexNumber;
import com.cbkj.diagnosis.service.mobileapi.vo.MessageListVo;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AdminUtils;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ApiSRoadTaskPatientsService {

    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;
    private RecordDiaStrategyFactory recordDiaStrategyFactory;

    private TRecordDiaMapper tRecordDiaMapper;

    private final TRecordMapper tRecordMapper;

    private final TPropagandaEduMapper tPropagandaEduMapper;

    private final MedicalRecordsMapper medicalRecordsMapper;

    private final SysInsMapper sysInsMapper;
    private final SysDeptMapper sysDeptMapper;


    ApiSRoadTaskPatientsService(SRoadTaskPatientsMapper sRoadTaskPatientsMapper, RecordDiaStrategyFactory recordDiaStrategyFactory,
                                TRecordDiaMapper tRecordDiaMapper, TRecordMapper tRecordMapper, TPropagandaEduMapper tPropagandaEduMapper, MedicalRecordsMapper medicalRecordsMapper, SysInsMapper sysInsMapper, SysDeptMapper sysDeptMapper) {
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.recordDiaStrategyFactory = recordDiaStrategyFactory;
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.tRecordMapper = tRecordMapper;
        this.tPropagandaEduMapper = tPropagandaEduMapper;
        this.medicalRecordsMapper = medicalRecordsMapper;
        this.sysInsMapper = sysInsMapper;
        this.sysDeptMapper = sysDeptMapper;
    }

    /**
     * 获取手机端首页的消息未读数量，
     */
    public HashMap<String, Integer> getMobileIndexNumber() {

        TAdminInfo tAdminInfo = AdminUtils.getCurrentHr();

        GetMobileIndexNumber indexNumber = new GetMobileIndexNumber();
        indexNumber.setPatientId(tAdminInfo.getUserId());
//        indexNumber.setTaskExcuteStatus(Constant.BASIC_STRING_TWO);
        //执行的具体内容类型1.健康宣教2.随访问卷（随访的一种类型）3.复诊提醒4.自测量表(随访的一种类型)
        indexNumber.setRoadExecuteEventType(Constant.BASIC_STRING_THREE);
        int number1 = sRoadTaskPatientsMapper.getMobileIndexNumber(indexNumber);
        //宣教
        indexNumber.setRoadExecuteEventType(Constant.BASIC_STRING_ONE);
        int number2 = sRoadTaskPatientsMapper.getMobileIndexNumber(indexNumber);
        //随访
        indexNumber.setRoadExecuteEventType(Constant.BASIC_STRING_TWO);
        int number3 = sRoadTaskPatientsMapper.getMobileIndexNumber2(indexNumber);

        HashMap<String, Integer> integerHashMap = new HashMap<>(16);

        integerHashMap.put("furtherConsultation", number1);
        integerHashMap.put("healthEducation", number2);
        integerHashMap.put("suiFang", number3);
        return integerHashMap;

    }

    /**
     * 列表
     *
     * @param page
     */
    public Object getFurtherConsultationList(Page page, String type) {
        TAdminInfo tAdminInfo = AdminUtils.getCurrentHr();

        PageHelper.startPage(page.getPage(), page.getLimit());

        //查询
        GetFurtherConsultationList fu = new GetFurtherConsultationList();
        fu.setPatientId(tAdminInfo.getUserId());
        fu.setRoadExecuteEventType(type);
        List<MessageListVo> messageList = sRoadTaskPatientsMapper.getMessageList(fu);

        PageHelper.clearPage();
        if (Constant.BASIC_STRING_THREE.equals(type)) {
            StringBuilder a = new StringBuilder();
            String userName = tAdminInfo.getUserName();
            if (StringUtils.isNotBlank(userName) && userName.length() > 1) {
                // 替换除了第一个字符以外的所有字符为'*'
                StringBuilder sb = new StringBuilder(userName);
                for (int i = 1; i < sb.length(); i++) {
                    sb.setCharAt(i, '*');
                }
                // 将结果转换为String
                String result = sb.toString();
                a.append("尊敬的").append(result ).append( "，根据对您的诊后评估，提醒您抽空来我院复诊。为了您的健康,请多关心自己！");

            } else {
                a.append(userName);
            }
            for (int i = 0; i < messageList.size(); i++) {
                messageList.get(i).setTitle("复诊提醒");
                messageList.get(i).setTitleAbstract(a.toString());
            }
        }

        for (int i = 0; i < messageList.size(); i++) {
            MessageListVo messageListVo = messageList.get(i);
            if (StringUtils.isBlank( messageListVo.getInsName()) ){
                Long taskPatientsId = messageListVo.getTaskPatientsId();
                SRoadTaskPatients taskPatients = sRoadTaskPatientsMapper.getObjectById(taskPatientsId + "");
                StringBuilder title = new StringBuilder();

                if (taskPatients != null) {
                    String recordsId = taskPatients.getRecordsId();
                    if (StringUtils.isBlank(recordsId)) {
                        //手动发送的，那就填医生。
                        String doctorName = taskPatients.getDoctorName();
                        if (StringUtils.isNotBlank(doctorName)) {
                            title.append(doctorName);
                        }
                        String deptName = taskPatients.getDeptName();
                        if (StringUtils.isNotBlank(deptName)) {
                            title.append("-").append(deptName);
                        }
                        String insName = taskPatients.getInsName();
                        if (StringUtils.isNotBlank(insName)) {
                            title.append("-").append(insName);
                        }
                    } else {
                        MedicalRecords objectById = medicalRecordsMapper.getObjectById(recordsId);
                        if (objectById != null) {
                            title.append(objectById.getDoctorName());
                            if (StringUtils.isNotBlank(objectById.getDeptName())) {
                                title.append("-").append(objectById.getDeptName());
                            }

                            //SysIns objectById1 = sysInsMapper.getObjectById(objectById.getInsId());
                            //  if (objectById1 != null){
                            String insName = objectById.getInsName();
                            if (StringUtils.isNotBlank(insName)) {
                                title.append("-").append(insName);
                            }
                            //}
                        }
                    }

                }
                messageListVo.setInsName(title.toString());
            }

        }

        return Page.getLayUiTablePageData(messageList);


    }

    public Object getFurtherConsultationDetails(String taskPatientsId) {
        TAdminInfo tAdminInfo = AdminUtils.getCurrentHr();
        String a = "";
        String userName = tAdminInfo.getUserName();
        if (StringUtils.isNotBlank(userName) && userName.length() > 1) {
            // 替换除了第一个字符以外的所有字符为'*'
            StringBuilder sb = new StringBuilder(userName);
            for (int i = 1; i < sb.length(); i++) {
                sb.setCharAt(i, '*');
            }
            // 将结果转换为String
            String result = sb.toString();
            a = "尊敬的" + result + "，根据对您的诊后评估，提醒您抽空来我院复诊。为了您的健康,请多关心自己！";

        } else {
            a = userName;
        }
        sRoadTaskPatientsMapper.updateTaskExcuteStatusByPrimaryKey(taskPatientsId);

        HashMap<String, String> map = new HashMap<String, String>();
        map.put("content", a);

        SRoadTaskPatients taskPatients = sRoadTaskPatientsMapper.getObjectById(taskPatientsId);
        String title = "";
        String time = "";
        if (taskPatients != null) {
            String recordsId = taskPatients.getRecordsId();

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            time = simpleDateFormat.format(taskPatients.getTaskExcuteTime());
            MedicalRecords objectById = medicalRecordsMapper.getObjectById(recordsId);
            if (objectById != null) {
                title = objectById.getDoctorName();
                if (StringUtils.isNotBlank(objectById.getDeptName())) {
                    title = title + "-" + objectById.getDeptName();
                }

                SysIns objectById1 = sysInsMapper.getObjectById(objectById.getInsId());
                if (objectById1 != null) {
                    String insName = objectById1.getInsName();
                    if (StringUtils.isNotBlank(insName)) {
                        title = title + "-" + insName;
                    }
                }
            }
        }

        map.put("title", title);
        map.put("time", time);
        return ResEntity.success(map);
    }


    public Object getSuiFangFurtherConsultationList(Page page, String type) {
        TAdminInfo tAdminInfo = AdminUtils.getCurrentHr();

        PageHelper.startPage(page.getPage(), page.getLimit());

        //查询
        GetFurtherConsultationList fu = new GetFurtherConsultationList();
        fu.setPatientId(tAdminInfo.getUserId());
        fu.setRoadExecuteEventType(type);
        List<MessageListVo> messageList = sRoadTaskPatientsMapper.getSuiFangMessageList(fu);
//        for (int i = 0; i < messageList.size(); i++) {
//            updatePatientTaskStatus(messageList.get(i).getTaskPatientsId()+"");
//        }

        PageHelper.clearPage();
        return Page.getLayUiTablePageData(messageList);


    }

    /**
     * 随访记录查询
     *
     * @param page
     * @param type
     * @return
     */
    public Object getFurtherConsultationRecordList(Page page) {
        TAdminInfo tAdminInfo = AdminUtils.getCurrentHr();
        PageHelper.startPage(page.getPage(), page.getLimit());
        GetFurtherConsultationList fu = new GetFurtherConsultationList();
        fu.setPatientId(tAdminInfo.getUserId());
        fu.setFormType("2");
        PageHelper.clearPage();
        List<FurtherConsultationRecordListVo> messageListVos = tRecordMapper.getFurtherRecordList(fu);
        return Page.getLayUiTablePageData(messageListVos);
    }


    /**
     * 加载预诊随访详情数据
     *
     * @param diaRecId
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-10-09
     */
    public ResEntity getPrePaper(String recId) {

        if (StringUtils.isBlank(recId)) {
            return ResEntity.entity(false, "recId参数不能为空哦", null);
        }
        List<TRecordDia> objectByRecId = tRecordDiaMapper.getObjectByRecId(recId);
        ArrayList<QuestionMain> list = new ArrayList<>();
        for (TRecordDia tRecordDia : objectByRecId) {
            list.add(recordDiaStrategyFactory.getNormalParamStrategy(tRecordDia.getQuestionType()).transRecordDia(tRecordDia));
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }


    /**
     * 修改患者的任务的状态为已读
     *
     * @param taskPatientsId
     */
    public void updatePatientTaskStatus(String taskPatientsId) {
        SRoadTaskPatients taskPatients = sRoadTaskPatientsMapper.getObjectById(taskPatientsId);
        if (taskPatients != null && Constant.BASIC_STRING_ZERO.equals(taskPatients.getStatus())
                && taskPatients.getTaskExcuteStatus() == 2) {

            if (Constant.BASIC_STRING_ONE.equals(taskPatients.getRoadExecuteEventType()) || Constant.BASIC_STRING_THREE.equals(taskPatients.getRoadExecuteEventType())) {
                //taskPatients.setTaskExcuteStatus(7);
                sRoadTaskPatientsMapper.updateTaskExcuteStatusByPrimaryKey(taskPatientsId);
            }

        }
    }

    public Object getPreDiaRecordList(Page page) {

        TAdminInfo tAdminInfo = AdminUtils.getCurrentHr();
        PageHelper.startPage(page.getPage(), page.getLimit());
        GetFurtherConsultationList fu = new GetFurtherConsultationList();
        fu.setPatientId(tAdminInfo.getUserId());
        fu.setFormType("1");
        PageHelper.clearPage();
        List<FurtherConsultationRecordListVo> messageListVos = tRecordMapper.getFurtherRecordList2(fu);
        for (int i = 0; i < messageListVos.size(); i++) {
            String formName = messageListVos.get(i).getFormName();
            if (StringUtils.isBlank(formName)){
                messageListVos.get(i).setFormName("预问诊记录");
            }


        }
        return Page.getLayUiTablePageData(messageListVos);
    }

    public Object getHealthEducationDetails(String roadExecuteEventContentId,String taskPatientsId) {
        HealthEducationDetails healthEducationDetails = new HealthEducationDetails();
        healthEducationDetails.setRoadExecuteEventContentId(roadExecuteEventContentId);
        healthEducationDetails.setTaskPatientsId(taskPatientsId);
        //TPropagandaEdu objectById = tPropagandaEduMapper.getObjectById(roadExecuteEventContentId);

        TPropagandaEdu objectById = tPropagandaEduMapper.getObjectByHealthEducationDetails(healthEducationDetails);
        return objectById;
    }
}
