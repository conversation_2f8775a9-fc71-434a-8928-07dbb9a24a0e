package com.cbkj.diagnosis.service;

import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.TRecord;
import com.cbkj.diagnosis.common.http.PreAiTemplate;
import com.cbkj.diagnosis.common.http.RestWebTemplate;
import com.cbkj.diagnosis.mapper.business.TRecordMapper;
import com.cbkj.diagnosis.service.business.DiagnosisStructureUtils;
import com.cbkj.diagnosis.service.webapi.business.WebTRecordService;
import com.cbkj.diagnosis.service.webapi.business.vo.GetQuestionClassTypeInfo;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/19 17:42
 * @Version 1.0
 */
@Service
public class SendDiagnosisForm {
    private final RestWebTemplate restWebTemplate;

    private final PreAiTemplate preAiTemplate;

    private final TRecordMapper tRecordMapper;

    private final WebTRecordService webTRecordService;

    private final DiagnosisStructureUtils diagnosisStructureUtils;
    public SendDiagnosisForm(RestWebTemplate restWebTemplate, PreAiTemplate preAiTemplate, TRecordMapper tRecordMapper, WebTRecordService webTRecordService, DiagnosisStructureUtils diagnosisStructureUtils) {
        this.restWebTemplate = restWebTemplate;
        this.preAiTemplate = preAiTemplate;
        this.tRecordMapper = tRecordMapper;
        this.webTRecordService = webTRecordService;
        this.diagnosisStructureUtils = diagnosisStructureUtils;
    }

    @Async
    public void send(String recId) {
        JSONObject params = new JSONObject();
        params.put("recId", recId);
        restWebTemplate.sendPreDiagnosis(params);
    }

    @Async
    public void sendPreAi(String patientCardNo, TAdminInfo tokenBo,String recId) {
        JSONObject params = new JSONObject();
        List<GetQuestionClassTypeInfo> getQuestionClassTypeInfoList1 = diagnosisStructureUtils.getStructureContent(recId);
//        if (getQuestionClassTypeInfoList1 == null){
//            List<GetQuestionClassTypeInfo> getQuestionClassTypeInfoList = tRecordMapper.getQuestionClassTypeInfo(recId);
//            getQuestionClassTypeInfoList1 = webTRecordService.questionClassTypeInfoListHandle(getQuestionClassTypeInfoList);
//        }



        params.put("patientCardNo", patientCardNo);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        params.put("checkTime", simpleDateFormat.format(new Date()));
        params.put("patientName", tokenBo.getUserName());
        params.put("patientSex", tokenBo.getSex());
        params.put("patientAge", tokenBo.getAge());
        params.put("mobile", tokenBo.getMobile());

        SimpleDateFormat df = new SimpleDateFormat("HH");
        String str = df.format(new Date());
        int a = Integer.parseInt(str);
        if (a >= 0 && a <= 12) {
            params.put("checkRange", new Byte("1"));
        }
        if (a > 12 && a <= 23) {
            params.put("checkRange", new Byte("2"));
        }


        params.put("preDiagnosisTime", simpleDateFormat2.format(new Date()) );
        if (getQuestionClassTypeInfoList1 != null && !getQuestionClassTypeInfoList1.isEmpty()) {
            for (int i = 0; i < getQuestionClassTypeInfoList1.size(); i++) {
                GetQuestionClassTypeInfo info = getQuestionClassTypeInfoList1.get(i);
                if ("1".equals(info.getQuestionClassType())) {
                    params.put("zhuShu", info.getContent());
                }
                if ("2".equals(info.getQuestionClassType())) {
                    params.put("xianBingShi", info.getContent());
                }
                if ("3".equals(info.getQuestionClassType())) {
                    params.put("jiWangShi", info.getContent());
                }
                if ("4".equals(info.getQuestionClassType())) {
                    params.put("tiGeJianCha", info.getContent());
                }
                if ("6".equals(info.getQuestionClassType())) {
                    params.put("qiTa", info.getContent());
                }
            }

        }
        preAiTemplate.post(params);
    }
}
