package com.cbkj.diagnosis.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.mapper.business.*;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.service.business.TRecordDiaDimensionService;
import com.cbkj.diagnosis.service.business.TRecordDiaImageService;
import com.cbkj.diagnosis.service.business.impl.TRecordEventServiceImpl;
import com.cbkj.diagnosis.service.common.vo.QuestionMainRes;
import com.cbkj.diagnosis.service.mobileapi.business.MobileTPreDiagnosisQuestionService;
import com.cbkj.diagnosis.service.common.vo.QuestionListVo;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.common.vo.QuestionMobile;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AdminUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class QuestionService {

    @Value("${send.pre.toHis}")
    private boolean sendPreToHIS;
    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;
    private final MobileTPreDiagnosisQuestionService mobileTPreDiagnosisQuestionService;
    private final TRecordMapper tRecordMapper;
    private final TRecordDiaMapper tRecordDiaMapper;
    private final TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;
    private final SendDiagnosisForm sendDiagnosisForm;
    private final TPreDiagnosisFormMapper tPreDiagnosisFormMapper;
    private final TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;
    private final TRecordEventServiceImpl tRecordEventServiceImpl;
    private final LargeFixFormMainService largeFixFormMainService;
    private final TRecordDiaImageService tRecordDiaImageService;
    private final TRecordDiaDimensionService tRecordDiaDimensionService;
    QuestionService(MobileTPreDiagnosisQuestionService mobileTPreDiagnosisQuestionService,
                    TRecordMapper tRecordMapper,
                    TRecordDiaMapper tRecordDiaMapper,
                    TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper,
                    SRoadTaskPatientsMapper sRoadTaskPatientsMapper,
                    SendDiagnosisForm sendDiagnosisForm, TPreDiagnosisFormMapper tPreDiagnosisFormMapper, TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper, TRecordEventServiceImpl tRecordEventServiceImpl, LargeFixFormMainService largeFixFormMainService, TRecordDiaImageService tRecordDiaImageService, TRecordDiaDimensionService tRecordDiaDimensionService) {

        this.mobileTPreDiagnosisQuestionService = mobileTPreDiagnosisQuestionService;
        this.tRecordMapper = tRecordMapper;
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.sendDiagnosisForm = sendDiagnosisForm;
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
        this.tPreDiagnosisOptionMapper = tPreDiagnosisOptionMapper;
        this.tRecordEventServiceImpl = tRecordEventServiceImpl;
        this.largeFixFormMainService = largeFixFormMainService;
        this.tRecordDiaImageService = tRecordDiaImageService;
        this.tRecordDiaDimensionService = tRecordDiaDimensionService;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity saveAndGetQuestions(QuestionListVo questionListVo) {
        if (questionListVo == null || questionListVo.getQuestionMobile() == null || StringUtils.isBlank(questionListVo.getQuestionMobile().getDiaId())) {
            return ResEntity.error("缺少必要字段diaId");
        } else {
            log.info("================1");
            TPreDiagnosisForm objectById = tPreDiagnosisFormMapper.getObjectById(questionListVo.getQuestionMobile().getDiaId());
            questionListVo.setFormType(objectById.getFormType());
            if (StringUtils.isNotBlank(objectById.getFormCode())) {
                //查编号
                TPreDiagnosisForm objectById2 = tPreDiagnosisFormMapper.getObjectByFormCode(objectById.getFormCode());
                if (objectById2 != null) {
                    questionListVo.getQuestionMobile().setDiaId(objectById2.getDiaId());

                }
            }
        }
        setRead(questionListVo);
        ResEntity questions = mobileTPreDiagnosisQuestionService.getQuestions(questionListVo);
        if (questions.getStatus() && ((QuestionMain) questions.getData()).isNoNextItem()) {
            //最后一题了
            ResEntity save = save(questionListVo);
            if (!save.getStatus()) {
                return save;
            } else {
                //调用web后端服务。将这个患者的当前这个预诊单发送给his。
                if (Constant.BASIC_STRING_ONE.equals(questionListVo.getFormType()) && sendPreToHIS) {
                    sendDiagnosisForm.send((String) save.getData());
                }

                QuestionMainRes questionMainRes = new QuestionMainRes();
                BeanUtils.copyProperties(((QuestionMain) questions.getData()), questionMainRes);
                questionMainRes.setRecId((String) save.getData());
                return ResEntity.success(questionMainRes);
            }
        }
        return questions;

    }

    @Transactional(rollbackFor = Exception.class)
    public void setRead(QuestionListVo questionListVo) {
        if (questionListVo.getQuestionMobilesList() == null || questionListVo.getQuestionMobilesList().isEmpty()) {
            if (questionListVo.getTaskPatientsId() != null) {
                sRoadTaskPatientsMapper.updateReadStatusByPrimaryKey(questionListVo.getTaskPatientsId());
            }
        }
    }

    public static boolean canStringBeConvertedToArray(String str) {
        try {
            // 尝试将字符串解析为JSON数组
            new JSONArray(str);
            return true;
        } catch (Exception e) {
            // 如果解析失败，说明不能转换
            return false;
        }
    }


    /**
     * 保存患者的答题
     *
     * @param questionListVo
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity save(QuestionListVo questionListVo) {
        QuestionMobile questionMobile = questionListVo.getQuestionMobile();
        TPreDiagnosisForm objectById = tPreDiagnosisFormMapper.getObjectById(questionMobile.getDiaId());
        if (null == objectById) {
            return ResEntity.error("表单不存在");
        }
        questionListVo.setFormType(objectById.getFormType());
        TRecord tRecord = new TRecord();
        TAdminInfo tokenBo = AdminUtils.getCurrentHr();
        if (tokenBo != null) {
            tRecord.setPatientId(tokenBo.getUserId());
            tRecord.setPatientAge(tokenBo.getAge());
            tRecord.setPatientSex("男".equals(tokenBo.getSex()) ? "M" : "F");
            tRecord.setPatientName(tokenBo.getUserName());
            tRecord.setPatientIdcard(tokenBo.getCardNumber());
            tRecord.setRegPlanId(tokenBo.getRegPlanId());
        } else {
            return ResEntity.error("未登录！");
        }
        //患者的答题
        List<QuestionMain> questionMobilesList = questionListVo.getQuestionMobilesList();
        tRecord.setCreateDate(new Date());
        tRecord.setDiaId(questionMobile.getDiaId());
        //tRecord.setPatientIdcard(tokenBo.getCardNumber());
        //加密
        tRecord.setPatientIdcard(tokenBo.getCardNumber());
//        tRecord.setRecId(IdUtil.nanoId());
        tRecord.setRecId(IdUtil.getSnowflake(snowflake).nextIdStr());
        tRecord.setRecordResource(Constant.BASIC_STRING_ONE);
        tRecord.setStatus("0");
        tRecord.setFormType(questionListVo.getFormType());
        //保存到明细
        List<TRecordDiaImage> questionImagesInsert = new ArrayList<>();
        for (QuestionMain questionMain : questionMobilesList) {
            TRecordDia dia = new TRecordDia();
            dia.setRecId(tRecord.getRecId());
            if (StringUtils.isBlank(questionMain.getQuestionType())) {
                return ResEntity.error("缺少questionType字段");
            }
            dia.setQuestionType(questionMain.getQuestionType());
            dia.setQuestionId(questionMain.getQuestionId());
            dia.setQuestionName(questionMain.getQuestionName());
            dia.setQuestionNumber(questionMain.getQuestionNumber());
            dia.setQuestionType(questionMain.getQuestionType());
            dia.setOptionIds(JSON.toJSONString(questionMain.getAnswerContentId()));
            //获取选项的随访事件代码
            //questionMain.getAnswerContentId() 转成Integer[]数组
            String[] answerContentId = questionMain.getAnswerContentId();
            Integer[] temp = new Integer[answerContentId.length];
            for (int i = 0; i < answerContentId.length; i++) {
                temp[i] = Integer.parseInt(answerContentId[i]);
            }
            if (temp.length > 0) {
                List<HashMap<String, String>> diagnosisEventCode = tPreDiagnosisOptionMapper.getDiagnosisEventCode(temp);
                ArrayList<TRecordEvent> tRecordEvents = new ArrayList<>();
                for (HashMap<String, String> map : diagnosisEventCode) {
                    TRecordEvent tRecordEvent = new TRecordEvent();
                    tRecordEvent.setRecId(tRecord.getRecId());
                    tRecordEvent.setDicId(map.get("dicId"));
                    tRecordEvent.setDicCode(map.get("dicCode"));
                    tRecordEvent.setDicName(map.get("dicName"));
                    tRecordEvents.add(tRecordEvent);
                }
                if (!tRecordEvents.isEmpty()) {
                    tRecordEventServiceImpl.saveBatch(tRecordEvents);
                }
            }
            if (null != questionMain.getAnswerContent() && questionMain.getAnswerContent().length > 0 && StringUtils.isNotBlank(JSON.toJSONString(questionMain.getAnswerContent()))){
                dia.setContent(JSON.toJSONString(questionMain.getAnswerContent()).replace("\\", ""));
            }
//            dia.setContent(JSON.toJSONString(questionMain.getAnswerContent()).replace("\\", ""));
            dia.setYear(StringUtils.isBlank(questionMain.getYear()) ? "0" : questionMain.getYear());
            dia.setMonth(StringUtils.isBlank(questionMain.getMonth()) ? "0" : questionMain.getMonth());
            dia.setDay(StringUtils.isBlank(questionMain.getDay()) ? "0" : questionMain.getDay());
            dia.setWeek(StringUtils.isBlank(questionMain.getWeek()) ? "0" : questionMain.getWeek());
            if (StringUtils.isNotBlank(dia.getDateUnit())) {
                if (dia.getDateUnit().contains("4")) {
                    if (StringUtils.isBlank(questionMain.getHour())) {
                        return ResEntity.error("含有题目是时间类型并且含有小时：未传hour");
                    }
                }
            }
            TPreDiagnosisQuestion objectById1 = tPreDiagnosisQuestionMapper.getObjectById(questionMain.getQuestionId() + "");
            dia.setDateUnit(objectById1.getDateUnit());
            dia.setHour(StringUtils.isBlank(questionMain.getHour()) ? "0" : questionMain.getHour());


            tRecordDiaMapper.insert(dia);

            //处理用户上传的图片
            List<TRecordDiaImage> questionImages = questionMain.getQuestionImages();
            if (questionImages != null && !questionImages.isEmpty()) {
                //把图片信息存到数据库 中
                questionImages.forEach(questionImage -> {
                    questionImage.setQuestionType(questionMain.getQuestionType());
                    questionImage.setQuestionId(questionMain.getQuestionId());
                    //sort按进来的顺序
                    questionImage.setSort(questionImages.indexOf(questionImage));
                    questionImage.setDiaRecId(dia.getDiaRecId());
                });
                questionImagesInsert.addAll(questionImages);
            }

        }
        //保存用户上传的图片
        if (!questionImagesInsert.isEmpty()){
            tRecordDiaImageService.saveBatch(questionImagesInsert);
        }
        //todo 计算保存用户的分数
        ArrayList<String> stringArrayList = new ArrayList<>();
        for (QuestionMain questionMain : questionMobilesList) {
            String[] answerContentId = questionMain.getAnswerContentId();
            //answerContentId塞到stringArrayList
            if (answerContentId != null) {
                Collections.addAll(stringArrayList, answerContentId);
            }
        }
        if (!stringArrayList.isEmpty()){
            List<HashMap<String, Object>> hashMaps = tPreDiagnosisQuestionMapper.selectDimensionScoreByList(stringArrayList);
            //对hashMaps 结果 中 的 map对象中questionDimensionCode字段进行分组分类 并且对同一类的optionDimensionScore字段值相加 分组分类和相加结果给到  TRecordDiaDimension 中
            HashMap<String, TRecordDiaDimension> stringTRecordDiaDimensionHashMap = new HashMap<>();
            for (HashMap<String, Object> map : hashMaps) {
                TRecordDiaDimension tRecordDiaDimension1 = stringTRecordDiaDimensionHashMap.get(map.get("questionDimensionCode").toString());
                Double optionDimensionScore = (Double) map.get("optionDimensionScore");
                if (null != tRecordDiaDimension1) {
                    tRecordDiaDimension1.setQuestionDimensionTotalScore(tRecordDiaDimension1.getQuestionDimensionTotalScore() + optionDimensionScore);
                }else {
                    TRecordDiaDimension tRecordDiaDimension = new TRecordDiaDimension();
                    tRecordDiaDimension.setQuestionDimensionTotalScore(optionDimensionScore);
                    tRecordDiaDimension.setQuestionDimensionCode(map.get("questionDimensionCode").toString());
                    tRecordDiaDimension.setQuestionDimensionName(map.get("questionDimensionName").toString());
                    tRecordDiaDimension.setTRecord(tRecord.getRecId());
                    stringTRecordDiaDimensionHashMap.put(map.get("questionDimensionCode").toString(), tRecordDiaDimension);
                }
            }
            List<TRecordDiaDimension> recordDiaDimensions = new ArrayList<>(stringTRecordDiaDimensionHashMap.values());
            tRecordDiaDimensionService.saveBatch(recordDiaDimensions);
        }
        tRecordMapper.insert(tRecord);
        if (StringUtils.isNotBlank(questionListVo.getTaskPatientsId())) {
            SRoadTaskPatients taskPatients = sRoadTaskPatientsMapper.getObjectById(questionListVo.getTaskPatientsId());
            if (null != taskPatients) {
                taskPatients.setRecId(tRecord.getRecId());
                taskPatients.setTaskExcuteStatus(8);
                sRoadTaskPatientsMapper.updateRecIdByPrimaryKey(taskPatients);
            }
        }
        largeFixFormMainService.transQuestion(questionListVo, tRecord.getRecId(),AdminUtils.getCurrentHr());
        return ResEntity.success(tRecord.getRecId());
    }


    public static void main(String[] args) {
        String a = "[\"清楚\"]";
        System.out.println(a.substring(2, a.lastIndexOf('\"')));

        boolean contains = "[\"2024\\\",\\\"05\\\",\\\"15\"]".contains("\\");
        System.out.println(contains);
        boolean contains1 = "[\"2024\",\"05\",\"15\"]".contains("\\");
        System.out.println(contains1);

        System.out.println("[\"2024\\\",\\\"05\\\",\\\"15\"]".replace("\\", ""));


        String[] aa = new String[]{"", "", ""};
//
//        System.out.println(aa);
        String sa = "";
        System.out.println(sa.split(",").length);
        System.out.println(aa[0].length());

    }

}
