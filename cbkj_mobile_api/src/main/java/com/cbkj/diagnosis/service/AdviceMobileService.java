package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.TBusinessProposal;
import com.cbkj.diagnosis.beans.business.adviceVo.AdviceSave;
import com.cbkj.diagnosis.beans.business.adviceVo.GetAdviceListRes;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.TBusinessProposalMapper;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AdminUtils;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/3 13:41
 * @Version 1.0
 */
@Service
public class AdviceMobileService {

    private final TBusinessProposalMapper tBusinessProposalMapper;

    public AdviceMobileService(TBusinessProposalMapper tBusinessProposalMapper) {
        this.tBusinessProposalMapper = tBusinessProposalMapper;
    }


    public Object getAdviceList(Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        TBusinessProposal tBusinessProposal = new TBusinessProposal();
        tBusinessProposal.setCreateUserId(AdminUtils.getCurrentHr().getUserId());
        List<GetAdviceListRes> adviceList = tBusinessProposalMapper.getAdviceList(tBusinessProposal);
        PageHelper.clearPage();
        for (GetAdviceListRes getAdviceListRes : adviceList) {
            getAdviceListRes.setAdviceImages(tBusinessProposalMapper.getAdviceImagesList(String.valueOf(getAdviceListRes.getId())) );
        }
        return Page.getResEntityPageData(adviceList);
    }


}
