package com.cbkj.diagnosis.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.GPTAskRequest;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisForm;
import com.cbkj.diagnosis.beans.business.TRecordLargeModel;
import com.cbkj.diagnosis.common.annotaionUtil.SonTransaction;
import com.cbkj.diagnosis.common.openfeign.LargeModelClient;
import com.cbkj.diagnosis.common.openfeign.LargeModelJsonExtractor;
import com.cbkj.diagnosis.common.openfeign.LargeModelRes;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import com.cbkj.diagnosis.mapper.business.TRecordLargeModelMapper;
import com.cbkj.diagnosis.service.business.TRecordLargeModelService;
import com.cbkj.diagnosis.service.common.vo.QuestionListVo;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AESPKCS7Util;
import com.cbkj.diagnosis.utils.AdminUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/28 10:40
 * @Version 1.0
 */
@Service
@Log4j2
public class LargeFixFormService {
    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;
    private final TPreDiagnosisFormMapper tPreDiagnosisFormMapper;
    private final LargeModelClient largeModelClient;
    private final TRecordLargeModelService tRecordLargeModelService;
    @Value(value = "${gpt.api.ageng.id}")
    private String agentId;
    @Value(value = "${gpt.api.cbdata.key}")
    private String cbdataKey;

    public LargeFixFormService(TPreDiagnosisFormMapper tPreDiagnosisFormMapper, LargeModelClient largeModelClient, TRecordLargeModelService tRecordLargeModelService) {
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
        this.largeModelClient =  largeModelClient;

        this.tRecordLargeModelService = tRecordLargeModelService;
    }

    @Async("myAsyncThreadPool")
//    @Transactional(rollbackFor = Exception.class)
//    @SonTransaction
    public void transQuestion(QuestionListVo questionListVo, String recId, TAdminInfo tokenBo) {
        if (questionListVo != null) {
            List<QuestionMain> questionMobilesList = questionListVo.getQuestionMobilesList();
            if (questionMobilesList != null && !questionMobilesList.isEmpty()) {
                StringBuilder stringBuffer = new StringBuilder();
                AtomicInteger temp = new AtomicInteger();

                String diaId = questionListVo.getQuestionMobile().getDiaId();
                TPreDiagnosisForm objectById = tPreDiagnosisFormMapper.getObjectById(diaId);
                if (objectById.getCueWordStatus() ==null || objectById.getCueWordStatus() == 1) {
                    return;
                }
                if (!objectById.getFormType().equals(Constant.BASIC_STRING_ONE)){
                    return;
                }
                questionMobilesList.stream().forEach(questionMain -> {
                    temp.getAndIncrement();
                    stringBuffer.append(temp).append("、").append(questionMain.getQuestionName());
                    String[] answerContent = questionMain.getAnswerContent();
                    //answerContent转成字符串
                    if (answerContent != null) {
                        stringBuffer.append(String.join(",", answerContent));
                    }
                });
                stringBuffer.append(objectById.getCueWordText()).append(objectById.getCueWordTrans());
//                TAdminInfo tokenBo = AdminUtils.getCurrentHr();
                HashMap<String, Object> map = new HashMap<>();
                map.put("appId", StringUtils.isBlank(tokenBo.getAppId()) ? "000000" : tokenBo.getAppId());
                map.put("insCode", StringUtils.isBlank(tokenBo.getInsCode()) ? "000000" : tokenBo.getInsCode());
                map.put("deptId", "000000");
                map.put("userName", tokenBo.getUserName());
                map.put("origin", "tcm");
                map.put("userId", tokenBo.getUserId());
                String json = JSON.toJSONString(map);
                String cbdata = null;
                try {
                    cbdata = AESPKCS7Util.encrypt(json, cbdataKey, "base64");
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

                GPTAskRequest gptAskRequest = new GPTAskRequest();
                gptAskRequest.setAgent_id(agentId);
                gptAskRequest.setQuestion(stringBuffer.toString());
                gptAskRequest.setCbdata(cbdata);
                TRecordLargeModel tRecordLargeModel = new TRecordLargeModel();
                tRecordLargeModel.setRecordModelId(IdUtil.getSnowflake(snowflake).nextIdStr());
                tRecordLargeModel.setRecId(recId);
                tRecordLargeModel.setDiaId(diaId);
                tRecordLargeModel.setRequestText(stringBuffer.toString());
                tRecordLargeModel.setInsertTime(new java.util.Date());
                tRecordLargeModel.setCueWordText(objectById.getCueWordText());
                tRecordLargeModel.setCuwWordTrans(objectById.getCueWordTrans());
                tRecordLargeModel.setReCallTimes(0);
                ResEntity<LargeModelRes> largeModelResResEntity = largeModelClient.gptAsk(gptAskRequest);
                if (largeModelResResEntity.getCode() == 0) {
                    tRecordLargeModel.setRecordModelStatus(0);
                    if (largeModelResResEntity.getData() != null) {
                        LargeModelRes data = largeModelResResEntity.getData();
                        tRecordLargeModel.setResponseText(data.getAnswer());
                        //入库
                    }
                } else {
                    tRecordLargeModel.setRecordModelStatus(1);
                    tRecordLargeModel.setResponseText(largeModelResResEntity.getMessage());
                }
                tRecordLargeModelService.save(tRecordLargeModel);
            }

        }
    }


}


