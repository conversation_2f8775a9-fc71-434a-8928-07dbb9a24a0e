package com.cbkj.diagnosis.service;


import com.cbkj.diagnosis.beans.AssociationUserInfo;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.TAdminInfo2;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.utils.AdminUtils;
import com.cbkj.diagnosis.utils.SystemConstants;
import com.cbkj.diagnosis.common.utils.IDUtil;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AESEncrypt;
import com.cbkj.diagnosis.utils.CardUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class TAdminInfoService {

    @Autowired
    private TAdminInfoMapper tAdminInfoMapper;

    /**
     * 获取用户userId
     *
     * @param
     * @return
     */
    public ResEntity getUserIdOrInsertUser(TAdminInfo adminInfo) {

        if (StringUtil.isNotEmpty(adminInfo.getIdcard())) {
//            adminInfo.setCardNumber(AESEncrypt.aes_encrypt(adminInfo.getIdcard(), SystemConstants.SYS_STR_KEY));
            String idcard = adminInfo.getIdcard();
            TAdminInfo2 tAdminInfo2 = new TAdminInfo2();
            tAdminInfo2.setIdCard(idcard);
            TAdminInfo admin = tAdminInfoMapper.getUserInfoByCardNumber(tAdminInfo2);
            if (admin == null) {
                adminInfo.setUserId(IDUtil.getID());
                adminInfo.setCreateTime(new Date());
                if (StringUtil.isNotEmpty(adminInfo.getIdcard()) && (adminInfo.getIdcard().length() == 15 || adminInfo.getIdcard().length() == 18)) {
                    adminInfo.setAge(CardUtil.getCarAge(adminInfo.getIdcard()));
                    adminInfo.setSex(CardUtil.getCarSex(adminInfo.getIdcard()));
                    adminInfo.setCardNumber(adminInfo.getIdcard());
                }
                if (StringUtil.isNotEmpty(adminInfo.getName())) {
                    adminInfo.setWxNiceName(adminInfo.getName());
                    adminInfo.setUserName(adminInfo.getName());
                }
                if (StringUtil.isNotEmpty(adminInfo.getMobile())) {
                    adminInfo.setMobile(adminInfo.getMobile());
                }
                tAdminInfoMapper.insert(adminInfo);
                return new ResEntity(true, "SUCCESS", adminInfo.getUserId());
            }
            return new ResEntity(true, "SUCCESS", admin.getUserId());
        } else {
            return new ResEntity(false, "缺少参数！", null);
        }
    }

    public List<AssociationUserInfo>  getaAssociation() {

//        if (StringUtils.isBlank(masterMobile)) {
//            masterMobile = AdminUtils.getCurrentHr().getMobile();
//        }
        //  TAdminInfo tAdminInfoById = tAdminInfoMapper.getTAdminInfoByMobile(AESEncrypt.aes_encrypt(masterMobile, SystemConstants.SYS_STR_KEY));
        TAdminInfo currentHr = AdminUtils.getCurrentHr();
        TAdminInfo tAdminInfoById = tAdminInfoMapper.getTAdminInfoById(currentHr.getUserId());

        List<AssociationUserInfo> associationUserInfos = tAdminInfoMapper.getaAssociation( StringUtils.isBlank( tAdminInfoById.getMasterUserId())?  tAdminInfoById.getUserId() :  tAdminInfoById.getMasterUserId()  );
//        for (AssociationUserInfo associationUserInfo : associationUserInfos) {
//            associationUserInfo.setMobile(masterMobile);
//        }
        return associationUserInfos;
    }


    public TAdminInfo getUserByMobile(String phone) {
        TAdminInfo2 tAdminInfo2 = new TAdminInfo2();
        tAdminInfo2.setMobile(phone);
        TAdminInfo userByMobile = tAdminInfoMapper.getUserByMobile(tAdminInfo2);
        return userByMobile;
    }
}