package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.business.TRecordDiaMapper;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategyFactory;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MyRecordDetailService {


    private final TRecordDiaMapper tRecordDiaMapper;

    private final RecordDiaStrategyFactory recordDiaStrategyFactory;

    public MyRecordDetailService(TRecordDiaMapper tRecordDiaMapper, RecordDiaStrategyFactory recordDiaStrategyFactory) {
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.recordDiaStrategyFactory = recordDiaStrategyFactory;
    }

    public ResEntity getPrePaper(String recId) {

        if (StringUtils.isBlank(recId)) {
            return ResEntity.entity(false, "recId参数不能为空哦", null);
        }
        List<TRecordDia> objectByRecId = tRecordDiaMapper.getObjectByRecId(recId);
        ArrayList<QuestionMain> list = new ArrayList<>();
        for (TRecordDia tRecordDia : objectByRecId) {
            list.add(recordDiaStrategyFactory.getNormalParamStrategy(tRecordDia.getQuestionType()).transRecordDia(tRecordDia));
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }
}
