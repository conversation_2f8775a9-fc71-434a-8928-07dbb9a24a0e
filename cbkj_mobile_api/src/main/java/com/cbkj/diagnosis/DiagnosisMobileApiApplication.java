package com.cbkj.diagnosis;

import com.cbkj.diagnosis.mybatis.multipleDataSource.DynamicDataSourceRegister;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@EnableAspectJAutoProxy
@SpringBootApplication
@MapperScan("com.cbkj.diagnosis.mapper*")
@Import({DynamicDataSourceRegister.class})
@EnableAsync
@EnableHystrix
@EnableCircuitBreaker
@EnableFeignClients
@ConfigurationPropertiesScan
//@EnableAsync//开启异步注解
//@EnableScheduling//开启定时任务注解
@EnableDiscoveryClient
public class DiagnosisMobileApiApplication {
    public static void main(String[] args) {
        SpringApplication.run(DiagnosisMobileApiApplication.class, args);
    }
}