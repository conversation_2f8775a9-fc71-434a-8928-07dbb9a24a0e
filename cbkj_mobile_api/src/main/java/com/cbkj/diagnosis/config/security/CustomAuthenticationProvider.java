//package com.cbkj.diagnosis.config.security;
//
//
//import com.cbkj.diagnosis.service.TAdminInfoService;
////import com.cbkj.diagnosis.service.sysService.AdminService;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.authentication.AuthenticationProvider;
//import org.springframework.security.authentication.BadCredentialsException;
//import org.springframework.security.authentication.DisabledException;
//import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.core.GrantedAuthority;
//import org.springframework.security.core.authority.SimpleGrantedAuthority;
//import org.springframework.security.core.userdetails.UserDetails;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.Collection;
//import java.util.List;
//
///**
// * AuthenticationProvider提供登录验证处理逻辑
// */
//@Component
//public class CustomAuthenticationProvider implements AuthenticationProvider {
//
//    @Autowired
//    TAdminInfoService tAdminInfoService;
//
//    @Override
//    public Authentication authenticate(Authentication authentication) {
//
//        CustomWebAuthenticationDetails details = (CustomWebAuthenticationDetails) authentication.getDetails();
//        String username = authentication.getName();
//        String password = (String) authentication.getCredentials();
//        UserDetails user = tAdminInfoService.loadUserByUsername(username);
//
//        if(null == user ){
//            throw new BadCredentialsException("账户不存在!");
//        }
//
//        if (!password.equals(user.getPassword())) {
//            throw new BadCredentialsException("用户名或者密码错误!");
//        }
//
//        if(!user.isEnabled()){
//            throw new DisabledException("当前账户被禁用！");
//        }
//
//
//        String roleName = details.getRoleName();
//        List<GrantedAuthority> newAuthorities = new ArrayList<>();
//        Collection<? extends GrantedAuthority> authorities = user.getAuthorities();
//
//        if(StringUtils.isNotBlank(roleName)){
//
//            for(GrantedAuthority grantedAuthority:authorities){
//                if(roleName.equals(grantedAuthority.getAuthority())){
//                    newAuthorities.add(new SimpleGrantedAuthority(roleName));
//                }
//            }
//        }
//
//        if(newAuthorities.size() <= 0){
//            newAuthorities = (List<GrantedAuthority>) authorities;
//        }
//
//        return new UsernamePasswordAuthenticationToken(user, password, newAuthorities);
//    }
//
//    @Override
//    public boolean supports(Class<?> authentication) {
//        return authentication.equals(UsernamePasswordAuthenticationToken.class);
//    }
//}