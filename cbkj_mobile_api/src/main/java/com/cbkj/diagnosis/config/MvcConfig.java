package com.cbkj.diagnosis.config;

import com.cbkj.diagnosis.config.interceptor.CustomInterceptor;
import com.cbkj.diagnosis.config.interceptor.TokenInterceptor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.*;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Configuration
public class MvcConfig implements WebMvcConfigurer {

    @Value("${file.address}")
    private String localtion;

    @Value("${root.preview}")
    private String preview;

    @Autowired
    private TokenUtil tokenUtil;
    @Autowired
    private ObjectMapper objectMapper;

    @Resource
    private CustomInterceptor customInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加自定义的 token 拦截器
        registry.addInterceptor(tokenInterceptor(tokenUtil, objectMapper))
                .addPathPatterns("/healthy/**")
                .excludePathPatterns("/preview/**", "/sys/help/download");

        // 添加设置 Referrer-Policy 头的拦截器
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
                response.setHeader("Referrer-Policy", "no-referrer");
                return true;
            }
        }).addPathPatterns("/**"); // 根据需要调整路径模式
        // 添加设置 X-Content-Type-Options 头的拦截器
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
                response.setHeader("X-Content-Type-Options", "nosniff");
                return true;
            }
        }).addPathPatterns("/**"); // 根据需要调整路径模式
    }



    public TokenInterceptor tokenInterceptor(TokenUtil tokenUtil, ObjectMapper objectMapper) {
        return new TokenInterceptor(tokenUtil, objectMapper);
    }



    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/" + preview + "**").addResourceLocations("file:" + localtion);
        WebMvcConfigurer.super.addResourceHandlers(registry);
    }

//    @Override
//    public void addCorsMappings(CorsRegistry registry) {
//        registry.addMapping("/**").
//                allowCredentials(true).
//                allowedHeaders("*").
//                allowedOrigins("*").
//                allowedMethods("*");
//    }

    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer.favorParameter(true).parameterName("format")
                .defaultContentType(MediaType.APPLICATION_JSON)
                .mediaType("xml", MediaType.APPLICATION_XML)
                .mediaType("json", MediaType.APPLICATION_JSON);
    }



}