package com.cbkj.diagnosis.config.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.config.TokenBo;
import com.cbkj.diagnosis.config.TokenUtil;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.SessionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * TokenInterceptor
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/11/9
 */
@Component
@Slf4j
public class TokenInterceptor implements HandlerInterceptor {

    private TokenUtil tokenUtil;
    private ObjectMapper objectMapper;

    public TokenInterceptor(TokenUtil tokenUtil, ObjectMapper objectMapper) {
        this.tokenUtil = tokenUtil;
        this.objectMapper = objectMapper;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = request.getHeader("Authorization");
        if (StringUtils.isBlank(token)) {
            response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
            response.getWriter().write(objectMapper.writeValueAsString(
                    ResEntity.entity(false, "Token不能为空！！", "602")));
            return false;
        }
        TokenBo tokenBo = tokenUtil.getTokenBo(token);
        if (tokenBo == null || tokenBo.getAdmin() == null) {
            response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
            response.getWriter().write(objectMapper.writeValueAsString(
                    ResEntity.entity(false, "登录信息不存在！！", "604")));
            return false;
        }
        System.out.println("PRS tokenBo ========== " + JSONObject.toJSONString(tokenBo));
        TAdminInfo user = tokenBo.getAdmin();
//        SessionUtils.setSessionAttribute(tokenBo.getUserId(), tokenBo.getAdmin());
        SessionUtils.setSessionAttribute("user", tokenBo.getAdmin());
        log.info("user=============={}", JSONObject.toJSONString(user));
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {
        // TODO Auto-generated method stub
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        // TODO Auto-generated method stub
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}
