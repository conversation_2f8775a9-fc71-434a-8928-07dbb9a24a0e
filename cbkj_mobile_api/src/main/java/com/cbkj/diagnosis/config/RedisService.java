package com.cbkj.diagnosis.config;


import com.cbkj.diagnosis.beans.TAdminInfo;
//import com.cbkj.diagnosis.beans.sysBeans.AdminMenu;
import com.cbkj.diagnosis.utils.SystemConstants;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
//import com.cbkj.diagnosis.mapper.sysmapper.AdminMenuMapper;
import com.cbkj.diagnosis.utils.AESEncrypt;
import com.github.pagehelper.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Set;

@Service
public class RedisService {

//    @Autowired
//    private AdminMenuMapper adminMenuMapper;
    @Autowired
    private TAdminInfoMapper tAdminInfoMapper;

    @Autowired
    private RedisTemplate redisTemplate;



//    @CusRedisCache(key = "allMenus")
//    public List<AdminMenu> getAllEnableMenu() {
//        return adminMenuMapper.getAllMenu();
//    }

    /**
     * 设置token
     *
     * @param tokenKey tokenKey
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/14
     */
    @Cacheable(value = "diagnosis-mobile-token", key = "#tokenKey")
    public TokenBo getToken(String tokenKey) {
        return null;
    }

    /**
     * 设置token
     *
     * @param tokenKey tokenKey
     * @param tokenBo  tokenBo
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/14
     */
    @CachePut(value = "diagnosis-mobile-token", key = "#tokenKey")
    public TokenBo putToken(String tokenKey, TokenBo tokenBo) {
        return tokenBo;
    }




    /**
     * 获取用户
     *
     * @param userId userId
     * @return com.example.cbkj_core.beans.business.TAdminInfo
     * <AUTHOR>
     * @date 2021/11/9
     */
   // @Cacheable(value = "diagnosis-mobile-user", keyGenerator = "cacheKeyGenerator")
    public TAdminInfo loadUserByUserId(String userId) {
        TAdminInfo obj = tAdminInfoMapper.getObjectById(userId);
        if (obj != null) {
            if (StringUtil.isNotEmpty(obj.getSex())) {
                if ("M".equals(obj.getSex())){
                    obj.setSex("男");
                }else {
                    obj.setSex("女");
                }
            }
            if (StringUtil.isNotEmpty(obj.getMobile())) {
                //obj.setMobile(AESEncrypt.aes_decrypt(obj.getMobile(), SystemConstants.SYS_STR_KEY));
            }
            if (StringUtil.isNotEmpty(obj.getCardNumber())) {
                //obj.setCardNumber(AESEncrypt.aes_decrypt(obj.getCardNumber(), SystemConstants.SYS_STR_KEY));
            }
            if (StringUtil.isNotEmpty(obj.getWxNiceName())) {
                //obj.setWxNiceName(AESEncrypt.aes_decrypt(obj.getUserName(), SystemConstants.SYS_STR_KEY));
            }

        } else {
            obj = new TAdminInfo();
            obj.setUserId(userId);
            obj.setCreateTime(new Date());
            tAdminInfoMapper.insert(obj);
        }
//        if (StringUtil.isNotEmpty(obj.getCardNumber())) {
//            obj.setCardNumber(obj.getCardNumber().substring(0, 4) + "**********" + obj.getCardNumber().substring(obj.getCardNumber().length() - 4, obj.getCardNumber().length()));
//        }
        return obj;
    }


    public void clearRedisCache(String value, String key) {

        try {
            if (StringUtils.isNotBlank(value)) {
                Set<String> keys = redisTemplate.keys(value + "*");
                Long result = redisTemplate.delete(keys);
            }
            if (StringUtils.isNotBlank(key)) {
                Boolean result = redisTemplate.delete(key);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }


    public void setFuZhenRecId(String id, String code) {
        redisTemplate.opsForList().rightPush("business:set", id + ":" + code);
    }
    public void setAiImage(String id) {
        redisTemplate.opsForList().rightPush("business:ai-image", id );
    }
}