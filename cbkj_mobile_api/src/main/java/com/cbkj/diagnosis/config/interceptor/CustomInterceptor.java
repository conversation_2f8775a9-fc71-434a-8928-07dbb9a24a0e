package com.cbkj.diagnosis.config.interceptor;


import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.common.annotaionUtil.StatisticsFunction;
import com.cbkj.diagnosis.config.annotaionUtil.LogRecord;
import com.cbkj.diagnosis.utils.AdminUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;

@Component
public class CustomInterceptor implements HandlerInterceptor {

    private Logger logger = LoggerFactory.getLogger(CustomInterceptor.class.getName());

    private ThreadLocal<Long> threadLocal = new ThreadLocal<>();




    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            LogRecord logRecord = method.getAnnotation(LogRecord.class);
            if (logRecord != null) {
                threadLocal.set(System.currentTimeMillis());
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();

        LogRecord logRecord = method.getAnnotation(LogRecord.class);
        if (logRecord != null) {
            long startTime = threadLocal.get();
            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;

            String requestUri = request.getRequestURI();
            String methodName = method.getDeclaringClass().getName() + "." + method.getName();
            String methodDesc = logRecord.methodDesc();

            logger.info("requestUri({})  methodName({})  methodDesc({}) ==> 花费时间：{}毫秒", requestUri, methodName, methodDesc, costTime);
            threadLocal.remove();
        }
    }
}
