package com.cbkj.diagnosis.config;


import com.cbkj.diagnosis.beans.TAdminInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * SecurityVo
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/7/12
 */
@Data
@NoArgsConstructor
public class TokenBo implements Serializable {

    /**
     * tokenKey
     */
    private String tokenKey;


    /**
     * authorization
     */
    private String authorization;

    /**
     * 患者id
     */
    private String userId;

    /**
     *
     */
    private TAdminInfo admin;

    public TokenBo(String tokenKey, String userId) {
        this.tokenKey = tokenKey;
        this.userId = userId;
    }

    public TokenBo(String tokenKey, String userId, TAdminInfo admin) {
        this.tokenKey = tokenKey;
        this.userId = userId;
        this.admin = admin;
    }

}
