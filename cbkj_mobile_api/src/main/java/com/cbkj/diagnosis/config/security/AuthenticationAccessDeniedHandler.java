package com.cbkj.diagnosis.config.security;

import com.alibaba.druid.util.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * 20180521
 */
@Component
public class AuthenticationAccessDeniedHandler implements AccessDeniedHandler {
    @Override
    public void handle(HttpServletRequest req, HttpServletResponse res, AccessDeniedException e) {
        String url = req.getContextPath()+"/403";
        try{
            if(!StringUtils.isEmpty(req.getHeader("x-requested-with")) && ("XMLHttpRequest".equals(req.getHeader("x-requested-with")))){
                res.setStatus(HttpServletResponse.SC_FORBIDDEN);
                res.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
                PrintWriter out = res.getWriter();
                out.write("{\"status\":\"error\",\"msg\":\"权限不足，请联系管理员!\",\"url\":"+url+"}");
                out.flush();
                out.close();
            }else{
                res.sendRedirect(url);
            }
        }catch(Exception exc){
            System.out.println(exc.getMessage());
        }

    }
}