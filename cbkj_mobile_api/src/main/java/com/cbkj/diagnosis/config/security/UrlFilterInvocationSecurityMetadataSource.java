package com.cbkj.diagnosis.config.security;


import com.cbkj.diagnosis.common.utils.Constant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.servlet.http.HttpSession;
import java.util.Collection;

/**
 *
 */
@Slf4j
@Component
public class UrlFilterInvocationSecurityMetadataSource implements FilterInvocationSecurityMetadataSource {





    AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Override
    public Collection<ConfigAttribute> getAttributes(Object o) throws IllegalArgumentException {
        //获取请求地址
        FilterInvocation invocation = ((FilterInvocation) o);
        HttpSession session = invocation.getRequest().getSession();

        String requestUrl = invocation.getRequestUrl();
        if ("/loginP".equals(requestUrl)) {
            session.removeAttribute(Constant.MENUS);
            return null;
        }
        if("/loginP?logout".equals(requestUrl)){
            session.removeAttribute(Constant.MENUS);
            return null;
        }
        if(requestUrl.startsWith("/loginWs/")){
            return SecurityConfig.createList("ROLE_LOGIN");
        }
        log.info("当前访问路径：{}",requestUrl);
        return SecurityConfig.createList("ROLE_LOGIN");
    }

    @Override
    public Collection<ConfigAttribute> getAllConfigAttributes() {
        return null;
    }

    @Override
    public boolean supports(Class<?> aClass) {
        return FilterInvocation.class.isAssignableFrom(aClass);
    }
}