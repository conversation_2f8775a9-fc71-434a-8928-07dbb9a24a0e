package com.cbkj.diagnosis.config.annotaionUtil;

import java.lang.annotation.*;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface CusRedisCache {
    /**
     * 缓存key
     * @return
     */
    String key() default "";


    String value() default "";
    /**
     * 过期时间
     * @return
     */
    long expired() default -1;

    /**
     * 清除缓存
     * @return
     */
    boolean clear() default false;
}