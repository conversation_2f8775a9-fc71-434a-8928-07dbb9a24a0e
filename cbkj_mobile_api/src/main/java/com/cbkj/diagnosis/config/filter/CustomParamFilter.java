//package com.cbkj.diagnosis.config.filter;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.http.HttpMethod;
//
//import javax.servlet.*;
//import javax.servlet.annotation.WebFilter;
//import javax.servlet.http.HttpServletRequest;
//import java.io.IOException;
//import java.io.PrintWriter;
//import java.util.HashMap;
//import java.util.Map;
//
//@WebFilter(filterName = "customParamFilter", urlPatterns = {"/*"})
//public class CustomParamFilter implements Filter {
//
//    @Override
//    public void init(FilterConfig filterConfig) throws ServletException {
//
//    }
//
//    @Override
//    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, <PERSON><PERSON><PERSON>hain filterChain) throws IOException, ServletException {
//        HttpServletRequest request = (HttpServletRequest) servletRequest;
//        MyHttpRequest requestWrapper = new MyHttpRequest(request);
//        try {
//            // get请求
//            if (requestWrapper.getMethod().equals(HttpMethod.GET.name())) {
//                // 对参数进行处理，得到新的参数，将参数重新设置到request中
//                Map<String, String> params = handle(requestWrapper.getParameterMap());
//                requestWrapper.setParameter(params);
//            } else { // post请求，这里没有对x-www-form-urlencoded类型进行处理
//                String body = requestWrapper.getBody();
//                if (StringUtils.isNotEmpty(body)) {
//                    JSONObject jsonObject = JSONObject.parseObject(body);
//                    // 对参数进行处理
//                    Map<String, String> params = handle(jsonObject);
//                    // 由于body是设置成不可变的，所以需要重新创建一个request，将body设置进去
//                    requestWrapper = new MyHttpRequest(requestWrapper, JSONObject.toJSONString(params).getBytes());
//                }
//            }
//            filterChain.doFilter(requestWrapper, servletResponse);
//        } catch (Exception ex) {
//            outErrorMessage(servletResponse, 500, ex.getMessage());
//        }
//
//    }
//
//    @Override
//    public void destroy() {
//
//    }
//
//    public void outErrorMessage(ServletResponse servletResponse, Integer code, String msg) throws IOException {
//        //返回json错误
//        servletResponse.setCharacterEncoding("UTF-8");
//        servletResponse.setContentType("application/json; charset=utf-8");
//        PrintWriter out = servletResponse.getWriter();
//        JSONObject res = new JSONObject();
//        res.put("code", code);
//        res.put("message", msg);
//        res.put("status", false);
//        out.append(JSON.toJSONString(res));
//    }
//
//    private Map<String, String> handle(Object params) {
//        // 自定义参数的处理逻辑，最后将参数以map形式返回回去
//        // ......
//
//        return new HashMap<>();
//    }
//}
//
