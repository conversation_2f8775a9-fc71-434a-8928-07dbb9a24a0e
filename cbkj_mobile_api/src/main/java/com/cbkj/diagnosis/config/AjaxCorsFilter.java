package com.cbkj.diagnosis.config;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;


/**
 * 跨域过滤器
 * <AUTHOR>
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class AjaxCorsFilter extends CorsFilter {


    public AjaxCorsFilter() {
        super(configurationSource());
    }

    private static UrlBasedCorsConfigurationSource configurationSource() {

        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.addAllowedOriginPattern("*");
//        corsConfig.addAllowedOrigin("*");
        corsConfig.addAllowedMethod("*");
        corsConfig.addAllowedHeader("*");
        corsConfig.setMaxAge(36000L);
        corsConfig.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);
        return source;
    }

//    private static UrlBasedCorsConfigurationSource configurationSource() {
//        CorsConfiguration corsConfig = new CorsConfiguration();
//        //Origin配置"*"时，cookie不生效
////        corsConfig.addAllowedOrigin("*");
//        corsConfig.addAllowedOrigin("https://cloud.tcmbrain.com");
//        corsConfig.addAllowedOrigin("https://zyjkb.wsjkw.hangzhou.gov.cn");
//        corsConfig.addAllowedOrigin("https://zyjkb.wsjkw.hangzhou.gov.cn:8084");
//        corsConfig.addAllowedOrigin("http://192.168.2.90");
//        corsConfig.addAllowedOrigin("http://192.168.2.81");
//        corsConfig.addAllowedMethod("*");
//        corsConfig.addAllowedHeader("*");
//        corsConfig.setMaxAge(36000L);
//        corsConfig.setAllowCredentials(true);
//
//        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
//        source.registerCorsConfiguration("/**", corsConfig);
//        return source;
//    }

}