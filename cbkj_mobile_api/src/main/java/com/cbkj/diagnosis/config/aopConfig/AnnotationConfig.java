package com.cbkj.diagnosis.config.aopConfig;


import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.beans.SysLogInterface;
import com.cbkj.diagnosis.common.utils.IDUtil;
import com.cbkj.diagnosis.config.annotaionUtil.LogAnnotaion;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.LogAsync;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;

@Slf4j
@Aspect
@Component
public class AnnotationConfig {



    private final LogAsync logAsync;

    public AnnotationConfig(LogAsync logAsync) {
        this.logAsync = logAsync;
    }


    @Pointcut("execution(public * com.cbkj.diagnosis.controller.*.*(..))")
    public void web() {
    }


    //@Around("web()")
    @Around("@annotation(logAnnotaion)")
    public Object arround(ProceedingJoinPoint joinPoint,LogAnnotaion logAnnotaion) throws Throwable {
        String params;
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Method method = getMethod(joinPoint);
        String methodName = joinPoint.getSignature().getName();
        //params = JSONObject.toJSONString(joinPoint.getArgs()[0]);
        params = JSONObject.toJSONString(joinPoint.getArgs());
        Object o = joinPoint.proceed();
        if (null != method && o instanceof ResEntity) {

            String descpt = null;
            Object logObj = method.getAnnotation(LogAnnotaion.class);
            if (null != logObj) {

                //LogAnnotaion logAnnotaion = ((LogAnnotaion) logObj);
                if (logAnnotaion.isWrite()) {
                    descpt = logAnnotaion.value();
                }
            }

            if (StringUtils.isNotBlank(descpt)) {

                ResEntity entity = (ResEntity) o;
                String methodType = request.getMethod();
                Map<String, String[]> paramMap = request.getParameterMap();


                if ("GET".equals(methodType)) {
                    params = getParams(paramMap);
                }

                SysLogInterface log = new SysLogInterface();
                log.setId(IDUtil.getID());
                log.setAppId(getParam(paramMap, "appId"));
                log.setInsCode(getParam(paramMap, "insCode"));
                log.setDoctorId(getParam(paramMap, "doctorId"));
                log.setDoctorName(getParam(paramMap, "doctorName"));
                log.setPatientId(getParam(paramMap, "patientId"));
                log.setPatientName(getParam(paramMap, "patientName"));
                log.setCreateTime(new Date());
                log.setInterfaceName(methodName);
                log.setInterfaceDesc(descpt);
                log.setInterfaceToken(getParam(paramMap, "token"));
                log.setInterfaceParams(params);
                log.setResultStatus(entity.getStatus() ? "1" : "0");
                log.setResultMeseage(entity.getMessage());
                if (entity.getData() != null) {
                    log.setResultData(JSONObject.toJSONString(entity.getData()));
                }

                    logAsync.logRecord(log);

            }
        }
        return o;
    }




    private static String getParam(Map<String, String[]> paramMap, String key) {
        String[] values = paramMap.get(key);
        if (values != null && values.length > 0) {
            StringBuilder sb = new StringBuilder();
            for (String value : values) {
                if (StringUtils.isNotBlank(value)) {
                    sb.append(value).append(",");
                }
            }
            return sb.substring(0, sb.length());
        }
        return "";
    }





    private static String getParams(Map<String, String[]> paramMap) {
        StringBuffer sb = new StringBuffer(" ");
        for (String key : paramMap.keySet()) {
            String[] values = paramMap.get(key);
            for (int i = 0; i < values.length; i++) {
                String value = values[i];
                if (StringUtils.isNotBlank(value)) {
                    sb.append(key).append(":").append(value).append(",");
                }
            }
        }
        return sb.substring(0, sb.length());
    }

    /**
     * 获取堆栈信息
     *
     * @param throwable throwable
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/4/13
     */
    private String getStackTrace(Throwable throwable) {

        StringWriter sw = new StringWriter();

        PrintWriter pw = new PrintWriter(sw);
        try {
            throwable.printStackTrace(pw);
            return sw.toString();
        } finally {
            pw.close();
        }
    }

    /**
     * 获取方法
     *
     * @param joinPoint joinPoint
     * @return Method
     */
    private Method getMethod(JoinPoint joinPoint) {

        Method[] methods = joinPoint.getTarget().getClass().getMethods();
        String methodName = joinPoint.getSignature().getName();
        Object[] objs = joinPoint.getArgs();

        return Arrays.stream(methods).filter(method ->
                        (method.getName().equals(methodName) && method.getParameterTypes().length == objs.length))
                .findAny()
                .orElse(null);

    }
}