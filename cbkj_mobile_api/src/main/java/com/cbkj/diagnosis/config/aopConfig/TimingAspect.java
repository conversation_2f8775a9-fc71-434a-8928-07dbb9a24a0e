package com.cbkj.diagnosis.config.aopConfig;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.beans.SysLogInterface;
import com.cbkj.diagnosis.common.utils.IDUtil;
import com.cbkj.diagnosis.config.annotaionUtil.Timing;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.LogAsync;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/22 11:45
 * @Version 1.0
 */
@Slf4j
@Aspect
@Component
public class TimingAspect {

    private final LogAsync logAsync;

    public TimingAspect(LogAsync logAsync) {
        this.logAsync = logAsync;
    }

    @Around("@annotation(timing)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint, Timing timing) throws Throwable {
        long start = System.nanoTime();
        Object result = joinPoint.proceed();
        long elapsedTime = System.nanoTime() - start;

        String className = joinPoint.getSignature().getDeclaringTypeName();
        String methodName = joinPoint.getSignature().getName();
        String annotationValue = timing.value();

        long convert = TimeUnit.MILLISECONDS.convert(elapsedTime, TimeUnit.NANOSECONDS);

        String result2 = String.format("方法 %s#%s (%s) 花销 %d 毫秒\n",
                className, methodName, annotationValue,
                convert);
        log.info(result2);


        if (convert > 3000) {
            Object[] args = joinPoint.getArgs();
            Object[] arguments = new Object[args.length];
            for (int i = 0; i < args.length; i++) {
                if (args[i] instanceof ServletRequest || args[i] instanceof ServletResponse || args[i] instanceof MultipartFile) {
                    //ServletRequest不能序列化，从入参里排除，否则报异常：java.lang.IllegalStateException: It is illegal to call this method if the current request is not in asynchronous mode (i.e. isAsyncStarted() returns false)
                    //ServletResponse不能序列化 从入参里排除，否则报异常：java.lang.IllegalStateException: getOutputStream() has already been called for this response
                    continue;
                }
                arguments[i] = args[i];
            }
            String paramter = "";
            try {
                paramter = JSONObject.toJSONString(arguments);
            } catch (Exception e) {
                paramter = arguments.toString();
            }

            SysLogInterface log = new SysLogInterface();
            log.setInterfaceParams(paramter);
            log.setId(IDUtil.getID());
            log.setCreateTime(new Date());
            log.setInterfaceName(methodName);
            log.setInterfaceDesc(annotationValue);
            log.setResultMeseage(convert + "");
            log.setResultData(JSON.toJSONString(result));
            logAsync.logRecord(log);
        }

        return result;

    }
}
