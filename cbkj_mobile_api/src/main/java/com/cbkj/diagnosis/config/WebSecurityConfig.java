package com.cbkj.diagnosis.config;


import com.cbkj.diagnosis.config.security.AuthenticationAccessDeniedHandler;
import com.cbkj.diagnosis.config.security.AuthorizationTokenFilter;
//import com.cbkj.diagnosis.config.security.CustomAuthenticationProvider;
import com.cbkj.diagnosis.config.security.UrlAccessDecisionManager;
import com.cbkj.diagnosis.config.security.UrlFilterInvocationSecurityMetadataSource;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AjaxRequestMatcher;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.config.annotation.ObjectPostProcessor;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.WebAuthenticationDetails;

import javax.servlet.http.HttpServletRequest;

/**
 *
 */
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {
    @Value("${jwt.token}")
    private String tokenHeader;
    @Value("${root.preview}")
    private String preview;
    @Autowired
    private TokenUtil tokenUtil;

    @Value("${jwt.expirationExt}")
    private Long expirationExt;
    @Autowired
    UrlFilterInvocationSecurityMetadataSource urlFilterInvocationSecurityMetadataSource;
    @Autowired
    UrlAccessDecisionManager urlAccessDecisionManager;
    @Autowired
    AuthenticationAccessDeniedHandler authenticationAccessDeniedHandler;
//    @Autowired
//    private CustomAuthenticationProvider authenticationProvider;
    @Autowired
    private AuthenticationEntryPoint authenticationEntryPoint;
    @Autowired
    private AjaxRequestMatcher ajaxRequestMatcher;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AuthenticationDetailsSource<HttpServletRequest, WebAuthenticationDetails> authenticationDetailsSource;

//    @Autowired
//    private SmsCodeAuthenticationSecurityConfig smsCodeAuthenticationSecurityConfig;


//    @Override
//    protected void configure(AuthenticationManagerBuilder auth) {
//        auth.authenticationProvider(authenticationProvider);
//    }

    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers(
                "/getToken",
                "/getTokenByM",

                "/doc.html","/swagger-ui.html",
                "/doc.html", "/swagger-ui.html", "/swagger-ui.html/**", "/webjars/**",
                "/swagger-resources/**", "/barcode/**", "/v2/api-docs",
                "/healthy/**",
                "index.html","favicon.ico","/webjars/**",
                "/" + preview + "**","/his/**","/sys/help/download","/swagger-ui.html","/swagger-ui/**","/v3/**");
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.cors() // 配置CORS
                .and()
                .csrf().disable() // 禁用CSRF保护
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS) // 无状态会话管理
                .and()
                .authorizeRequests().anyRequest().authenticated() // 所有请求都需要认证
                .and()
                .addFilterAfter(getAuthorizationTokenFilter(), UsernamePasswordAuthenticationFilter.class) // 添加自定义过滤器
                .headers() // 配置HTTP头
                .httpStrictTransportSecurity() // 配置HSTS
                .maxAgeInSeconds(16070400) //
                .includeSubDomains(true) // 包括子域名
                .preload(true)
                .and()
                .xssProtection()
                .block(true)// 启用XSS保护并阻止页面渲染
                .and()
                .contentSecurityPolicy("default-src 'self'")
                .and()
                .frameOptions()
                .deny(); // 允许预加载
         }

    private AuthorizationTokenFilter getAuthorizationTokenFilter() {
        return new AuthorizationTokenFilter(tokenUtil, objectMapper, tokenHeader, expirationExt);
    }

}