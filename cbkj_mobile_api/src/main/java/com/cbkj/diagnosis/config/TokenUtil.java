package com.cbkj.diagnosis.config;

import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.beans.TAdminInfo;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Clock;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.DefaultClock;
import io.jsonwebtoken.impl.TextCodec;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019年12月13日 14:35:00
 */
@Component
@Slf4j
public class TokenUtil implements Serializable {
    private static final long serialVersionUID = -3301605591108950415L;
    private static final String TOKEN_PREFIX = "TOKEN_";
    private Clock clock = DefaultClock.INSTANCE;
    @Autowired
    private RedisService redisService;

    @Value("${login.one}")
    private boolean loginOne;
    @Value("${jwt.secret:}")
    private String secret;

    @Value("${jwt.expiration:}")
    private Long expiration;


    /**
     * 获取Token
     *
     * @param tokenKey tokenKey
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public TokenBo getTokenBo(String tokenKey) {
        TokenBo tokenBo = redisService.getToken(tokenKey);
        if(tokenBo == null || !tokenKey.equals(tokenBo.getTokenKey())){
            return null;
        }else{
            //缓存过期时自动重新获取。如果需处理登录过期，可以改为抛出异常等
            log.info("PRS redis ========== {}", JSONObject.toJSONString(tokenBo));
            tokenBo = createTokenBo(tokenBo.getAdmin(),tokenBo.getAdmin().getRegPlanId());
        }
        return tokenBo;
    }

    /**
     * 创建Token
     *
     * @param userId userId
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public TokenBo createTokenBo(String userId,String regPlanId) {
        String tokenKey = createTokenKey(userId);
        String authorization = generateToken(tokenKey);
        TAdminInfo admin = redisService.loadUserByUserId(userId);
        admin.setRegPlanId(regPlanId);
        TokenBo tokenBo = new TokenBo(authorization, userId, admin);
        //TokenBo tokenBo = new TokenBo(authorization, userId, admin);
        redisService.putToken(tokenKey, tokenBo);
        return tokenBo;
    }
    public TokenBo createTokenBo(TAdminInfo tAdminInfo,String regPlanId) {
        String tokenKey = createTokenKey(tAdminInfo.getUserId());
        String authorization = generateToken(tokenKey);
        TAdminInfo admin = redisService.loadUserByUserId(tAdminInfo.getUserId());
        admin.setRegPlanId(regPlanId);
        admin.setAppId(tAdminInfo.getAppId());
        admin.setInsCode(tAdminInfo.getInsCode());
        admin.setInsId(tAdminInfo.getInsId());
        admin.setInsName(tAdminInfo.getInsName());
        TokenBo tokenBo = new TokenBo(authorization, tAdminInfo.getUserId(), admin);
        //TokenBo tokenBo = new TokenBo(authorization, userId, admin);
        redisService.putToken(tokenKey, tokenBo);
        return tokenBo;
    }

    public String createTokenKey(String adminId) {
        return TOKEN_PREFIX + adminId;
    }

    public String generateToken(String key) {
        return doGenerateToken(new HashMap<>(), key);

    }
    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }
    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret).setAllowedClockSkewSeconds(0)
                .parseClaimsJws(token)
                .getBody();
    }

    private Date calculateExpirationDate(Date createdDate) {
        return new Date(createdDate.getTime() + expiration);
    }
    private String doGenerateToken(Map<String, Object> claims, String subject) {

        final Date createdDate = clock.now();
        final Date expirationDate = calculateExpirationDate(createdDate);
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(createdDate)
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }


    public Map<String, Object> getKeyAndExp(String token) {
        Map<String, Object> map = new HashMap<>(16);
        String base64UrlEncodedPayload = token.split("\\.")[1];
        String payload = TextCodec.BASE64URL.decodeToString(base64UrlEncodedPayload);
        if (payload.charAt(0) == '{' && payload.charAt(payload.length() - 1) == '}') {
            JSONObject object = JSONObject.parseObject(payload);
            Object sub = object.get("sub");
            Object exp = object.get("exp");
            Object iat = object.get("iat");
            map.put("key", sub);
            map.put("expirationTime", ((Number) exp).longValue());
            map.put("loginTime", ((Number) iat).longValue());

        }
        return map;
    }

    /**
     * 登录信息交给Security管理
     *
     * @param tokenBo tokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public void setSecurityContext(TokenBo tokenBo) {
        if (tokenBo != null && SecurityContextHolder.getContext().getAuthentication() == null) {

            UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(tokenBo, null, new ArrayList<>());

            SecurityContextHolder.getContext().setAuthentication(auth);
        }
    }

    /**
     * 获取Token
     *
     * @param tokenKey tokenKey
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public TokenBo getSecurityRegister(String tokenKey) {
        return redisService.getToken(tokenKey);
    }


    public boolean getLoginOne() {
        return loginOne;
    }

    /**
     * 更新Token
     *
     * @param tokenBo tokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public void updateTokenBo(TokenBo tokenBo) {

        redisService.putToken(tokenBo.getTokenKey(), tokenBo);
    }


    public String getKeyFromToken(String token) {
        //TODO 可以加盐、加密等
//        return TOKEN_PREFIX + userId;
        //return TOKEN_PREFIX+ AESEncrypt.aes_encrypt(userId, SystemConstants.SYS_STR_KEY);
        return getClaimFromToken(token, Claims::getSubject);
    }

    public void deleteToken(String tokenKey) {
        redisService.clearRedisCache(null, "diagnosis-mobile-token::" + tokenKey);
    }

//    public   String getKeyFromToken(String tokenKey) {
//        if (!tokenKey.startsWith(TOKEN_PREFIX)) {
//            throw new RuntimeException("token编码规则错误！");
//        }
////        return tokenKey.substring(TOKEN_PREFIX.length());
//        return AESEncrypt.aes_decrypt(tokenKey.substring(TOKEN_PREFIX.length()),SystemConstants.SYS_STR_KEY);
//    }


}