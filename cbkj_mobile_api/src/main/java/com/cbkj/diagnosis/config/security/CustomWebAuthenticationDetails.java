package com.cbkj.diagnosis.config.security;

import org.springframework.security.web.authentication.WebAuthenticationDetails;

import javax.servlet.http.HttpServletRequest;

/**
 * 自定义WebAuthenticationDetails 获取额外参数
 */
public class CustomWebAuthenticationDetails extends WebAuthenticationDetails {

    private final String roleName;

    public CustomWebAuthenticationDetails(HttpServletRequest request) {
        super(request);
        roleName = request.getParameter("roleName");
    }

    public String getRoleName() {
        return roleName;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(super.toString()).append("; roleID: ").append(this.getRoleName());
        return sb.toString();
    }
}