//package com.cbkj.diagnosis.config;
//
//import com.cbkj.diagnosis.beans.TAdminInfo;
//import com.cbkj.diagnosis.utils.SessionUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import org.springframework.web.servlet.HandlerInterceptor;
//import org.springframework.web.servlet.ModelAndView;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
///*
// * 拦截器
// */
//@Component
//@Slf4j
//public class MyInterceptor implements HandlerInterceptor {
//
//    //controller前执行
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
//            throws Exception {
//        StringBuffer requestURL = request.getRequestURL();
//        TAdminInfo user = (TAdminInfo) SessionUtils.getSessionAttribute("user");
//        if (user == null) {
//            response.sendRedirect(request.getContextPath() + "/mobile/toLogin");
//            return false;
//        }else{
//            log.info("user=============="+user.toString());
//        }
//        return true;
//    }
//
//    //controller方法后执行
//    @Override
//    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
//                           ModelAndView modelAndView) throws Exception {
//        // TODO Auto-generated method stub
//        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
//    }
//
//    //视图页面渲染后执行
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
//            throws Exception {
//        // TODO Auto-generated method stub
//        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
//    }
//}