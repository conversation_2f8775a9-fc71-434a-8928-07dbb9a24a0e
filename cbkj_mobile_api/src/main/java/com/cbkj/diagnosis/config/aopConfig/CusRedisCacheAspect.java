package com.cbkj.diagnosis.config.aopConfig;


import com.cbkj.diagnosis.config.annotaionUtil.CusRedisCache;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
@Slf4j
public class CusRedisCacheAspect {

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${redisKey.perfix}")
    private String keyPefix;

    @Value("${cus.redis.start-up}")
    private boolean startUp;


    @Around("@annotation(cusRedisCache)")
    public Object WriteReadFromRedis(ProceedingJoinPoint point, CusRedisCache cusRedisCache){

        Object obj = null;

        if(startUp){

            ValueOperations valueOperations = redisTemplate.opsForValue();

            StringBuffer key = new StringBuffer(keyPefix);

            String value = cusRedisCache.value();

            String cacheKey = cusRedisCache.key();

            String keyStr = null;

            if(StringUtils.isNotBlank(cacheKey)){

                if(cacheKey.startsWith("#")){

                    cacheKey = cacheKey.substring(1,cacheKey.length());

                    String[] cacheKeyArray = cacheKey.split("\\.");

                    Object[] args = point.getArgs();

                    Signature signature = point.getSignature();

                    MethodSignature methodSignature = (MethodSignature) signature;

                    String[] parameterNames = methodSignature.getParameterNames();

                    for (int i =0 ,len = parameterNames.length;i < len ;i++){

                        String name = parameterNames[i];

                        if(cacheKeyArray.length > 1){

                            if(name.equals(cacheKeyArray[0])){
                                keyStr = key.append(":").append(getFieldValueByObject(args[i],cacheKeyArray[1])).toString();
                            }

                        }else{

                            if(name.equals(cacheKey)){
                                keyStr = key.append(":").append(args[i]).toString();
                            }
                        }
                    }
                }else{
                    keyStr = key.append(":").append(cacheKey).toString();
                }
            }else{

                String className = point.getSignature().getDeclaringTypeName();
                className = (className.substring(className.lastIndexOf(".")+1));
                String methodName =  point.getSignature().getName();
                keyStr = className+methodName;

            }

            if(StringUtils.isNotBlank(value)){
                keyStr += value;
            }

            if(cusRedisCache.clear() && redisTemplate.hasKey(keyStr)){
                redisTemplate.delete(keyStr);
            }
            try{
                obj = valueOperations.get(keyStr);
            }catch (Exception e){
                log.error("redis 异常:{}",e);
            }
            if(null == obj){

                try {

                    obj = point.proceed();

                } catch (Throwable throwable) {

                    log.error("redis 业务层回去数据异常：{}",throwable);
                }
                if(null != obj && !cusRedisCache.clear()) {//不等于null 存入redis

                    if(cusRedisCache.expired() > 0) {

                        valueOperations.set(keyStr, obj, cusRedisCache.expired(), TimeUnit.SECONDS);

                    }else {

                        valueOperations.set(keyStr, obj);
                    }
                }
            }
        }else{

            try{

                obj = point.proceed();

            } catch (Throwable throwable) {

                log.error("redis 业务层回去数据异常：{}",throwable);
            }

        }
        return obj;
    }


    @SneakyThrows
    public  Object getFieldValueByObject (Object object , String targetFieldName) {
        // 获取该对象的Class
        Class objClass = object.getClass();
        Field[] fields = objClass.getDeclaredFields();

        for (Field field:fields) {
            // 属性名称
            field.setAccessible(true);
            String currentFieldName;
            // 获取属性上面的注解 import com.fasterxml.jackson.annotation.JsonProperty;
            boolean has_JsonProperty =  field.isAnnotationPresent(JsonProperty.class);
            if(has_JsonProperty){
                currentFieldName = field.getAnnotation(JsonProperty.class).value();
            }else {
                currentFieldName = field.getName();
            }
            if(currentFieldName.equals(targetFieldName)){
                return field.get(object);
            }
        }
        return null;
    }

}
