<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>cbkj_v5</artifactId>
        <groupId>com.cbkj.diagnosis</groupId>
        <version>1.0</version>
    </parent>


    <version>1.0.0</version>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cbkj_web_diagnosis</artifactId>
    <packaging>jar</packaging>

<dependencies>


    <dependency>
        <groupId>cbkj_v5</groupId>
        <artifactId>cbkj_core_diagnosis</artifactId>
        <version>1.0</version>
    </dependency>

    <dependency>
        <groupId>org.mybatis.generator</groupId>
        <artifactId>mybatis-generator-core</artifactId>
        <version>1.3.5</version>
    </dependency>
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>4.0.1</version>
    </dependency>

    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>4.0.1</version>
    </dependency>

    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml-schemas</artifactId>
        <version>4.0.1</version>
    </dependency>
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-scratchpad</artifactId>
        <version>3.14</version>
    </dependency>
    <!-- Spring Web Services -->
    <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-core</artifactId>
        <version>3.0.10.RELEASE</version>
    </dependency>
    <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-core</artifactId>
    </dependency>
    <!--    <dependency>-->
<!--        <groupId>org.flywaydb</groupId>-->
<!--        <artifactId>flyway-core</artifactId>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--        <groupId>com.github.xiaoymin</groupId>-->
<!--        <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>-->
<!--        <version>4.5.0</version>-->
<!--    </dependency>-->
    <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>32.1.3-jre</version>
        <scope>compile</scope>
    </dependency>
    <!-- OpenFeign -->
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-openfeign</artifactId>
        <version>3.1.5</version>
    </dependency>

    <!-- Hystrix -->
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        <version>2.2.10.RELEASE</version>
    </dependency>
<!--    <dependency>-->
<!--        <groupId>io.projectreactor</groupId>-->
<!--        <artifactId>reactor-core</artifactId>-->
<!--        <version>3.5.0</version> &lt;!&ndash; 建议使用最新稳定版 &ndash;&gt;-->
<!--    </dependency>-->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>

</dependencies>

    <build>
        <finalName>diagnosis_web_api</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <!-- 设置跳过测试 -->
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>