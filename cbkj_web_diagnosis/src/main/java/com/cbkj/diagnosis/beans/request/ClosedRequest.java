package com.cbkj.diagnosis.beans.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ClosedRequest.java
 * @Description TODO
 * @createTime 2025年05月07日 14:33:00
 */

@Data
public class ClosedRequest {

    @Schema(description =  "患者id")
    private String patientId;

    @Schema(description =  "诊前诊后诊中宣教主键")
    private String otherId;

    @Schema(description =  "1预诊 ,2就诊 ,3宣教 ,4随访")
    private String recordTypeCode;
}
