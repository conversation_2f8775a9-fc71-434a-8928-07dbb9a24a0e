package com.cbkj.diagnosis.beans.sysBeans;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Schema
public class AdminRuleMenuVo implements Serializable {
    @Schema(description =  "角色ID")
    private String roleId;

//    @Schema(description =  "平台代码")
//    private String modualCode;

//    @Schema(description =  "平台名称")
//    private String modualName;

    @Schema(description =  "菜单数组")
    List<String> menuId;

/*    private List<MenuVo> menu;*/
}
