package com.cbkj.diagnosis.beans.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ClosedStatusRequest.java
 * @Description TODO
 * @createTime 2025年05月08日 14:43:00
 */

@Data
public class ClosedStatusRequest {
    @Schema(description =  "病案状态 0:创建(产集中)，1:结案，2:归档，3:上传成功，4：上传失败 ")
    private String closedStatus;
    @Schema(description =  "档案编号")
    private String closedNo;
}
