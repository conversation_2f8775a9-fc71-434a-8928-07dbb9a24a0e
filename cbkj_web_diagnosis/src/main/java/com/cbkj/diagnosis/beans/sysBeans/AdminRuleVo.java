package com.cbkj.diagnosis.beans.sysBeans;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Schema
public class AdminRuleVo {

    @Schema(description =  "角色ID")
    private String roleId;

    @Schema(description =  "角色英文名")
    private String roleName;

    @Schema(description =  "角色中文名")
    private String rnameZh;

    @Schema(description =  "备注说明")
    private String roleDesc;
}
