package com.cbkj.diagnosis.beans;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Getter
@Setter
@TableName("medical_records_expenses")
@Schema(description =  "MedicalRecordsExpenses对象")
public class MedicalRecordsExpenses implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="诊疗费用信息id")
    @TableId(value = "expenses_id", type = IdType.AUTO)
    private Long expensesId;

    @Schema(description="就诊记录id")
    @TableField("records_id")
    private String recordsId;

    @Schema(description="中医辨证论治费")
    @TableField("bian_zheng_cost")
    private String bianZhengCost;

    @Schema(description="中医医疗服务项目收费")
    @TableField("t_c_m_service_item_cost")
    private String tCMServiceItemCost;

    @Schema(description="处方中药金额")
    @TableField("t_c_m_cost")
    private String tCMCost;

    @Schema(description="处方中成药金额")
    @TableField("c_p_d_cost")
    private String cPDCost;

    @Schema(description="处方院内制剂金额")
    @TableField("i_p_cost")
    private String iPCost;

    @Schema(description="治疗处置金额")
    @TableField("t_d_cost")
    private String tDCost;


}
