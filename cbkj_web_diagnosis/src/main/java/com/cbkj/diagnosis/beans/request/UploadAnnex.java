package com.cbkj.diagnosis.beans.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/28 17:26
 * @Version 1.0
 */
@Data
@Schema
public class UploadAnnex {
    private MultipartFile file;
    @Schema(description="1系统建议反馈,2系统帮助")
    private String annexType;
}
