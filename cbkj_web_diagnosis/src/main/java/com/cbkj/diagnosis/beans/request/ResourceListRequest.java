package com.cbkj.diagnosis.beans.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Created by zbh on 2024/6/17 16:16
 *
 * @description：资源列表请求体
 */
@Getter
@Setter
public class ResourceListRequest {

    //多个以逗号隔开
    @Schema(description =  "中医疾病id")
    private String chineseDisIds;

//    @Schema(description =  "中医疾病名")
//    private String chineseDisName;

    @Schema(description =  "患者姓别")
    private String patientSex;

    @Schema(description =  "患者最小年龄")
    private String patientMinAge;

    @Schema(description =  "患者最大年龄")
    private String patientMaxAge;

//    @Schema(description =  "医疗机构")
//    private List<String> insNames;

    //多个以逗号隔开
    @Schema(description =  "医疗机构id")
    private String insCodes;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "监测开始日期")
    private String monitorStartDateStr;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "监测结束日期")
    private String monitorEndDateStr;

    @Schema(description =  "姓名或者身份证号")
    private String patientName;

    //多个以逗号隔开
    @Schema(description = "数据范围")
    private String recordTypeCode;

    @Schema(description =  "病案状态 0:创建(产集中)，1:结案，2:归档，3:上传成功，4：上传失败")
    private String closedStatus;

}
