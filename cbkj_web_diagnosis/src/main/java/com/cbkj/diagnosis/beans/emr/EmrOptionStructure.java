package com.cbkj.diagnosis.beans.emr;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/16 11:00
 * @Version 1.0
 */
@Schema
@Data
public class EmrOptionStructure {
    @Schema(description =  "选项id")
    private Integer optionId;

    @Schema(description =  "病历转换术语")
    private String optionStructureValue;
    private String optionName;
    private String optionFillBlank;


    @Schema(description =  "单选、多选有选项填空时候：0未勾病历转化结构化术语 1勾了 病历转化结构化术语")
    private Integer optionStructureSaveBlank;
    @Schema(description =  "单选、多选有选项填空时候：0未勾了保留患者输入 1勾了 保留患者输入")
    private Integer optionStructureSavePatient ;
}
