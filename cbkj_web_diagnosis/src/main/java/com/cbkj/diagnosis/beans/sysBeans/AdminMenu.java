package com.cbkj.diagnosis.beans.sysBeans;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 权限
 */
@Data
@Schema(description =  "菜单")
public class AdminMenu implements Serializable {

    private static final long serialVersionUID = 1654318301580927318L;
    @Schema(description =  "菜单id")
    private String menuId;

    @Schema(description =  "菜单名称")
    private String menuName;

//    @Schema(description =  "过滤路径")
//    private String url;

    @Schema(description =  "请求路径")
    private String menuPath;

    @Schema(hidden = true)
    private String menuClass;

    @Schema(description =  "是否有效（1删除 0有效 2禁用） 默认1")
    private String status;

    @Schema(description =  "上级菜单id")
    private String parentMenuId;

    @Schema(hidden = true)
    private Date createDate;

    @Schema(hidden = true)
    private String createUser;

//    @Schema(hidden = true)
//    private String enabledS;

    @Schema(description="1:菜单 2：按钮")
    private String menuType;

    @Schema(hidden = true)
    private String btnClass;

    @Schema(description="按钮类型")
    private String btnType;

    @Schema(hidden = true)
    private String btnWeight;

    @Schema(description="排序")
    private int sort;
    @Schema(description="菜单层级")
    private int menuLevel;


    /**
     * 业务模块code，用于标记菜单归属业务
     */
    private String modualCode;


    @Schema(description="打开方式（1. 打开内部页面地址 2. 打开外部地址 3. 执行函数方法）")
    private String openType;

    @Schema(description="子菜单")
    private List<AdminMenu> childList;

    @Schema(hidden = true)
    private List<AdminRule> rules;

    @Schema(hidden = true)
    private boolean first = false;

    @Schema(hidden = true)
    private String ids;

}