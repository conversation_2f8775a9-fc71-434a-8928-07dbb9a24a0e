package com.cbkj.diagnosis.beans.sysBeans;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色
 */
@Data
@NoArgsConstructor
@Schema
public class AdminRule implements Serializable {

    private static final long serialVersionUID = -6335654944450560361L;
    @Schema(description =  "角色ID")
    private String roleId;

    @Schema(description =  "角色名")
    private String roleName;

    @Schema(description =  "角色中文名")
    private String rnameZh;

    @Schema(description =  "备注说明")
    private String roleDesc;
//
    @Schema(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
//
    private String createUser;
//
    private String objId;//扩展第三方主键【】
//
    @Schema(description =  "首页地址")
    private String indexUrl;

    @Schema(description =  "搜索条件")
    private String keyWord;

    @Schema(description="搜索数据范围：all：全部。user:用户自己。")
    private String searchData;
}