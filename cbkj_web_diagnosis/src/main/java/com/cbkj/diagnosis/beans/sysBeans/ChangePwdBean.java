package com.cbkj.diagnosis.beans.sysBeans;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * ChangePwd
 */
@Data
@Schema
public class ChangePwdBean {
    private String userId;
    @Schema(description="当前 Authorization 的密码（当 Authorization 的 id 等于 useId 为旧密码）")
    private String authPwd;

    @Schema(description="用户密码")
    private String userPwd;

}
