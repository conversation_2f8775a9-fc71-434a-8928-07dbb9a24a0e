package com.cbkj.diagnosis.beans.sysBeans;

import com.cbkj.diagnosis.beans.business.AdminDisList;
import com.cbkj.diagnosis.common.utils.Constant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;



import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 管理员
 * <AUTHOR>
 */
@Schema
@Data
@NoArgsConstructor
public class AdminInfo implements UserDetails, Serializable {

    private String userId;

    @Schema(description =  "用户登录名字")
    private String userName;

    @Schema(description =  "密码")
    private String password;

    @Schema(description =  "性别")
    private String sex;

    @Schema(description =  "是否有效（1删除 0有效 2禁用 3待审核）默认0")
    private String status;


//    @Schema(description =  "中医资质（1有0无）")
//    private String isQualifier;

    @Schema(description =  "1拼音 2五笔")
    private String isPyWb;

    @Schema(description =  "创建时间")
    private Date createDate;

    @Schema(description =  "创建人id")
    private String createUser;
    @Schema(description =  "创建人姓名")
    private String createUserName;
//    @Schema(description =  "修改时间")
//    private Date updateDate;
    @Schema(description =  "APPID")
    private String appId;
    @Schema(description =  "所属医疗机构代码")
    private String insCode;
    @Schema(description =  "所属医疗机构主键id")
    private String insId;
    @Schema(description =  "所属医疗机构名称")
    private String insName;

    @Schema(description =  "科室id")
    private String deptId;
    private String deptCode;

    @Schema(description =  "科室名称")
    private String deptName;

//    @Schema(description =  "HIS科室ID")
//    private String deptIdHis;
//
//    @Schema(description =  "HIS科室名称")
//    private String deptNameHis;

    @Schema(description =  "最后登录IP")
    private String lastIp;
//    @Schema(description =  "中医资质图片路径(多张图片用英文逗号分隔)")
//    private String qualifierPicPath;

//    @Schema(description =  "协定方分享范围0 私有 1 本科室 2 本医疗机构")
//    private String personalShare;
//
//    @Schema(description =  "病历模板共享权限(0私有 1本科室 2本医疗机构 3医共体 多个英文逗号拼接)")
//    private String templateShare;
//
//    @Schema(description =  "默认处方权限(1内服中药方 2外用中药方 4适宜技术方 多个英文逗号拼接)")
//    private String defaultPre;

     
      
    @Schema(description =  "手机号")
    private String phone;

     
      
    @Schema(description =  "住址")
    private String address;
     
      
    @Schema(description =  "姓名")
    private String nameZh;

    @Schema(description =  "头像")
    private String userHeand;

     
      
    @Schema(description =  "邮箱")
    private String email;
     
      
    @Schema(description =  "身份证号")
    private String certificate;
//    private String rnamess;

    /**
     * 入参格式化
     * 出参格式化
     */
    @Schema(description =  "有效时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireDate;

    @Schema(description =  "创建人")
    private String createUsername;
    public String getCreateUserName(){
        return createUsername;
    }
//    @Schema(description =  "是否试用(1:试用,0:不试用)")
//    private String grayscaleStatus;

//    private List<String> picList;


    private List<AdminRule> roles;
//    @JsonIgnore
//    @Schema(description =  "查询使用字段：角色")
//    private String roleName;
//    @Schema(description =  "查询使用字段：角色")
//    private String roleId;
//    @Schema(description =  "11.执业医师12.执业助理医师13.见习医师21.注册护士22.助产士31.西药师（士）32.中药师（士）41.检验技师（士）42.影像技师（士）69.其他卫生技术人员70.其他技术人员80.管理人员90.工勤及技能人员")
//    private String professional;

    @Schema(description =  "更新人")
    private String updateUser;
    private String updateUserName;
    @Schema(description =  "更新时间")
    private Date updateDate;

//
//    @Schema(description =  "职称证书，多图用,分隔")
//    private String professionalPicPath;

    @Schema(description =  "工号")
    private String employeeId;

    private String sort;
    private String doctorMultipoint;
    @Schema(description =  "用户所属病种")
    List<AdminDisList> userDisMappingList;

    @Schema(description =  "上次修改密码时间")
    private Date lastUpdatePwd;
//    @Schema(description =  "医生签名图片路径")
//    private String doctorSignImgPath;

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp == null ? null : lastIp.trim();
    }

    public void setSex(String sex) {
        this.sex = sex == null ? null : sex.trim();
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public void setUsername(String username) {
        this.userName = username == null ? null : username.trim();
    }
    public void setCreateUserName(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    public void setRoles(List<AdminRule> roles) {
        this.roles = roles;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<GrantedAuthority> authorities = new ArrayList<>();
        if (null != roles && roles.size() > 0) {
            return roles
                    .stream()
                    .map(role -> new SimpleGrantedAuthority("ROLE_" + role.getRoleName()))
                    .collect(Collectors.toList());
        }
//        for (Permission permission : permissions) {
//            authorities.add(new SimpleGrantedAuthority(permission.getName()));
//        }
        return null;

    }

    @Override
    public String getUsername() {
        return this.userName;
    }

    /**
     * 账户是否过期,过期无法验证
     *
     * @return
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否被锁定或者解锁,锁定的用户无法进行身份验证
     *
     * @return
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     *
     * @return
     */
    @JsonIgnore
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }


    @JsonIgnore
    @Override
    public boolean isEnabled() {
        return this.status != null && !Constant.BASIC_STRING_THREE.equals(this.status) && !Constant.BASIC_STRING_TWO.equals(this.status);
    }


}