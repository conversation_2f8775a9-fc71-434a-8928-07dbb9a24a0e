package com.cbkj.diagnosis.mapper.sysmapper;


import com.cbkj.diagnosis.beans.sysBeans.AdminInfoRule;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.beans.sysBeans.SysAdminRuleMenu;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
  *
  * <AUTHOR>
  * @date 2022/4/26 14:07
 **/

public interface AdminRuleMapper {
    /**
      * 删除通过角色id
      * <AUTHOR>
      * @date 2022/4/26 14:07
      * @param rid
      * @return int
     **/
    int deleteByPrimaryKey(String rid);
    /**
      * 插入
      * <AUTHOR>
      * @date 2022/4/26 14:07
      * @param record
      * @return int
     **/
    int insert(AdminRule record);

    /**
     * 插入如果不存在不插入
     * @param record
     * @return
     */
    int insertSelective(AdminRule record);

    /**
     * 获取通过主键
     * @param rid
     * @return
     */
    AdminRule selectByPrimaryKey(String rid);

    /**
     * 更新
     * @param record
     * @return int
     */
    int updateByPrimaryKeySelective(AdminRule record);
    /**
      * 更新
      * <AUTHOR>
      * @date 2022/4/26 14:08
      * @param record
      * @return int
     **/
    int updateByPrimaryKey(AdminRule record);

    /**
     * 分页
     * @param adminRule
     * @return
     */
    List<Map<String,Object>> getPageDatas(AdminRule adminRule);

    /**
     * 获取用户角色数量
     * @param ids
     * @return
     */
    long getRuleRelesCount(String ids);

    /**
     * 删除角色下的配置用户
     * @param ids
     * @return
     */
    long deleteRuleMenuByRid(String ids);

    /**
     * 删除角色下配置的菜单
     * @param ids
     * @return
     */
    long deleteRuleMenuByRids(String ids);

    /**
     * 获取角色简单信息
     * @param adminRule
     * @return
     */
    List<AdminRule> getRepeatNameS(AdminRule adminRule);

    /**
     * 查询除了本角色之外的角色名称
     * @param
     * @return
     */
    List<AdminRule> getRepeatNameById(String roleId);
    /**
     * 获取所有角色
     * @return
     */
    List<AdminRule> getAllAdminRulesByKeyWord(String keyWord);

    /**
     * 获取角色下所有菜单
     * @return
     */
    List<SysAdminRuleMenu> getAllAdminRuleMenuByRoleId(String roleId);

   long insertSysAdminRuleMenuList(List<SysAdminRuleMenu> sysAdminRuleMenus);

    AdminRule getAdminRuleById(String roleId);

    void insertSysAdminRule(AdminInfoRule adminInfoRule);

    void deleteUserRoleByUserId(String userId);

    List<AdminRule> getAdminRuleInfo(String userId);
}