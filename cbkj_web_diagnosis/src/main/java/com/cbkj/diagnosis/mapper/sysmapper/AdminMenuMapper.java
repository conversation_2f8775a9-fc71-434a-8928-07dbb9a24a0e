package com.cbkj.diagnosis.mapper.sysmapper;


import com.cbkj.diagnosis.beans.sysBeans.AdminMenu;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public interface AdminMenuMapper {

    /**
     * 获取所有菜单
     *
     * @return java.util.List<com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu>
     * <AUTHOR>
     * @date 2022/4/26 13:56
     **/
    List<AdminMenu> getAllMenu();

    /**
     * 获取菜单 根据父菜单id
     *
     * @param [params]
     * @return java.util.List<com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu>
     * <AUTHOR>
     * @date 2022/4/26 13:56
     **/
    List<AdminMenu> getMenuByPID(Map<String, Object> params);

    /**
     * 通过userId 获取该用户所有权限名称 ,拼接
     * @param userId
     * @return
     */
    String getRolesNamesByUserId(String userId);
    /**
     * 获取菜单信息
     *
     * @param adminMenu
     * @return List<Map < String, Object>>
     */
    List<Map<String, Object>> getPageDatas(AdminMenu adminMenu);

    /**
     * 查询单个
     *
     * @param mid
     * @return com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu
     * <AUTHOR>
     * @date 2022/4/26 13:55
     **/
    AdminMenu selectByPrimaryKey(String mid);

    long insert(AdminMenu adminMenu);

    /**
     * 删除角色映射
     *
     * @param split
     * @return
     */
    long deleteRMbyMid(String[] split);

    /**
     * 删除
     *
     * @param split
     * @return
     */
    long deleteBylis(String[] split);

    /**
     * 通过菜单id获取菜单对象
     *
     * @param id
     * @return java.util.List<com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu>
     * <AUTHOR>
     * @date 2022/4/26 14:03
     **/
    List<AdminMenu> getMenuObjByMID(String id);

    /**
     * 获取菜单通过角色id
     *
     * @param id
     * @return java.util.List<com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu>
     * <AUTHOR>
     * @date 2022/4/26 14:04
     **/
    List<AdminMenu> getMenuObjByRID(String id);

    /**
     * 获取所有菜单信息
     *
     * @return
     */
    List<AdminMenu> getAllMenuList();

    /**
     * 更新菜单sort
     *
     * @param list
     * @return
     */
    int updateSortNumberByList(List<AdminMenu> list);

    /**
     * 更新
     *
     * @param record
     * @return int
     * <AUTHOR>
     **/
    int updateByPrimaryKey(AdminMenu record);

    /**
     * 更新状态
     *
     * @param params
     * @return long
     * <AUTHOR>
     * @date 2022/4/26 14:01
     **/
    long updateEnabled(Map<String, Object> params);

    /**
     * 删除角色菜单映射通过角色id
     *
     * @param rid
     * @return long
     * <AUTHOR>
     * @date 2023/09/15 13:59
     **/
    long deleteRmByRid(String rid);

    /**
     * 插入角色菜单
     *
     * @param resultList
     * @return
     */
    long insertListM(List<Map<String, Object>> resultList);
}