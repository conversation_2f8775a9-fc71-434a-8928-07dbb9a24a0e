package com.cbkj.diagnosis.mapper.sysmapper;


import com.cbkj.diagnosis.beans.business.AdminDisList;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.service.vo.QueryContent;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */

public interface AdminInfoMapper {
    /**
     * 获取列表根据id
     *
     * @param id id
     * @return AdminInfo
     */
    AdminInfo selectByPrimaryKey(String id);

    /**
     * 加载用户信息
     *
     * @param username 用户名
     * @return AdminInfo
     */
    AdminInfo loadUserByUsername(String username);

    /**
     * 获取用户信息列表
     * @param queryContent
     * @return
     */
    List<AdminInfo> getAdminList(QueryContent queryContent);
    List<AdminDisList> getAdminDisList(String id);

    List<AdminRule> getRoleListPage(AdminRule adminRule);

    void deleteAdminInfoRulebyRuleId(String roleId);

    void updateRoleInfo(AdminRule adminRule);

    AdminInfo getUserById(String userId);

    void updateUserInfo(AdminInfo adminInfo);

    void insertUserInfo(AdminInfo adminInfo);

    List<Map<String, Object>> validateParam(Map<String, Object> param);

    long updatePwd(Map<String, Object> params);

    AdminInfo getUserByEmployeeId(AdminInfo employeeId);

    List<AdminInfo> getDiagnosisUserList(ArrayList<String> strings);

    List<AdminInfo> getSuiFangUserList(String doctorName);

    int countAdminList(QueryContent content);

    void insertRule(AdminRule adminRule);
}