package com.cbkj.diagnosis.service.statistics.impl;

import com.cbkj.diagnosis.beans.statistics.BlockFourDetail;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayAdverseService;
import com.cbkj.diagnosis.service.statistics.factory.StatisticsStrategy;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 不良反应
 * @Date 2024/1/29 11:30
 * @Version 1.0
 */
@Service("block-index-blfy")
public class BlockFourService implements StatisticsStrategy {


    private StatisticsErverDayAdverseService statisticsErverDayAdverseService;

    public BlockFourService(StatisticsErverDayAdverseService statisticsErverDayAdverseService) {
        this.statisticsErverDayAdverseService = statisticsErverDayAdverseService;
    }

    @Override
    public ResEntity getData(StatisticsVo statisticsVo) {
        List<BlockFourDetail> list = statisticsErverDayAdverseService.getBlockFourList(statisticsVo);
        String[] names = {"不良反应（人次）"};
        List<BlockFourDetail>[] lists = new List[]{list};

        HashMap<String, Object> map = new HashMap<>(32);
        map.put("lists",lists);
        map.put("names",names);
        return ResEntity.success(map);
    }
}
