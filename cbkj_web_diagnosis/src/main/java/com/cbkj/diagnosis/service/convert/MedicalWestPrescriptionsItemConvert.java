package com.cbkj.diagnosis.service.convert;


import com.cbkj.diagnosis.beans.monitor.dto.MedicalWestPrescriptionsItemDTO;
import com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem;

/**
 * Created by zbh on 2024/6/6 17:09
 *
 * @description：西药处方明细转换器
 */
public class MedicalWestPrescriptionsItemConvert {

    public static MedicalWestPrescriptionsItemDTO medicalWestPrescriptionsItemConvertToDTO(MedicalWestPrescriptionsItem response) {
        if(null == response){
            return null;
        }
        MedicalWestPrescriptionsItemDTO dto = new MedicalWestPrescriptionsItemDTO();
        dto.setDrugName(response.getDrugName());
        dto.setDrugSpecifications(response.getDrugSpecifications());
        dto.setUsage(response.getDrugDose()+response.getDrugDoseUnit());
        dto.setDrugFrequency(response.getDrugFrequency());
        dto.setAdministrationRoute(response.getAdministrationRoute());
        return dto;
    }
}
