package com.cbkj.diagnosis.service.monitor.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.business.TDiseaseNo;
import com.cbkj.diagnosis.beans.monitor.vo.GetPatientInfo;
import com.cbkj.diagnosis.beans.monitor.vo.ResourceListVO;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.monitor.dto.*;
import com.cbkj.diagnosis.beans.monitor.vo.DataSourceList;
import com.cbkj.diagnosis.beans.request.ClosedRequest;
import com.cbkj.diagnosis.beans.request.ClosedStatusRequest;
import com.cbkj.diagnosis.beans.request.ResourceListRequest;
import com.cbkj.diagnosis.common.enums.RecordTypeEnum;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsMappingMapper;
import com.cbkj.diagnosis.mapper.business.TRecordMapper;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.convert.MedicalRecordsConvert;
import com.cbkj.diagnosis.service.monitor.IMedicalRecordsService;
import com.cbkj.diagnosis.service.monitor.MedicalBasicHandleRecordService;
import com.cbkj.diagnosis.service.monitor.MedicalPatientRecordCostService;
import com.cbkj.diagnosis.service.monitor.MedicalRecordsPrescriptionsService;
import com.cbkj.diagnosis.utils.Constant;
import com.cbkj.diagnosis.utils.DesensitizeUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 就诊记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@Slf4j
@Service
public class MedicalRecordsServiceImpl extends ServiceImpl<MedicalRecordsMapper, MedicalRecords> implements IMedicalRecordsService {


    @Autowired
    private MedicalRecordsMapper medicalRecordsMapper;

    @Autowired
    private MedicalRecordsPrescriptionsService medicalRecordsPrescriptionsService;

    @Autowired
    private MedicalBasicHandleRecordService medicalBasicHandleRecordService;

    @Autowired
    private MedicalPatientRecordCostService medicalPatientRecordCostService;

    @Autowired
    private TRecordMapper tRecordMapper;

    @Autowired
    private SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    /**
     * 获取预诊随访明细数据信息
     *
     * @param recordId
     * @return
     */
    public FileDetailsConsultationDTO detail(String recordId) {

        MedicalRecords medicalRecords = medicalRecordsMapper.getMedicalRecordByRecordId(recordId);

        MedicalRecordsDTO medicalRecordsDTO = MedicalRecordsConvert.medicalRecordsConvertToDTO(medicalRecords);

        PrescriptionInfoDTO prescriptionInfoDTO = medicalRecordsPrescriptionsService.getMedicalRecordsPrescriptionsByRecordId(recordId);

        DisposalRecordDTO disposalRecordDTO = medicalBasicHandleRecordService.getMedicalBasicHandleRecordByRecordId(recordId);

        ManifestFeeDTO manifestFeeDTO = medicalPatientRecordCostService.getRecordCostByRecordId(recordId);


        FileDetailsConsultationDTO fileDetailsConsultationDTO = new FileDetailsConsultationDTO();
        fileDetailsConsultationDTO.setMedicalRecordsDTO(medicalRecordsDTO);
        fileDetailsConsultationDTO.setPrescriptionInfoDTO(prescriptionInfoDTO);
        fileDetailsConsultationDTO.setDisposalRecordDTO(disposalRecordDTO);
        fileDetailsConsultationDTO.setManifestFeeDTO(manifestFeeDTO);

        return fileDetailsConsultationDTO;
    }


    public List<DataSourceList> getDataSourceList(ResourceListRequest request) {
        MedicalRecordsMapper medicalRecordsMapper = baseMapper;

        //处理入参
        ResourceListVO vo = MedicalRecordsConvert.resourceListRequestConvertToVO(request);

        List<DataSourceList> dataSourceLists = medicalRecordsMapper.getDataSourceList(vo);
        List<DataSourceList> collect = dataSourceLists.stream().map(i -> {
            String name = DesensitizeUtils.nameDesensitize(i.getPatientName());
            i.setPatientName(name);
            return i;
        }).collect(Collectors.toList());
        return collect;
    }

    public Object getCaseSelectionList(ResourceListRequest request, Page page) {
        MedicalRecordsMapper medicalRecordsMapper = baseMapper;

        //处理入参
        ResourceListVO vo = MedicalRecordsConvert.resourceListRequestConvertToVO(request);

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<DataSourceList> dataSourceLists = medicalRecordsMapper.getCaseSelectionList(vo);
        PageInfo<DataSourceList> pageInfo = new PageInfo<>(dataSourceLists);

        List<DataSourceList> collect = dataSourceLists.stream().map(i -> {
            String name = DesensitizeUtils.nameDesensitize(i.getPatientName());
            i.setPatientName(name);
            return i;
        }).collect(Collectors.toList());

        PageInfo<DataSourceList> page2 = new PageInfo<>(collect);
        BeanUtils.copyProperties(pageInfo, page2);
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("status", true);
        result.put("message", com.cbkj.diagnosis.common.utils.Constant.SUCCESS);
        result.put("isHasNextPage", page2.isHasNextPage());
        result.put("count", page2.getTotal());
        result.put("data", collect);
        return result;
    }

    public Object getCaseClosedList(ResourceListRequest request, Page page) {
        MedicalRecordsMapper medicalRecordsMapper = baseMapper;

        //处理入参
        ResourceListVO vo = MedicalRecordsConvert.resourceListRequestConvertToVO(request);

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<DataSourceList> dataSourceLists = medicalRecordsMapper.getCaseClosedList(vo);
        PageInfo<DataSourceList> pageInfo = new PageInfo<>(dataSourceLists);

        List<DataSourceList> collect = dataSourceLists.stream().map(i -> {
            String name = DesensitizeUtils.nameDesensitize(i.getPatientName());
            i.setPatientName(name);
            return i;
        }).collect(Collectors.toList());

        PageInfo<DataSourceList> page2 = new PageInfo<>(collect);
        BeanUtils.copyProperties(pageInfo, page2);
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("status", true);
        result.put("message", com.cbkj.diagnosis.common.utils.Constant.SUCCESS);
        result.put("isHasNextPage", page2.isHasNextPage());
        result.put("count", page2.getTotal());
        result.put("data", collect);
        return result;
    }

    @Transactional
    public Object setClosed(List<ClosedRequest> requests) {

        if (requests.size() > 0) {

            String closedNo = "";
            if (StringUtils.isNotEmpty(requests.get(0).getOtherId())) {
                TDiseaseNo tDiseaseNo = new TDiseaseNo();
                for (ClosedRequest request : requests) {
                    if ("1".equals(request.getRecordTypeCode())) {
                        tDiseaseNo = medicalRecordsMapper.getcloseNo("", request.getOtherId());
                    }

                    if ("4".equals(request.getRecordTypeCode()) && StringUtils.isEmpty(closedNo)) {
                        tDiseaseNo = medicalRecordsMapper.getcloseNo("", request.getOtherId());
                    }
                }
                if (null != tDiseaseNo.getDisNo()) {
                    closedNo = tDiseaseNo.getInsName() + "-" + tDiseaseNo.getDisName() + "-" + String.format("%04d", tDiseaseNo.getDisNo());
                    medicalRecordsMapper.updateTDiseaseNo(tDiseaseNo);
                } else {
                    return RespDTO.onError("当前专病全流程数据缺失，请检查预诊或随访数据！");
                }

            }

            for (ClosedRequest request : requests) {

                if (StringUtils.isNotEmpty(request.getPatientId())) {
                    TDiseaseNo tDiseaseNo = medicalRecordsMapper.getcloseNo(request.getPatientId(), "");
                    if (null != tDiseaseNo.getDisNo()) {
                        closedNo = tDiseaseNo.getInsName() + "-" + tDiseaseNo.getDisName() + "-" + String.format("%04d", tDiseaseNo.getDisNo());
                        medicalRecordsMapper.updateClosedByPatientId(request.getPatientId(), closedNo);
                        tRecordMapper.updateClosedByPatientId(request.getPatientId(), closedNo);
                        sRoadTaskPatientsMapper.updateClosedByPatientId(request.getPatientId(), closedNo);

                        medicalRecordsMapper.updateTDiseaseNo(tDiseaseNo);
                    } else {
                        return RespDTO.onError("当前专病全流程数据缺失，请检查预诊或随访数据！");
                    }


                } else if (StringUtils.isNotEmpty(request.getOtherId()) && StringUtils.isNotEmpty(request.getRecordTypeCode())) {

                    if ("1".equals(request.getRecordTypeCode()) || "4".equals(request.getRecordTypeCode())) {
                        tRecordMapper.updateClosedById(request.getOtherId(), closedNo);
                    }
                    if ("2".equals(request.getRecordTypeCode())) {
                        medicalRecordsMapper.updateClosedById(request.getOtherId(), closedNo);
                    }
                    if ("3".equals(request.getRecordTypeCode())) {
                        sRoadTaskPatientsMapper.updateClosedById(request.getOtherId(), closedNo);
                    }

                }


            }
        }
        return RespDTO.onSuc("成功");
    }

    @Transactional
    public Object setClosedStatus(List<ClosedStatusRequest> requests) {

        if (requests.size() > 0) {
            for (ClosedStatusRequest request : requests) {
                if (StringUtils.isNotEmpty(request.getClosedNo()) && StringUtils.isNotEmpty(request.getClosedStatus())) {
                    medicalRecordsMapper.updateClosedByclosdNo(request.getClosedNo(), request.getClosedStatus());
                    tRecordMapper.updateClosedByclosdNo(request.getClosedNo(), request.getClosedStatus());
                    sRoadTaskPatientsMapper.updateClosedByclosdNo(request.getClosedNo(), request.getClosedStatus());
                }
            }
        }
        return "成功";
    }

    @Override
    public List<RecordInfoDTO> getRecordListByCreateDate(GetPatientInfo getPatientInfo) {
        LinkedList<RecordInfoDTO> list = medicalRecordsMapper.getRecordListByCreateDate(getPatientInfo);
        if (!CollectionUtils.isEmpty(list)) {
            int a = 1;
            int b = 1;
            int c = 1;
            int d = 1;
            for (int i = 0; i < list.size(); i++) {
                String massage = RecordTypeEnum.getMassage(list.get(i).getRecordTypeCode());

                StringBuffer name = new StringBuffer();
                if (massage.equals(Constant.PREDIAGNOSIS)) {
                    name.append(Constant.PREDIAGNOSIS).append(a++);
                } else if (massage.equals(Constant.VISIT_CLINIC)) {
                    name.append(Constant.VISIT_CLINIC).append(b++);
                } else if (massage.equals(Constant.PROPAGANDA_EDUCATION)) {
                    name.append(Constant.PROPAGANDA_EDUCATION).append(c++);
                } else if (massage.equals(Constant.FOLLOW_UP)) {
                    name.append(Constant.FOLLOW_UP).append(d++);
                }

                list.get(i).setRecordType(name.toString());
            }

        }
        if (!CollectionUtils.isEmpty(list)) {
            int a = 1;
            int b = 1;
            int c = 1;
            int d = 1;
            for (int i = 0; i < list.size(); i++) {
                String massage = RecordTypeEnum.getMassage(list.get(i).getRecordTypeCode());
                String closedStatus = list.get(i).getClosedStatus();

                StringBuffer name = new StringBuffer();
                if (massage.equals(Constant.PREDIAGNOSIS) && !"0".equals(closedStatus)) {
                    name.append(Constant.PREDIAGNOSIS).append(a++);
                } else if (massage.equals(Constant.VISIT_CLINIC) && !"0".equals(closedStatus)) {
                    name.append(Constant.VISIT_CLINIC).append(b++);
                } else if (massage.equals(Constant.PROPAGANDA_EDUCATION) && !"0".equals(closedStatus)) {
                    name.append(Constant.PROPAGANDA_EDUCATION).append(c++);
                } else if (massage.equals(Constant.FOLLOW_UP) && !"0".equals(closedStatus)) {
                    name.append(Constant.FOLLOW_UP).append(d++);
                }

                list.get(i).setRecordType2(name.toString());
            }

        }
        return list;
    }


}
