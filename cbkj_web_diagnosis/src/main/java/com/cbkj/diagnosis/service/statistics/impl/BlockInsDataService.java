package com.cbkj.diagnosis.service.statistics.impl;//package com.cbkj.diagnosis.service.statistics.impl;
//
//import com.cbkj.diagnosis.sysBeans.ResEntity;
//import com.cbkj.diagnosis.controller.statistics.vo.StatisticsVo;
//import com.cbkj.diagnosis.beans.statistics.StatisticsInstitution;
//import com.cbkj.diagnosis.service.statistics.StatisticsInstitutionService;
//import com.cbkj.diagnosis.service.statistics.factory.StatisticsStrategy;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @Description TODO
// * @Date 2024/1/26 16:17
// * @Version 1.0
// */
//@Service("block-index-ins")
//public class BlockInsDataService implements StatisticsStrategy {
//
//    private StatisticsInstitutionService statisticsInstitutionService;
//
//    public BlockInsDataService(StatisticsInstitutionService statisticsInstitutionService) {
//        this.statisticsInstitutionService = statisticsInstitutionService;
//    }
//
//    @Override
//    public ResEntity getData(StatisticsVo statisticsVo) {
//        List<StatisticsInstitution> list = statisticsInstitutionService.list();
//        return ResEntity.success(list);
//    }
//}
