package com.cbkj.diagnosis.service.monitor.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsPrescriptionsItemMapper;
import com.cbkj.diagnosis.service.monitor.MedicalRecordsPrescriptionsItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Service
public class MedicalRecordsPrescriptionsItemServiceImpl extends ServiceImpl<MedicalRecordsPrescriptionsItemMapper, MedicalRecordsPrescriptionsItem> implements MedicalRecordsPrescriptionsItemService {

    @Autowired
    private MedicalRecordsPrescriptionsItemMapper medicalRecordsPrescriptionsItemMapper;


    @Override
    public List<MedicalRecordsPrescriptionsItem> getMedicalRecordsPrescriptionsItemByPreId(String preId) {
        return medicalRecordsPrescriptionsItemMapper.getItemByPreId(preId);
    }
}
