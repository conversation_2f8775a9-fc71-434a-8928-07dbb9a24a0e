//package com.cbkj.diagnosis.service.statistics;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMapping;
//import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis;
//import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
//import com.cbkj.diagnosis.common.utils.AdminWebUtils;
//import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisDisMappingServiceImpl;
//import com.cbkj.diagnosis.service.statistics.impl.StatisticsErverDayDisServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
///**
// * <AUTHOR> statistics_erver_day_dis
// * @Description TODO
// * @Date 2024/12/30 15:08
// * @Version 1.0
// */
//@Slf4j
//@Service
//public class StatisticsDisWeb {
//    private final StatisticsErverDayDisServiceImpl statisticsErverDayDisService;
//    private final TPreDiagnosisDisMappingServiceImpl tPreDiagnosisDisMappingService;
//
//    public StatisticsDisWeb(StatisticsErverDayDisServiceImpl statisticsErverDayDisService, TPreDiagnosisDisMappingServiceImpl tPreDiagnosisDisMappingService) {
//        this.statisticsErverDayDisService = statisticsErverDayDisService;
//        this.tPreDiagnosisDisMappingService = tPreDiagnosisDisMappingService;
//    }
//
//
//    @Async
//    public void statisticsDis(String recId, String diaId,AdminInfo currentHr) {
//        log.info("监测病种(预诊、随访数量)统计表recId= {}", recId);
////        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
//        QueryWrapper<TPreDiagnosisDisMapping> mappingQueryWrapper = new QueryWrapper<>();
//        mappingQueryWrapper.eq("dia_id", diaId);
//        List<TPreDiagnosisDisMapping> list1 = tPreDiagnosisDisMappingService.list(mappingQueryWrapper);
//        if (!list1.isEmpty()) {
//            ArrayList<StatisticsErverDayDis> statisticsDiagnosisDics = new ArrayList<>();
//            /**
//             * 取出当前问卷的疾病ids
//             */
//            for (int i = 0; i < list1.size(); i++) {
//                StatisticsErverDayDis erverDayDis = new StatisticsErverDayDis();
//                erverDayDis.setAppId(currentHr.getAppId());
//                erverDayDis.setInsCode(currentHr.getInsCode());
//                erverDayDis.setInsId(currentHr.getInsId());
//                erverDayDis.setInsName(currentHr.getInsName());
//                erverDayDis.setDisId(list1.get(i).getDisId());
//                erverDayDis.setDisCode(list1.get(i).getDisCode());
//                erverDayDis.setDisName(list1.get(i).getDisName());
//                erverDayDis.setCreateTime(new Date());
//                statisticsDiagnosisDics.add(erverDayDis);
//            }
//            statisticsErverDayDisService.writeReadFromRedis(statisticsDiagnosisDics, recId, diaId);
//        }
//    }
//}
