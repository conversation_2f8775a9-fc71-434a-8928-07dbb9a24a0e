//package com.cbkj.diagnosis.service.webtask;
//
//import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsMappingMapper;
//import com.cbkj.diagnosis.service.webapi.business.vo.RoadEventTaskSchedulerRes;
//import lombok.extern.log4j.Log4j2;
//import org.springframework.transaction.PlatformTransactionManager;
//import org.springframework.transaction.TransactionDefinition;
//import org.springframework.transaction.TransactionStatus;
//import org.springframework.transaction.support.DefaultTransactionDefinition;
//
//import java.util.List;
//import java.util.concurrent.RecursiveAction;
//
///**
// * 就只执行 2.就诊后3.服药4.服药结束前5.针灸治疗后 中的 2、3、4. 路径2。就诊后 已经在自动入组和手动入组，中加入到任务了。
// * 每个患者对应的任务 都在s_road_task_patients_mapping 进行映射，
// * 多次入组，在s_road_task_patients_mapping的体现依旧是一条，对于一个任务一个患者（同个病历id）来说。
// * <p>
// * 方案：通过 s_road_task_patients_mapping 表 查询  medical_records_prescriptions 表。
// *
// * <AUTHOR>
// */
//@Log4j2
//public class RoadEventTask extends RecursiveAction {
//
//    private PlatformTransactionManager transactionManager;
//    /**
//     * 阈值=5000
//     */
//    private static final int THRESHOLD = 5000;
//
//    private List<RoadEventTaskSchedulerRes> roadEventTaskSchedulerResList;
//    private SRoadTaskPatientsMappingMapper sRoadTaskPatientsMappingMapper;
//
//    public RoadEventTask(List<RoadEventTaskSchedulerRes> roadEventTaskSchedulerResList, SRoadTaskPatientsMappingMapper sRoadTaskPatientsMappingMapper) {
//
//        this.roadEventTaskSchedulerResList = roadEventTaskSchedulerResList;
//        this.sRoadTaskPatientsMappingMapper = sRoadTaskPatientsMappingMapper;
//    }
//
//    public void task() {
//
//    }
//
//    @Override
//    protected void compute() {
//        log.info("--计算");
//        //List<RoadEventTaskSchedulerRes> roadEventTaskSchedulerList = sRoadTaskPatientsMappingMapper.getRoadEventTaskSchedulerList();
//        //log.info("--数量" + roadEventTaskSchedulerList.size());
//        int size = roadEventTaskSchedulerResList.size();
//        DefaultTransactionDefinition defGo = new DefaultTransactionDefinition();
//        defGo.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
//        TransactionStatus statusGo = transactionManager.getTransaction(defGo);
//
//    }
//}
