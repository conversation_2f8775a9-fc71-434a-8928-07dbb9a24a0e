package com.cbkj.diagnosis.service.webtask;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.SRoadTaskEx;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.TAdminInfo2;
import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping;
import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisForm;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.beans.task.*;
import com.cbkj.diagnosis.beans.task.webvo.*;
import com.cbkj.diagnosis.common.http.XiaMenResEntity;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.common.utils.excel.ExcelUtils;
import com.cbkj.diagnosis.controller.webtask.vo.AddPrediagnosis;
import com.cbkj.diagnosis.controller.webtask.vo.ResetPatientTasksStatus;
import com.cbkj.diagnosis.mapper.SRoadTaskExMapper;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsMappingMapper;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsPhoneMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import com.cbkj.diagnosis.mapper.sysmapper.AdminInfoMapper;
import com.cbkj.diagnosis.mapper.task.*;
import com.cbkj.diagnosis.service.his.XiaMenSendWXMessage;
import com.cbkj.diagnosis.service.sysService.RedisService;
import com.cbkj.diagnosis.service.webapi.business.SRoadTaskPatientsOtherService;
import com.cbkj.diagnosis.service.webapi.business.vo.*;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AESPKCS7Util;
import com.cbkj.diagnosis.utils.CardUtil;
import com.cbkj.diagnosis.utils.SystemConstants;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WebTaskService {
    @Value("${sys.aes.key}")
    private String key;
    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;
    private final SRoadMapper sRoadMapper;
    private final SRoadTaskPatientsMappingMapper sRoadTaskPatientsMappingMapper;

    private final SRoadTaskMapper sRoadTaskMapper;

    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;

    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    private final SRoadConditionsMapper sRoadConditionsMapper;

    private final SRoadExecuteMapper sRoadExecuteMapper;

    private final TAdminInfoMapper tAdminInfoMapper;


    private final TPreDiagnosisFormMapper tPreDiagnosisFormMapper;

    @Value("${xia.men.mobile.wx.url}")
    private String url;
    private final RedisService redisService;
    private final SRoadTaskPatientsOtherService sRoadTaskPatientsOtherService;

    private final XiaMenSendWXMessage xiaMenSendWXMessage;

    private final SRoadTaskExMapper sRoadTaskExMapper;

    private final AdminInfoMapper adminInfoMapper;
    ;

    WebTaskService(SRoadMapper sRoadMapper,
                   SRoadTaskPatientsMappingMapper sRoadTaskPatientsMappingMapper, SRoadTaskMapper sRoadTaskMapper,
                   SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper, SRoadTaskPatientsMapper sRoadTaskPatientsMapper, SRoadConditionsMapper sRoadConditionsMapper, SRoadExecuteMapper sRoadExecuteMapper, TAdminInfoMapper tAdminInfoMapper, TPreDiagnosisFormMapper tPreDiagnosisFormMapper, RedisService redisService, SRoadTaskPatientsOtherService sRoadTaskPatientsOtherService, XiaMenSendWXMessage xiaMenSendWXMessage, SRoadTaskExMapper sRoadTaskExMapper, AdminInfoMapper adminInfoMapper) {
        this.sRoadMapper = sRoadMapper;
        this.sRoadTaskPatientsMappingMapper = sRoadTaskPatientsMappingMapper;
        this.sRoadTaskMapper = sRoadTaskMapper;
        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.sRoadConditionsMapper = sRoadConditionsMapper;
        this.sRoadExecuteMapper = sRoadExecuteMapper;
        this.tAdminInfoMapper = tAdminInfoMapper;

        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
        this.redisService = redisService;
        this.sRoadTaskPatientsOtherService = sRoadTaskPatientsOtherService;
        this.xiaMenSendWXMessage = xiaMenSendWXMessage;
        this.sRoadTaskExMapper = sRoadTaskExMapper;
        this.adminInfoMapper = adminInfoMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    public SRoadTask insertOrUpdateTask(InsertOrUpdateTask insertOrUpdateTask, String nameZh, String userId, SRoad sRoad) {
        RoadTaskConditons roadTaskConditons = insertOrUpdateTask.getRoadTaskConditons();

        SRoadTask sRoadTask = new SRoadTask();


        sRoadTask.setSRoadId(insertOrUpdateTask.getSRoadId());
        sRoadTask.setPatientNum(0);
        sRoadTask.setSRoadTaskContent(insertOrUpdateTask.getTaskContent());
        sRoadTask.setSRoadGroupWay(sRoad.getSRoadGroupWay());
        sRoadTask.setStatus("0");
        sRoadTask.setTaskName(insertOrUpdateTask.getTaskName());

        sRoadTask.setCreateTime(new Date());
        if (roadTaskConditons != null) {
            BeanUtils.copyProperties(roadTaskConditons, sRoadTask);
            if (StringUtils.isNotBlank(sRoadTask.getRecordStartTime())) {

                sRoadTask.setRecordStartTime(sRoadTask.getRecordStartTime() + " 00:00:00");
            }
            if (StringUtils.isNotBlank(sRoadTask.getRecordEndTime())) {
                sRoadTask.setRecordEndTime(sRoadTask.getRecordEndTime() + " 23:59:59");
            }
        }

        if (StringUtils.isNotBlank(insertOrUpdateTask.getSRoadTaskId())) {
            sRoadTask.setSRoadTaskId(insertOrUpdateTask.getSRoadTaskId());
            //       sRoadTaskMapper.updateStatusDeleteByPrimaryKey(insertOrUpdateTask.getSRoadTaskId());
            sRoadTaskMapper.updateByPrimaryKey(sRoadTask);
            //        sRoadTask.setSRoadTaskId(IdUtil.getSnowflake(snowflake).nextIdStr());
            //         int insert = sRoadTaskMapper.insert(sRoadTask);
        } else {
            sRoadTask.setCreateUserId(userId);
            sRoadTask.setCreateUserName(nameZh);
            sRoadTask.setCreateTime(new Date());
            sRoadTask.setSRoadTaskId(IdUtil.getSnowflake(snowflake).nextIdStr());
            int insert = sRoadTaskMapper.insert(sRoadTask);
        }

        // 更新患者表join_road_task

        // 更新s_road_task_ex表信息
        int sRoadTaskId = sRoadTaskExMapper.delete(new QueryWrapper<SRoadTaskEx>().eq("s_road_task_id", sRoadTask.getSRoadTaskId()));
        ArrayList<SRoadTaskEx> exArrayList = new ArrayList<>();

        if (null != roadTaskConditons) {
            List<RoadTaskConditonsSQLResult> deptList = roadTaskConditons.getDeptList();
            List<RoadTaskConditonsSQLResult> diagnosisDoctorList = roadTaskConditons.getDiagnosisDoctorList();
            List<RoadTaskConditonsSQLResult> recordDoctorList = roadTaskConditons.getRecordDoctorList();

            deptList.forEach(dept -> {
                SRoadTaskEx sRoadTaskEx = new SRoadTaskEx();
                sRoadTaskEx.setSRoadTaskId(sRoadTask.getSRoadTaskId());
                sRoadTaskEx.setDeptId(dept.getDeptId());
                sRoadTaskEx.setAppId(AdminWebUtils.getCurrentHr().getAppId());
                sRoadTaskEx.setInsCode(AdminWebUtils.getCurrentHr().getInsCode());
                sRoadTaskEx.setExType(0);
                sRoadTaskEx.setDeptName(dept.getDeptName());
                exArrayList.add(sRoadTaskEx);
            });
            diagnosisDoctorList.forEach(diagnosisDoctor -> {
                SRoadTaskEx sRoadTaskEx = new SRoadTaskEx();
                sRoadTaskEx.setSRoadTaskId(sRoadTask.getSRoadTaskId());
                sRoadTaskEx.setDiagnosisDoctorId(diagnosisDoctor.getDiagnosisDoctorId());
                sRoadTaskEx.setAppId(AdminWebUtils.getCurrentHr().getAppId());
                sRoadTaskEx.setInsCode(AdminWebUtils.getCurrentHr().getInsCode());
                sRoadTaskEx.setDiagnosisDoctorName(diagnosisDoctor.getDiagnosisDoctorName());
                sRoadTaskEx.setExType(2);
                exArrayList.add(sRoadTaskEx);
            });

            recordDoctorList.forEach(recordDoctor -> {
                SRoadTaskEx sRoadTaskEx = new SRoadTaskEx();
                sRoadTaskEx.setSRoadTaskId(sRoadTask.getSRoadTaskId());
                sRoadTaskEx.setRecordDoctorId(recordDoctor.getRecordDoctorId());
                sRoadTaskEx.setAppId(AdminWebUtils.getCurrentHr().getAppId());
                sRoadTaskEx.setInsCode(AdminWebUtils.getCurrentHr().getInsCode());
                sRoadTaskEx.setRecordDoctorName(recordDoctor.getRecordDoctorName());
                sRoadTaskEx.setExType(1);
                exArrayList.add(sRoadTaskEx);
            });

            sRoadTaskExMapper.saveBatch(exArrayList);
        }


        return sRoadTask;
    }

    /**
     * 获取随访任务自动入组条件
     *
     * @param sRoadTaskId
     * @return
     */
    public RoadTaskConditons getRoadTaskConditon(String sRoadTaskId) {
        SRoadTask objectById = sRoadTaskMapper.getObjectById(sRoadTaskId);
        RoadTaskConditons roadTaskConditons = new RoadTaskConditons();
        if (objectById == null) {
            return roadTaskConditons;
        }
        BeanUtils.copyProperties(objectById, roadTaskConditons);

        RoadTaskConditonsQuery roadTaskConditonsQuery = new RoadTaskConditonsQuery();
        roadTaskConditonsQuery.setSRoadTaskId(sRoadTaskId);
        roadTaskConditonsQuery.setExType(0);
        List<RoadTaskConditonsSQLResult> roadTaskConditons0 = sRoadTaskMapper.getRoadTaskConditons(roadTaskConditonsQuery);
        roadTaskConditons.setDeptList(roadTaskConditons0);
        roadTaskConditonsQuery.setExType(1);
        List<RoadTaskConditonsSQLResult> roadTaskConditons1 = sRoadTaskMapper.getRoadTaskConditons(roadTaskConditonsQuery);
        roadTaskConditons.setRecordDoctorList(roadTaskConditons1);
        roadTaskConditonsQuery.setExType(2);
        List<RoadTaskConditonsSQLResult> roadTaskConditons2 = sRoadTaskMapper.getRoadTaskConditons(roadTaskConditonsQuery);
        roadTaskConditons.setDiagnosisDoctorList(roadTaskConditons2);

        return roadTaskConditons;
    }

    /**
     * 获取随访任务的 列表
     *
     * @param getPageTaskListRe
     * @param page
     */
    public Object getPageTaskList(GetPageTaskListRe getPageTaskListRe, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();

        HashMap<String, String> params = new HashMap<>();
//        SRoadTaskResponse sRoadTask = new SRoadTaskResponse();
//
//        sRoadTask.setTaskName(getPageTaskListRe.getTaskName());
//        sRoadTask.setSRoadGroupWay(getPageTaskListRe.getSRoadGroupWay());
//        sRoadTask.setCreateUserId(AdminWebUtils.getCurrentHr().getUserId());

        params.put("taskName", getPageTaskListRe.getTaskName());
        params.put("sRoadGroupWay", getPageTaskListRe.getSRoadGroupWay());
        params.put("createUserId", AdminWebUtils.getCurrentHr().getUserId());
//        params.put("diaId", getPageTaskListRe.getDiaId());
        //sRoadTask.setStatus("0");

        List<AdminRule> roles = currentHr.getRoles();

        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
//            sRoadTask.setCreateUserId(null);
            params.put("createUserId", null);
        }

        List<SRoadTaskResponse> list = sRoadTaskMapper.getPageListByObjResponse(params);

//        ArrayList<String> list1 = new ArrayList<>();
//        list.forEach(sRoadTaskPageResponse -> {
//            list1.add(sRoadTaskPageResponse.getSRoadTaskId());
//        });
//        if (list1.size() > 0){
//            //查询ex表
//            List<RoadTaskConditons> a = sRoadTaskMapper.getRoadTaskConditons(list1);
//        }
        PageHelper.clearPage();
        return Page.getLayUiTablePageData(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public Object updateTaskStatus(UpdateTaskStatus updateTaskStatus) {
        SRoadTask objectById = sRoadTaskMapper.getObjectById(updateTaskStatus.getSRoadTaskId());
        if (null == objectById) {
            return ResEntity.error("不存在");
        }
        objectById.setStatus(updateTaskStatus.getStatus());
        if (Constant.BASIC_STRING_ONE.equals(updateTaskStatus.getStatus())) {
            //删除任务,删除没执行的患者任务

            sRoadTaskPatientsMapper.deleteStatusByRoadTaskId(updateTaskStatus.getSRoadTaskId());

        } else if (Constant.BASIC_STRING_TWO.equals(updateTaskStatus.getStatus())) {
            sRoadTaskPatientsMapper.cancelStatusByRoadTaskId(updateTaskStatus.getSRoadTaskId());
        }
        sRoadTaskMapper.updateByPrimaryKey(objectById);
        return ResEntity.success();

    }

    public void deleteTask(String sRoadTaskId) {

    }

    /**
     * 患者管理-列表
     *
     * @param getPatientListVo
     * @param page
     * @return
     */
    public Object getPatientList(GetPatientListVo getPatientListVo, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TAdminInfo> list = null;
        if (StringUtils.isNotBlank(getPatientListVo.getSRoadTaskId())) {
            list = sRoadTaskPatientsMapper.getPatientListTask(getPatientListVo);
        } else {
            list = sRoadTaskPatientsMapper.getPatientList(getPatientListVo);
        }

        PageHelper.clearPage();
        return Page.getLayUiTablePageData(list);
    }


    public Object getPatientFilterList(GetPatientFilterListVo getPatientFilterListVo, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TAdminInfo> list = null;
        if (StringUtils.isNotBlank(getPatientFilterListVo.getDoctorName()) ||
                StringUtils.isNotBlank(getPatientFilterListVo.getDeptName()) ||
                StringUtils.isNotBlank(getPatientFilterListVo.getDiseaseName())
                || getPatientFilterListVo.getStartDate() != null) {
            list = sRoadTaskPatientsMapper.getPatientFilterList(getPatientFilterListVo);

        } else {
            list = sRoadTaskPatientsMapper.getPatientFilterList2(getPatientFilterListVo);
        }

        PageHelper.clearPage();
        return Page.getLayUiTablePageData(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public Object insertPatientFilterAdd(InsertPatientFilterAddVo insertPatientFilterAddVo) {
        String sRoadTaskId = insertPatientFilterAddVo.getSRoadTask().getSRoadTaskId();
        List<TAdminInfo> tAdminInfoList1 = insertPatientFilterAddVo.getTAdminInfoList();

        if (StringUtils.isBlank(sRoadTaskId)) {
            return ResEntity.error("缺少sRoadTask.sRoadTaskId");
        }
        // 检查tAdminInfoList1是否不为null且不为空
        if (tAdminInfoList1 != null && tAdminInfoList1.size() > 0) {
            if (StringUtils.isBlank(tAdminInfoList1.get(0).getRecordsId())) {
                //没有病历id，需要获取这个人所有病历，然后都导入。场景：患者管理，诊前管理，需要对单个病人加入随访，但是诊前是没有病历的，但是能对现有的病历进行加入任务中。
                for (TAdminInfo tAdminInfo : tAdminInfoList1) {
                    SRoadTaskPatientsMapping sRoadTaskPatientsMapping = new SRoadTaskPatientsMapping();
                    sRoadTaskPatientsMapping.setPatientId(tAdminInfo.getUserId());
                    sRoadTaskPatientsMapping.setSRoadTaskId(sRoadTaskId);
                    List<TAdminInfo> tAdminInfoList = tAdminInfoMapper.getOnePatientAllRecords(sRoadTaskPatientsMapping);
                    ResEntity resEntity = addPatientsToTask(sRoadTaskId, tAdminInfoList, false);

                }
                return ResEntity.success("加入任务成功");

            } else {
                return addPatientsToTask(sRoadTaskId, tAdminInfoList1, true);
            }
        } else {
            return ResEntity.error("请勾选患者");
        }


    }


    public ResEntity addPatientsToTask(String sRoadTaskId, List<TAdminInfo> tAdminInfoList1, boolean adminResearch) {

        SRoadTask objectById = sRoadTaskMapper.getObjectById(sRoadTaskId);
        //就取路径事件类型是：就诊后
        UpdateOrInsertRoad updateOrInsertRoad = getRoadEventTwoDetailById(objectById.getSRoadId());
        List<SRoadExecute> sRoadExecuteList = updateOrInsertRoad.getSRoadExecuteList();
        //获取人
        List<TAdminInfo> tAdminInfoList = null;
        if (adminResearch) {
            tAdminInfoList = tAdminInfoMapper.getListByIds(tAdminInfoList1);
            for (int i = 0; i < tAdminInfoList.size(); i++) {
                //把缺失的病历id重新赋值上去
                tAdminInfoList.get(i).setRecordsId(tAdminInfoList1.get(i).getRecordsId());
            }
        } else {
            tAdminInfoList = tAdminInfoList1;
        }


        ArrayList<SRoadTaskPatientsMapping> sRoadTaskPatientsMappings = new ArrayList<>();
        ArrayList<SRoadTaskPatients> sRoadTaskPatients = new ArrayList<>();

        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        for (int i = 0; i < tAdminInfoList.size(); i++) {
            TAdminInfo tAdminInfo = tAdminInfoList.get(i);
            //关联病历去查用户是否已经加入到这个任务了
            SRoadTaskPatientsMapping patientsMapping = new SRoadTaskPatientsMapping();
            patientsMapping.setPatientId(tAdminInfo.getUserId());
            patientsMapping.setSRoadTaskId(sRoadTaskId);
            patientsMapping.setRecordsId(tAdminInfo.getRecordsId());
            if (tAdminInfoMapper.checkPatientJoinThisTask(patientsMapping) > 0) {
                //已经加入过了这个任务，不需要再次添加。
                continue;
            }
            for (SRoadExecute sRoadExecute : sRoadExecuteList) {
                if (sRoadExecute.getRoadExecuteId() == null) {
                    return ResEntity.error("缺少sRoadExecuteList.roadExecuteId");
                }
                SRoadTaskPatients roadPatients = new SRoadTaskPatients();
                BeanUtils.copyProperties(sRoadExecute, roadPatients);
                roadPatients.setRoadExecuteEventTime(sRoadExecute.getRoadExecuteTime());

                List<SRoadExecuteContents> sRoadExecuteContentsList = sRoadExecute.getSRoadExecuteContentsList();
                for (SRoadExecuteContents executeContents : sRoadExecuteContentsList) {
                    BeanUtils.copyProperties(executeContents, roadPatients);

                    roadPatients.setRoadExecuteEventTime(sRoadExecute.getRoadExecuteTime());
                    roadPatients.setPatientId(tAdminInfo.getUserId());
                    roadPatients.setPatientAge(tAdminInfo.getAge());
                    roadPatients.setPatientSex(tAdminInfo.getSex());
                    roadPatients.setPatientName(tAdminInfo.getUserName());
                    roadPatients.setDoctorId(AdminWebUtils.getCurrentHr().getUserId());
                    roadPatients.setDoctorName(AdminWebUtils.getCurrentHr().getNameZh());
                    log.info("筛选导入：患者card" + tAdminInfo.getCardNumber());
                    roadPatients.setPatientCardNumber(tAdminInfo.getCardNumber());
                    //判断下，当前数据就诊后，是否到执行日期了，如果是，就直接设置成2

                    roadPatients.setTaskExcuteStatus(1);
                    roadPatients.setStatus("0");
                    roadPatients.setTaskName(objectById.getTaskName());
                    roadPatients.setSRoadTaskId(objectById.getSRoadTaskId());
                    roadPatients.setHandSend("0");
                    roadPatients.setRecordsId(tAdminInfo.getRecordsId());
                    roadPatients.setAppId(currentHr.getAppId());
                    roadPatients.setInsCode(currentHr.getInsCode());
                    roadPatients.setInsId(currentHr.getInsId());
                    roadPatients.setInsName(currentHr.getInsName());
                    log.info("==========导入病人。就诊记录id=" + tAdminInfo.getRecordsId());
                    try {
                        if (sRoadTaskPatientsOtherService.checkJIuZhenHouTimeIsArrived(roadPatients, 0)) {
                            roadPatients.setTaskExcuteStatus(2);
                        }
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    sRoadTaskPatients.add(roadPatients);


                    sRoadTaskPatientsOtherService.setsroadtaskpatientsmappinglist(sRoadTaskPatientsMappings
                            , roadPatients.getPatientId()
                            , roadPatients.getSRoadTaskId()
                            , roadPatients.getRecordsId()
                    );
                }

            }

        }

        if (sRoadTaskPatients.size() > 0) {
            sRoadTaskPatientsMapper.insertList(sRoadTaskPatients);
            ArrayList<SRoadTaskPatientsPhone> sRoadTaskPatientsPhones = new ArrayList<>();
            //此处都是就诊后，所以病历已经有时间了
            for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                if (sRoadTaskPatients1.getRoadExecuteEventWay().equals(Constant.BASIC_STRING_THREE) &&

                        (sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_TWO) ||
                                sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_FOUR)
                        )
                ) {
                    sRoadTaskPatientsPhones.add(sRoadTaskPatientsOtherService.newSRoadTaskPatientsPhone(sRoadTaskPatients1));
                }
            }
            if (sRoadTaskPatientsPhones.size() > 0) {
                sRoadTaskPatientsPhoneMapper.insertList(sRoadTaskPatientsPhones);
                for (SRoadTaskPatientsPhone sRoadTaskPatients1 : sRoadTaskPatientsPhones) {
                    if (sRoadTaskPatients1.getPhoneStatus() == 1) {
                        redisService.putTaskWait1("2", sRoadTaskPatients1.getSRoadTaskPatientsPhoneId() + "", sRoadTaskPatients1.getSuiFangTime());
                    }

                }
            }

            for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                if (sRoadTaskPatients1.getTaskExcuteStatus() == 1) {
                    redisService.putTaskWait1("1", sRoadTaskPatients1.getTaskPatientsId() + "", sRoadTaskPatients1.getTaskExcuteTime());
                }

            }

        }
        if (sRoadTaskPatientsMappings.size() > 0) {
            sRoadTaskPatientsMappingMapper.insertList(sRoadTaskPatientsMappings);
        }


        return ResEntity.success();
    }


    /**
     * 获取路径详情
     *
     * @param sRoadIid
     * @return
     */
    public UpdateOrInsertRoad getRoadDetailById(String sRoadIid) {
        UpdateOrInsertRoad updateOrInsertRoad = new UpdateOrInsertRoad();

        SRoad sRoad = sRoadMapper.getObjectById(sRoadIid);

//        SRoadConditions roadConditions = new SRoadConditions();
//        roadConditions.setSRoadId(sRoadIid);
//        List<SRoadConditions> conditionsList = sRoadConditionsMapper.getAllListByObj(roadConditions);

        List<SRoadExecute> sRoadExecuteList = sRoadExecuteMapper.selectsRoadExecuteList(sRoadIid);
        updateOrInsertRoad.setSRoad(sRoad);
//        updateOrInsertRoad.setRoadConditionsList(conditionsList);
        updateOrInsertRoad.setSRoadExecuteList(sRoadExecuteList);

        return updateOrInsertRoad;
    }

    public UpdateOrInsertRoad getRoadEventTwoDetailById(String sRoadIid) {
        UpdateOrInsertRoad updateOrInsertRoad = new UpdateOrInsertRoad();
        SRoad sRoad = sRoadMapper.getObjectById(sRoadIid);
        List<SRoadExecute> sRoadExecuteList = sRoadExecuteMapper.getRoadEventTwoDetailById(sRoadIid);
        updateOrInsertRoad.setSRoad(sRoad);
        updateOrInsertRoad.setSRoadExecuteList(sRoadExecuteList);

        return updateOrInsertRoad;
    }

    /**
     * 排期
     *
     * @param searchSchedulingVo
     * @return
     */
    public Object searchScheduling(SearchSchedulingVo searchSchedulingVo, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SRoadTaskPatients> list = sRoadTaskPatientsMapper.searchScheduling(searchSchedulingVo);
        PageHelper.clearPage();
        return Page.getLayUiTablePageData(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public Object setPatientMark(SetPatientMarkVo setPatientMarkVo) {

        tAdminInfoMapper.setPatientMark(setPatientMarkVo);
        return ResEntity.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public Object setPatientTaskDelete(PatientTaskDelete patientTaskDelete) {
        if (StringUtils.isBlank(patientTaskDelete.getSRoadTaskId())) {
            return ResEntity.error("缺少字段");
        }
        Integer taskExcuteStatus = patientTaskDelete.getTaskPatientStatus();
        if (taskExcuteStatus == null) {
            return ResEntity.error("状态值不正确");
        }
        if (taskExcuteStatus != 1 && taskExcuteStatus != 3) {
            return ResEntity.error("状态值不正确");
        }
        if (StringUtils.isBlank(patientTaskDelete.getRecordsId())) {
            return ResEntity.error("缺少患者就诊记录id");
        }
        //

        SRoadTaskPatientsMapping sRoadTaskPatientsMapping = new SRoadTaskPatientsMapping();
        sRoadTaskPatientsMapping.setStatus(patientTaskDelete.getTaskPatientStatus() + "");
        sRoadTaskPatientsMapping.setPatientId(patientTaskDelete.getPatientId());
        sRoadTaskPatientsMapping.setSRoadTaskId(patientTaskDelete.getSRoadTaskId());
        sRoadTaskPatientsMapping.setRecordsId(patientTaskDelete.getRecordsId());
        sRoadTaskPatientsMappingMapper.updateByPrimaryKey(sRoadTaskPatientsMapping);


        if (Constant.BASIC_STRING_ONE.equals((patientTaskDelete.getTaskPatientStatus() + ""))) {
            patientTaskDelete.setTaskPatientStatusPast(3);
            patientTaskDelete.setTaskPatientStatus(1);
            sRoadTaskPatientsMapper.updatePatientTaskStatusByPAndR(patientTaskDelete);
            sRoadTaskPatientsPhoneMapper.updatePatientTaskStatusByPAndR(patientTaskDelete);
        }
        if (Constant.BASIC_STRING_THREE.equals((patientTaskDelete.getTaskPatientStatus() + ""))) {
            patientTaskDelete.setTaskPatientStatusPast(1);
            patientTaskDelete.setTaskPatientStatus(3);
            sRoadTaskPatientsMapper.updatePatientTaskStatusByPAndR(patientTaskDelete);
            sRoadTaskPatientsPhoneMapper.updatePatientTaskStatusByPAndR(patientTaskDelete);
        }

        return ResEntity.success();
    }

    public Object importPatientsTaskByExcel(MultipartFile file, String sroadTaskId) {
        InputStream inputStream = null;
        List<Map<String, Object>> excelList = null;
        try {
            inputStream = file.getInputStream();
            excelList = ExcelUtils.readXlsxFirstSheet(inputStream, 0);
        } catch (IOException e) {
            e.printStackTrace();
            return ResEntity.error("excel IO流错误");
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return ResEntity.success(startImportExcel(excelList, sroadTaskId));
    }

    /**
     * excel导入患者任务 具体实现
     *
     * @param excelList
     * @param sroadTaskId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> startImportExcel(List<Map<String, Object>> excelList, String sroadTaskId) {
        HashMap<String, Object> map = new HashMap<>();
        if (excelList == null) {
            return null;
        }
        ArrayList<SRoadTaskPatients> sRoadTaskPatients = new ArrayList<SRoadTaskPatients>();
        ArrayList<TAdminInfo> needSaveTAdmin = new ArrayList<TAdminInfo>();
        ArrayList<TAdminInfo> notSaveTAdmin = new ArrayList<TAdminInfo>();
        StringBuilder errorCount = new StringBuilder();

        Integer errorCounts = 0;
        //真实总条数
        Integer realCounts = 0;


        SRoadTask objectById = sRoadTaskMapper.getObjectById(sroadTaskId);
        //就取路径事件类型是：就诊后
        UpdateOrInsertRoad updateOrInsertRoad = getRoadEventTwoDetailById(objectById.getSRoadId());
        List<SRoadExecute> sRoadExecuteList = updateOrInsertRoad.getSRoadExecuteList();

        for (int i = 0; i < excelList.size(); i++) {

            if (null == excelList.get(i)) {
                //realCounts = realCounts+1;
                continue;
            }

            Map<String, Object> om = excelList.get(i);
            if (om.size() == 0) {
                continue;
            }
            realCounts = realCounts + 1;
            String patientName = om.get("姓名") == null ? null : om.get("姓名").toString();
            String patientAge = om.get("年龄") == null ? null : om.get("年龄").toString();

            String patientPhone = om.get("手机号") == null ? null : om.get("手机号").toString();
            String patientCardNumber = om.get("身份证号") == null ? null : om.get("身份证号").toString();
            String patientSex = om.get("性别") == null ? null : om.get("性别").toString();
            //新增
            String last_records_time = om.get("末次就诊日期") == null ? null : om.get("末次就诊日期").toString();
            String last_west_dis_name = om.get("西医末次诊断") == null ? null : om.get("西医末次诊断").toString();
            String last_chinese_dis_name = om.get("中医末次诊断") == null ? null : om.get("中医末次诊断").toString();


            String patientMark = om.get("备注") == null ? null : om.get("备注").toString();

            if (!StringUtils.isBlank(patientName) &&
                    !StringUtils.isBlank(patientPhone) &&
                    !StringUtils.isBlank(patientCardNumber) &&
                    !StringUtils.isBlank(patientSex)
            ) {

            } else {
                errorCount.append("第").append(i + 1).append("行导入失败，请检查数据(姓名、手机号、身份证号、性别 不能为空)").append(",");
                errorCounts = errorCounts + 1;
                continue;
            }
            //检查患者是否存在，不存在就插入
            TAdminInfo patientInfo = tAdminInfoMapper.checkPatientEx(new TAdminInfo2(patientCardNumber));
            if (patientInfo == null) {
                TAdminInfo tAdminInfo = new TAdminInfo();
                tAdminInfo.setUserId(IdUtil.getSnowflake(snowflake).nextIdStr());
                tAdminInfo.setUserName(patientName);
                tAdminInfo.setMobile(patientPhone);
                tAdminInfo.setAge(CardUtil.getCarAge(patientCardNumber));
                //tAdminInfo.setSex(CardUtil.getCarSex(patientCardNumber));
                tAdminInfo.setSex("男".equals(patientSex) ? "M" : "F");
                tAdminInfo.setCardNumber(patientCardNumber);
                tAdminInfo.setRemark(patientMark);
                tAdminInfo.setLastChineseDisName(last_chinese_dis_name);
                tAdminInfo.setLastWestDisName(last_west_dis_name);
                if (StringUtils.isNotBlank(last_records_time)) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                    try {
                        tAdminInfo.setLastRecordsTime(simpleDateFormat.parse(last_records_time));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }

                needSaveTAdmin.add(tAdminInfo);
//                patientInfo = tAdminInfo;
                notSaveTAdmin.add(tAdminInfo);
            } else {
                //查询这个患者所有的病历加进去。
                SRoadTaskPatientsMapping sRoadTaskPatientsMapping = new SRoadTaskPatientsMapping();
                sRoadTaskPatientsMapping.setPatientId(patientInfo.getUserId());
                sRoadTaskPatientsMapping.setSRoadTaskId(sroadTaskId);
                List<TAdminInfo> tAdminInfoList = tAdminInfoMapper.getOnePatientAllRecords(sRoadTaskPatientsMapping);
                notSaveTAdmin.addAll(tAdminInfoList);
            }


        }
        ArrayList<SRoadTaskPatientsMapping> sRoadTaskPatientsMappings = new ArrayList<>();
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        for (TAdminInfo tAdminInfo : notSaveTAdmin) {
            for (SRoadExecute sRoadExecute : sRoadExecuteList) {
                SRoadTaskPatients roadPatients = new SRoadTaskPatients();

                //关联病历去查用户是否已经加入到这个任务了--因该不需要在判断，上面的 getOnePatientAllRecords 查询出来的结果集就是没加入到任务。
//                SRoadTaskPatientsMapping patientsMapping = new SRoadTaskPatientsMapping();
//                patientsMapping.setPatientId(tAdminInfo.getUserId());
//                patientsMapping.setSRoadTaskId(sroadTaskId);
//                patientsMapping.setRecordsId(tAdminInfo.getRecordsId());
//                if (tAdminInfoMapper.checkPatientJoinThisTask(patientsMapping) > 0) {
//                    //已经加入过了这个任务，不需要再次添加。
//                    continue;
//                }
                if (!StringUtils.isNotBlank(tAdminInfo.getRecordsId())) {
                    //m没有病历，判断下有没有末次就诊时间
                    if (tAdminInfo.getLastRecordsTime() == null) {
                        continue;
                    }
                }

                BeanUtils.copyProperties(sRoadExecute, roadPatients);
                roadPatients.setRoadExecuteEventTime(sRoadExecute.getRoadExecuteTime());
                List<SRoadExecuteContents> sRoadExecuteContentsList = sRoadExecute.getSRoadExecuteContentsList();
                for (SRoadExecuteContents executeContents : sRoadExecuteContentsList) {
                    if (sRoadExecute.getRoadExecuteId() == null) {
                        throw new RuntimeException("缺少sRoadExecuteList.roadExecuteId");
                    }
                    BeanUtils.copyProperties(executeContents, roadPatients);

                    roadPatients.setPatientId(tAdminInfo.getUserId());
                    roadPatients.setPatientAge(tAdminInfo.getAge());
                    roadPatients.setPatientSex(tAdminInfo.getSex());
                    roadPatients.setPatientName(tAdminInfo.getUserName());
                    roadPatients.setPatientCardNumber(tAdminInfo.getCardNumber());

                    roadPatients.setAppId(currentHr.getAppId());
                    roadPatients.setInsCode(currentHr.getInsCode());
                    roadPatients.setInsId(currentHr.getInsId());
                    roadPatients.setInsName(currentHr.getInsName());

                    roadPatients.setStatus("0");
                    roadPatients.setTaskExcuteStatus(1);
                    roadPatients.setTaskName(objectById.getTaskName());
                    roadPatients.setSRoadTaskId(objectById.getSRoadTaskId());
                    roadPatients.setHandSend("0");
                    try {
                        if (sRoadTaskPatientsOtherService.checkJIuZhenHouTimeIsArrived(roadPatients, 0)) {
                            roadPatients.setTaskExcuteStatus(2);
                        }
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    sRoadTaskPatients.add(roadPatients);

                    sRoadTaskPatientsOtherService.setsroadtaskpatientsmappinglist(sRoadTaskPatientsMappings
                            , roadPatients.getPatientId()
                            , roadPatients.getSRoadTaskId()
                            , roadPatients.getRecordsId()
                    );
                }
            }
        }
        //存患者任务
        if (sRoadTaskPatients.size() > 0) {
            sRoadTaskPatientsMapper.insertList(sRoadTaskPatients);

            ArrayList<SRoadTaskPatientsPhone> sRoadTaskPatientsPhones = new ArrayList<>();
            for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                if (sRoadTaskPatients1.getRoadExecuteEventWay().equals(Constant.BASIC_STRING_THREE) &&

                        (sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_TWO) ||
                                sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_FOUR)
                        )
                ) {
                    SRoadTaskPatientsPhone sRoadTaskPatientsPhone = sRoadTaskPatientsOtherService.newSRoadTaskPatientsPhone(sRoadTaskPatients1);
                    sRoadTaskPatientsPhones.add(sRoadTaskPatientsPhone);
                }
            }
            if (sRoadTaskPatientsPhones.size() > 0) {
                sRoadTaskPatientsPhoneMapper.insertList(sRoadTaskPatientsPhones);
                for (SRoadTaskPatientsPhone sRoadTaskPatients1 : sRoadTaskPatientsPhones) {
                    if (sRoadTaskPatients1.getPhoneStatus() == 1) {
                        redisService.putTaskWait1("2", sRoadTaskPatients1.getSRoadTaskPatientsPhoneId() + "", sRoadTaskPatients1.getSuiFangTime());
                    }

                }
            }

            for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                if (sRoadTaskPatients1.getTaskExcuteStatus() == 1) {
                    redisService.putTaskWait1("1", sRoadTaskPatients1.getTaskPatientsId() + "", sRoadTaskPatients1.getTaskExcuteTime());
                }

            }


        }
        //存患者
        if (needSaveTAdmin.size() > 0) {
            tAdminInfoMapper.insertList(needSaveTAdmin);
        }
        if (sRoadTaskPatientsMappings.size() > 0) {
            sRoadTaskPatientsMappingMapper.insertList(sRoadTaskPatientsMappings);
        }

        map.put("errorNumber", errorCounts);
        String[] split = errorCount.toString().split(",");
        map.put("description", split);
        map.put("total", realCounts);
        map.put("successNumber", realCounts - errorCounts);
        return map;

    }

    public static void main(String[] args) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = simpleDateFormat.parse("2023-11-12");
        String format = simpleDateFormat.format(new Date());
        System.out.println(format);
        long between = DateUtil.between(simpleDateFormat.parse(format), parse, DateUnit.DAY, false);
        System.out.println(between);


        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        try {
            System.out.println(simpleDateFormat2.parse("2023/11/22 10:10:00"));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }


    }

    @Transactional(rollbackFor = Exception.class)
    public Object addPatientToTask(AddPrediagnosis addPrediagnosis) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if ("admin".equals(currentHr.getUsername())) {
            return ResEntity.error("请使用admin账号之外的账号进行操作！");
        }
        if (Constant.BASIC_APP_ID.equals(currentHr.getAppId()) || Constant.BASIC_INS_CODE.equals(currentHr.getInsCode())) {
            return ResEntity.error("请先配置用户账号机构后在执行此操作！");
        }
        TAdminInfo tAdminInfo = tAdminInfoMapper.getTAdminInfoById(addPrediagnosis.getPatientId());
        if (tAdminInfo != null) {


            SRoadTaskPatients roadPatients = new SRoadTaskPatients();
            roadPatients.setSRoadTaskId("-1");
            roadPatients.setSRoadId("-1");
            roadPatients.setPatientId(tAdminInfo.getUserId());
            roadPatients.setPatientAge(tAdminInfo.getAge());
            roadPatients.setPatientSex(tAdminInfo.getSex());
            roadPatients.setPatientName(tAdminInfo.getUserName());
            roadPatients.setPatientCardNumber(tAdminInfo.getCardNumber());
            roadPatients.setTaskExcuteStatus(2);
            //手动
            roadPatients.setHandSend("1");
            //设置为今天
            roadPatients.setTaskExcuteTime(new Date());
            roadPatients.setStatus("0");
            roadPatients.setTaskName("");
            roadPatients.setRoadExecuteEventWay("1");
            //AdminInfo currentHr = AdminWebUtils.getCurrentHr();
            roadPatients.setAppId(currentHr.getAppId());
            roadPatients.setInsCode(currentHr.getInsCode());
            roadPatients.setInsId(currentHr.getInsId());
            roadPatients.setInsName(currentHr.getInsName());

            roadPatients.setDeptName(currentHr.getDeptName());
            roadPatients.setDeptCode(currentHr.getDeptCode());
            roadPatients.setDeptId(currentHr.getDeptId());

            roadPatients.setDoctorName(currentHr.getNameZh());
            roadPatients.setDoctorId(currentHr.getUserId());
            String diaId = addPrediagnosis.getDiaId();
            TPreDiagnosisForm objectById = tPreDiagnosisFormMapper.getObjectById(diaId);
            if (objectById.getDiaType().equals(Constant.BASIC_STRING_THREE)) {
                //随访问卷
                roadPatients.setRoadExecuteEventType("2");
            }
            if (objectById.getDiaType().equals(Constant.BASIC_STRING_FOUR)) {
                //自测量表
                roadPatients.setRoadExecuteEventType("4");
            }

            roadPatients.setRoadExecuteEventContentId(diaId);
            roadPatients.setRoadExecuteEventContentName(objectById.getFormName());
            sRoadTaskPatientsMapper.insert(roadPatients);
            //组装cbdata字段值。
            HashMap<String, String> map = new HashMap<>();
            map.put("appId", currentHr.getAppId());
            map.put("insCode", currentHr.getInsCode());
            map.put("insName", currentHr.getInsName());
            map.put("timestamp", System.currentTimeMillis() + "");
            map.put("userName", tAdminInfo.getUserName());
            map.put("mobile", tAdminInfo.getMobile());
            map.put("healthCardNum", tAdminInfo.getHealthCardNum());
            String mak = JSON.toJSONString(map);
            String base64 = "";
            try {
                base64 = AESPKCS7Util.encrypt(mak, key, "base64");
            } catch (Exception e) {
                log.error("cbdata加密错误：" + e.getMessage());
            }
            String a = "doctorfollowup?taskPatientsId=" + roadPatients.getTaskPatientsId() + "&diaId=" + roadPatients.getRoadExecuteEventContentId();
            String temp = null;
            try {
                temp = URLEncoder.encode(a, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            xiaMenSendWXMessage.sendWxTemplateWSDL(tAdminInfo.getMobile(), null,
                    url + "?cbdata=" + base64 + "&jumpUrl=" + temp,
                    //就诊人姓名|医生姓名|科室名称|问诊单名称
                    tAdminInfo.getUserName() + "|" + currentHr.getNameZh() + "|" + (StringUtils.isBlank(currentHr.getDeptName()) ? "" : currentHr.getDeptName())
                            + "|" + objectById.getFormName(), com.cbkj.diagnosis.common.utils.DateUtil.getDateFormats( "yyyy-MM-dd HH:mm:ss",roadPatients.getTaskExcuteTime()),
                    tAdminInfo.getCardNumber(),Constant.BASIC_STRING_THREE,tAdminInfo.getMedicalCard());


            return ResEntity.success();
        }
        return ResEntity.error("发送失败");
    }

    @Transactional(rollbackFor = Exception.class)
    public void resetPatientTasksStatus(ResetPatientTasksStatus resetPatientTasksStatus) {
        if (resetPatientTasksStatus == null || resetPatientTasksStatus.getTaskPatientsId() == null || resetPatientTasksStatus.getTaskPatientsId().length == 0) {
            return;
        }
        //1.待执行（未发送）2.已执行（已发送,未读（微信短信））3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 7.已读（微信短信）8.已填写
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        List<String> strings = Arrays.asList(resetPatientTasksStatus.getTaskPatientsId());
        List<SRoadTaskPatients> sRoadTaskPatientsList = sRoadTaskPatientsMapper.getPatientListTaskByIds(strings);


        for (int i = 0; i < sRoadTaskPatientsList.size(); i++) {
            SRoadTaskPatients sRoadTaskPatients = sRoadTaskPatientsList.get(i);
            //组装cbdata字段值。
            HashMap<String, String> map = new HashMap<>();
            map.put("appId", currentHr.getAppId());
            map.put("insCode", currentHr.getInsCode());
            map.put("insName", currentHr.getInsName());
            map.put("timestamp", System.currentTimeMillis() + "");
            map.put("userName", sRoadTaskPatients.getPatientName());
            map.put("mobile", sRoadTaskPatients.getPatientMobile());
//            map.put("healthCardNum", tAdminInfo.getHealthCardNum());
            String mak = JSON.toJSONString(map);
            String base64 = "";
            try {
                base64 = AESPKCS7Util.encrypt(mak, key, "base64");
            } catch (Exception e) {
                log.error("cbdata加密错误：" + e.getMessage());
            }
            String a = "doctorfollowup?taskPatientsId=" + sRoadTaskPatients.getTaskPatientsId() + "&diaId=" + sRoadTaskPatients.getRoadExecuteEventContentId();
            String temp = null;
            try {
                temp = URLEncoder.encode(a, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            String type = "0";
            String roadExecuteEventType = sRoadTaskPatients.getRoadExecuteEventType();
            if(roadExecuteEventType.equals("2") || roadExecuteEventType.equals("4")){
                type = "3";;
            }else if(roadExecuteEventType.equals("1")){
                type = "1";
            } else if (roadExecuteEventType.equals("3")) {
                type = "2";
            }
            TAdminInfo tAdminInfo = tAdminInfoMapper.getTAdminInfoById(sRoadTaskPatients.getPatientId());
            XiaMenResEntity xiaMenResEntity = xiaMenSendWXMessage.sendWxTemplateWSDL(sRoadTaskPatients.getPatientMobile(), null,
                    url + "?cbdata=" + base64 + "&jumpUrl=" + temp,
                    //就诊人姓名|医生姓名|科室名称|问诊单名称
                    sRoadTaskPatients.getPatientName() + "|" + currentHr.getNameZh() + "|" + (StringUtils.isBlank(currentHr.getDeptName()) ? "" : currentHr.getDeptName())
                            + "|" + sRoadTaskPatients.getRoadExecuteEventContentName(),
                    com.cbkj.diagnosis.common.utils.DateUtil.getDateFormats( "yyyy-MM-dd HH:mm:ss",sRoadTaskPatients.getTaskExcuteTime()),
                    sRoadTaskPatients.getPatientCardNumber(),type,tAdminInfo.getMedicalCard());
            if (xiaMenResEntity.getSuccess()) {
                sRoadTaskPatients.setTaskExcuteStatus(2);
                sRoadTaskPatients.setTaskExcuteTime(new Date());
            }

        }

        //更新
        for (int i = 0; i < sRoadTaskPatientsList.size(); i++) {
            sRoadTaskPatientsMapper.updateByPrimaryKey(sRoadTaskPatientsList.get(i));
        }
    }
}
