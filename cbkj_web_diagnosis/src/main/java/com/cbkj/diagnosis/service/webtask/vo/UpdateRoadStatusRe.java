package com.cbkj.diagnosis.service.webtask.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description =  "更新随访状态")
public class UpdateRoadStatusRe {

    @Schema(description="0正常1删除2.隐藏（任务中创建选择不保存）3停用")
    private String status;
    @Schema(description="随访id")
    private String sRoadIid;
}
