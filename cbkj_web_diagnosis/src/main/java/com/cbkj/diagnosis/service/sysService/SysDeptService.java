package com.cbkj.diagnosis.service.sysService;

import com.cbkj.diagnosis.beans.business.SysDept;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.SysDeptMapper;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysDeptService {

    @Autowired
    private SysDeptMapper sysDeptMapper;


    public Object getDeptList(SysDept sysDept, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SysDept> pageListByObj = sysDeptMapper.getPageListByObj(sysDept);
        PageHelper.clearPage();
        return Page.getLayUiTablePageData(pageListByObj);

    }
}
