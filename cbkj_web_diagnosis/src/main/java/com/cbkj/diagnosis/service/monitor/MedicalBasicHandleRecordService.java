package com.cbkj.diagnosis.service.monitor;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cbkj.diagnosis.beans.monitor.dto.DisposalRecordDTO;
import com.cbkj.diagnosis.beans.health.MedicalBasicHandleRecord;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
public interface MedicalBasicHandleRecordService extends IService<MedicalBasicHandleRecord> {
     DisposalRecordDTO getMedicalBasicHandleRecordByRecordId(String recordId);
}
