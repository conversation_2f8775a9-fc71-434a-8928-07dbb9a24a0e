package com.cbkj.diagnosis.service.sysService;


import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminMenu;
import com.cbkj.diagnosis.common.config.security.TokenBo;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.sysmapper.AdminInfoMapper;
import com.cbkj.diagnosis.mapper.sysmapper.AdminMenuMapper;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RedisService {

    @Autowired
    private AdminMenuMapper adminMenuMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private AdminService adminService;

    @Autowired
    private AdminInfoMapper adminInfoMapper;


    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void set(String key, Object value, long time, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, time, unit);
    }

    /**
     * 设置token
     *
     * @param tokenKey tokenKey
     * @param tokenBo  tokenBo
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/14
     */
    @CachePut(value = "pre-diagnosis-token", key = "#tokenKey")
    public TokenBo putToken(String tokenKey, TokenBo tokenBo) {
        return tokenBo;
    }

    /**
     * 设置token
     *
     * @param tokenKey tokenKey
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/14
     */
    @Cacheable(value = "pre-diagnosis-token", key = "#tokenKey")
    public TokenBo getToken(String tokenKey) {
        return null;
    }


    public void putTaskWait1(String type, String id, Date time) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        String formatDateStr = simpleDateFormat.format(time);

        log.info("type:{}--任务id{}--执行时间{}", type,id,formatDateStr);
        if (Constant.BASIC_STRING_TWO.equals(type)) {
            set("task-wait-2" + "::" + id, time);
        } else {
            set("task-wait-1" + "::" + id, time);
        }

    }

    public void removeKey(String key) {
        redisTemplate.delete(key);
    }

    @Cacheable(value = "task-wait-1", key = "#sRoadTaskPatients")
    public String getTaskWait1(String sRoadTaskPatients) {
        return null;
    }

    public Set<String> getAllKeys(String value) {
        Set<String> keys = redisTemplate.keys(value + "*");
        return keys;
    }

    /**
     * 所有菜单
     *
     * <AUTHOR>
     * @date 2021/2/4
     */
    //@Cacheable(value = "pre-parameter-menu", keyGenerator = "cacheKeyGenerator")
    public List<AdminMenu> getAllEnableMenu() {
        return adminMenuMapper.getAllMenu();
    }

    /**
     * 获取某个用户所有菜单
     *
     * @param uid
     * @return
     */
    //@Cacheable(value = "pre-parameter-menu", keyGenerator = "cacheKeyGenerator")
    public ResEntity getMenuByUID(String uid) {

        List<AdminMenu> resultLis = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
//        params.put("modualCode", modualCode);
        params.put("menuTypes", "1");
//        params.put("menuType", "1");
        //共三级菜单，不要第一级菜单
//        params.put("noParentId", "0");
        List<AdminMenu> lisM = adminMenuMapper.getMenuByPID(params);

        if (null != lisM) {
            Map<String, AdminMenu> resultM = new LinkedHashMap<>();

            for (AdminMenu menu : lisM) {
                if (resultM.containsKey(menu.getParentMenuId())) {
                    resultM.get(menu.getParentMenuId()).getChildList().add(menu);
                } else {
                    int temp = 0;
                    for (AdminMenu menu2 : lisM) {
                        List<AdminMenu> childList = menu2.getChildList();
                        if (childList != null && childList.size() > 0) {
                            for (AdminMenu adminMenu : childList) {
                                if (adminMenu.getMenuId().equals(menu.getParentMenuId())) {
                                    if (null == adminMenu.getChildList()) {
                                        adminMenu.setChildList(new ArrayList<AdminMenu>());
                                    }
                                    adminMenu.getChildList().add(menu);
                                    temp = 1;
                                }
                            }
                        }
                    }
                    if (temp == 0) {
                        menu.setChildList(new ArrayList<AdminMenu>());
                        resultM.put(menu.getMenuId(), menu);
                    }
                }
            }

            for (Map.Entry<String, AdminMenu> entry : resultM.entrySet()) {
                resultLis.add(entry.getValue());
            }
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, resultLis);
    }



    /**
     * 用户ID获取用户
     * 注：登录token以id唯一改动
     *
     * <AUTHOR>
     * @date 2020/11/27
     */
    //@Cacheable(value = "pre-parameter-user", key = "#userId")
    public AdminInfo loadUserByUserId(String userId) {
        AdminInfo admin = adminInfoMapper.selectByPrimaryKey(userId);
        return adminService.loadUser(admin);
    }

    /**
     * 清除缓存
     *
     * <AUTHOR>
     * @date 2021/2/4
     */
    public ResEntity clearRedisCache(String value, String key) {

        try {
            if (StringUtils.isNotBlank(value)) {
                Set<String> keys = redisTemplate.keys(value + "*");

                Long result = redisTemplate.delete(keys);
            }
            if (StringUtils.isNotBlank(key)) {
                Boolean result = redisTemplate.delete(key);
            }
        } catch (Exception e) {
            log.error("clearRedisCacheError. " + e.getMessage());
            return ResEntity.entity(false, "", null);
        }
        return ResEntity.entity(true, "", null);
    }

}