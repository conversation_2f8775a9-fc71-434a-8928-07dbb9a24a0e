package com.cbkj.diagnosis.service.advice;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TBusinessAnnex;
import com.cbkj.diagnosis.beans.business.adviceVo.SystemHelpList;
import com.cbkj.diagnosis.beans.request.SystemHelp;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.TBusinessAnnexMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/3 10:21
 * @Version 1.0
 */
@Service
public class SystemHelpService {

    private final TBusinessAnnexMapper tBusinessAnnexMapper;

    public SystemHelpService(TBusinessAnnexMapper tBusinessAnnexMapper) {
        this.tBusinessAnnexMapper = tBusinessAnnexMapper;
    }


    public Object getSystemHelpList(SystemHelp systemHelp, Page page) {
        QueryWrapper<TBusinessAnnex> tBusinessAnnexQueryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(systemHelp.getAnnexType())) {
            String[] split = systemHelp.getAnnexType().split(",");
            tBusinessAnnexQueryWrapper.in("annex_type", split);
        }else {
            tBusinessAnnexQueryWrapper.eq("annex_type", -100);
        }
        tBusinessAnnexQueryWrapper.eq("is_del", 0);
        PageHelper.startPage(page.getPage(),page.getLimit());
        List<TBusinessAnnex> tBusinessAnnexes = tBusinessAnnexMapper.selectList(tBusinessAnnexQueryWrapper);
        PageHelper.clearPage();
        if (!tBusinessAnnexes.isEmpty()) {
            ArrayList<SystemHelpList> systemHelpLists = new ArrayList<>();
            for (TBusinessAnnex tBusinessAnnex1 : tBusinessAnnexes) {
                SystemHelpList systemHelpList = new SystemHelpList();
                systemHelpList.setId(tBusinessAnnex1.getId());
                systemHelpList.setAnnexName(tBusinessAnnex1.getAnnexName());
                systemHelpList.setAnnexPath(tBusinessAnnex1.getAnnexPath());
                systemHelpList.setCreateTime(tBusinessAnnex1.getCreateTime());systemHelpList.setAnnexType(tBusinessAnnex1.getAnnexType());
                systemHelpLists.add(systemHelpList);
            }
            return Page.getResEntityPageData(systemHelpLists);
        }
        return Page.getResEntityPageData(null);
    }
}
