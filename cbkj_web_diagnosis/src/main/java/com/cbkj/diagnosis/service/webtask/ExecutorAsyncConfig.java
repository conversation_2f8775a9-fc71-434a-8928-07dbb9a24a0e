//package com.cbkj.diagnosis.service.webtask;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import java.util.concurrent.*;
//
///**
// * <AUTHOR>
// */
//@Configuration
//public class ExecutorAsyncConfig {
//
//    @Bean(name = "newAsyncExecutor")
//    public ThreadPoolTaskExecutor newAsync() {
//        int logicCpus = Runtime.getRuntime().availableProcessors();
//        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
//        //设置核心线程数
//        //io密集型线程池设定最佳线程数目 = （ （线程池设定的线程等待时间+线程 CPU 时间） /线程 CPU 时间 ） * CPU 数目
//        //cpu密集型，一般就是cpu核心数 或者是 cpu核心数+1
//        taskExecutor.setCorePoolSize(logicCpus+1);
//        // 线程池维护线程的最大数量，只有在缓冲队列满了以后才会申请超过核心线程数的线程,
//        taskExecutor.setMaxPoolSize(logicCpus * 2);
//        //缓存队列
//        taskExecutor.setQueueCapacity(50);
//        //允许的空闲时间，当超过了核心线程数之外的线程在空闲时间到达之后会被销毁
//        taskExecutor.setKeepAliveSeconds(200);
//        //异步方法内部线程名称
//        taskExecutor.setThreadNamePrefix("my-AsyncExecutor-");
//        //拒绝策略 :调用者线程执行
//        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        taskExecutor.initialize();
//        return taskExecutor;
//    }
//}
