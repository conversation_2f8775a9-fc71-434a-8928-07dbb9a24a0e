package com.cbkj.diagnosis.service.monitor.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.monitor.dto.ManifestFeeDTO;
import com.cbkj.diagnosis.beans.monitor.dto.ProjectFeeDTO;
import com.cbkj.diagnosis.beans.health.MedicalPatientRecordCost;
import com.cbkj.diagnosis.common.utils.CollectionUtils;
import com.cbkj.diagnosis.mapper.health.MedicalPatientRecordCostMapper;
import com.cbkj.diagnosis.service.convert.MedicalPatientRecordCostConvert;
import com.cbkj.diagnosis.service.monitor.MedicalPatientRecordCostService;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Service
public class MedicalPatientRecordCostServiceImpl extends ServiceImpl<MedicalPatientRecordCostMapper, MedicalPatientRecordCost> implements MedicalPatientRecordCostService {


    private final MedicalPatientRecordCostMapper medicalPatientRecordCostMapper;

    public MedicalPatientRecordCostServiceImpl(MedicalPatientRecordCostMapper medicalPatientRecordCostMapper) {
        this.medicalPatientRecordCostMapper = medicalPatientRecordCostMapper;
    }

    @Override
    public ManifestFeeDTO getRecordCostByRecordId(String recordId) {
        List<MedicalPatientRecordCost> list = medicalPatientRecordCostMapper.getRecordCostByRecordId(recordId);
        Map<String, List<MedicalPatientRecordCost>> collect = list.stream().collect(Collectors.groupingBy(MedicalPatientRecordCost::getMedicalPatientRecordItemType));

        ManifestFeeDTO cost = getCost(collect);

        BigDecimal total = list.stream().map(i -> new BigDecimal(i.getMedicalPatientRecordCost())).reduce(BigDecimal.ZERO, BigDecimal::add);
        cost.setTotalHealthcareCosts(total.toString());
        
        return cost;
    }

    public ManifestFeeDTO getCost(Map<String, List<MedicalPatientRecordCost>> listMap) {
        ManifestFeeDTO dto = new ManifestFeeDTO();

         List<ProjectFeeDTO> tCMMedicalServiceProjectFee = null;

         List<ProjectFeeDTO> imagingFees = null;

         List<ProjectFeeDTO> feesForLaboratoryTests = null;

         List<ProjectFeeDTO> chineseHerbalMedicineFee = null;

         List<ProjectFeeDTO> westernMedicineFee = null;

         List<ProjectFeeDTO> chinesePatentMedicineFee = null;

         List<ProjectFeeDTO> institutionChineseMedicinePreparationFee = null;

         List<ProjectFeeDTO> surgicalTreatmentFee = null;

         List<ProjectFeeDTO>  otherFees = null;

        listMap.forEach((bigItemType,list)->{
            List<ProjectFeeDTO> collect = list.stream().map(MedicalPatientRecordCostConvert::feeConvertToDTO).collect(Collectors.toList());

            switch (bigItemType) {
                case "1.1":
                    //tCMMedicalServiceProjectFee.addAll(collect);
                    dto.setTcmMedicalServiceProjectFee(collect);


                    break;
                case "1.2":
                    //tCMMedicalServiceProjectFee.addAll(collect);
                    dto.setTcmSyndromeDifferentiation(collect);
                    //dto.setTCMMedicalServiceProjectFee(collect);
                    if(CollectionUtils.isNotEmpty(collect) && !CollectionUtils.listContainsNullElement(collect)){
                        BigDecimal bianZhengCost =  collect.stream().map(i -> new BigDecimal(i.getFee())).reduce(BigDecimal.ZERO, BigDecimal::add);
                        dto.setBianZhengCost(String.valueOf(bianZhengCost));
                    }
//                    if(CollectionUtils.isNotEmpty(collect) && !CollectionUtils.listContainsNullElement(collect)){
//                        BigDecimal bianZhengCost =  collect.stream().map(i -> new BigDecimal(i.getFee())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        dto.setBianZhengCost(String.valueOf(bianZhengCost));
//                    }
                    break;
                case "3.1":
                    dto.setImagingFees(collect);
//                    imagingFees.addAll(collect);
                    break;
                case "3.2":
                    dto.setFeesForLaboratoryTests(collect);
//                    feesForLaboratoryTests.addAll(collect);
                    break;
                case "4.1":
                    dto.setChineseHerbalMedicineFee(collect);
//                    chineseHerbalMedicineFee.addAll(collect);
                    break;
                case "4.2":
                    dto.setChinesePatentMedicineFee(collect);
                    break;
                case "4.3":
                    dto.setInstitutionChineseMedicinePreparationFee(collect);
//                    institutionChineseMedicinePreparationFee.addAll(collect);
                    break;
                case "5.1":
                    dto.setWesternMedicineFee(collect);
//                    westernMedicineFee.addAll(collect);
                    break;
                case "6.1":
                    dto.setSurgicalTreatmentFee(collect);
//                    surgicalTreatmentFee.addAll(collect);
                    break;
                case "7.1":
//                    otherFees.addAll(collect);
                    dto.setOtherFees(collect);
                    BigDecimal decoctingCost = collect.stream().map(i -> new BigDecimal(i.getFee())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    dto.setDecoctingCost(decoctingCost.toString());
                    break;
                case "10.1":
                    BigDecimal personalCost = collect.stream().map(i -> new BigDecimal(i.getFee())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    dto.setPersonalCost(personalCost.toString());
                    break;
                case "11.1":
                    BigDecimal insuranceReimbursementCost = collect.stream().map(i -> new BigDecimal(i.getFee())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    dto.setInsuranceReimbursementCost(insuranceReimbursementCost.toString());
                    break;
            }

        });

        return dto;
    }


}
