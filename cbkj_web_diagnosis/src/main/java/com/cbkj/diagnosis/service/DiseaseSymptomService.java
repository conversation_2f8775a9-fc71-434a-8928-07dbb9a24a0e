package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.SSymptom;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.SSymptomMapper;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class DiseaseSymptomService {

    private final SSymptomMapper sSymptomMapper;

    DiseaseSymptomService(SSymptomMapper sSymptomMapper) {
        this.sSymptomMapper = sSymptomMapper;
    }


    public Object getDiseaseSymptomList(String keyWord, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SSymptom> list = sSymptomMapper.selectListByKeyWord(keyWord);
        PageHelper.clearPage();
        return Page.getLayUiTablePageData(list);
    }
}
