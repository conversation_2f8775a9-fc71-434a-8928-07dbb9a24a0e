package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion;
import com.cbkj.diagnosis.common.utils.PerformanceMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/4 12:42
 * @Version 1.0
 */
@Slf4j
@Service
public class PreDiagnosisListCacheService {
    private final TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;

    public PreDiagnosisListCacheService(TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper) {
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
    }

    /**
     * 缓存问卷问题数据，避免重复查询
     */
    @Cacheable(value = "questionnairePreQuestions", key = "#diaId")
    public List<TPreDiagnosisQuestion> getQuestionsByDiaIdCached(String diaId) {
        long startTime = PerformanceMonitor.startTimer("db_query_questions");
        try {
            List<TPreDiagnosisQuestion> questions = tPreDiagnosisQuestionMapper.getAllQuestionListByDiaId(diaId);
            PerformanceMonitor.recordDatabaseQuery("questions", System.currentTimeMillis() - startTime, questions.size());
            return questions;
        } catch (Exception e) {
            log.error("查询问卷问题失败, diaId: {}", diaId, e);
            return new ArrayList<>();
        }
    }
}
