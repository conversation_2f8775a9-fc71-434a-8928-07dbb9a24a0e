package com.cbkj.diagnosis.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.business.GPTAskRequest;
import com.cbkj.diagnosis.beans.business.TRecordLargeModel;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.monitor.dto.RecordInfoDTO;
import com.cbkj.diagnosis.beans.monitor.vo.GetPatientInfo;
import com.cbkj.diagnosis.beans.request.AISymptomAnalysisVo;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.common.exception.CustomRuntimeException;
import com.cbkj.diagnosis.common.openfeign.LargeModelClient;
import com.cbkj.diagnosis.common.openfeign.reqAndres.LargeModelRes;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.common.webclient.AIWebClient;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.monitor.impl.MedicalRecordsServiceImpl;
import com.cbkj.diagnosis.service.webapi.business.WebTRecordDiaService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AESPKCS7Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.BufferOverflowStrategy;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/29 11:16
 * @Version 1.0
 */
@Service
@Slf4j
public class AISymptomAnalysisService {

    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;
    private final AIModeltLogService aIModeltLogService;
    private final LargeModelClient largeModelClient;
    private final WebTRecordDiaService webTRecordDiaService;
    private final AIWebClient aiWebClient;
    private final MedicalRecordsServiceImpl medicalRecordsService;

    @Value("${gpt.api.ageng.id}")
    private String agentId;
    @Value("${gpt.api.cbdata.key}")
    private String key;
    public AISymptomAnalysisService(AIModeltLogService aIModeltLogService, LargeModelClient largeModelClient, WebTRecordDiaService webTRecordDiaService, AIWebClient aiWebClient, MedicalRecordsServiceImpl medicalRecordsService) {
        this.aIModeltLogService = aIModeltLogService;
        this.largeModelClient = largeModelClient;
        this.webTRecordDiaService = webTRecordDiaService;
        this.aiWebClient = aiWebClient;
        this.medicalRecordsService = medicalRecordsService;
    }

    public GPTAskRequest getGPTAskRequest(AISymptomAnalysisVo analysisVo,GPTAskRequest gptAskRequest){
        GetPatientInfo getPatientInfo = new GetPatientInfo();
        getPatientInfo.setPatientId(analysisVo.getPatientId());
        List<RecordInfoDTO> recordListByCreateDate = medicalRecordsService.getRecordListByCreateDate(getPatientInfo);
        //用stream流 倒序取出前十条 并且 recordTypeCode字段不等于3
        ArrayList<RecordInfoDTO> recordInfoDTOS = new ArrayList<>();
        IntStream.range(0, recordListByCreateDate.size()).
                map(i -> recordListByCreateDate.size() - 1 - i).mapToObj(recordListByCreateDate::get).
                filter(i -> !StringUtils.equals(i.getRecordTypeCode(), "3"))
                .limit(10)
                .forEach(recordInfoDTOS::add);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("请根据该患者的历次就诊及随访记录，对患者的核心症状变化情况进行分析，包括症状发生频率、持续时间、部位、严重程度等变化，以及伴随症状有无、药物反应、治疗依从性等。");
        recordInfoDTOS.forEach(recordInfoDTO -> {
            if (recordInfoDTO.getRecordTypeCode().equals(Constant.BASIC_STRING_TWO)) {
                MedicalRecords byId = medicalRecordsService.getById(recordInfoDTO.getId());
                if (byId != null) {
                    stringBuilder.append("就诊1:").append(DateUtil.getDateFormats(DateUtil.date1, byId.getRecordTime())).append("中医病名：")
                            .append(byId.getChineseDisName()).append("中医症状：").append(byId.getSymName())
                            .append("主诉：").append(byId.getChiefComplaint()).append("现病史：").append(byId.getPresentIllness()).append("既往史：").append(byId.getPastHistory())
                            .append("个人史：").append(byId.getPersonalHistory()).append("家族史：").append(byId.getFamilyHistory()).append("体格检查：").append(byId.getPhysicalExamination())
                            .append("治疗意见:").append(byId.getRecordsAdvice()).append("过敏史:").append(byId.getAllergyHistory());
                }
            } else if (recordInfoDTO.getRecordTypeCode().equals(Constant.BASIC_STRING_ONE) ||
                    recordInfoDTO.getRecordTypeCode().equals(Constant.BASIC_STRING_FOUR)
            ) {
                ResEntity<ArrayList<QuestionMain>> prePaper = webTRecordDiaService.getPrePaper(recordInfoDTO.getId());
                if (prePaper.getStatus()) {
                    stringBuilder.append(recordInfoDTO.getRecordType().equals(Constant.BASIC_STRING_ONE) ? "预诊：" : "随访：");
                    AtomicInteger temp = new AtomicInteger();
                    ArrayList<QuestionMain> list = prePaper.getData();
                    list.stream().forEach(questionMain -> {
                        temp.getAndIncrement();
                        stringBuilder.append(temp).append("、").append(questionMain.getQuestionName());
                        String[] answerContent = questionMain.getAnswerContent();
                        //answerContent转成字符串
                        if (answerContent != null) {
                            stringBuilder.append(String.join(",", answerContent));
                        }
                    });
                }
            }

        });
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
//        GPTAskRequest gptAskRequest = new GPTAskRequest();
        gptAskRequest.setQuestion(stringBuilder.toString());
        HashMap<String, Object> map = new HashMap<>();
        map.put("appId", StringUtils.isBlank(currentHr.getAppId()) ? "000000" : currentHr.getAppId() );
        map.put("insCode", StringUtils.isBlank(currentHr.getInsCode()) ? "000000" : currentHr.getInsCode() );
        map.put("deptId", "000000");
        map.put("userName", currentHr.getUsername());
        map.put("origin", "tcm");
        map.put("userId", currentHr.getUserId());
        String json = JSON.toJSONString(map);
        String s = null;
        try {
            s = AESPKCS7Util.encrypt(json, "99748ee4718e43cd", "base64");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        gptAskRequest.setCbdata(s);
        return gptAskRequest;
    }

    public ResEntity<LargeModelRes> getAiSymptomAnalysisJSON(AISymptomAnalysisVo analysisVo){
        GPTAskRequest gptAskRequest = new GPTAskRequest();
        gptAskRequest.setStream(false);
        gptAskRequest.setThink(false);
        gptAskRequest.setAgent_id(agentId);
        GPTAskRequest gptAskRequest1 = getGPTAskRequest(analysisVo, gptAskRequest);

        TRecordLargeModel tRecordLargeModel = new TRecordLargeModel();
        tRecordLargeModel.setRecordModelId(IdUtil.getSnowflake(snowflake).nextIdStr());
        tRecordLargeModel.setRecId(null);
        tRecordLargeModel.setDiaId(null);
        tRecordLargeModel.setRequestText(JSON.toJSONString(gptAskRequest1));
        tRecordLargeModel.setInsertTime(new java.util.Date());
        tRecordLargeModel.setCueWordText(null);
        tRecordLargeModel.setCuwWordTrans(null);
        tRecordLargeModel.setReCallTimes(0);
        tRecordLargeModel.setType(1);

        return largeModelClient.gptAsk(gptAskRequest1);
    }

    public Flux<ResEntity> getAiSymptomAnalysisRecordsInfoText(AISymptomAnalysisVo analysisVo) {
        GPTAskRequest gptAskRequest = new GPTAskRequest();
        gptAskRequest.setStream(true);
        gptAskRequest.setThink(false);
        gptAskRequest.setAgent_id(agentId);
        return getAISymptomAnalysis(getGPTAskRequest(analysisVo,gptAskRequest));
    }


    public Flux<ResEntity> getAISymptomAnalysis(GPTAskRequest gptAskRequest) {
//        return aiWebClient.webClient( gptAskRequest);
//        return largeModelClient.gptAskFlux(gptAskRequest);
        return aiWebClient.webClient(gptAskRequest)
                // 1. 异步调度优化
                .publishOn(Schedulers.boundedElastic()) // 阻塞操作专用线程池
                .subscribeOn(Schedulers.parallel())     // 上游请求线程池

                // 2. 安全背压策略
                .onBackpressureBuffer(
                        50,
                        BufferOverflowStrategy.DROP_LATEST // 避免内存溢出
                )

                // 3. 非阻塞式异常处理
                .doOnNext(entity -> Mono.fromRunnable(() -> {
                            if (entity.getCode() != 0) {
                                log.debug("业务异常: {}", entity.getMessage()); // 异步日志
                                throw new CustomRuntimeException(entity.getCode(), entity.getMessage());
                            }
                        }).subscribeOn(Schedulers.single()).subscribe()
                )

                // 4. 流控增强
                .timeout(Duration.ofSeconds(50),
                        Mono.defer(() -> Mono.just(
                                ResEntity.error("调用模型超时")
                        )).flux()  // 超时兜底:ml-citation{ref="2,6" data="citationList"}
                )
                .retryWhen(Retry.backoff(3, Duration.ofMillis(100))) // 指数退避重试

                // 5. 非阻塞过滤
                .filterWhen(entity -> Mono.just(
                        entity.getData() != null // 简单条件判断
                        // && !(entity.getData() instanceof Boolean) // 复杂判断需异步化
                ).subscribeOn(Schedulers.parallel()))

                // 6. 安全日志
                .doOnError(e ->
                        Mono.fromRunnable(() -> log.error("流处理异常:{}", e.getMessage()))
                                .subscribeOn(Schedulers.single())
                                .subscribe()
                )
                .onErrorResume(e -> Mono.defer(() -> Mono.just(
                        ResEntity.error("调用模型错误，服务降级")
                )).flux())//服务降级
                .doFinally(signal ->
                        log.info("大模型调用流处理结束: {}", signal.name()) // 同步日志需简短
                );
    }


}
