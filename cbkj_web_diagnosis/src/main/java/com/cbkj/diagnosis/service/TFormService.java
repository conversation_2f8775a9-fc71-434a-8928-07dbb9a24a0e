package com.cbkj.diagnosis.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.SysAdminInfoDisMapping;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisForm;
import com.cbkj.diagnosis.beans.DoctorDocDisCode;
import com.cbkj.diagnosis.beans.business.statictis.UserDisQuery;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.mapper.business.SysAdminInfoDisMappingMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import com.cbkj.diagnosis.service.vo.DeleteFormReVO;
import com.cbkj.diagnosis.service.webapi.business.WebTDiseaseService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TFormService {

    private final TPreDiagnosisFormMapper tPreDiagnosisFormMapper;

    private final SysAdminInfoDisMappingMapper sysAdminInfoDisMappingMapper;

    private final WebTDiseaseService tDiseaseService;


    TFormService(TPreDiagnosisFormMapper tPreDiagnosisFormMapper, SysAdminInfoDisMappingMapper sysAdminInfoDisMappingMapper, WebTDiseaseService tDiseaseService) {
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
        this.sysAdminInfoDisMappingMapper = sysAdminInfoDisMappingMapper;
        this.tDiseaseService = tDiseaseService;
    }


    public ResEntity deletePaper(DeleteFormReVO deleteFormReVO) {
//        if (Constant.BASIC_STRING_ONE.equals( deleteFormReVO.getStatus() )){
//            tPreDiagnosisFormMapper.deleteFormMapping(deleteFormReVO.getDiaId())
//        }

        TPreDiagnosisForm objectById = tPreDiagnosisFormMapper.getObjectById(deleteFormReVO.getDiaId());
        objectById.setStatus("1");
        objectById.setDelDate(new Date());
        objectById.setDelUser(AdminWebUtils.getCurrentHr().getUserId());
        objectById.setDelUsername(AdminWebUtils.getCurrentHr().getNameZh());
//        tPreDiagnosisFormMapper.updateStatusToDeleteByDiaId(deleteFormReVO.getDiaId());
        if (!"admin".equals(AdminWebUtils.getCurrentHr().getUsername())) {
            objectById.setCreateUser(AdminWebUtils.getCurrentHr().getUserId());
        }
        int i = tPreDiagnosisFormMapper.updateByPrimaryKey(objectById);
        return i == 1 ? ResEntity.success() : ResEntity.error("没有权限！");

    }

    public TPreDiagnosisForm changeShowStatus(String diaId, boolean status) {
        TPreDiagnosisForm objectById = tPreDiagnosisFormMapper.getObjectById(diaId);
        objectById.setShowStatus(status);
        int i = tPreDiagnosisFormMapper.updateByPrimaryKey(objectById);
        return objectById;
    }

    public List<SysAdminInfoDisMapping> getDoctorMappingDisList(String disName) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if (null != currentHr) {
            List<AdminRule> roles = currentHr.getRoles();
            if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
//                DiseaseVo diseaseVo = new DiseaseVo();
//                diseaseVo.setDisType("1");
//                diseaseVo.setQueryAll("0");
//                diseaseVo.setDiaName(disName);
//                List<TDisease> diseaseList = tDiseaseService.getDiseaseList(diseaseVo);
//                //转成List<SysAdminInfoDisMapping>
//                return diseaseList.stream().map(disease -> new SysAdminInfoDisMapping(disease.getDisId(),disease.getDisName(),currentHr.getUserId(),null)).collect(Collectors.toList());
                List<DoctorDocDisCode> disFromGroupBy = sysAdminInfoDisMappingMapper.getDisFromGroupBy(disName);
                return disFromGroupBy.stream().map(disease -> new SysAdminInfoDisMapping(disease.getDisId(), disease.getDisName(), currentHr.getUserId(), null)).collect(Collectors.toList());

            }
        }
        QueryWrapper<SysAdminInfoDisMapping> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", AdminWebUtils.getCurrentHr().getUserId());
        if (StringUtils.isNotBlank(disName)) {
            wrapper.like("dis_name", disName);
        }
        return sysAdminInfoDisMappingMapper.selectList(wrapper);

    }

    public List<DoctorDocDisCode> getDoctorDocDisCodeList(String disName) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if (null != currentHr) {
            List<AdminRule> roles = currentHr.getRoles();
            if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
                List<DoctorDocDisCode> disFromGroupBy = sysAdminInfoDisMappingMapper.getDisFromGroupBy(disName);
                return disFromGroupBy.stream().map(disease -> new DoctorDocDisCode(disease.getDisId(), disease.getDisCode(), disease.getDisName())).collect(Collectors.toList());
            }
        }
        UserDisQuery userDisQuery = new UserDisQuery();
        userDisQuery.setDisName(disName);
        if (currentHr != null) {
            userDisQuery.setUserId(currentHr.getUserId());
        }
        return sysAdminInfoDisMappingMapper.selectDisCodeList(userDisQuery);

    }


    public ResEntity changeCueWordStatus(String diaId, Integer cueWordStatus) {
        //校验cueWordStatus的值不为空并且为0或者1
        if (cueWordStatus == null || !(cueWordStatus == 0 || cueWordStatus == 1)) {
            return ResEntity.error("cueWordStatus的值不为空并且为0或者1");
        }
        TPreDiagnosisForm objectById = tPreDiagnosisFormMapper.getObjectById(diaId);
        objectById.setCueWordStatus(cueWordStatus);
        int i = tPreDiagnosisFormMapper.updateByPrimaryKey(objectById);
        return i == 1 ? ResEntity.success() : ResEntity.error("更新失败！");
    }
}
