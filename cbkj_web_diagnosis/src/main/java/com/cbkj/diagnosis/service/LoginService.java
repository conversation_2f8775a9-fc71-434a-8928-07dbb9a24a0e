package com.cbkj.diagnosis.service;

import cn.hutool.core.util.RandomUtil;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysExt.LoginBean;
import com.cbkj.diagnosis.common.config.security.TokenBo;
import com.cbkj.diagnosis.common.config.security.TokenUtil;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.encry.RSAEncryption;
import com.cbkj.diagnosis.service.sysService.AdminService;
import com.cbkj.diagnosis.service.sysService.RedisService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.concurrent.TimeUnit;

/**
 * Created by zbh on 2024/12/23 9:37
 *
 * @description：
 */
@Service
@Slf4j
public class LoginService {


    @Value("${login.attempts:5}")
    private Integer loginAttempts;

    @Value("${login.limit.time:5}")
    private Integer loginLimitTime;

    @Value("${on.user.lock:false}")
    private Boolean onUserLock;
    @Value("${rsa.privateKey}")
    private String privateKey;
    @Autowired
    private RSAEncryption rsaEncryption;
    @Autowired
    private AdminService adminService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private TokenUtil tokenUtil;


    //账户锁定前缀
    private static String loginLockPrefix = Constant.CACHE_PREFIX + ":verification:LoginFrequency::";


    public ResEntity login(LoginBean loginBean) {
        String captcha = loginBean.getCaptcha();
        String pwd = loginBean.getPwd();
        try {
            pwd = URLDecoder.decode(pwd, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String name = loginBean.getName();
        String checkKey = loginBean.getCheckKey();

//        String LoginFrequency = (String) redisService.get(loginLockPrefix + name);
//        if (StringUtils.isNotEmpty(LoginFrequency)) {
//            int count = Integer.parseInt(LoginFrequency);
//            if (count >= 5) {
//                return ResEntity.error(Constant.USER_NAME_OR_PASSWORD_ERROR + "当前访问受限，请联系管理员或稍后尝试！");
//            }
//        }
        String redisPrefix = Constant.CACHE_PREFIX + ":verification::";

        //校验验证码
        ResEntity resEntity = captchaMethod(checkKey, captcha, redisPrefix);
        if (!resEntity.getStatus()) {
            return resEntity;
        }

        //校验用户信息
        AdminInfo admin = adminService.loadUserByUsername(name);

        if (null == admin || pwd == null) {
            return ResEntity.error(Constant.USER_NAME_OR_PASSWORD_ERROR);
        }

        try {
            pwd = rsaEncryption.dencryptByPrivateKey(pwd, privateKey);
        } catch (Exception e) {
            log.error("RSA解密失败！");
            return ResEntity.error(Constant.USER_NAME_OR_PASSWORD_ERROR);
        }
        if (!pwd.equals(admin.getPassword())) {
            return ResEntity.error(Constant.USER_NAME_OR_PASSWORD_ERROR);
        }

        if (!admin.isEnabled()) {
            return ResEntity.error("该用户被禁用！");
        }

        TokenBo tokenBo = null;

        tokenBo = tokenUtil.createTokenBo(admin);
        return ResEntity.success(tokenBo.getAuthorization());
    }


    /**
     * 账户锁定
     *
     * @param userName  登录名
     * @param resEntity 响应实体
     * @return
     */
    public ResEntity accountNumberLock(String userName, ResEntity resEntity) {

        //是否开启账户锁定功能
        if (onUserLock) {
            //登录频率
            if (!resEntity.getStatus()) {
                String LoginFrequency = (String) redisService.get(loginLockPrefix + userName);
                if (StringUtils.isNotEmpty(LoginFrequency)) {
                    int count = Integer.parseInt(LoginFrequency);
                    if (count >= 5) {
                        return ResEntity.error(resEntity.getMessage() + "当前访问受限，请联系管理员或稍后尝试！");
                    }
                    String msg = String.format(Constant.ACCOUNT_LOCK_PROMPT, resEntity.getMessage(), loginAttempts - count);
                    redisService.set(loginLockPrefix + userName, String.valueOf(++count), loginLimitTime, TimeUnit.MINUTES);
                    return ResEntity.error(msg);
                } else {
                    String msg = String.format(Constant.ACCOUNT_LOCK_PROMPT, resEntity.getMessage(), loginAttempts);
                    redisService.set(loginLockPrefix + userName, "1", loginLimitTime, TimeUnit.MINUTES);
                    return ResEntity.error(msg);
                }
            }
        }
        return resEntity;
    }


    /**
     * 验证码方法
     *
     * @param checkKey         校验Key
     * @param verificationCode 验证码
     * @param redisPrefix      缓存前缀
     * @return
     */
    private ResEntity captchaMethod(String checkKey, String verificationCode, String redisPrefix) {

        String lowerCaseCaptcha = verificationCode.toLowerCase();

        String realKey = lowerCaseCaptcha + checkKey;

        //通过拼接key 获取缓存
        Object checkCode = redisService.get(redisPrefix + realKey);

        if (StringUtils.isBlank((String) checkCode) || !checkCode.toString().equals(lowerCaseCaptcha)) {
            return ResEntity.error("验证码错误！");
        }

        return ResEntity.success();
    }
}
