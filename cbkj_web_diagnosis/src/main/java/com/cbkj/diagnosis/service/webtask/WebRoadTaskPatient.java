package com.cbkj.diagnosis.service.webtask;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.SRoadTaskEx;
import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping;
import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.task.*;
import com.cbkj.diagnosis.beans.task.webvo.UpdateOrInsertRoad;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.SRoadTaskExMapper;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsMappingMapper;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsPhoneMapper;
import com.cbkj.diagnosis.mapper.task.SRoadExecuteMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.statistics.StatisticsExpenses;
import com.cbkj.diagnosis.service.statistics.StatisticsHealth;
import com.cbkj.diagnosis.service.sysService.RedisService;
import com.cbkj.diagnosis.service.sysService.SysAdminService;
import com.cbkj.diagnosis.service.webapi.business.SRoadTaskPatientsOtherService;
import com.cbkj.diagnosis.service.webapi.business.vo.GetRecordAndPatientByConditions;
import com.cbkj.diagnosis.service.webapi.business.vo.RecordAndPatientByConditions;
import com.cbkj.diagnosis.service.webapi.business.vo.RoadTaskCondition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Transactional(rollbackFor = Exception.class)
public class WebRoadTaskPatient {

    private final SysAdminService sysAdminService;
    private SRoadExecuteMapper sRoadExecuteMapper;
    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;

    private SRoadTaskMapper sRoadTaskMapper;

    private final RedisService redisService;

    private SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    private final SRoadTaskExMapper sRoadTaskExMapper;
    private WebRoadService webRoadService;
    private final SRoadTaskPatientsOtherService sRoadTaskPatientsOtherService;

    private final SRoadTaskPatientsMappingMapper sRoadTaskPatientsMappingMapper;



    private final StatisticsHealth statisticsHealth;
    WebRoadTaskPatient(SRoadExecuteMapper sRoadExecuteMapper, SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper, WebRoadService webRoadService, SRoadTaskPatientsMapper sRoadTaskPatientsMapper, SRoadTaskMapper sRoadTaskMapper, RedisService redisService, SRoadTaskExMapper sRoadTaskExMapper, SRoadTaskPatientsOtherService sRoadTaskPatientsOtherService, SRoadTaskPatientsMappingMapper sRoadTaskPatientsMappingMapper, StatisticsHealth statisticsHealth, SysAdminService sysAdminService) {
        this.sRoadExecuteMapper = sRoadExecuteMapper;
        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
        this.webRoadService = webRoadService;
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.sRoadTaskMapper = sRoadTaskMapper;
        this.redisService = redisService;
        this.sRoadTaskExMapper = sRoadTaskExMapper;
        this.sRoadTaskPatientsOtherService = sRoadTaskPatientsOtherService;
        this.sRoadTaskPatientsMappingMapper = sRoadTaskPatientsMappingMapper;
        this.statisticsHealth = statisticsHealth;
        this.sysAdminService = sysAdminService;
    }

    /**
     * 异步。把创建好的任务，符合条件的用户加入到待执行表中。
     */
//    @Async(value = "newAsyncExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void setTaskPatient(SRoadTask task) {
        //更新任务表的患者数量字段
        SRoadTask objectById = sRoadTaskMapper.getObjectById(task.getSRoadTaskId());
        if (objectById == null) {
            return;
        }
        if (!Constant.BASIC_STRING_TWO.equals(task.getSRoadGroupWay())) {
            return;
        }
        ArrayList<SRoadTaskPatientsMapping> sRoadTaskPatientsMappings = new ArrayList<>();
        //删除计划中的任务，不需要执行了
        sRoadTaskPatientsMapper.deleteStatusByRoadTaskId(task.getSRoadTaskId());
        sRoadTaskPatientsMapper.deletePhoneStatusByRoadTaskId(task.getSRoadTaskId());

        //先关联用户表、任务路径条件表，查找最新的病历 并且 病历信息符合条件的用户（不看病历啥状态,自动入组的，用户也愿意自动入组的）
        UpdateOrInsertRoad updateOrInsertRoad = webRoadService.getRoadDetailById(task.getSRoadId());
        List<SRoadConditions> roadConditionsList = updateOrInsertRoad.getRoadConditionsList();
        List<SRoadExecute> sRoadExecuteList = updateOrInsertRoad.getSRoadExecuteList();
        ArrayList<RoadTaskCondition> roadTaskConditions = new ArrayList<>();
        HashMap<String, GetRecordAndPatientByConditions> stringObjectHashMap = new HashMap<>();
        RecordAndPatientByConditions byConditions = new RecordAndPatientByConditions();
        for (int i = 0; i < sRoadExecuteList.size(); i++) {
            SRoadExecute sRoadExecute = sRoadExecuteList.get(i);
            List<SRoadExecuteContents> sRoadExecuteContentsList = sRoadExecute.getSRoadExecuteContentsList();
            for (SRoadExecuteContents contents : sRoadExecuteContentsList) {
                RoadTaskCondition condition = new RoadTaskCondition();
                condition.setRoadExecuteEventTime(sRoadExecute.getRoadExecuteTime());
                condition.setRoadExecuteEventUnit(sRoadExecute.getRoadExecuteEventUnit());
                condition.setRoadExecuteEventContentId(contents.getRoadExecuteEventContentId());
                condition.setRoadExecuteEventType(contents.getRoadExecuteEventType());
                roadTaskConditions.add(condition);
            }
        }
        //byConditions.setRoadTaskConditionList(roadTaskConditions);

        for (SRoadConditions sRoadConditions : roadConditionsList) {
            if (StringUtils.isNotBlank(sRoadConditions.getTWestDiseaseId()) && !"-1".equals(sRoadConditions.getTWestDiseaseId())) {
                byConditions.setWestDiseaseId(sRoadConditions.getTWestDiseaseId());
            }
            if (StringUtils.isNotBlank(sRoadConditions.getTChineseDiseaseId()) && !"-1".equals(sRoadConditions.getTChineseDiseaseId())) {
                byConditions.setChineseDisease(sRoadConditions.getTChineseDiseaseId());
            }
            if (StringUtils.isNotBlank(sRoadConditions.getSSymptomId()) && !"-1".equals(sRoadConditions.getSSymptomId())) {
                byConditions.setSymptomId(sRoadConditions.getSSymptomId());
            }
//            if (StringUtils.isNotBlank(sRoadConditions.getSysDeptId()) && !"-1".equals(sRoadConditions.getSysDeptId())) {
//                byConditions.setDeptId(sRoadConditions.getSysDeptId());
//            }
//            if (StringUtils.isNotBlank(sRoadConditions.getDoctorId()) && !"-1".equals(sRoadConditions.getDoctorId())) {
//                byConditions.setDoctorId(sRoadConditions.getDoctorId());
//            }
        }
        //加上任务本身的条件，科室和就诊医生、随访医生。
        QueryWrapper<SRoadTaskEx> sRoadTaskExQueryWrapper = new QueryWrapper<>();
        sRoadTaskExQueryWrapper.eq("s_road_task_id", task.getSRoadTaskId());
        List<SRoadTaskEx> a = sRoadTaskExMapper.selectList(sRoadTaskExQueryWrapper);
        if (a != null || a.size() > 0) {
            for (SRoadTaskEx sRoadTaskEx : a) {
                if (sRoadTaskEx.getExType() == 0) {
                    byConditions.setDeptId(sRoadTaskEx.getDeptId());
                } else if (sRoadTaskEx.getExType() == 1 || sRoadTaskEx.getExType() == 2) {
                    byConditions.setDoctorId(sRoadTaskEx.getRecordDoctorId());
                    //byConditions.setDoctorId(sRoadTaskEx.getDiagnosisDoctorId());
                }
            }
        }
        //获取任务主表的筛选条件，这个是比有条件
        String recordStartTime = objectById.getRecordStartTime();
        String recordEndTime = objectById.getRecordEndTime();
        //下面不是必有条件
        Integer limitDiagnosisDaysInfo = objectById.getLimitDiagnosisDaysInfo();
        Integer limitRepeatRecord = objectById.getLimitRepeatRecord();
        String joinRoadTask = objectById.getJoinRoadTask();

        Integer limitRepeatRecordCheck = objectById.getLimitRepeatRecordCheck();
        Integer limitDiagnosisDaysInfoCheck = objectById.getLimitDiagnosisDaysInfoCheck();

        if (null != limitRepeatRecordCheck && limitDiagnosisDaysInfoCheck == 1) {
            byConditions.setLimitDiagnosisDaysInfo(limitDiagnosisDaysInfo);
        }

        if (null != limitRepeatRecordCheck && limitRepeatRecordCheck == 1) {
            byConditions.setLimitRepeatRecord(limitRepeatRecord);
        }

        byConditions.setRecordEndTime(recordEndTime);
        byConditions.setRecordStartTime(recordStartTime);
        byConditions.setJoinRoadTask(joinRoadTask);
        //byConditions.setLimitRepeatRecord(limitRepeatRecord);
        //byConditions.setLimitDiagnosisDaysInfo(limitDiagnosisDaysInfo);

        //获取患者的就诊记录信息表,!!!!!!!!!!!!只匹配患者最后一次病历信息，注意看sql的条件
        List<GetRecordAndPatientByConditions> recordAndPatientByConditions = sRoadExecuteMapper.getRecordAndPatientByConditions(byConditions);
        for (GetRecordAndPatientByConditions recordAndPatientByCondition : recordAndPatientByConditions) {
            stringObjectHashMap.put(recordAndPatientByCondition.getPatientId(), recordAndPatientByCondition);
        }


        Integer patientNum = objectById.getPatientNum();
        if (patientNum == null) {
            patientNum = 0;
        }
        objectById.setPatientNum(stringObjectHashMap.size() + patientNum);
        sRoadTaskMapper.updateByPrimaryKey(objectById);

        //查下任务的路径-执行的具体信息
        //UpdateOrInsertRoad updateOrInsertRoad = webRoadService.getRoadDetailById(task.getSRoadId());
        ArrayList<SRoadTaskPatients> sRoadTaskPatients = new ArrayList<>();
//        List<SRoadExecute> sRoadExecuteList = updateOrInsertRoad.getSRoadExecuteList();
        //对查出来结果，记录到待执行表中。
        Collection<GetRecordAndPatientByConditions> values = stringObjectHashMap.values();

//        ArrayList<String> staticsEduPatient = new ArrayList<>();
        ArrayList<String> staticsEduEduId = new ArrayList<>();

        for (GetRecordAndPatientByConditions getRecordAndPatientByConditions : values) {

            for (int i1 = 0; i1 < sRoadExecuteList.size(); i1++) {
                SRoadExecute sRoadExecute = sRoadExecuteList.get(i1);

                if (sRoadExecute.getRoadExecuteId() == null) {
                    throw new RuntimeException("缺少sRoadExecuteList.roadExecuteId");
                }
                //排除路径事件不是2的：就诊后
//                if (!sRoadExecute.getRoadExecuteEvent().equals(Constant.BASIC_STRING_TWO)) {
//                    continue;
//                }
                SRoadTaskPatients roadPatients = new SRoadTaskPatients();
                BeanUtils.copyProperties(sRoadExecute, roadPatients);
                List<SRoadExecuteContents> sRoadExecuteContentsList = sRoadExecute.getSRoadExecuteContentsList();
                for (int i2 = 0; i2 < sRoadExecuteContentsList.size(); i2++) {
                    SRoadExecuteContents executeContents = sRoadExecuteContentsList.get(i2);



                    // 统计宣教发送
                    if (Constant.BASIC_STRING_ONE.equals(executeContents.getRoadExecuteEventType())){

//                        staticsEduPatient.add(getRecordAndPatientByConditions.getPatientId());
                        staticsEduEduId.add(executeContents.getRoadExecuteEventContentId());

                        //   statisticsHealth.writeReadFromRedis(executeContents.getRoadExecuteEventContentId(),getRecordAndPatientByConditions.getPatientId());
                    }

                    BeanUtils.copyProperties(executeContents, roadPatients);
                    roadPatients.setPatientId(getRecordAndPatientByConditions.getPatientId());
                    roadPatients.setPatientAge(getRecordAndPatientByConditions.getPatientAge());
                    roadPatients.setPatientSex(getRecordAndPatientByConditions.getPatientSex());
                    roadPatients.setPatientName(getRecordAndPatientByConditions.getPatientName());
                    roadPatients.setRecordsId(getRecordAndPatientByConditions.getRecordsId());
                    roadPatients.setPatientCardNumber(getRecordAndPatientByConditions.getPatientCardNumber());

                    roadPatients.setAppId(getRecordAndPatientByConditions.getAppId());
                    roadPatients.setInsId(getRecordAndPatientByConditions.getInsId());
                    roadPatients.setInsName(getRecordAndPatientByConditions.getInsName());
                    roadPatients.setInsCode(getRecordAndPatientByConditions.getInsCode());
                    roadPatients.setDeptId(getRecordAndPatientByConditions.getDeptId());
                    roadPatients.setDeptName(getRecordAndPatientByConditions.getDeptName());
                    roadPatients.setDeptCode(getRecordAndPatientByConditions.getDeptCode());
                    roadPatients.setDeptId(getRecordAndPatientByConditions.getDeptId());
                    //BeanUtils.copyProperties(getRecordAndPatientByConditions, roadPatients);
                    roadPatients.setStatus("0");
                    roadPatients.setTaskName(task.getTaskName());
                    roadPatients.setSRoadTaskId(task.getSRoadTaskId());
                    roadPatients.setTaskExcuteStatus(1);
                    roadPatients.setRoadExecuteEventTime(sRoadExecute.getRoadExecuteTime());
                    roadPatients.setHandSend("0");
//                    roadPatients.setDoctorId(getRecordAndPatientByConditions.getDoctorId());
                    roadPatients.setDoctorId(AdminWebUtils.getCurrentHr().getUserId());
                    roadPatients.setDoctorName(AdminWebUtils.getCurrentHr().getNameZh());
                    try {
                        if (sRoadTaskPatientsOtherService.checkJIuZhenHouTimeIsArrived(roadPatients, 0)) {
                            roadPatients.setTaskExcuteStatus(2);
                        }
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    //此处判断数据库中是否有重复
                    SRoadTaskPatients roadPatientsNew = new SRoadTaskPatients();
                    roadPatientsNew.setPatientId(roadPatients.getPatientId());
                    roadPatientsNew.setSRoadTaskId(task.getSRoadTaskId());
//                    roadPatientsNew.setRoadExecuteEventContentId(roadPatients.getRoadExecuteEventContentId());
//                    roadPatientsNew.setRoadExecuteEventTime(roadPatients.getRoadExecuteEventTime());
//                    roadPatientsNew.setRoadExecuteEventUnit(roadPatients.getRoadExecuteEventUnit());
//                    roadPatientsNew.setRecordsId(roadPatients.getRecordsId());
//                    roadPatientsNew.setRoadExecuteEventType(roadPatients.getRoadExecuteEventType());
                    if (StringUtils.isNotBlank(roadPatients.getRecordsId())){
                        int tempNum = sRoadTaskPatientsMapper.countByConditons(roadPatientsNew);
                        if (tempNum > 0) {
                            continue;
                        }
                    }
                    sRoadTaskPatients.add(roadPatients);


                    sRoadTaskPatientsOtherService.setsroadtaskpatientsmappinglist(sRoadTaskPatientsMappings
                            , getRecordAndPatientByConditions.getPatientId()
                            , task.getSRoadTaskId()
                            , roadPatients.getRecordsId()
                    );
                }

            }

        }
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        statisticsHealth.writeReadFromRedisMaps(staticsEduEduId,currentHr.getAppId(),currentHr.getInsCode(),currentHr.getInsId(),currentHr.getInsName());
        if (sRoadTaskPatients.size() > 0) {
            sRoadTaskPatientsMapper.insertList(sRoadTaskPatients);

            ArrayList<SRoadTaskPatientsPhone> sRoadTaskPatientsPhones = new ArrayList<>();
            for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                if (sRoadTaskPatients1.getRoadExecuteEventWay().equals(Constant.BASIC_STRING_THREE) &&

                        (sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_TWO) ||
                                sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_FOUR)
                        )
                ) {
                    sRoadTaskPatientsPhones.add(sRoadTaskPatientsOtherService.newSRoadTaskPatientsPhone(sRoadTaskPatients1));

                }
            }
            if (sRoadTaskPatientsPhones.size() > 0) {
                sRoadTaskPatientsPhoneMapper.insertList(sRoadTaskPatientsPhones);

                for (SRoadTaskPatientsPhone sRoadTaskPatients1 : sRoadTaskPatientsPhones) {
                    if (sRoadTaskPatients1.getPhoneStatus() == 1) {
                        redisService.putTaskWait1("2", sRoadTaskPatients1.getSRoadTaskPatientsPhoneId() + "", sRoadTaskPatients1.getSuiFangTime());
                    }

                }
            }


            for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                // if (sRoadTaskPatients1.getTaskExcuteStatus() == 1) {

                redisService.putTaskWait1("1", sRoadTaskPatients1.getTaskPatientsId() + "", sRoadTaskPatients1.getTaskExcuteTime());
                //}

            }
        }


        if (sRoadTaskPatientsMappings.size() > 0) {
            sRoadTaskPatientsMappingMapper.insertList(sRoadTaskPatientsMappings);
        }


        //后续逻辑 1.一个定时任务，定时查待执行表关联病例表 查状态，并更新待执行表的执行时间。2.一个定时器，每日定时执行可以执行的任务（必须有出发时间）。
    }


    /**
     * todo 先这样子搞，有时间在优化。
     *
     * @param recordId
     */
//    @Async
    public void newRecordsCreateTask(String recordId) {
        //todo 获取所有任务，当前这个病历去匹配所有任务，符合条件的就加入到任务中。
        List<SRoadTask> allList = sRoadTaskMapper.getAllList();
        //todo 先这样子搞，有时间在优化。

        for (int i = 0; i < allList.size(); i++) {
            ArrayList<SRoadTaskPatientsMapping> sRoadTaskPatientsMappings = new ArrayList<>();
            HashMap<String, GetRecordAndPatientByConditions> stringObjectHashMap = new HashMap<>();
            SRoadTask sRoadTask = allList.get(i);
            UpdateOrInsertRoad updateOrInsertRoad = webRoadService.getRoadDetailById(sRoadTask.getSRoadId());
            List<SRoadConditions> roadConditionsList = updateOrInsertRoad.getRoadConditionsList();
            RecordAndPatientByConditions byConditions = new RecordAndPatientByConditions();
            for (SRoadConditions sRoadConditions : roadConditionsList) {
                if (StringUtils.isNotBlank(sRoadConditions.getTWestDiseaseId()) && !"-1".equals(sRoadConditions.getTWestDiseaseId())) {
                    byConditions.setWestDiseaseId(sRoadConditions.getTWestDiseaseId());
                }
                if (StringUtils.isNotBlank(sRoadConditions.getTChineseDiseaseId()) && !"-1".equals(sRoadConditions.getTChineseDiseaseId())) {
                    byConditions.setChineseDisease(sRoadConditions.getTChineseDiseaseId());
                }
                if (StringUtils.isNotBlank(sRoadConditions.getSSymptomId()) && !"-1".equals(sRoadConditions.getSSymptomId())) {
                    byConditions.setSymptomId(sRoadConditions.getSSymptomId());
                }
//                if (StringUtils.isNotBlank(sRoadConditions.getSysDeptId()) && !"-1".equals(sRoadConditions.getSysDeptId())) {
//                    byConditions.setDeptId(sRoadConditions.getSysDeptId());
//                }
//                if (StringUtils.isNotBlank(sRoadConditions.getDoctorId()) && !"-1".equals(sRoadConditions.getDoctorId())) {
//                    byConditions.setDoctorId(sRoadConditions.getDoctorId());
//                }
            }
            //加上任务本身的条件，科室和就诊医生、随访医生。
            QueryWrapper<SRoadTaskEx> sRoadTaskExQueryWrapper = new QueryWrapper<>();
            sRoadTaskExQueryWrapper.eq("s_road_task_id", sRoadTask.getSRoadTaskId());
            List<SRoadTaskEx> a = sRoadTaskExMapper.selectList(sRoadTaskExQueryWrapper);
            if (a != null || a.size() > 0) {
                for (SRoadTaskEx sRoadTaskEx : a) {
                    if (sRoadTaskEx.getExType() == 0) {
                        byConditions.setDeptId(sRoadTaskEx.getDeptId());
                    } else if (sRoadTaskEx.getExType() == 1 || sRoadTaskEx.getExType() == 2) {
                        byConditions.setDoctorId(sRoadTaskEx.getRecordDoctorId());
                        //byConditions.setDoctorId(sRoadTaskEx.getDiagnosisDoctorId());
                    }
                }
            }

            //获取任务主表的筛选条件，这个是比有条件
            String recordStartTime = sRoadTask.getRecordStartTime();
            String recordEndTime = sRoadTask.getRecordEndTime();
            //下面不是必有条件
            Integer limitDiagnosisDaysInfo = sRoadTask.getLimitDiagnosisDaysInfo();
            Integer limitRepeatRecord = sRoadTask.getLimitRepeatRecord();
            String joinRoadTask = sRoadTask.getJoinRoadTask();

            Integer limitRepeatRecordCheck = sRoadTask.getLimitRepeatRecordCheck();
            Integer limitDiagnosisDaysInfoCheck = sRoadTask.getLimitDiagnosisDaysInfoCheck();

            if (null != limitRepeatRecordCheck && limitDiagnosisDaysInfoCheck == 1) {
                byConditions.setLimitDiagnosisDaysInfo(limitDiagnosisDaysInfo);
            }

            if (null != limitRepeatRecordCheck && limitRepeatRecordCheck == 1) {
                byConditions.setLimitRepeatRecord(limitRepeatRecord);
            }

            byConditions.setRecordEndTime(recordEndTime);
            byConditions.setRecordStartTime(recordStartTime);
            byConditions.setJoinRoadTask(joinRoadTask);
            //byConditions.setLimitRepeatRecord(limitRepeatRecord);
            //byConditions.setLimitDiagnosisDaysInfo(limitDiagnosisDaysInfo);
            byConditions.setRecordsId(recordId);
            //获取患者的就诊记录信息表,!!!!!!!!!!!!只匹配患者当前病历，注意看sql的条件
            List<GetRecordAndPatientByConditions> recordAndPatientByConditions = sRoadExecuteMapper.getRecordAndPatientByConditions(byConditions);
            for (GetRecordAndPatientByConditions recordAndPatientByCondition : recordAndPatientByConditions) {
                stringObjectHashMap.put(recordAndPatientByCondition.getPatientId(), recordAndPatientByCondition);
            }
            Integer patientNum = sRoadTask.getPatientNum();
            if (patientNum == null) {
                patientNum = 0;
            }
            ArrayList<SRoadTaskPatients> sRoadTaskPatients = new ArrayList<>();
            List<SRoadExecute> sRoadExecuteList = updateOrInsertRoad.getSRoadExecuteList();
            //对查出来结果，记录到待执行表中。
            Collection<GetRecordAndPatientByConditions> values = stringObjectHashMap.values();
            ArrayList<String> staticsEduEduId = new ArrayList<>();
            String tempUserId = null;
            for (GetRecordAndPatientByConditions getRecordAndPatientByConditions : values) {

                for (int i1 = 0; i1 < sRoadExecuteList.size(); i1++) {
                    SRoadExecute sRoadExecute = sRoadExecuteList.get(i1);

                    if (sRoadExecute.getRoadExecuteId() == null) {
                        throw new RuntimeException("缺少sRoadExecuteList.roadExecuteId");
                    }
                    //排除路径事件不是2的：就诊后
//                if (!sRoadExecute.getRoadExecuteEvent().equals(Constant.BASIC_STRING_TWO)) {
//                    continue;
//                }
                    SRoadTaskPatients roadPatients = new SRoadTaskPatients();
                    BeanUtils.copyProperties(sRoadExecute, roadPatients);
                    List<SRoadExecuteContents> sRoadExecuteContentsList = sRoadExecute.getSRoadExecuteContentsList();
                    for (int i2 = 0; i2 < sRoadExecuteContentsList.size(); i2++) {
                        SRoadExecuteContents executeContents = sRoadExecuteContentsList.get(i2);

                        // 统计宣教发送
                        if (Constant.BASIC_STRING_ONE.equals(executeContents.getRoadExecuteEventType())){

//                        staticsEduPatient.add(getRecordAndPatientByConditions.getPatientId());
                            staticsEduEduId.add(executeContents.getRoadExecuteEventContentId());

                            //   statisticsHealth.writeReadFromRedis(executeContents.getRoadExecuteEventContentId(),getRecordAndPatientByConditions.getPatientId());
                        }

                        BeanUtils.copyProperties(executeContents, roadPatients);
                        roadPatients.setPatientId(getRecordAndPatientByConditions.getPatientId());
                        roadPatients.setPatientAge(getRecordAndPatientByConditions.getPatientAge());
                        roadPatients.setPatientSex(getRecordAndPatientByConditions.getPatientSex());
                        roadPatients.setPatientName(getRecordAndPatientByConditions.getPatientName());
                        roadPatients.setRecordsId(getRecordAndPatientByConditions.getRecordsId());
                        roadPatients.setPatientCardNumber(getRecordAndPatientByConditions.getPatientCardNumber());

                        roadPatients.setAppId(getRecordAndPatientByConditions.getAppId());
                        roadPatients.setInsId(getRecordAndPatientByConditions.getInsId());
                        roadPatients.setInsName(getRecordAndPatientByConditions.getInsName());
                        roadPatients.setInsCode(getRecordAndPatientByConditions.getInsCode());
                        roadPatients.setDeptId(getRecordAndPatientByConditions.getDeptId());
                        roadPatients.setDeptName(getRecordAndPatientByConditions.getDeptName());
                        roadPatients.setDeptCode(getRecordAndPatientByConditions.getDeptCode());
                        roadPatients.setDeptId(getRecordAndPatientByConditions.getDeptId());
                        //BeanUtils.copyProperties(getRecordAndPatientByConditions, roadPatients);
                        roadPatients.setStatus("0");
                        roadPatients.setTaskName(sRoadTask.getTaskName());
                        roadPatients.setSRoadTaskId(sRoadTask.getSRoadTaskId());
                        roadPatients.setTaskExcuteStatus(1);
                        roadPatients.setRoadExecuteEventTime(sRoadExecute.getRoadExecuteTime());
                        roadPatients.setHandSend("0");
//                    roadPatients.setDoctorId(getRecordAndPatientByConditions.getDoctorId());
//                    AdminInfo currentHr = AdminWebUtils.getCurrentHr();
                      //  if (currentHr != null) {
                            roadPatients.setDoctorId(sRoadTask.getCreateUserId());
                            roadPatients.setDoctorName(sRoadTask.getCreateUserName());
                        tempUserId=sRoadTask.getCreateUserId();
                        //} else {
                                //创建任务的医生
                        //}

                        try {
                            if (sRoadTaskPatientsOtherService.checkJIuZhenHouTimeIsArrived(roadPatients, 0)) {
                                roadPatients.setTaskExcuteStatus(2);
                            }
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                        sRoadTaskPatients.add(roadPatients);


                        sRoadTaskPatientsOtherService.setsroadtaskpatientsmappinglist(sRoadTaskPatientsMappings
                                , getRecordAndPatientByConditions.getPatientId()
                                , sRoadTask.getSRoadTaskId()
                                , roadPatients.getRecordsId()
                        );
                    }

                }

            }
            if (StringUtils.isNotEmpty(tempUserId)){
                AdminInfo adminInfoByUserId = sysAdminService.getAdminInfoByUserId(tempUserId);
                if (null != adminInfoByUserId){
                    statisticsHealth.writeReadFromRedisMaps(staticsEduEduId,adminInfoByUserId.getAppId(),
                            adminInfoByUserId.getInsCode(),
                            adminInfoByUserId.getInsId(),
                            adminInfoByUserId.getInsName());

                }

            }
            if (sRoadTaskPatients.size() > 0) {
                sRoadTaskPatientsMapper.insertList(sRoadTaskPatients);

                ArrayList<SRoadTaskPatientsPhone> sRoadTaskPatientsPhones = new ArrayList<>();
                for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                    if (sRoadTaskPatients1.getRoadExecuteEventWay().equals(Constant.BASIC_STRING_THREE) &&

                            (sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_TWO) ||
                                    sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_FOUR)
                            )
                    ) {
                        sRoadTaskPatientsPhones.add(sRoadTaskPatientsOtherService.newSRoadTaskPatientsPhone(sRoadTaskPatients1));

                    }
                }
                if (sRoadTaskPatientsPhones.size() > 0) {
                    sRoadTaskPatientsPhoneMapper.insertList(sRoadTaskPatientsPhones);

                    for (SRoadTaskPatientsPhone sRoadTaskPatients1 : sRoadTaskPatientsPhones) {
                        if (sRoadTaskPatients1.getPhoneStatus() == 1) {
                            redisService.putTaskWait1("2", sRoadTaskPatients1.getSRoadTaskPatientsPhoneId() + "", sRoadTaskPatients1.getSuiFangTime());
                        }

                    }
                }


                for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                    // if (sRoadTaskPatients1.getTaskExcuteStatus() == 1) {

                    redisService.putTaskWait1("1", sRoadTaskPatients1.getTaskPatientsId() + "", sRoadTaskPatients1.getTaskExcuteTime());
                    //}

                }
            }
            if (sRoadTaskPatientsMappings.size() > 0) {
                sRoadTaskPatientsMappingMapper.insertList(sRoadTaskPatientsMappings);
            }


        }

    }


}
