package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategyFactory;
import com.cbkj.diagnosis.service.webapi.business.vo.GetSuiFangPaperList;
import com.cbkj.diagnosis.service.webapi.business.vo.LookPaperRecive;
import com.cbkj.diagnosis.service.webapi.business.vo.PaperListReVo;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class WebTPreDiagnosisFormService {


    private TPreDiagnosisFormMapper tPreDiagnosisFormMapper;
    private TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;

    private RecordDiaStrategyFactory recordDiaStrategyFactory;

    @Value("${xia.men.mobile.wx.url}")
    private String url;

    @Value("${xia.men.his.jump.login.url}")
    private String loginUrl;

    WebTPreDiagnosisFormService(TPreDiagnosisFormMapper tPreDiagnosisFormMapper, RecordDiaStrategyFactory recordDiaStrategyFactory,
                                TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper,
                                TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper) {
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
        this.recordDiaStrategyFactory = recordDiaStrategyFactory;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.tPreDiagnosisOptionMapper = tPreDiagnosisOptionMapper;
    }

    /**
     * 查看预诊单详情
     *
     * @param diaId
     */
    public LookPaperRecive getLookPaper(String diaId) {
        LookPaperRecive lookPaper = tPreDiagnosisFormMapper.getLookPaper(diaId);
        //预诊并且是专科
        if (lookPaper != null) {
            List<TPreDiagnosisDisMappingV> list = tPreDiagnosisFormMapper.getPaperMappingListByDiaId(diaId);
            ArrayList<DisRe> west = new ArrayList<>();
            ArrayList<DisRe> chinese = new ArrayList<>();
            for (TPreDiagnosisDisMappingV disMapping : list) {
                DisRe re = new DisRe();
                re.setId(disMapping.getDisId());
                re.setName(disMapping.getDisName());
                if ("1".equals(disMapping.getDisType())) {
                    chinese.add(re);
                } else {
                    west.add(re);
                }
            }
            if (Constant.BASIC_STRING_TWO.equals(lookPaper.getFormType())) {
                lookPaper.setChineseDisDiagnosisList(chinese);
            } else {
                lookPaper.setWestDis(west);
                lookPaper.setChineseDis(chinese);
            }
        }

        if (lookPaper != null) {


            if (Constant.BASIC_STRING_ONE.equals(lookPaper.getFormType())) {
                String a = "doctorfollowup?yuzhen=1&diaId=" + lookPaper.getDiaId();
                String temp = null;
                try {
                    temp = URLEncoder.encode(a, "UTF-8");
                    String congbao = url + "?jumpUrl=" + temp;
                    String encode = URLEncoder.encode(congbao, "UTF-8");
                    String hislogin = loginUrl + "?congbaoUrl=" + encode;
//HIS互联网url?congbaoUrl=使用urlEnCode后的聪宝url
//his取congbaoUrl值 urlDecode该值 后 拼接&cbdata=his生成用户登录信息
                    lookPaper.setHisDiagnosisFormUrl(hislogin);
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            } else {
                String a = "doctorfollowup?diaId=" + lookPaper.getDiaId();
                String temp = null;
                try {
                    temp = URLEncoder.encode(a, "UTF-8");
                    String congbao = url + "?jumpUrl=" + temp;
                    String encode = URLEncoder.encode(congbao, "UTF-8");
                    String hislogin = loginUrl + "?congbaoUrl=" + encode;

                    lookPaper.setHisDiagnosisFormUrl(hislogin);
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            }

        }

        return lookPaper;
    }

    public Object getPaperList(PaperListReVo paperListReVo, Page page) {
        List<AdminRule> roles = AdminWebUtils.getCurrentHr().getRoles();
        PageHelper.startPage(page.getPage(), page.getLimit());
        if (Constant.BASIC_STRING_ONE.equals(paperListReVo.getFormType())) {
            HashMap<String, String> params = new HashMap<>();
            params.put("disId", paperListReVo.getDisId());
            params.put("userId", AdminWebUtils.getCurrentHr().getUserId());


            for (AdminRule role : roles) {
                boolean v = role.getRoleName().contains("管理");
                if (v) {
                    params.remove("userId");
                    params.put("userId", "");
                    break;
                }
            }

            List<TPreDiagnosisForm> list = tPreDiagnosisFormMapper.getPaperList(params);
            for (TPreDiagnosisForm tPreDiagnosisForm : list) {

                String westDisName = tPreDiagnosisForm.getWestDisName();
                String chineseDisName = tPreDiagnosisForm.getChineseDisName();
                if (StringUtils.isNotBlank(westDisName)) {
                    tPreDiagnosisForm.setWestDisName(westDisName.substring(0, westDisName.length() - 1));
                }
                if (StringUtils.isNotBlank(chineseDisName)) {
                    tPreDiagnosisForm.setChineseDisName(chineseDisName.substring(0, chineseDisName.length() - 1));
                }
                String a = "doctorfollowup?diaId=" + tPreDiagnosisForm.getDiaId() + "&yuzhen=1" + "&diaType=" + tPreDiagnosisForm.getDiaType();
                String temp = null;
                try {
                    temp = URLEncoder.encode(a, "UTF-8");
                    String congbao = url + "?jumpUrl=" + temp;
                    String encode = URLEncoder.encode(congbao, "UTF-8");
                    String hislogin = loginUrl + "?congbaoUrl=" + encode;

                    tPreDiagnosisForm.setHisDiagnosisFormUrl(hislogin);
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }

            }
            PageHelper.clearPage();
            return Page.getLayUiTablePageData(list);
        } else {


            GetSuiFangPaperList getSuiFangPaperList = new GetSuiFangPaperList();
            getSuiFangPaperList.setFormName(paperListReVo.getFormName());
            getSuiFangPaperList.setDiaType(paperListReVo.getDiaType());
            getSuiFangPaperList.setShowStatus(paperListReVo.getShowStatus());
            if (StringUtils.isNotBlank(paperListReVo.getSroadIdsstr())){
                getSuiFangPaperList.setSroadIds(paperListReVo.getSroadIdsstr().split(","));
            }

            getSuiFangPaperList.setUserId(AdminWebUtils.getCurrentHr().getUserId());
            for (AdminRule role : roles) {
                boolean v = role.getRoleName().contains("管理");
                if (v) {
                    getSuiFangPaperList.setUserId(null);
                    break;
                }
            }

            List<TPreDiagnosisForm> list = tPreDiagnosisFormMapper.getSuiFangPaperList(getSuiFangPaperList);

            for (TPreDiagnosisForm tPreDiagnosisForm : list) {
                tPreDiagnosisForm.setRoadExecuteEventWay(Constant.BASIC_STRING_FOUR);
                tPreDiagnosisForm.setTaskExcuteStatus(Integer.valueOf(Constant.BASIC_STRING_EIGHT));
                String a = "doctorfollowup?diaId=" + tPreDiagnosisForm.getDiaId();
//                String a = "doctorfollowup?diaId="+tPreDiagnosisForm.getDiaId();
                String temp = null;
                try {
                    temp = URLEncoder.encode(a, "UTF-8");
                    String congbao = url + "?jumpUrl=" + temp;
                    String encode = URLEncoder.encode(congbao, "UTF-8");
                    String hislogin = loginUrl + "?congbaoUrl=" + encode;

                    tPreDiagnosisForm.setHisDiagnosisFormUrl(hislogin);
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            }
            PageHelper.clearPage();
            return Page.getLayUiTablePageData(list);
        }

    }


    public TPreDiagnosisForm getdetailById(String diaId) {
         return tPreDiagnosisFormMapper.getObjectById(diaId);
    }
}
