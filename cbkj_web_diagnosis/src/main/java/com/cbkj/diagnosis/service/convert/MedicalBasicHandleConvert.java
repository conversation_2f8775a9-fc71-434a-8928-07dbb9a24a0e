package com.cbkj.diagnosis.service.convert;


import com.cbkj.diagnosis.beans.monitor.dto.*;
import com.cbkj.diagnosis.beans.response.MedicalBasicHandleRecordResponse;

/**
 * Created by zbh on 2024/6/7 9:58
 *
 * @description：处置信息转换器
 */
public class MedicalBasicHandleConvert {
    public static DisposalRecordDTO disposalRecordConvertDTO(MedicalBasicHandleRecordResponse response) {
        if( null == response){
            return null;
        }
        DisposalRecordDTO dto = new DisposalRecordDTO();
        dto.setDiagnosticNo(response.getDiagnosticNo());
        dto.setEleAppForm(response.getEleAppForm());
        dto.setDeptName(response.getDeptName());
        dto.setPatientAge(response.getPatientAge());
        dto.setWeight(response.getWeight());

        dto.setAllergyHistory(response.getAllergyHistory());
        dto.setAllergicDrug(response.getAllergicDrug());


        return dto;
    }

    public static WesternMedicineTreatmentDTO westernMedicineTreatmentConvertDTO(MedicalBasicHandleRecordResponse response) {
        if( null == response){
            return null;
        }
        WesternMedicineTreatmentDTO dto = new WesternMedicineTreatmentDTO();
        dto.setPrescribingDoctorSign(response.getPrescribingDoctorSign());
        dto.setExecutiveDoctorSign(response.getExecutiveDoctorSign());

        return dto;
    }

    public static WesternMedicineTreatmentItemDTO westernMedicineTreatmentItemConvertDTO(MedicalBasicHandleRecordResponse response) {
        if( null == response){
            return null;
        }
        WesternMedicineTreatmentItemDTO dto = new WesternMedicineTreatmentItemDTO();
        dto.setOperationCode(response.getOperationCode());
        dto.setOperationName(response.getOperationName());
        dto.setOperationMethod(response.getOperationMethod());
        dto.setOperationTime(response.getOperationTime());
        dto.setOperationTarget(response.getOperationTarget());
        dto.setOperationNumber(response.getOperationNumber());

        return dto;
    }

    public static TCMTreatmentDTO tCMTreatmentConvertDTO(MedicalBasicHandleRecordResponse response) {
        if( null == response){
            return null;
        }
        TCMTreatmentDTO dto = new TCMTreatmentDTO();
        dto.setPrescribingDoctorSign(response.getPrescribingDoctorSign());
        dto.setExecutiveDoctorSign(response.getExecutiveDoctorSign());
        dto.setMemo(response.getMemo());

        return dto;
    }

    public static TCMItemTreatmentDTO tCMTreatmentItemConvertDTO(MedicalBasicHandleRecordResponse response) {
        if( null == response){
            return null;
        }
        TCMItemTreatmentDTO dto = new TCMItemTreatmentDTO();
        dto.setOperationCode(response.getOperationCode());
        dto.setOperationName(response.getOperationName());
        dto.setOperationMethod(response.getOperationMethod());
        dto.setOperationTime(response.getOperationTime());
        dto.setOperationTarget(response.getOperationTarget());
        dto.setOperationNumber(response.getOperationNumber());

        return dto;
    }
}
