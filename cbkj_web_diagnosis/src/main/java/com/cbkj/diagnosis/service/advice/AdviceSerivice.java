package com.cbkj.diagnosis.service.advice;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TBusinessProposal;
import com.cbkj.diagnosis.beans.business.adviceVo.*;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.TBusinessAnnexMapper;
import com.cbkj.diagnosis.mapper.business.TBusinessProposalMapper;
import com.cbkj.diagnosis.service.business.impl.TBusinessProposalServiceImpl;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/2 17:29
 * @Version 1.0
 */
@Service
public class AdviceSerivice {
    private final TBusinessProposalServiceImpl proposalService;
    private final TBusinessProposalMapper tBusinessProposalMapper;

    private final TBusinessAnnexMapper tBusinessAnnexMapper;

    public AdviceSerivice(TBusinessProposalServiceImpl proposalService, TBusinessProposalMapper tBusinessProposalMapper, TBusinessAnnexMapper tBusinessAnnexMapper) {
        this.proposalService = proposalService;
        this.tBusinessProposalMapper = tBusinessProposalMapper;
        this.tBusinessAnnexMapper = tBusinessAnnexMapper;
    }

    public Object getAdviceList(com.cbkj.diagnosis.common.utils.Page page, GetAdviceListReq getAdviceListReq, String userId) {
        TBusinessProposal tBusinessProposalService = new TBusinessProposal();
        if(StringUtils.isNotBlank(getAdviceListReq.getProposalReceiveState())){
            if (getAdviceListReq.getProposalReceiveState().length()>=3){
                tBusinessProposalService.setProposalReceiveState(null);
            }else {
                tBusinessProposalService.setProposalReceiveState(Integer.valueOf(getAdviceListReq.getProposalReceiveState()));
            }
        }

        PageHelper.startPage(page.getPage(), page.getLimit());
        if (StringUtils.isNotBlank(userId)) {
            tBusinessProposalService.setCreateUserId(userId);
        }
        List<GetAdviceListRes> list = tBusinessProposalMapper.getAdviceList(tBusinessProposalService);
        PageHelper.clearPage();
        for (GetAdviceListRes getAdviceListRes : list) {
            getAdviceListRes.setAdviceImages(tBusinessProposalMapper.getAdviceImagesList(String.valueOf(getAdviceListRes.getId())) );
        }
        return Page.getResEntityPageData(list);
    }

    public TBusinessProposalRes getAdviceDetail(Long id, String userId) {
        if (id == null) {
            return null;
        }
        QueryWrapper<TBusinessProposal> tBusinessProposalQueryWrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(userId)) {
            tBusinessProposalQueryWrapper.eq("create_user_id", userId);
        }
        tBusinessProposalQueryWrapper.eq("id", id);
        TBusinessProposal one = proposalService.getOne(tBusinessProposalQueryWrapper);
        if (one != null) {
            TBusinessProposalRes tBusinessProposalRes = new TBusinessProposalRes();
            BeanUtils.copyProperties(one, tBusinessProposalRes);
            List<ImageUploadRes> imagesListByProposalId = tBusinessAnnexMapper.getImagesListByProposalId(String.valueOf(one.getId()));
            tBusinessProposalRes.setAdviceImages(imagesListByProposalId);
            return tBusinessProposalRes;
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteAdvice(Long id, String userId) {
        QueryWrapper<TBusinessProposal> tBusinessProposalQueryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(userId)) {
            tBusinessProposalQueryWrapper.eq("create_user_id", userId);
        }
        tBusinessProposalQueryWrapper.eq("id", id);
        TBusinessProposal tBusinessProposal = new TBusinessProposal();
        tBusinessProposal.setIsDel(true);
        tBusinessProposal.setDeleteTime(new Date());
        tBusinessProposal.setDeleteUserId(AdminWebUtils.getCurrentHr().getUserId());
        tBusinessProposalMapper.update(tBusinessProposal, tBusinessProposalQueryWrapper);
    }
    @Transactional(rollbackFor = Exception.class)
    public void fixAdvice(FixAdvice fixAdvice, String userId) {
        QueryWrapper<TBusinessProposal> tBusinessProposalQueryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(userId)) {
            tBusinessProposalQueryWrapper.eq("create_user_id", userId);
        }
        tBusinessProposalQueryWrapper.eq("id", fixAdvice.getId());
        TBusinessProposal tBusinessProposal1 = tBusinessProposalMapper.selectById(fixAdvice.getId());

//        TBusinessProposal tBusinessProposal = new TBusinessProposal();
        tBusinessProposal1.setProposalReceiveState(0);
        tBusinessProposal1.setProposalReceiveOpinion(fixAdvice.getProposalReceiveOpinion());
        tBusinessProposal1.setProposalReceiveName(AdminWebUtils.getCurrentHr().getNameZh());
        tBusinessProposal1.setProposalReceiveId(AdminWebUtils.getCurrentHr().getUserId());

        tBusinessProposalMapper.updateById(tBusinessProposal1);
    }
}
