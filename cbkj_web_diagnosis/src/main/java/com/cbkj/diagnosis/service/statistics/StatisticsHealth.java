package com.cbkj.diagnosis.service.statistics;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.TPropagandaEduDisMapping;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.service.business.impl.TPropagandaEduDisMappingServiceImpl;
import com.cbkj.diagnosis.service.statistics.impl.StatisticsErverDayHealthServiceImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/31 13:29
 * @Version 1.0
 */
@Service
public class StatisticsHealth {
    private final StatisticsErverDayHealthServiceImpl statisticsErverDayHealthService;

    private final TPropagandaEduDisMappingServiceImpl tPropagandaEduDisMappingService;

    private final TAdminInfoMapper tAdminInfoMapper;

    public StatisticsHealth(StatisticsErverDayHealthServiceImpl statisticsErverDayHealthService, TPropagandaEduDisMappingServiceImpl tPropagandaEduDisMappingService, TAdminInfoMapper tAdminInfoMapper) {
        this.statisticsErverDayHealthService = statisticsErverDayHealthService;
        this.tPropagandaEduDisMappingService = tPropagandaEduDisMappingService;
        this.tAdminInfoMapper = tAdminInfoMapper;
    }

    @Async
    public void writeReadFromRedis(Integer tPropagandaEduId,String appId,String insCode ,String insId,String insName) {
        QueryWrapper<TPropagandaEduDisMapping> wrapper = new QueryWrapper<>();
        wrapper.eq("t_propaganda_edu_id", tPropagandaEduId).eq("dis_type","1");
        List<TPropagandaEduDisMapping> list = tPropagandaEduDisMappingService.list(wrapper);
        if (list != null && !list.isEmpty()) {
            ArrayList<StatisticsErverDayHealth> statisticsErverDayHealths = new ArrayList<>();
            for (TPropagandaEduDisMapping tPropagandaEduDisMapping : list) {
                StatisticsErverDayHealth health = getHealth(tPropagandaEduDisMapping, appId, insCode, insId, insName);
                statisticsErverDayHealths.add(health);
            }
            statisticsErverDayHealthService.writeReadFromRedis(statisticsErverDayHealths);
        }

    }

    private static StatisticsErverDayHealth getHealth(TPropagandaEduDisMapping tPropagandaEduDisMapping, String appId,String insCode ,String insId,String insName) {
        StatisticsErverDayHealth health = new StatisticsErverDayHealth();
        health.setDisCode(tPropagandaEduDisMapping.getDisCode());
        health.setDisName(tPropagandaEduDisMapping.getDisName());
        health.setDisId(tPropagandaEduDisMapping.getDisId());
        health.setAppId(appId);
        health.setInsCode(insCode);
        health.setInsId(insId);
        health.setInsName(insName);
        health.setCreateTime(new Date());
        return health;
    }

    @Async
    public void writeReadFromRedisMaps( ArrayList<String> staticsEduEduId,String appId,String insCode ,String insId,String insName) {
        ArrayList<StatisticsErverDayHealth> statisticsErverDayHealths = new ArrayList<>();
        if (staticsEduEduId != null && !staticsEduEduId.isEmpty()){
            List<TPropagandaEduDisMapping> list = tPropagandaEduDisMappingService.listByList(staticsEduEduId);
            for (TPropagandaEduDisMapping tPropagandaEduDisMapping : list) {
                StatisticsErverDayHealth health = getHealth(tPropagandaEduDisMapping, appId, insCode, insId, insName);
                statisticsErverDayHealths.add(health);
            }
            statisticsErverDayHealthService.writeReadFromRedis(statisticsErverDayHealths);
        }

//        for (int i = 0; i < staticsEduEduId.size(); i++) {
//            QueryWrapper<TPropagandaEduDisMapping> wrapper = new QueryWrapper<>();
//            wrapper.eq("t_propaganda_edu_id", staticsEduEduId.get(i));
//            List<TPropagandaEduDisMapping> list = tPropagandaEduDisMappingService.list(wrapper);
//            for (int i1 = 0; i1 < list.size(); i1++) {
//                StatisticsErverDayHealth health = getHealth(list.get(i1), tAdminInfoMapper.getTAdminInfoById(staticsEduPatient.get(i)));
//                statisticsErverDayHealths.add(health);
//            }
//
//        }

    }
}
