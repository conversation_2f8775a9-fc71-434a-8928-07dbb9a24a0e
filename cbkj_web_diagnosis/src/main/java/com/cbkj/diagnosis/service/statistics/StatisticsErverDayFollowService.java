package com.cbkj.diagnosis.service.statistics;

import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayFollow;
import com.cbkj.diagnosis.common.utils.*;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsPhoneMapper;
import com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayFollowMapper;
import com.cbkj.diagnosis.beans.statistics.PastSevenDaysRe;
import com.cbkj.diagnosis.service.webapi.business.vo.YesterDaySuiFangFinishPeopleNumbSearch;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class StatisticsErverDayFollowService {


    private final StatisticsErverDayFollowMapper statisticsErverDayFollowMapper;
    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;

    public StatisticsErverDayFollowService(StatisticsErverDayFollowMapper statisticsErverDayFollowMapper, SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper) {
        this.statisticsErverDayFollowMapper = statisticsErverDayFollowMapper;
        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
    }

    public Object getPastSevenDays() {

        boolean containsSearchKey = AdminWebUtils.containsSearchKey("all");
        String userId = AdminWebUtils.getCurrentHr().getUserId();
        if (containsSearchKey) {
            userId = null;
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        PastSevenDaysRe pastSevenDaysRe = new PastSevenDaysRe();
        pastSevenDaysRe.setDoctorId(userId);
        pastSevenDaysRe.setStartDate(simpleDateFormat.format(DateUtil.getNextDate(-7)));
        pastSevenDaysRe.setStartDate(simpleDateFormat.format(new Date()));
        List<StatisticsErverDayFollow> list = statisticsErverDayFollowMapper.getPastSevenDays(pastSevenDaysRe);

        for (int i = 1; i < 8; i++) {
            SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            YesterDaySuiFangFinishPeopleNumbSearch yesterDaySuiFangFinishPeopleNumbSearch = new YesterDaySuiFangFinishPeopleNumbSearch();
            yesterDaySuiFangFinishPeopleNumbSearch.setDoctorId(userId);
            yesterDaySuiFangFinishPeopleNumbSearch.setPhoneStatus(8);
            // 0 -1 -2 -3 -4 -5 -6
            //-6 -5 -4 -3 -2 -1 0
            yesterDaySuiFangFinishPeopleNumbSearch.setStartDate(simpleDateFormat2.format(DateUtil.getNextDate( i-7) ));
            //1  0  -1 -2 -3 -4 -5
            yesterDaySuiFangFinishPeopleNumbSearch.setEndDate(simpleDateFormat2.format(DateUtil.getNextDate( i - 6  )));
            Long a = sRoadTaskPatientsPhoneMapper.getYesterDaySuiFangFinishPeopleNumb2(yesterDaySuiFangFinishPeopleNumbSearch);

            StatisticsErverDayFollow statisticsErverDayFollow = new StatisticsErverDayFollow();
            statisticsErverDayFollow.setCreateTime((DateUtil.getNextDate(i-7)));
            statisticsErverDayFollow.setNum(Math.toIntExact(a));



            list.add(statisticsErverDayFollow);
        }

        return list;
    }
}
