package com.cbkj.diagnosis.service.convert;


import com.cbkj.diagnosis.beans.monitor.vo.ResourceListVO;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.monitor.dto.*;
import com.cbkj.diagnosis.beans.request.ResourceListRequest;
import com.cbkj.diagnosis.common.enums.FlagEnum;
import com.cbkj.diagnosis.common.enums.SexEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.List;

/**
 * Created by zbh on 2024/6/6 13:36
 *
 * @description：就诊记录转换器
 */
public class MedicalRecordsConvert {
    public static MedicalRecordsDTO medicalRecordsConvertToDTO(MedicalRecords records) {
        if(null == records){
            return null;
        }
        MedicalRecordsDTO dto = new MedicalRecordsDTO();
        dto.setInsName(records.getInsName());
        dto.setDeptName(records.getDeptName());
        dto.setDoctorName(records.getDoctorName());
        dto.setRecordTime(records.getRecordTime());
        dto.setPatientAge(records.getPatientAge());
        dto.setPatientSex(StringUtils.isNotEmpty(records.getPatientSex()) ? Enum.valueOf(SexEnum.class, records.getPatientSex()).getMessage():null );
        dto.setIsFirstVisit(StringUtils.isNotEmpty(records.getInitialDiagnosisCode())? FlagEnum.getMassage( records.getInitialDiagnosisCode()):null);
        if (StringUtils.isBlank(records.getDiagnosticNo())){
            dto.setDiagnosticNo(records.getVisitNo());
        }else {
            dto.setDiagnosticNo(records.getDiagnosticNo());
        }



        dto.setIsPregnant(StringUtils.isNotEmpty(records.getPregnancyFlag())?FlagEnum.getMassage(records.getPregnancyFlag()) :null);
        dto.setIsLactationPeriod(StringUtils.isNotEmpty(records.getSucklingPeriodFlag())? FlagEnum.getMassage( records.getSucklingPeriodFlag()) :null);
        dto.setSolarTerm(records.getOnsetSolarTermCode());
        dto.setTreatmentCategory(records.getTreatmentCategoryCode());
        dto.setIsInfectiousHistory(StringUtils.isNotEmpty(records.getInfectiousHistoryFlag())?  FlagEnum.getMassage( records.getInfectiousHistoryFlag()):null);
        dto.setScientificRecord(StringUtils.isNotEmpty(records.getScientificResearchFlag())?  FlagEnum.getMassage(records.getScientificResearchFlag()):null);


        //病史信息
        MedicalHistoryDTO medicalHistoryDTO = medicalHistoryConvertToDTO(records);
        dto.setMedicalHistoryDTO(medicalHistoryDTO);

        //中医四诊信息
        TCMDiagnosticMethodsDTO tcmDiagnosticMethodsDTO = tcmDiagnosticMethodsConvertToDTO(records);
        dto.setTcmDiagnosticMethodsDTO(tcmDiagnosticMethodsDTO);

        //中医四诊信息
        DialecticalAnalysisDTO dialecticalAnalysisDTO = dialecticalAnalysisConvertToDTO(records);
        dto.setDialecticalAnalysisDTO(dialecticalAnalysisDTO);

        //辅助检查信息
        AuxiliaryInspectionDTO auxiliaryInspectionDTO = auxiliaryInspectionConvertToDTO(records);
        dto.setAuxiliaryInspectionDTO(auxiliaryInspectionDTO);

        //治疗建议信息
        TreatmentSuggestionDTO treatmentSuggestionDTO = treatmentSuggestionConvertToDTO(records);
        dto.setTreatmentSuggestionDTO(treatmentSuggestionDTO);

        dto.setDoctorSign(records.getYiShiSign());

        return dto;
    }




    public static MedicalHistoryDTO medicalHistoryConvertToDTO(MedicalRecords records) {
        if(null == records){
            return null;
        }
        MedicalHistoryDTO dto = new MedicalHistoryDTO();
        BeanUtils.copyProperties(records,dto);

        return dto;
    }


    public static TCMDiagnosticMethodsDTO tcmDiagnosticMethodsConvertToDTO(MedicalRecords records) {
        if(null == records){
            return null;
        }
        TCMDiagnosticMethodsDTO dto = new TCMDiagnosticMethodsDTO();
        BeanUtils.copyProperties(records,dto);


        return dto;
    }
    public static DialecticalAnalysisDTO dialecticalAnalysisConvertToDTO(MedicalRecords records) {
        if(null == records){
            return null;
        }
        DialecticalAnalysisDTO dto = new DialecticalAnalysisDTO();
        BeanUtils.copyProperties(records,dto);
        return dto;
    }

    private static AuxiliaryInspectionDTO auxiliaryInspectionConvertToDTO(MedicalRecords records) {
        if(null == records){
            return null;
        }
        AuxiliaryInspectionDTO dto = new AuxiliaryInspectionDTO();
        dto.setAuxiliaryInspectionItems(records.getAuxiliaryInspectionItems());
        dto.setAuxiliaryInspectionResults(records.getAuxiliaryInspectionResults());

        return dto;
    }

    private static TreatmentSuggestionDTO treatmentSuggestionConvertToDTO(MedicalRecords records) {
        if(null == records){
            return null;
        }
        TreatmentSuggestionDTO dto = new TreatmentSuggestionDTO();
        BeanUtils.copyProperties(records,dto);
        return dto;
    }


    public static ResourceListVO resourceListRequestConvertToVO(ResourceListRequest request) {
        if(null == request){
            return null;
        }
        ResourceListVO vo = new ResourceListVO();
        BeanUtils.copyProperties(request,vo);
        if(StringUtils.isNotEmpty(request.getChineseDisIds())){
            String[] chineseDisIdArray = request.getChineseDisIds().split(",");
            List<String> chineseDisIdList = Arrays.asList(chineseDisIdArray);
            vo.setChineseDisIds(chineseDisIdList);
        }

        if(StringUtils.isNotEmpty(request.getInsCodes())){
            String[] insCodeArray = request.getInsCodes().split(",");
            List<String> insCodeList = Arrays.asList(insCodeArray);
            vo.setInsCodes(insCodeList);
        }

        if(StringUtils.isNotEmpty(request.getRecordTypeCode())){
            String[] recordTypeCodeArray = request.getRecordTypeCode().split(",");
            for (int i = 0; i < recordTypeCodeArray.length; i++) {
                String recordTypeCode = recordTypeCodeArray[i];
                if ("1".equals(recordTypeCode)) {
                    vo.setRecordTypeCode1(recordTypeCode);
                }
                if ("2".equals(recordTypeCode)) {
                    vo.setRecordTypeCode2(recordTypeCode);
                }
                if ("3".equals(recordTypeCode)) {
                    vo.setRecordTypeCode3(recordTypeCode);
                }
                if ("4".equals(recordTypeCode)) {
                    vo.setRecordTypeCode4(recordTypeCode);
                }
            }
        }else {
            vo.setRecordTypeCode1("1");
            vo.setRecordTypeCode2("2");
            vo.setRecordTypeCode3("3");
            vo.setRecordTypeCode4("4");
        }
        return vo;
    }

}
