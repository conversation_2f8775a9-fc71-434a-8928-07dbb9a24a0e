package com.cbkj.diagnosis.service.sysService;

import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.mapper.sysmapper.AdminInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class AdminService implements UserDetailsService {

    @Autowired
    private AdminInfoMapper adminInfoMapper;

    /**
     * 根据用户名加载用户信息
     * 用户名唯一 使用手机号
     *
     * <AUTHOR> @date
     */
    @Override
    public AdminInfo loadUserByUsername(String username) throws UsernameNotFoundException {
        AdminInfo admin = adminInfoMapper.loadUserByUsername(username);
        return loadUser(admin);
    }

    public AdminInfo loadUser(AdminInfo admin) {
        if (null == admin) {
            throw new UsernameNotFoundException("用户不存在");
        }
        if (admin.getExpireDate() != null && admin.getExpireDate().before(new Date())) {
            throw new UsernameNotFoundException("用户已过期");
        }
        return admin;
    }
}
