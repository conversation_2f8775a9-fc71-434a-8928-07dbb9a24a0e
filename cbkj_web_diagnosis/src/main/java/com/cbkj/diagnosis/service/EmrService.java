package com.cbkj.diagnosis.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.diagnosisstructure.EmrContent;
import com.cbkj.diagnosis.beans.diagnosisstructure.EmrItem;
import com.cbkj.diagnosis.beans.diagnosisstructure.EmrList;
import com.cbkj.diagnosis.beans.diagnosisstructure.Paragraph;
import com.cbkj.diagnosis.beans.emr.EmrOptionStructure;
import com.cbkj.diagnosis.beans.emr.EmrQuestionStructure;
import com.cbkj.diagnosis.beans.emr.SaveOptionList;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.service.business.DiagnosisStructureUtils;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisStructureChildServiceImpl;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisStructureContentServiceImpl;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisStructureServiceImpl;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/16 09:37
 * @Version 1.0
 */
@Service
public class EmrService {
    private final TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;
    private final TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;

    private final DiagnosisStructureUtils diagnosisStructureUtils;

    private final TPreDiagnosisStructureServiceImpl tPreDiagnosisStructureService;
    private final TPreDiagnosisStructureContentServiceImpl tPreDiagnosisStructureContentService;
    private final TPreDiagnosisStructureChildServiceImpl tPreDiagnosisStructureChildService;


    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;

    public EmrService(TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper, TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper, DiagnosisStructureUtils diagnosisStructureUtils, TPreDiagnosisStructureServiceImpl tPreDiagnosisStructureService, TPreDiagnosisStructureContentServiceImpl tPreDiagnosisStructureContentService, TPreDiagnosisStructureChildServiceImpl tPreDiagnosisStructureChildService) {
        this.tPreDiagnosisOptionMapper = tPreDiagnosisOptionMapper;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.diagnosisStructureUtils = diagnosisStructureUtils;
        this.tPreDiagnosisStructureService = tPreDiagnosisStructureService;
        this.tPreDiagnosisStructureContentService = tPreDiagnosisStructureContentService;
        this.tPreDiagnosisStructureChildService = tPreDiagnosisStructureChildService;
    }

    @Transactional(rollbackFor = Exception.class)
    public Object savePageEmr(EmrList emrLists) {
        if (emrLists == null) {
            return ResEntity.error("参数不能为空");
        }
        String diaId = emrLists.getDiaId();
        if (StringUtils.isBlank(diaId)) {
            return ResEntity.error("参数diaId不能为空");
        }
        List<EmrItem> emrItems = emrLists.getEmrItems();
        if (emrItems == null) {
            return ResEntity.error("参数emrItems不能为空");
        }
        //todo 需要删除t_pre_diagnosis_structure、t_pre_diagnosis_structure_child、t_pre_diagnosis_structure_content 表对应数据用diaId关联
        tPreDiagnosisStructureChildService.deleteByDiaId(diaId);
        tPreDiagnosisStructureContentService.deleteByDiaId(diaId);
        QueryWrapper<TPreDiagnosisStructure> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dia_id", diaId);
        tPreDiagnosisStructureService.remove(queryWrapper);
        ArrayList<TPreDiagnosisStructure> structures = new ArrayList<>();
        //重新插数据。

        for (int i = 0; i < emrItems.size(); i++) {
            EmrItem emrItem = emrItems.get(i);
            if (emrItem == null) {
                continue;
            }
            TPreDiagnosisStructure tPreDiagnosisStructure = new TPreDiagnosisStructure();
            tPreDiagnosisStructure.setDiagnosisStructureId(IdUtil.getSnowflake(snowflake).nextIdStr());
            tPreDiagnosisStructure.setEmrType(emrItem.getEmrType());
            tPreDiagnosisStructure.setSort(emrItem.getSort() == null ? i : emrItem.getSort());
            tPreDiagnosisStructure.setTypeCode(emrItem.getTypeCode());
            tPreDiagnosisStructure.setDiaId(diaId);
            emrItem.setDiagnosisStructureId(tPreDiagnosisStructure.getDiagnosisStructureId());
            structures.add(tPreDiagnosisStructure);
        }
        if (!structures.isEmpty()) {
            tPreDiagnosisStructureService.saveBatch(structures);
        }
        ArrayList<TPreDiagnosisStructureContent> emrContents = new ArrayList<>();

        for (EmrItem emrItem : emrItems) {
            List<EmrContent> emrContent = emrItem.getEmrContent();
            if (!emrContent.isEmpty()) {
                for (EmrContent emrContent1 : emrContent) {
                    TPreDiagnosisStructureContent content = new TPreDiagnosisStructureContent();
                    content.setDiagnosisStructureId(emrItem.getDiagnosisStructureId());
                    emrContents.add(content);
                }
            }
        }

        if (!emrContents.isEmpty()) {
            //保存中间表
            tPreDiagnosisStructureContentService.saveBatch(emrContents);
            int temp = 0;
            ArrayList<TPreDiagnosisStructureChild> tPreDiagnosisStructureChildNewList = new ArrayList<>();
            for (EmrItem emrItem : emrItems) {
                List<EmrContent> emrContent = emrItem.getEmrContent();
                if (!emrContent.isEmpty()) {
                    for (int i = 0; i < emrContent.size(); i++) {
                        EmrContent emrContent1 = emrContent.get(i);
                        emrContent1.setStructureContentId(emrContents.get(temp).getStructureContentId());
                        temp++;
                        List<Paragraph> paragraph = emrContent1.getParagraph();
                        if (paragraph != null && !paragraph.isEmpty()) {

                            for (int i1 = 0; i1 < paragraph.size(); i1++) {
                                Paragraph paragraphNew = paragraph.get(i1);
                                TPreDiagnosisStructureChild child = new TPreDiagnosisStructureChild();
                                child.setContent(paragraphNew.getContent());
                                child.setContentType(paragraphNew.getContentType());
                                child.setParagraphSort(paragraphNew.getParagraphSort());
                                child.setStructureContentId(emrContent1.getStructureContentId());
                                child.setQuestionIsDel(0);
                                tPreDiagnosisStructureChildNewList.add(child);
                            }

                        }

                    }


                }
            }
            if (!tPreDiagnosisStructureChildNewList.isEmpty()) {
                tPreDiagnosisStructureChildService.saveBatch(tPreDiagnosisStructureChildNewList);
            }

        }


        return ResEntity.success(emrLists);
    }

    public Object getPageEmr(String diaId) {
        EmrList diagnosisStructure = diagnosisStructureUtils.getDiagnosisStructure(diaId);
        //
        if (diagnosisStructure == null){
            //返回一个默认的
            EmrList emrList = new EmrList();
            emrList.setDiaId(diaId);
            ArrayList<EmrItem> emrItems = new ArrayList<>();
            EmrItem emrItem = new EmrItem();
            emrItem.setTypeCode("1");
            emrItem.setSort(0);
            emrItem.setEmrType("主诉");
            emrItem.setEmrContent(new ArrayList<EmrContent>());
            emrItems.add(emrItem);
            emrList.setEmrItems(emrItems);


            return ResEntity.success(JSON.toJSON(emrList));
        }
        diagnosisStructure.setDiaId(diaId);
        return ResEntity.success(JSON.toJSON(diagnosisStructure));
    }

    /**
     * 获取单选、多选的选项转化术语信息
     *
     * @param questionId
     */
    public ResEntity getOptionList(String questionId) {
        if (questionId == null) {
            return ResEntity.error("参数questionId不能为空");
        }
        TPreDiagnosisQuestion objectById = tPreDiagnosisQuestionMapper.getObjectById(questionId);
        if (objectById == null) {
            return ResEntity.error("问题不存在");
        }
        EmrQuestionStructure emrQuestionStructure = new EmrQuestionStructure();
        if (
                !(Constant.BASIC_STRING_THREE.equals(objectById.getQuestionType()) || Constant.BASIC_STRING_TWO.equals(objectById.getQuestionType())
                )
        ) {
            //return ResEntity.error("非单选、多选，无法获取选项转化术语信息");
        }else {
            TPreDiagnosisOption tPreDiagnosisOption = new TPreDiagnosisOption();
            tPreDiagnosisOption.setQuestionId(Integer.parseInt(questionId));
            List<TPreDiagnosisOption> pageListByObj = tPreDiagnosisOptionMapper.getPageListByObj(tPreDiagnosisOption);

            ArrayList<EmrOptionStructure> emrOptionStructures = new ArrayList<>();
            if (!pageListByObj.isEmpty()) {
                pageListByObj.forEach(tPreDiagnosisOption1 -> {
                    EmrOptionStructure emrOptionStructure = new EmrOptionStructure();
                    emrOptionStructure.setOptionId(tPreDiagnosisOption1.getOptionId());
                    emrOptionStructure.setOptionStructureSaveBlank(tPreDiagnosisOption1.getOptionStructureSaveBlank());
                    emrOptionStructure.setOptionStructureValue(tPreDiagnosisOption1.getOptionStructureValue());
                    emrOptionStructure.setOptionName(tPreDiagnosisOption1.getOptionName());
                    emrOptionStructure.setOptionFillBlank(tPreDiagnosisOption1.getOptionFillBlank());
                    emrOptionStructure.setOptionStructureSavePatient(tPreDiagnosisOption1.getOptionStructureSavePatient());
                    emrOptionStructures.add(emrOptionStructure);
                });
            }
            emrQuestionStructure.setEmrOptionStructure(emrOptionStructures);
        }

        BeanUtils.copyProperties(objectById, emrQuestionStructure);
        emrQuestionStructure.setQuestionNumber(emrQuestionStructure.getQuestionNumber() + 1);
        return ResEntity.success(emrQuestionStructure);
    }

    /**
     * 保存单选、多选的选项转化术语信息
     *
     * @param emrOptionStructure
     * @return
     */
    public Object saveOptionList(SaveOptionList emrOptionStructure) {
        if (emrOptionStructure == null) {
            return ResEntity.error("参数不能为空");
        }
        if (emrOptionStructure.getEmrOptionStructure() == null || emrOptionStructure.getEmrOptionStructure().isEmpty()) {
            return ResEntity.error("参数不能为空");
        }
        ArrayList<TPreDiagnosisOption> tPreDiagnosisOptionsList = new ArrayList<>();
        for (EmrOptionStructure emrOptionStructure1 : emrOptionStructure.getEmrOptionStructure()) {
            TPreDiagnosisOption tPreDiagnosisOption = new TPreDiagnosisOption();
            tPreDiagnosisOption.setOptionId(emrOptionStructure1.getOptionId());
            tPreDiagnosisOption.setOptionStructureSaveBlank(emrOptionStructure1.getOptionStructureSaveBlank());
            tPreDiagnosisOption.setOptionStructureValue(emrOptionStructure1.getOptionStructureValue());
            tPreDiagnosisOption.setOptionStructureSavePatient(emrOptionStructure1.getOptionStructureSavePatient());
            tPreDiagnosisOptionsList.add(tPreDiagnosisOption);
        }
        for (int i = 0; i < tPreDiagnosisOptionsList.size(); i++) {
            TPreDiagnosisOption tPreDiagnosisOption = tPreDiagnosisOptionsList.get(i);
            tPreDiagnosisOptionMapper.updateByPrimaryKey(tPreDiagnosisOption);
        }

        return ResEntity.success("保存成功");


    }
}
