package com.cbkj.diagnosis.service.sysService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.SysDic;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.SysDicService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/30 13:23
 * @Version 1.0
 */
@Service
public class SysWebDicService {

    private final SysDicService sysDicService;

    public SysWebDicService(SysDicService sysDicService) {
        this.sysDicService = sysDicService;
    }

    public ResEntity getEleDisDicList(){
//        QueryWrapper<SysDic> sysDicQueryWrapper = new QueryWrapper<>();
//        sysDicQueryWrapper.eq("parent_id","1").orderBy(true,true,"dic_sort");
//        List<SysDic> list = sysDicService.list(sysDicQueryWrapper);
//        ArrayList<Map<String, String>> maps = new ArrayList<>();
//        list.forEach(sysDic -> {
//            maps.add(sysDic.getExtend());
//        });
        return ResEntity.success(getEleDicMap());
    }

    public ArrayList<Map<String, String>> getEleDicMap(){
        QueryWrapper<SysDic> sysDicQueryWrapper = new QueryWrapper<>();
        sysDicQueryWrapper.eq("parent_id","1").orderBy(true,true,"dic_sort");
        List<SysDic> list = sysDicService.list(sysDicQueryWrapper);
        ArrayList<Map<String, String>> maps = new ArrayList<>();
        list.forEach(sysDic -> {
            maps.add(sysDic.getExtend());
        });
        return maps;
    }

    public ResEntity getDiagnosisDicList() {
        ArrayList<Map<String, String>> maps = new ArrayList<>();
        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("value","3");
        objectObjectHashMap.put("label","电话");

        HashMap<String, String> objectObjectHashMap2 = new HashMap<>();
        objectObjectHashMap2.put("value","4");
        objectObjectHashMap2.put("label","面访");

        HashMap<String, String> objectObjectHashMap3 = new HashMap<>();
        objectObjectHashMap3.put("value","5");
        objectObjectHashMap3.put("label","其它");

        maps.add(objectObjectHashMap);
        maps.add(objectObjectHashMap2);
        maps.add(objectObjectHashMap3);

        return ResEntity.success(maps);
    }
}
