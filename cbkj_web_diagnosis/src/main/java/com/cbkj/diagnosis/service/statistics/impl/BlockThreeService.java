package com.cbkj.diagnosis.service.statistics.impl;

import com.cbkj.diagnosis.beans.statistics.BlockThreeDetail;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayEffectService;
import com.cbkj.diagnosis.service.statistics.factory.StatisticsStrategy;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/29 11:12
 * @Version 1.0
 */
@Service("block-index-lxpj")
public class BlockThreeService implements StatisticsStrategy {

    private StatisticsErverDayEffectService statisticsErverDayEffectService;

    public BlockThreeService(StatisticsErverDayEffectService statisticsErverDayEffectService) {
        this.statisticsErverDayEffectService = statisticsErverDayEffectService;
    }

    @Override
    public ResEntity getData(StatisticsVo statisticsVo) {
        List<BlockThreeDetail> list_0 = statisticsErverDayEffectService.getBlockThreeList(statisticsVo);

        String[] names = {"疗效评价"};
        List<BlockThreeDetail>[] lists = new List[]{list_0};

        HashMap<String, Object> map = new HashMap<>(32);
        map.put("lists",lists);
        map.put("names",names);
        return ResEntity.success(map);

    }
}
