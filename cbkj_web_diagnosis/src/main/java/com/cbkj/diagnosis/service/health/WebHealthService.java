package com.cbkj.diagnosis.service.health;

import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.health.vo.GetMedicalRecordsDetailsQuery;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsMapper;
import com.cbkj.diagnosis.beans.health.vo.GetMedicalRecordsDetails;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 */
@Service
public class WebHealthService {


    private final MedicalRecordsMapper medicalRecordsMapper;

    public WebHealthService(MedicalRecordsMapper medicalRecordsMapper) {
        this.medicalRecordsMapper = medicalRecordsMapper;
    }

    public List<MedicalRecords> getMedicalRecordsDetails(GetMedicalRecordsDetails medicalRecordsDetails) {


        GetMedicalRecordsDetailsQuery detailsQuery = new GetMedicalRecordsDetailsQuery();
        detailsQuery.setPatientId(medicalRecordsDetails.getPatientId());
        detailsQuery.setQueryYearStart(medicalRecordsDetails.getQueryYear());
        if (StringUtils.isNotBlank(medicalRecordsDetails.getQueryYear())) {
            detailsQuery.setQueryYearEnd((Integer.parseInt(medicalRecordsDetails.getQueryYear()) + 1) + "");
        }
        List<MedicalRecords> a = medicalRecordsMapper.getMedicalRecordsDetails(detailsQuery);
        return a;

    }
}
