package com.cbkj.diagnosis.service.monitor.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.health.MedicalBasicHandleRecord;
import com.cbkj.diagnosis.beans.monitor.dto.*;
import com.cbkj.diagnosis.beans.response.MedicalBasicHandleRecordResponse;
import com.cbkj.diagnosis.common.enums.TreatmentTypeEnum;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.health.MedicalBasicHandleRecordMapper;
import com.cbkj.diagnosis.service.convert.MedicalBasicHandleConvert;
import com.cbkj.diagnosis.service.monitor.MedicalBasicHandleRecordService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Service
public class MedicalBasicHandleRecordServiceImpl extends ServiceImpl<MedicalBasicHandleRecordMapper, MedicalBasicHandleRecord> implements MedicalBasicHandleRecordService {


    private final MedicalBasicHandleRecordMapper medicalBasicHandleRecordMapper;

    public MedicalBasicHandleRecordServiceImpl(MedicalBasicHandleRecordMapper medicalBasicHandleRecordMapper) {
        this.medicalBasicHandleRecordMapper = medicalBasicHandleRecordMapper;
    }

    @Override
    public DisposalRecordDTO getMedicalBasicHandleRecordByRecordId(String recordId){
        List<MedicalBasicHandleRecordResponse> list = medicalBasicHandleRecordMapper.getMedicalBasicHandleRecordByRecordId(recordId);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        Map<String, List<MedicalBasicHandleRecordResponse>> listMap = list.stream()
                        .filter(i-> i.getTreatmentType()!=null)
                .collect(Collectors.groupingBy(MedicalBasicHandleRecordResponse::getTreatmentType));
        DisposalRecordDTO dto = MedicalBasicHandleConvert.disposalRecordConvertDTO(list.get(Constant.ADMIN_EXT_ZERO));

        listMap.forEach((k,v)->{
            String massage = TreatmentTypeEnum.getMassage(k);
            if(TreatmentTypeEnum.WESTERN_MEDICINE.getMessage().equals(massage)){
                WesternMedicineTreatmentDTO westernMedicineTreatmentDTO = MedicalBasicHandleConvert.westernMedicineTreatmentConvertDTO(v.get(Constant.ADMIN_EXT_ZERO));

                List<WesternMedicineTreatmentItemDTO> westernMedicineTreatmentItemDTOS = v.stream().map(MedicalBasicHandleConvert::westernMedicineTreatmentItemConvertDTO).collect(Collectors.toList());
                westernMedicineTreatmentDTO.setWesternMedicineTreatmentItems(westernMedicineTreatmentItemDTOS);
                dto.setWesternMedicineTreatment(westernMedicineTreatmentDTO);

            }else if(TreatmentTypeEnum.TCM.getMessage().equals(massage)){
                TCMTreatmentDTO tcmTreatmentDTO = MedicalBasicHandleConvert.tCMTreatmentConvertDTO(v.get(Constant.ADMIN_EXT_ZERO));

                List<TCMItemTreatmentDTO> tcmItemTreatmentDTOList = v.stream().map(MedicalBasicHandleConvert::tCMTreatmentItemConvertDTO).collect(Collectors.toList());
                tcmTreatmentDTO.setWesternMedicineTreatmentItems(tcmItemTreatmentDTOList);
                dto.setTCMTreatment(tcmTreatmentDTO);
            }
        });
        return dto;
    }
}
