package com.cbkj.diagnosis.service.webtask;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.SRoadTaskEx;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.beans.task.*;
import com.cbkj.diagnosis.beans.task.webvo.UpdateOrInsertRoad;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.SRoadTaskExMapper;
import com.cbkj.diagnosis.mapper.task.*;
import com.cbkj.diagnosis.service.webtask.vo.UpdateRoadStatusRe;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WebRoadService {

    private SRoadMapper sRoadMapper;
    private SRoadExecuteMapper sRoadExecuteMapper;
    private SRoadExecuteContentsMapper sRoadExecuteContentsMapper;
    private SRoadConditionsMapper sRoadConditionsMapper;
    private final SRoadTaskMapper sRoadTaskMapper;
    private final SRoadTaskExMapper sRoadTaskExMapper;


    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;

    @Autowired
    WebRoadService(SRoadMapper sRoadMapper,
                   SRoadExecuteMapper sRoadExecuteMapper,
                   SRoadConditionsMapper sRoadConditionsMapper,
                   SRoadExecuteContentsMapper sRoadExecuteContentsMapper, SRoadTaskMapper sRoadTaskMapper, SRoadTaskExMapper sRoadTaskExMapper
    ) {
        this.sRoadMapper = sRoadMapper;
        this.sRoadExecuteMapper = sRoadExecuteMapper;
        this.sRoadConditionsMapper = sRoadConditionsMapper;
        this.sRoadExecuteContentsMapper = sRoadExecuteContentsMapper;

        this.sRoadTaskMapper = sRoadTaskMapper;
        this.sRoadTaskExMapper = sRoadTaskExMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity updateOrInsertRoad(UpdateOrInsertRoad updateOrInsertRoad) {
        SRoad sRoad = updateOrInsertRoad.getSRoad();
        if (StringUtils.isBlank(sRoad.getSRoadGroupWay())) {
            return ResEntity.error("缺少字段sRoadGroupWay");
        }
        //  boolean isUpdateRoad = false;
        String oldSRoadId = sRoad.getSRoadId();

        if (StringUtils.isNotBlank(oldSRoadId)) {

            //修改。作废现在的，新增一条新的。
            SRoad sRoad1 = sRoadMapper.getObjectById(oldSRoadId);
            sRoad1.setStatus("1");
            sRoadMapper.updateByPrimaryKey(sRoad1);
        }
        sRoad.setSRoadId(IdUtil.getSnowflake(snowflake).nextIdStr());

        AdminInfo currentHr = AdminWebUtils.getCurrentHr();

        sRoad.setCreateTime(new Date());
        sRoad.setCreateUserName(currentHr.getNameZh());
        sRoad.setCreateUserId(currentHr.getUserId());
        if (StringUtils.isBlank(sRoad.getStatus())) {
            sRoad.setStatus("0");
        }

        //设置路径规则-那些人加入到规则中
        List<SRoadConditions> roadConditionsList = updateOrInsertRoad.getRoadConditionsList();
        for (SRoadConditions sRoadConditions : roadConditionsList) {
            //setRecordType(sRoadConditions);
            sRoadConditions.setSRoadId(sRoad.getSRoadId());
            sRoadConditions.setRoadConditionsId(null);

        }
        if (roadConditionsList.size() > 0) {
            if (roadConditionsList.get(0).getRoadConditionsId() == null) {
                //新增
                sRoadConditionsMapper.insertList(roadConditionsList);
            }

        }

        //设置路劲执行方案
        List<SRoadExecute> sRoadExecuteList = updateOrInsertRoad.getSRoadExecuteList();
        for (SRoadExecute sRoadExecute : sRoadExecuteList) {
            sRoadExecute.setSRoadId(sRoad.getSRoadId());
            sRoadExecute.setRoadExecuteId(null);
            sRoadExecuteMapper.insert(sRoadExecute);

            List<SRoadExecuteContents> sRoadExecuteContentsList = sRoadExecute.getSRoadExecuteContentsList();
            for (int i1 = 0; i1 < sRoadExecuteContentsList.size(); i1++) {
                SRoadExecuteContents contents = sRoadExecuteContentsList.get(i1++);
                contents.setRoadExecuteId(sRoadExecute.getRoadExecuteId());
                contents.setRoadExecuteContentsId(null);
            }
            if (sRoadExecuteContentsList.size() > 0) {
                if (sRoadExecuteContentsList.get(0).getRoadExecuteContentsId() == null) {
                    //插入 。批量插入
                    sRoadExecuteContentsMapper.insertList(sRoadExecuteContentsList);
                }
            }
        }

        sRoadMapper.insert(sRoad);
/**·
 * 先注释掉。
 */
        //查询正常任务中有这个路径得任务，删除，重新添加。
//        SRoadTask sRoadTask = new SRoadTask();
//        sRoadTask.setSRoadId(oldSRoadId);
//        List<SRoadTask> sRoadTaskOldList = sRoadTaskMapper.getListByObj(sRoadTask);
//
//        if (!sRoadTaskOldList.isEmpty()) {
//            ArrayList<SRoadTaskEx> sRoadTaskExesNew = new ArrayList<>();
//            ArrayList<SRoadTask> sRoadTaskesNew = new ArrayList<>();
//            for (SRoadTask task : sRoadTaskOldList) {
//                //删除旧的任务
//                sRoadTaskMapper.updateStatusDeleteByPrimaryKey(task.getSRoadTaskId());
//                task.setSRoadId(sRoad.getSRoadId());
//                //查找表s_road_task_ex
//                QueryWrapper<SRoadTaskEx> wrapper = new QueryWrapper<>();
//                wrapper.eq("s_road_task_id", task.getSRoadTaskId());
//                List<SRoadTaskEx> sRoadTaskExes = sRoadTaskExMapper.selectList(wrapper);
//                task.setSRoadTaskId(IdUtil.getSnowflake(snowflake).nextIdStr());
//                sRoadTaskesNew.add(task);
//                if (!sRoadTaskExes.isEmpty()) {
//                    sRoadTaskExes.forEach(ex ->
//                            ex.setSRoadTaskId(task.getSRoadTaskId())
//                    );
//                }
//
//                sRoadTaskExesNew.addAll(sRoadTaskExes);
//            }
//            //插入新任务-就是复制了一下旧得 改了路径id而已
//            sRoadTaskMapper.insertList(sRoadTaskesNew);
//            if (!sRoadTaskExesNew.isEmpty()) {
//                //插入新任务其他条件-就是复制一下旧得
//                sRoadTaskExMapper.saveBatch(sRoadTaskExesNew);
//            }
//        }
        return ResEntity.success(updateOrInsertRoad);
    }

    public void setRecordType(SRoadConditions a) {
        if (StringUtils.isNotBlank(a.getTWestDiseaseId())) {
            a.setRecordType(Constant.BASIC_STRING_ONE);
        } else if (StringUtils.isNotBlank(a.getTChineseDiseaseId())) {
            a.setRecordType(Constant.BASIC_STRING_TWO);
        } else if (StringUtils.isNotBlank(a.getSSymptomId())) {
            a.setRecordType(Constant.BASIC_STRING_THREE);
        } else if (StringUtils.isNotBlank(a.getDoctorId())) {
            a.setRecordType(Constant.BASIC_STRING_FOUR);
        } else {
            a.setRecordType(Constant.BASIC_STRING_FIVE);
        }
    }

    /**
     * 获取路径详情
     *
     * @param sRoadIid
     * @return
     */
    public UpdateOrInsertRoad getRoadDetailById(String sRoadIid) {
        UpdateOrInsertRoad updateOrInsertRoad = new UpdateOrInsertRoad();

        SRoad sRoad = sRoadMapper.getObjectById(sRoadIid);

        SRoadConditions roadConditions = new SRoadConditions();
        roadConditions.setSRoadId(sRoadIid);
        List<SRoadConditions> conditionsList = sRoadConditionsMapper.getAllListByObj(roadConditions);

        List<SRoadExecute> sRoadExecuteList = sRoadExecuteMapper.selectsRoadExecuteList(sRoadIid);
        updateOrInsertRoad.setSRoad(sRoad);
        updateOrInsertRoad.setRoadConditionsList(conditionsList);
        updateOrInsertRoad.setSRoadExecuteList(sRoadExecuteList);

        return updateOrInsertRoad;
    }

    /**
     * 获取随访路径列表
     *
     * @param sRoadName
     * @param page
     * @return
     */
    public Object getPageRoadList(String sRoadName, Page page) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        PageHelper.startPage(page.getPage(), page.getLimit());

        SRoad sRoad = new SRoad();
        sRoad.setSRoadName(sRoadName);
        sRoad.setStatus("0");
        sRoad.setCreateUserId(currentHr.getUserId());
        List<AdminRule> roles = currentHr.getRoles();

        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            sRoad.setCreateUserId(null);
        }
        List<SRoad> lis = sRoadMapper.getPageRoadListBySRoad(sRoad);

        PageHelper.clearPage();


        return Page.getLayUiTablePageData(lis);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRoadStatus(UpdateRoadStatusRe updateRoadStatusRe) {
        SRoad objectById = sRoadMapper.getObjectById(updateRoadStatusRe.getSRoadIid());
        if (objectById != null) {
            objectById.setStatus(updateRoadStatusRe.getStatus());
            if (Constant.BASIC_STRING_ONE.equals(updateRoadStatusRe.getStatus())) {
                objectById.setDelTime(new Date());
                AdminInfo currentHr = AdminWebUtils.getCurrentHr();
                objectById.setDelUserId(currentHr.getUserId());
                objectById.setDelUserName(currentHr.getNameZh());
            }
            sRoadMapper.updateByPrimaryKey(objectById);
        }

    }
}
