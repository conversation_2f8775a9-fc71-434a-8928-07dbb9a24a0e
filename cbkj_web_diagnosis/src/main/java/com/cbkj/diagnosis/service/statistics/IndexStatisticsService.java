package com.cbkj.diagnosis.service.statistics;

import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsPhoneMapper;
import com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayFollowMapper;
import com.cbkj.diagnosis.beans.statistics.PastSevenDaysRe;
import com.cbkj.diagnosis.service.webapi.business.vo.XiaoShanPhoneSuiFangStatisticsInfo;
import com.cbkj.diagnosis.service.webapi.business.vo.XiaoShanPhoneSuiFangStatisticsTopRight;
import com.cbkj.diagnosis.service.webapi.business.vo.YesterDaySuiFangFinishPeopleNumbSearch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class IndexStatisticsService {

    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;

    private final StatisticsErverDayFollowMapper statisticsErverDayFollowMapper;

    public IndexStatisticsService(SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper, StatisticsErverDayFollowMapper statisticsErverDayFollowMapper) {
        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
        this.statisticsErverDayFollowMapper = statisticsErverDayFollowMapper;
    }


    public XiaoShanPhoneSuiFangStatisticsInfo getXiaoShanPhoneSuiFangStatisticsInfo() {
        //注意：都是统计状态2 已经执行任务了（患者病历状态触发了这个任务路径）
        //昨日完成随访：同一天-同一人-同一问卷-完成问卷。为一次。
        //昨日新增待随访：昨天比前天新增的待随访
        //当前待随访:是名下所有的待随访
        //今日待随访:是指排期到今天的

        boolean containsSearchKey = AdminWebUtils.containsSearchKey("all");
        String userId = AdminWebUtils.getCurrentHr().getUserId();
        if (containsSearchKey) {
            userId = null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //昨日完成随访
        PastSevenDaysRe pastSevenDaysRe = new PastSevenDaysRe();
        pastSevenDaysRe.setStartDate( simpleDateFormat.format(DateUtil.getNextDate(-1)) );
        pastSevenDaysRe.setEndDate( simpleDateFormat.format(DateUtil.getToDayZeroDate() ) );
        pastSevenDaysRe.setDoctorId(userId);
        pastSevenDaysRe.setInsCode(null);
        pastSevenDaysRe.setType(1);
        Long a = statisticsErverDayFollowMapper.getYesterDayNumber(pastSevenDaysRe);
        YesterDaySuiFangFinishPeopleNumbSearch yesterDaySuiFangFinishPeopleNumbSearch = new YesterDaySuiFangFinishPeopleNumbSearch();
        yesterDaySuiFangFinishPeopleNumbSearch.setDoctorId(userId);
//        yesterDaySuiFangFinishPeopleNumbSearch.setPhoneStatus(8);
//        yesterDaySuiFangFinishPeopleNumbSearch.setStartDate(simpleDateFormat.format(DateUtil.getToDayZeroDate()));
//        yesterDaySuiFangFinishPeopleNumbSearch.setEndDate(simpleDateFormat.format(DateUtil.getNextDate()));
       // Long a = sRoadTaskPatientsPhoneMapper.getYesterDaySuiFangFinishPeopleNumb(yesterDaySuiFangFinishPeopleNumbSearch);
        //昨日新增待随访
        yesterDaySuiFangFinishPeopleNumbSearch.setStartDate(simpleDateFormat.format(DateUtil.getNextDate(-1)));
        yesterDaySuiFangFinishPeopleNumbSearch.setEndDate(simpleDateFormat.format(DateUtil.getToDayZeroDate()));
        Long a2 = sRoadTaskPatientsPhoneMapper.getYesterDaySuiFangFinishPeopleNumb(yesterDaySuiFangFinishPeopleNumbSearch);
        //当前待随访,是名下所有的待随访
        YesterDaySuiFangFinishPeopleNumbSearch yester2 = new YesterDaySuiFangFinishPeopleNumbSearch();
        yester2.setDoctorId(userId);
        Long a3 = sRoadTaskPatientsPhoneMapper.getTodayDaySuiFangTotalPeopleNumb(yester2);
        //今日待随访
        YesterDaySuiFangFinishPeopleNumbSearch yester3 = new YesterDaySuiFangFinishPeopleNumbSearch();
        yester3.setStartDate(simpleDateFormat.format(DateUtil.getToDayZeroDate()));
        yester3.setEndDate(simpleDateFormat.format(DateUtil.getNextDate()));
        yester3.setDoctorId(userId);
        Long a4 = sRoadTaskPatientsPhoneMapper.getTodayDaySuiFangTotalPeopleNumb(yester3);
        XiaoShanPhoneSuiFangStatisticsInfo statisticsInfo = new XiaoShanPhoneSuiFangStatisticsInfo();
        statisticsInfo.setYesterDaySuiFangFinishPeopleNumb(a == null ? 0 : a);
        statisticsInfo.setYesterDaySuiFangAddPeopleNumb(a2 == null ? 0 : a2);
        statisticsInfo.setTodayDaySuiFangWaitPeopleNumb(a4 == null ? 0 : a4);
        statisticsInfo.setCurrentDaySuiFangTotalPeopleNumb(a3 == null ?0 : a3);
        return statisticsInfo;
    }

    public Object getXiaoShanPhoneSuiFangStatisticsTopRight() {

        boolean containsSearchKey = AdminWebUtils.containsSearchKey("all");
        String userId = AdminWebUtils.getCurrentHr().getUserId();
        if (containsSearchKey) {
            userId = null;
        }

        YesterDaySuiFangFinishPeopleNumbSearch yester4 = new YesterDaySuiFangFinishPeopleNumbSearch();
        yester4.setDoctorId(userId);
        Long a2 = sRoadTaskPatientsPhoneMapper.getXiaoShanPhoneSuiFangStatisticsTopRight2(yester4);

        XiaoShanPhoneSuiFangStatisticsTopRight xiaoShanPhoneSuiFangStatisticsTopRight = new XiaoShanPhoneSuiFangStatisticsTopRight();
        YesterDaySuiFangFinishPeopleNumbSearch yester3 = new YesterDaySuiFangFinishPeopleNumbSearch();
        yester3.setDoctorId(userId);
        Long a = sRoadTaskPatientsPhoneMapper.getXiaoShanPhoneSuiFangStatisticsTopRight(yester3);

        xiaoShanPhoneSuiFangStatisticsTopRight.setXiaoShanTopRightAllPatient(a);
        xiaoShanPhoneSuiFangStatisticsTopRight.setXiaoShanTopRightAllDiaForm(a2);
        return xiaoShanPhoneSuiFangStatisticsTopRight;
    }

    public static void main(String[] args) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        System.out.println(
                simpleDateFormat.format(DateUtil.getNextDate(-1))
        );
    }
}
