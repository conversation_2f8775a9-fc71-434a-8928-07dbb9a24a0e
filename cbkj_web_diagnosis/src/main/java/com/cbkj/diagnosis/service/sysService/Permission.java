package com.cbkj.diagnosis.service.sysService;


import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.mapper.sysmapper.AdminMenuMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class Permission {


    private AdminMenuMapper adminMenuMapper;

    Permission(AdminMenuMapper adminMenuMapper){
        this.adminMenuMapper = adminMenuMapper;
    }

    public boolean hasRole(String permission){
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if (currentHr == null){
            log.error("当前鉴权用户过期");
            return false;
        }
        log.info("当前鉴权用户："+ JSON.toJSONString(currentHr));
        String rolesName = adminMenuMapper.getRolesNamesByUserId(currentHr.getUserId());
        if (StringUtils.isBlank(rolesName)){
            return false;
        }
        if (rolesName.contains(permission)){
            return true;
        }
        log.info("权限入参:"+permission);
        return false;
    }
}
