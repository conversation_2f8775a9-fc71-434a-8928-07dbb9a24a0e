package com.cbkj.diagnosis.service.convert;


import com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem;
import com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem;
import com.cbkj.diagnosis.beans.monitor.dto.*;
import com.cbkj.diagnosis.beans.response.MedicalRecordsPrescriptionsResponse;
import com.cbkj.diagnosis.common.enums.FlagEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by zbh on 2024/6/6 16:02
 *
 * @description：处方信息转换器
 */
public class MedicalRecordsPrescriptionsConvert {

    public static PrescriptionInfoDTO medicalRecordsConvertToDTO(List<MedicalRecordsPrescriptionsResponse> response,
                                                                 List<MedicalRecordsPrescriptionsResponse> response2,
                                                                 List<List<MedicalRecordsPrescriptionsItem>> prescriptionsItems,
                                                                 List<List<MedicalWestPrescriptionsItem>> westPrescriptionsItems) {

        PrescriptionInfoDTO dto = new PrescriptionInfoDTO();

        TCMPrescriptionParentDTO tcmPrescriptionParentDTO = new TCMPrescriptionParentDTO();
        MedicalWestPrescriptionsParentDTO medicalWestPrescriptionsParentDTO = new MedicalWestPrescriptionsParentDTO();

//        tcmPrescriptionParentDTO.setChineseDisName(response1.getChineseDisName());
//        tcmPrescriptionParentDTO.setSymName(response1.getSymName());
//        tcmPrescriptionParentDTO.setTcmApproach(response1.getTcmApproach());
//        medicalWestPrescriptionsParentDTO.setWestDisName(response1.getWestDisName());
        ArrayList<TCMPrescriptionDTO> tcmPrescriptionDTOS = new ArrayList<>();
        ArrayList<MedicalWestPrescriptionsDTO> MedicalWestPrescriptionsDTOList = new ArrayList<>();
        if (response != null && !response.isEmpty()){
            response.forEach(item -> {
                if (StringUtils.isNotBlank(item.getTcmPreId()) && !prescriptionsItems.isEmpty()  ) {

                    prescriptionsItems.forEach(item2 -> {

                        if (item2 !=null && !item2.isEmpty() && item.getTcmPreId().equals(item2.get(0).getPrescriptionsId())){
                            TCMPrescriptionDTO dto1 = tCMPrescriptionDTOConvertToDTO(item, item2);
                            dto1.setPreNo(item.getPreNo());
                            dto1.setDeptName(item.getDeptName());
                            dto1.setPrescriptionCategory(item.getPrescriptionCategory());
                            dto1.setPrescriptionsTime(item.getPrescriptionsTime());
                            dto1.setPrescriptionsEffectiveDays(item.getPrescriptionsEffectiveDays());
                            tcmPrescriptionDTOS.add(dto1);
                        }


                    });
                    tcmPrescriptionParentDTO.setTcmPrescriptionDTO(tcmPrescriptionDTOS);
                }



            });
        }else {
            TCMPrescriptionDTO tcmPrescriptionDTO = new TCMPrescriptionDTO();
            ArrayList<MedicalRecordsPrescriptionsItemDTO> a = new ArrayList<>();
            MedicalRecordsPrescriptionsItemDTO b = new MedicalRecordsPrescriptionsItemDTO();
            a.add(b);
            tcmPrescriptionDTO.setDrugNames(a);
            tcmPrescriptionDTOS.add(tcmPrescriptionDTO);
            tcmPrescriptionParentDTO.setTcmPrescriptionDTO(tcmPrescriptionDTOS);
        }
        if (response2 != null && !response2.isEmpty()){
            response2.forEach(item -> {
                if (StringUtils.isNotBlank(item.getWestPreId()) && !westPrescriptionsItems.isEmpty()) {
                    westPrescriptionsItems.forEach(item2 -> {
                        if (item2 !=null && !item2.isEmpty() && item.getWestPreId().equals(item2.get(0).getWestPrescriptionsId())){
                            MedicalWestPrescriptionsDTO medicalWestPrescriptionsDTO = medicalWestPrescriptionsConvertToDTO(item, item2);
                            MedicalWestPrescriptionsDTOList.add(medicalWestPrescriptionsDTO);
                        }

                    });
                    medicalWestPrescriptionsParentDTO.setWestPrescriptionDTO(MedicalWestPrescriptionsDTOList);
                }
            });
        }else {
            MedicalWestPrescriptionsDTO dto1 = new MedicalWestPrescriptionsDTO();
            ArrayList<MedicalWestPrescriptionsItemDTO> a = new ArrayList<>();
            MedicalWestPrescriptionsItemDTO b = new MedicalWestPrescriptionsItemDTO();
            a.add(b);
            dto1.setWestPrescriptionsItemDTO(a);
            MedicalWestPrescriptionsDTOList.add(dto1);
            medicalWestPrescriptionsParentDTO.setWestPrescriptionDTO(MedicalWestPrescriptionsDTOList);
        }



        dto.setMedicalWestPrescriptionsParentDTO(medicalWestPrescriptionsParentDTO);
        dto.setTcmPrescriptionParentDTO(tcmPrescriptionParentDTO);
        return dto;
    }

    public static TCMPrescriptionDTO tCMPrescriptionDTOConvertToDTO(MedicalRecordsPrescriptionsResponse response,
                                                                    List<MedicalRecordsPrescriptionsItem> prescriptionsItems) {
        if (null == response) {
            return null;
        }
        TCMPrescriptionDTO dto = new TCMPrescriptionDTO();
        BeanUtils.copyProperties(response, dto);
        dto.setCipherPrescription(StringUtils.isNotEmpty(response.getCipherPrescription()) ? Enum.valueOf(FlagEnum.class, response.getCipherPrescription()).getMessage() : null);
        dto.setPreparationFlag(StringUtils.isNotEmpty(response.getPreparationFlag()) ? Enum.valueOf(FlagEnum.class, response.getPreparationFlag()).getMessage() : null);
        dto.setDecoctingFlag(StringUtils.isNotEmpty(response.getDecoctingFlag()) ?
                Enum.valueOf(FlagEnum.class, response.getDecoctingFlag()).getMessage() : null);
        dto.setJunChenZuoShi(response.getJunChenZuoShi());
        dto.setPrescribingDoctorSign(response.getPrescribingDoctorSign());
        dto.setReviewedDoctorSign(response.getReviewedDoctorSign());

        List<MedicalRecordsPrescriptionsItemDTO> list = Optional.ofNullable(prescriptionsItems).orElseGet(ArrayList::new).stream().map(MedicalRecordsPrescriptionsItemConvert::medicalWestPrescriptionsItemConvertToDTO).collect(Collectors.toList());
        dto.setDrugNames(list);


        return dto;
    }


    public static MedicalWestPrescriptionsDTO medicalWestPrescriptionsConvertToDTO(MedicalRecordsPrescriptionsResponse response,
                                                                                   List<MedicalWestPrescriptionsItem> prescriptionsItems) {
        if (null == response) {
            return null;
        }
        MedicalWestPrescriptionsDTO dto = new MedicalWestPrescriptionsDTO();
        BeanUtils.copyProperties(response, dto);
//        dto.setWestDisName(response.getPreNo());
//        dto.setReviewedDoctorSign(response.getPreNo());
//        dto.setPrescriptionRemark(response.getPreNo());
//        dto.setPrescribingDoctorSign(response.getPreNo());
        List<MedicalWestPrescriptionsItemDTO> list = Optional.ofNullable(prescriptionsItems).orElseGet(ArrayList::new).stream().map(MedicalWestPrescriptionsItemConvert::medicalWestPrescriptionsItemConvertToDTO).collect(Collectors.toList());

        dto.setWestPrescriptionsItemDTO(list);

        return dto;
    }
}
