package com.cbkj.diagnosis.service.monitor;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cbkj.diagnosis.beans.monitor.vo.GetPatientInfo;
import com.cbkj.diagnosis.beans.monitor.dto.RecordInfoDTO;
import com.cbkj.diagnosis.beans.health.MedicalRecords;


import java.util.List;

/**
 * <p>
 * 就诊记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
public interface IMedicalRecordsService extends IService<MedicalRecords> {


    List<RecordInfoDTO> getRecordListByCreateDate(GetPatientInfo getPatientInfo);

}