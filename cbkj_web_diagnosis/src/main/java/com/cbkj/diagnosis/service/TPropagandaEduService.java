package com.cbkj.diagnosis.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.common.http.XiaMenResEntity;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.mapper.business.TDiseaseMapper;
import com.cbkj.diagnosis.mapper.business.TPropagandaEduDisMappingMapper;
import com.cbkj.diagnosis.mapper.business.TPropagandaEduMapper;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.business.TPropagandaEduDisMappingService;
import com.cbkj.diagnosis.service.his.XiaMenSendWXMessage;
import com.cbkj.diagnosis.service.statistics.StatisticsHealth;
import com.cbkj.diagnosis.service.vo.EduSendMessage;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AESPKCS7Util;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TPropagandaEduService {
    @Value("${sys.aes.key}")
    private String key;
    private final TPropagandaEduMapper tPropagandaEduMapper;

    private final TAdminInfoMapper tAdminInfoMapper;

    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    private final MedicalRecordsMapper medicalRecordsMapper;

    private final TDiseaseMapper tDiseaseMapper;
    private final TPropagandaEduDisMappingService tPropagandaEduDisMappingService;
    private final StatisticsHealth statisticsHealth;
    TPropagandaEduService(TPropagandaEduMapper tPropagandaEduMapper, TAdminInfoMapper tAdminInfoMapper, SRoadTaskPatientsMapper sRoadTaskPatientsMapper, MedicalRecordsMapper medicalRecordsMapper, TDiseaseMapper tDiseaseMapper, TPropagandaEduDisMappingService tPropagandaEduDisMappingService, StatisticsHealth statisticsHealth) {

        this.tPropagandaEduMapper = tPropagandaEduMapper;
        this.tAdminInfoMapper = tAdminInfoMapper;
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.medicalRecordsMapper = medicalRecordsMapper;
        this.tDiseaseMapper = tDiseaseMapper;
        this.tPropagandaEduDisMappingService = tPropagandaEduDisMappingService;
        this.statisticsHealth = statisticsHealth;
    }

    public Object getEduPageList(TPropagandaEdu tPropagandaEdu, Page page) {
        List<AdminRule> roles = AdminWebUtils.getCurrentHr().getRoles();
        tPropagandaEdu.setUserId(AdminWebUtils.getCurrentHr().getUserId());
        for (AdminRule role : roles) {
            boolean v = role.getRoleName().contains("管理");
            if (v) {
                tPropagandaEdu.setUserId(null);
                break;
            }
        }
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TPropagandaEdu> list = tPropagandaEduMapper.getPageListByObj(tPropagandaEdu);
        PageHelper.clearPage();
        return Page.getLayUiTablePageData(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public Object updateEduPageList(TPropagandaEdu tPropagandaEdu) {

        tPropagandaEdu.setStatus("0");
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
//        log.info("当前用户：{}", JSON.toJSONString(currentHr));
        if (tPropagandaEdu.getTPropagandaEduId() != null) {
//            if (!"admin".equals(currentHr.getUsername())) {
//                tPropagandaEdu.setCreateUserId(currentHr.getUserId());
//            }
//            tPropagandaEdu.setCreateUserId(currentHr.getUserId());
//            tPropagandaEdu.setCreateUserName(currentHr.getNameZh());
            tPropagandaEdu.setAppId(currentHr.getAppId());
            tPropagandaEdu.setInsId(currentHr.getInsId());
            tPropagandaEdu.setInsCode(currentHr.getInsCode());
            tPropagandaEdu.setInsName(currentHr.getInsName());
            tPropagandaEdu.setEduType(tPropagandaEdu.getEduType());
            tPropagandaEdu.setUpdateDate(new Date());
            tPropagandaEdu.setUpdateUserId(currentHr.getUserId());
            tPropagandaEdu.setUpdateUserName(currentHr.getNameZh());
//            log.info("当前机构：{}", JSON.toJSONString(tPropagandaEdu.getInsName()));
            int i = tPropagandaEduMapper.updateByPrimaryKey(tPropagandaEdu);
            TPropagandaEdu propagandaEdu = tPropagandaEduMapper.getObjectById(tPropagandaEdu.getTPropagandaEduId().toString());
            if ( i == 1){
                //先删除疾病和疾病映射关系
                QueryWrapper<TPropagandaEduDisMapping> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("t_propaganda_edu_id", tPropagandaEdu.getTPropagandaEduId());
                tPropagandaEduDisMappingService.remove(queryWrapper);
                StringBuilder chineseName = new StringBuilder();
                ArrayList<TPropagandaEduDisMapping> tPropagandaEduDisMappingArrayList = new ArrayList<>();
                for (String disMapping : tPropagandaEdu.getChineseDis()) {
                    TPropagandaEduDisMapping mapping = new TPropagandaEduDisMapping();
                    TDisease objectById = tDiseaseMapper.getObjectById(disMapping);
                    if (objectById != null) {
                        mapping.setDisCode(objectById.getDisCode());
                        mapping.setDisId(objectById.getDisId());
                        mapping.setDisName(objectById.getDisName());
                    } else {
                        if ("-1".equals(disMapping)){
                            mapping.setDisCode("-1");
                            mapping.setDisId("-1");
                            mapping.setDisName("全科");
                        }
                    }
                    mapping.setTPropagandaEduId(tPropagandaEdu.getTPropagandaEduId());
                    mapping.setDisType("1");
                    tPropagandaEduDisMappingArrayList.add(mapping);
                    chineseName.append(objectById == null ? "全科" : objectById.getDisName()).append("/");
                }
                tPropagandaEdu.setChineseName(chineseName.toString());
                tPropagandaEduMapper.updateByPrimaryKey(tPropagandaEdu);
                tPropagandaEduDisMappingService.saveBatch(tPropagandaEduDisMappingArrayList);
            }
            return i == 1 ? ResEntity.success(propagandaEdu) : ResEntity.error("无权限修改");
        } else {
            tPropagandaEdu.setCreateUserId(AdminWebUtils.getCurrentHr().getUserId());
            tPropagandaEdu.setCreateUserName(AdminWebUtils.getCurrentHr().getNameZh());
            tPropagandaEdu.setCreateTime(new Date());
            tPropagandaEdu.setAppId(currentHr.getAppId());
            tPropagandaEdu.setInsId(currentHr.getInsId());
            tPropagandaEdu.setInsCode(currentHr.getInsCode());
            tPropagandaEdu.setInsName(currentHr.getInsName());
            tPropagandaEdu.setShowStatus(true);
//            log.info("当前机构：{}", JSON.toJSONString(tPropagandaEdu.getInsName()));
            tPropagandaEdu.setUpdateDate(new Date());
            tPropagandaEdu.setUpdateUserId(currentHr.getUserId());
            tPropagandaEdu.setUpdateUserName(currentHr.getNameZh());

            ArrayList<TPropagandaEduDisMapping> tPropagandaEduDisMappingArrayList = new ArrayList<>();
            StringBuilder chineseName = new StringBuilder();
            for (String disMapping : tPropagandaEdu.getChineseDis()) {
                TPropagandaEduDisMapping mapping = new TPropagandaEduDisMapping();
                TDisease objectById = tDiseaseMapper.getObjectById(disMapping);
                if (objectById != null) {
                    mapping.setDisCode(objectById.getDisCode());
                    mapping.setDisId(objectById.getDisId());
                    mapping.setDisName(objectById.getDisName());
                } else {
                    if ("-1".equals(disMapping)){
                        mapping.setDisCode("-1");
                        mapping.setDisId("-1");
                        mapping.setDisName("全科");
                    }
                }
                mapping.setTPropagandaEduId(tPropagandaEdu.getTPropagandaEduId());
                mapping.setDisType("1");
                tPropagandaEduDisMappingArrayList.add(mapping);

                chineseName.append(objectById == null ? "全科" : objectById.getDisName()).append("/");
            }
            tPropagandaEdu.setChineseName(chineseName.toString());
            tPropagandaEduMapper.insert(tPropagandaEdu);
            for (int i = 0; i < tPropagandaEduDisMappingArrayList.size(); i++) {
                tPropagandaEduDisMappingArrayList.get(i).setTPropagandaEduId(tPropagandaEdu.getTPropagandaEduId());
            }

            tPropagandaEduDisMappingService.saveBatch(tPropagandaEduDisMappingArrayList);
            return ResEntity.success(tPropagandaEdu);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public Object deleteEduPageList(Integer tPropagandaEduId) {
        TPropagandaEdu objectById = tPropagandaEduMapper.getObjectById(tPropagandaEduId + "");
        if (objectById != null) {
            objectById.setStatus("1");
            if (!"admin".equals(AdminWebUtils.getCurrentHr().getUsername())) {
                objectById.setCreateUserId(AdminWebUtils.getCurrentHr().getUserId());
            }
            int i = tPropagandaEduMapper.updateByPrimaryKey(objectById);
            return i == 1 ? ResEntity.success() : ResEntity.error("不允许删除");
        }
        return ResEntity.success();
    }

    @Autowired
    private XiaMenSendWXMessage xiaMenSendWXMessage;

    @Value("${xia.men.mobile.wx.url}")
    private String url;

    @Transactional(rollbackFor = Exception.class)
    public ResEntity sendMessage(EduSendMessage eduSendMessage) {


        List<TAdminInfo> tAdminInfoList = tAdminInfoMapper.getListByIds(eduSendMessage.getPatientList());
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if ("admin".equals(currentHr.getUsername())) {
            return ResEntity.error("请使用admin账号之外的账号进行操作！");
        }
//        ArrayList<String> staticsEduPatient = new ArrayList<String>();
        ArrayList<String> staticsEduEduId = new ArrayList<String>();

        ArrayList<SRoadTaskPatients> plist = new ArrayList<SRoadTaskPatients>();
        for (int i = 0; i < tAdminInfoList.size(); i++) {
            TAdminInfo tAdminInfoById = tAdminInfoList.get(i);
            //统计宣教发送技术
//            staticsEduPatient.add(tAdminInfoById.getUserId());
//            staticsEduEduId.add(eduSendMessage.getTPropagandaEduId() + "");

            SRoadTaskPatients patients = new SRoadTaskPatients();
            patients.setPatientId(tAdminInfoById.getUserId());
            patients.setPatientSex(tAdminInfoById.getSex());
            patients.setPatientName(tAdminInfoById.getUserName());
            patients.setPatientAge(tAdminInfoById.getAge());
            patients.setPatientCardNumber(tAdminInfoById.getCardNumber());
            patients.setHandSend("1");
            patients.setRoadExecuteEventType("1");
            patients.setRoadExecuteEventContentId(eduSendMessage.getTPropagandaEduId() + "");
            patients.setRoadExecuteEventContentName(eduSendMessage.getEduTitle());
            patients.setTaskExcuteTime(new Date());
            patients.setStatus("0");
            patients.setTaskExcuteStatus(2);

            patients.setAppId(currentHr.getAppId());
            patients.setInsCode(currentHr.getInsCode());
            patients.setInsId(currentHr.getInsId());
            patients.setInsName(currentHr.getInsName());
            patients.setDeptName(currentHr.getDeptName());
            patients.setDoctorId(currentHr.getUserId());
            patients.setDoctorName(currentHr.getNameZh());
            patients.setRecordsId(tAdminInfoById.getRecordsId());
            patients.setDeptId(currentHr.getDeptId());
            patients.setDeptCode(currentHr.getDeptCode());
            //plist.add(patients);

            sRoadTaskPatientsMapper.insert(patients);

            //组装cbdata字段值。
            HashMap<String, String> map = new HashMap<>();
            map.put("appId", patients.getAppId());
            map.put("insCode", patients.getInsCode());
            map.put("insName", patients.getInsName());
            map.put("timestamp", System.currentTimeMillis() + "");
            map.put("userName", tAdminInfoById.getUserName());
            map.put("mobile", tAdminInfoById.getMobile());
            map.put("healthCardNum", tAdminInfoById.getHealthCardNum());
            map.put("cardNumber", tAdminInfoById.getCardNumber());
            String mak = JSON.toJSONString(map);
            String base64 = "";
            try {
                base64 = AESPKCS7Util.encrypt(mak, key, "base64");
            } catch (Exception e) {
                log.error("cbdata加密错误：" + e.getMessage());
            }
            String temp = null;
            try {
                temp = URLEncoder.encode("healtheducation?roadExecuteEventContentId=" + eduSendMessage.getTPropagandaEduId() + "&taskPatientsId=" + patients.getTaskPatientsId(), "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            //宣教 XiaMenZoeAes256Util
            XiaMenResEntity xiaMenResEntity = xiaMenSendWXMessage.sendWxTemplateWSDL(tAdminInfoById.getMobile(), null,
                    url + "?cbdata=" + base64 + "&jumpUrl=" + temp,

                    tAdminInfoById.getUserName() + "|" + currentHr.getNameZh() + "|" + currentHr.getDeptName() + "|" + eduSendMessage.getEduTitle(),

                    com.cbkj.diagnosis.common.utils.DateUtil.getDateFormats("yyyy-MM-dd HH:mm:ss", patients.getTaskExcuteTime()),
                    tAdminInfoById.getCardNumber(), Constant.BASIC_STRING_ONE, tAdminInfoById.getMedicalCard()
            );


        }
       // statisticsHealth.writeReadFromRedisMaps(staticsEduPatient, staticsEduEduId);
        if (plist.size() > 0) {
            //  sRoadTaskPatientsMapper.insertList(plist);
        }
        return ResEntity.success();

    }

    public Object details(Integer tPropagandaEduId) {
        TPropagandaEdu objectById = tPropagandaEduMapper.getObjectById(tPropagandaEduId + "");
        QueryWrapper<TPropagandaEduDisMapping> wrapper = new QueryWrapper<>();
        wrapper.eq("t_propaganda_edu_id", tPropagandaEduId);

        List<TPropagandaEduDisMapping> list = tPropagandaEduDisMappingService.list(wrapper);
        if (list != null && !list.isEmpty()){
            //转成去除list数组对象中的 disId 字段值，给到 新的数组对象 List<String>
            List<String> disIdList = list.stream().map(TPropagandaEduDisMapping::getDisId).collect(Collectors.toList());
            objectById.setChineseDis(disIdList);
        }
        return objectById;
    }

    public Object changeShowStatus(Integer tPropagandaEduId, boolean showStatus) {
        TPropagandaEdu objectById = tPropagandaEduMapper.getObjectById(tPropagandaEduId + "");
        objectById.setShowStatus(showStatus);
        tPropagandaEduMapper.updateByPrimaryKey(objectById);
        return ResEntity.success(objectById);
    }
}
