package com.cbkj.diagnosis.service.webtask;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.cbkj.diagnosis.service.sysService.RedisService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Log4j2
@Service
public class TaskRedisService {

    private final RedisService redisService;

    public TaskRedisService(RedisService redisService) {
        this.redisService = redisService;
    }

    /**
     * 获取时间到了的任务id数组
     *
     * @return
     */
    public ArrayList<String> getArrivedKey() {
        Set<String> allKeys = redisService.getAllKeys("task-wait-1::");
        ArrayList<String> strings = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        if (!allKeys.isEmpty()) {
            Iterator<String> iterator = allKeys.iterator();

            while (iterator.hasNext()) {
                String next = iterator.next();

                Object o = redisService.get(next);
                if (o instanceof Date) {
                    Date taskWait1 = (Date) o;
                    long between = 0;
                    try {
                        between = DateUtil.between(simpleDateFormat.parse(simpleDateFormat.format(new Date())),
                                taskWait1, DateUnit.SECOND, false);
                    } catch (ParseException e) {
                        //throw new RuntimeException(e);
                        log.error("========================================getArrivedKey,异常：{}", e.getMessage());
                        return strings;
                    }
                    if (between <= 0) {
                        //任务时间到了`

                        strings.add(next.substring(next.lastIndexOf(":") + 1));

                    }
                }
            }
            return strings;
        }
        return strings;
    }

    public void removeKey(ArrayList<String> strings) {
        for (int i = 0; i < strings.size(); i++) {
            redisService.removeKey("task-wait-1::"+strings.get(i));
        }
    }
}
