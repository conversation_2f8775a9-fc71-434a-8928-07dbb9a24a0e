package com.cbkj.diagnosis.service.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TDisease;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayDisService;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayHealthService;
import com.cbkj.diagnosis.service.statistics.factory.StatisticsStrategy;
import com.cbkj.diagnosis.service.statistics.impl.StatisticsDiagnosisDicServiceImpl;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 首页数据
 *
 * <AUTHOR>
 */
@Service("block-index-top")
public class BlockIndexTopDataService implements StatisticsStrategy {

//    private StatisticsInstitutionService statisticsInstitutionService;
//
//    private TDiseaseService tDiseaseService;

//    private StatisticsErverDayDiagnosisService statisticsErverDayDiagnosisService;
//
//    private StatisticsErverDayFollowService statisticsErverDayFollowService;

    private final StatisticsErverDayHealthService statisticsErverDayHealthService;

    private final StatisticsErverDayDisService statisticsErverDayDiagnosisService;

    private final StatisticsDiagnosisDicServiceImpl statisticsDiagnosisDicService;

    public BlockIndexTopDataService(
//            BlockIndexTopDataServiceStatisticsInstitutionService statisticsInstitutionService, TDiseaseService tDiseaseService,
            StatisticsErverDayHealthService statisticsErverDayHealthService, StatisticsErverDayDisService statisticsErverDayDiagnosisService, StatisticsDiagnosisDicServiceImpl statisticsDiagnosisDicService) {
//        this.statisticsInstitutionServicervice = statisticsInstitutionService;
//        this.tDiseaseService = tDiseaseService;

        this.statisticsErverDayHealthService = statisticsErverDayHealthService;
        this.statisticsErverDayDiagnosisService = statisticsErverDayDiagnosisService;
        this.statisticsDiagnosisDicService = statisticsDiagnosisDicService;
    }

    @Override
    public ResEntity getData(StatisticsVo statisticsVo) {
        Map<String, Object> map = new LinkedHashMap<>(16);
        //监测机构数量
//        long count = statisticsInstitutionService.count();
        //监测病种数量
        QueryWrapper<TDisease> tDiseaseQueryWrapper = new QueryWrapper<>();
        tDiseaseQueryWrapper.eq("monitor_dis", 1);
//        long count1 = tDiseaseService.count(tDiseaseQueryWrapper);
        //预诊数量
        Long sumAllNum = statisticsErverDayDiagnosisService.getSumAllNumDia(statisticsVo);
        //随访量
        Long sumAllNum1 = statisticsErverDayDiagnosisService.getSumAllNumFlow(statisticsVo);
        //健康宣教
        Long sumAllNum2 = statisticsErverDayHealthService.getSumAllNum(statisticsVo);
        //病历结案量
        Long sumAllNum3 = statisticsDiagnosisDicService.getRecordFinishSum(statisticsVo);
        //随访异常
        Long sumAllNum4 = statisticsDiagnosisDicService.getDiagnosisFoucsSum(statisticsVo);
        map.put("专病预诊量", sumAllNum);
        map.put("专病随访量", sumAllNum1);
        map.put("专病宣教量", sumAllNum2);
        map.put("患者服务量", (sumAllNum1 + sumAllNum));
//        map.put("接入机构","count"+"家");
//        map.put("监测病种","count1"+"种");
        map.put("病例结案量", sumAllNum3);
        map.put("随访异常", sumAllNum4);

        return ResEntity.success(map);
    }
}
