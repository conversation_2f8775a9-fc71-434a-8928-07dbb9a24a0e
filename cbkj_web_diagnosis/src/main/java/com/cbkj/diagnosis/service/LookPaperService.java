package com.cbkj.diagnosis.service;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.business.*;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisOptionEventServiceImpl;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisStructureChildServiceImpl;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisStructureContentServiceImpl;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisStructureServiceImpl;
import com.cbkj.diagnosis.beans.dao.CheckDisMappingIsMu;
import com.cbkj.diagnosis.service.impl.SysDicServiceImpl;
import com.cbkj.diagnosis.service.webapi.business.vo.LookPaperQuestionMain;
import com.cbkj.diagnosis.service.webapi.business.vo.LookPaperRecive;
import com.cbkj.diagnosis.service.webapi.business.vo.QuestionOptionSave;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class LookPaperService {
    private TPreDiagnosisFormMapper tPreDiagnosisFormMapper;
    private TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;
    private TPreDiagnosisSkipMapper tPreDiagnosisSkipMapper;
    private TPreDiagnosisChildMapper tPreDiagnosisChildMapper;

    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;

    private TDiseaseMapper tDiseaseMapper;

    private final TPreDiagnosisOptionMutexMapper tPreDiagnosisOptionMutexMapper;


    private final TPreDiagnosisStructureServiceImpl tPreDiagnosisStructureService;
    private final TPreDiagnosisStructureChildServiceImpl tPreDiagnosisStructureChildService;
    private final TPreDiagnosisStructureContentServiceImpl tPreDiagnosisStructureContentService;
private final SysDicServiceImpl service;
private final TPreDiagnosisOptionEventServiceImpl tPreDiagnosisOptionEventService;
    LookPaperService(TPreDiagnosisFormMapper tPreDiagnosisFormMapper,
                     TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper,
                     TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper,
                     TPreDiagnosisSkipMapper tPreDiagnosisSkipMapper,
                     TPreDiagnosisChildMapper tPreDiagnosisChildMapper, TDiseaseMapper tDiseaseMapper, TPreDiagnosisOptionMutexMapper tPreDiagnosisOptionMutexMapper, TPreDiagnosisStructureServiceImpl tPreDiagnosisStructureService, TPreDiagnosisStructureChildServiceImpl tPreDiagnosisStructureChildService, TPreDiagnosisStructureContentServiceImpl tPreDiagnosisStructureContentService, SysDicServiceImpl service, TPreDiagnosisOptionEventServiceImpl tPreDiagnosisOptionEventService) {
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;

        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.tPreDiagnosisOptionMapper = tPreDiagnosisOptionMapper;
        this.tPreDiagnosisSkipMapper = tPreDiagnosisSkipMapper;
        this.tPreDiagnosisChildMapper = tPreDiagnosisChildMapper;
        this.tDiseaseMapper = tDiseaseMapper;
        this.tPreDiagnosisOptionMutexMapper = tPreDiagnosisOptionMutexMapper;
        this.tPreDiagnosisStructureService = tPreDiagnosisStructureService;
        this.tPreDiagnosisStructureChildService = tPreDiagnosisStructureChildService;
        this.tPreDiagnosisStructureContentService = tPreDiagnosisStructureContentService;
        this.service = service;
        this.tPreDiagnosisOptionEventService = tPreDiagnosisOptionEventService;
    }

    /**
     * 插入预诊单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity setPaper(LookPaperRecive lookPaperRecive) {
        if (StringUtils.isNotBlank(lookPaperRecive.getDiaId())) {
            //判断表单是不是自己的
//            if (!"admin".equals(AdminWebUtils.getCurrentHr().getUsername())) {
//                TPreDiagnosisForm diagnosisForm = tPreDiagnosisFormMapper.getObjectById(lookPaperRecive.getDiaId());
//                if (diagnosisForm.getCreateUser().equals(AdminWebUtils.getCurrentHr().getUserId())) {
//                    return ResEntity.error("不允许修改");
//                }
//            }

            //修改预诊单子的状态为。修改为删除状态。
            TPreDiagnosisForm tPreDiagnosisForm = new TPreDiagnosisForm();
            tPreDiagnosisForm.setDiaId(lookPaperRecive.getDiaId());
            tPreDiagnosisForm.setFormCode(lookPaperRecive.getFormCode());
            tPreDiagnosisFormMapper.updateFormCodeIfNull(tPreDiagnosisForm);
            tPreDiagnosisForm.setUpdateDate(new Date());
            tPreDiagnosisFormMapper.updateStatusToDeleteByDiaId(lookPaperRecive.getDiaId());
        }


//        Boolean isInsert = true;
        //插入预诊主表。
        TPreDiagnosisForm tPreDiagnosisForm = new TPreDiagnosisForm();
        BeanUtils.copyProperties(lookPaperRecive, tPreDiagnosisForm);
        tPreDiagnosisForm.setCreateDate(new Date());
        tPreDiagnosisForm.setCreateUser(AdminWebUtils.getCurrentHr().getUserId());
        tPreDiagnosisForm.setCreateUsername(AdminWebUtils.getCurrentHr().getNameZh());
        tPreDiagnosisForm.setShowStatus(true);
//        if (StringUtils.isNotBlank(lookPaperRecive.getDiaId())) {
            tPreDiagnosisForm.setUpdateDate(new Date());
            tPreDiagnosisForm.setUpdateUser(AdminWebUtils.getCurrentHr().getUserId());
            tPreDiagnosisForm.setUpdateUsername(AdminWebUtils.getCurrentHr().getNameZh());
//        }
        tPreDiagnosisForm.setDiaId(IdUtil.getSnowflake(snowflake).nextIdStr());
        tPreDiagnosisForm.setStatus("0");
        tPreDiagnosisForm.setAppId(AdminWebUtils.getCurrentHr().getAppId());
        tPreDiagnosisForm.setInsCode(AdminWebUtils.getCurrentHr().getInsCode());
        tPreDiagnosisForm.setInsId(AdminWebUtils.getCurrentHr().getInsId());

        int countFormCode = tPreDiagnosisFormMapper.selctCountFormCode(tPreDiagnosisForm);
        if (countFormCode > 0) {
            return ResEntity.error("表单编码重复");
        }
        StringBuilder westName = new StringBuilder();
        StringBuilder chineseName = new StringBuilder();
//        String chineseId = "";
//        String westId = "";

        if (lookPaperRecive.getDiaType().equals(Constant.BASIC_STRING_ONE)) {
            for (DisRe disMapping : lookPaperRecive.getWestDis()) {
                TPreDiagnosisDisMappingV mapping = new TPreDiagnosisDisMappingV();
                mapping.setDisId(disMapping.getId());
                mapping.setDisName(disMapping.getName());
                mapping.setDiaId(tPreDiagnosisForm.getDiaId());
                mapping.setDisType("2");
                TDisease objectById = tDiseaseMapper.getObjectById(disMapping.getId());
                if (objectById != null) {
                    mapping.setDisCode(objectById.getDisCode());
                }

                tPreDiagnosisFormMapper.insertPaperMapping(mapping);
                westName.append(disMapping.getName()).append("/");
//                westId = westId + disMapping.getId() + "/";
            }
            for (DisRe disMapping : lookPaperRecive.getChineseDis()) {
                TPreDiagnosisDisMappingV mapping = new TPreDiagnosisDisMappingV();
                mapping.setDisId(disMapping.getId());
                mapping.setDisName(disMapping.getName());
                mapping.setDiaId(tPreDiagnosisForm.getDiaId());
                mapping.setDisType("1");
                TDisease objectById = tDiseaseMapper.getObjectById(disMapping.getId());
                if (objectById != null) {
                    mapping.setDisCode(objectById.getDisCode());
                }
                tPreDiagnosisFormMapper.insertPaperMapping(mapping);
                chineseName.append(disMapping.getName()).append("/");
//                chineseId = chineseId + disMapping.getId() + "/";
            }
            //}
        } else if (lookPaperRecive.getDiaType().equals(Constant.BASIC_STRING_TWO)) {
            //全科预诊单也插入到系统里！用预诊单list查询
            TPreDiagnosisDisMappingV mapping = new TPreDiagnosisDisMappingV();
            mapping.setDisId("-1");
            mapping.setDisName("全科");
            mapping.setDiaId(tPreDiagnosisForm.getDiaId());
            mapping.setDisType("1");
            mapping.setDisCode("-1");
            tPreDiagnosisFormMapper.insertPaperMapping(mapping);
        }

        if (lookPaperRecive.getFormType().equals(Constant.BASIC_STRING_TWO)) {
            List<DisRe> chineseDisDiagnosisList = lookPaperRecive.getChineseDisDiagnosisList();
            if(chineseDisDiagnosisList != null && !chineseDisDiagnosisList.isEmpty()){
                for (DisRe disMapping : chineseDisDiagnosisList) {
                    TPreDiagnosisDisMappingV mapping = new TPreDiagnosisDisMappingV();
                    mapping.setDisId(disMapping.getId());
                    mapping.setDisName(disMapping.getName());
                    mapping.setDiaId(tPreDiagnosisForm.getDiaId());
                    mapping.setDisType("1");
                    TDisease objectById = tDiseaseMapper.getObjectById(disMapping.getId());
                    if (objectById != null) {
                        mapping.setDisCode(objectById.getDisCode());
                    }else {
                        mapping.setDisCode("-1");
                    }
                    tPreDiagnosisFormMapper.insertPaperMapping(mapping);
                    chineseName.append(disMapping.getName()).append("/");
                }
            }

        }
        tPreDiagnosisForm.setChineseDisName(chineseName.toString());
        tPreDiagnosisForm.setWestDisName(westName.toString());



        //插入题目

        //k存放现在题目id，v存放现在插入的题目id
        HashMap<String, Integer> map = new HashMap<>(16);
        //k存前端过来的optionId，v存插入的optionId
        HashMap<String, Integer> map2 = new HashMap<>(16);

        List<LookPaperQuestionMain> questionMobilesList = lookPaperRecive.getQuestionMobilesList();

        ArrayList<TPreDiagnosisQuestion> tPreDiagnosisQuestions = new ArrayList<>();
        ArrayList<TPreDiagnosisOption> tPreDiagnosisOptions = new ArrayList<>();
        ArrayList<TPreDiagnosisSkip> tPreDiagnosisSkips = new ArrayList<>();
        ArrayList<TPreDiagnosisChild> tPreDiagnosisChilds = new ArrayList<>();
        ArrayList<TPreDiagnosisOptionEvent> tPreDiagnosisEventList = new ArrayList<>();
        ArrayList<TPreDiagnosisOptionMutex> tPreDiagnosisOptionMutexArrayList = new ArrayList<>();
        HashSet<String> set = new HashSet<>();
        HashSet<String> set2 = new HashSet<>();
        for (LookPaperQuestionMain main : questionMobilesList) {
            //题目对象
            TPreDiagnosisQuestion question = new TPreDiagnosisQuestion();
            BeanUtils.copyProperties(main, question);
            question.setDiaId(tPreDiagnosisForm.getDiaId());
            tPreDiagnosisQuestions.add(question);
            tPreDiagnosisQuestionMapper.insert(question);
            map.put(main.getQuestionId(), question.getQuestionId());
            //修改传进来的问题id，用来返回给前端。
            main.setQuestionId(question.getQuestionId() + "");
            //选项列表对象
            List<QuestionOptionSave> questionOptionSaveList = main.getQuestionOptionSaveList();
            int a = 0;
            for (QuestionOptionSave optionSave : questionOptionSaveList) {
                TPreDiagnosisOption option = new TPreDiagnosisOption();
                BeanUtils.copyProperties(optionSave, option);
                option.setQuestionId(question.getQuestionId());
                option.setOptionSort(a);
                option.setOptionIdStr(optionSave.getOptionId());
                a++;
                tPreDiagnosisOptionMapper.insert(option);
                List<String> optionFollowEventCode = optionSave.getOptionFollowEventCode();
                if (null != optionFollowEventCode && !optionFollowEventCode.isEmpty()) {
                    List<HashMap<String,String>> resultList = service.getDetailsByDicCodeList(optionFollowEventCode);
                    for (HashMap<String, String> map1 : resultList) {
                        TPreDiagnosisOptionEvent optionEvent = new TPreDiagnosisOptionEvent();
                        optionEvent.setOptionId(option.getOptionId());
                        optionEvent.setDicCode(map1.get("dicCode"));
                        optionEvent.setDicId(map1.get("dicId"));
                        optionEvent.setDicName(map1.get("dicName"));
                        optionEvent.setDiaId(tPreDiagnosisForm.getDiaId());
                        tPreDiagnosisEventList.add(optionEvent);
                    }
//                    for (String dicCode : optionFollowEventCode) {
//                        TPreDiagnosisOptionEvent optionEvent = new TPreDiagnosisOptionEvent();
//                        optionEvent.setOptionId(option.getOptionId());
//                        optionEvent.setDicCode(dicCode);
//                        tPreDiagnosisEventList.add(optionEvent);
//                    }
                }
                //存一下前端过来的optionId，对应存到数据中optionId是多少
                map2.put(optionSave.getOptionId(), option.getOptionId());

                tPreDiagnosisOptions.add(option);
                //修改传进来的问题id、选项id，用来返回给前端。
                optionSave.setOptionId(option.getOptionId() + "");
                optionSave.setQuestionId(question.getQuestionId() + "");

                List<String> questionSkipSaveList = optionSave.getQuestionSkipSaveList();
                if (null != questionSkipSaveList) {
                    for (String skipQuestionId : questionSkipSaveList) {
                        //当前题目的选项的调题
                        TPreDiagnosisSkip skip = new TPreDiagnosisSkip();
                        skip.setSkipQuestionId(question.getQuestionId());
                        skip.setOptionId(option.getOptionId());
                        skip.setOptionName(option.getOptionName());
                        skip.setSkipQuestionIdStr(skipQuestionId);
                        tPreDiagnosisSkips.add(skip);
                    }
                }

                //子题目
                List<String> questionChildSaveList = optionSave.getQuestionChildSaveList();
                if (null != questionChildSaveList) {
                    for (int i = 0; i < questionChildSaveList.size(); i++) {
                        TPreDiagnosisChild tPreDiagnosisChild = new TPreDiagnosisChild();
                        tPreDiagnosisChild.setSort(i);
                        tPreDiagnosisChild.setOptionId(option.getOptionId());
                        tPreDiagnosisChild.setOptionName(option.getOptionName());
                        //前端传过来的问题id
                        tPreDiagnosisChild.setChildQuestionIdStr(questionChildSaveList.get(i));
                        //数据库的问题id
                        tPreDiagnosisChild.setChildQuestionId(question.getQuestionId());
                        tPreDiagnosisChild.setMasterQuestionId(question.getQuestionId());
                        tPreDiagnosisChild.setDiaId(tPreDiagnosisForm.getDiaId());
                        tPreDiagnosisChilds.add(tPreDiagnosisChild);
                        set2.add(question.getQuestionId() + "");
                    }
                }

            }


        }
        for (LookPaperQuestionMain main : questionMobilesList) {

            List<QuestionOptionSave> questionOptionSaveList = main.getQuestionOptionSaveList();
            //出选项外循环。处理选项互斥。
            for (QuestionOptionSave optionSave : questionOptionSaveList) {
                List<String> optionMutexList = optionSave.getOptionMutexList();
                if (null == optionMutexList) {
                    continue;
                }
                for (String s : optionMutexList) {
                    TPreDiagnosisOptionMutex tPreDiagnosisOptionMutex = new TPreDiagnosisOptionMutex();
                    tPreDiagnosisOptionMutex.setOptionName(optionSave.getOptionName());
                    tPreDiagnosisOptionMutex.setOptionId(Integer.parseInt(optionSave.getOptionId()));
                    tPreDiagnosisOptionMutex.setMutexOptionId(map2.get(s));
                    tPreDiagnosisOptionMutexArrayList.add(tPreDiagnosisOptionMutex);
                }
                //更新 optionMutexList
                List<String> newOptionMutexList = new ArrayList<>();
                for (String s : optionMutexList) {
                    newOptionMutexList.add(map2.get(s) + "");
                }
                optionSave.setOptionMutexList(newOptionMutexList);
            }
        }
        //存子题设置
        for (TPreDiagnosisChild child : tPreDiagnosisChilds) {
            //set.add(child.getChildQuestionIdStr());
            child.setChildQuestionId(map.get(child.getChildQuestionIdStr()));

        }
        //存跳题设置
        for (TPreDiagnosisSkip skip : tPreDiagnosisSkips) {
            set.add(skip.getSkipQuestionIdStr());
            skip.setSkipQuestionId(map.get(skip.getSkipQuestionIdStr()));

        }

        if (!tPreDiagnosisSkips.isEmpty()) {
            //插入跳题
            tPreDiagnosisSkipMapper.insertList(tPreDiagnosisSkips);
        }
        if (!tPreDiagnosisChilds.isEmpty()) {
            //插入子题
            tPreDiagnosisChildMapper.insertList(tPreDiagnosisChilds);
        }
        if (!tPreDiagnosisOptionMutexArrayList.isEmpty()) {
            tPreDiagnosisOptionMutexMapper.insertList(tPreDiagnosisOptionMutexArrayList);
        }
        if (!tPreDiagnosisEventList.isEmpty()){
            tPreDiagnosisOptionEventService.saveBatch(tPreDiagnosisEventList);
        }
        //更新返回值（跳过的题目）
        for (int i = 0; i < lookPaperRecive.getQuestionMobilesList().size(); i++) {
            LookPaperQuestionMain lookPaperQuestionMain = lookPaperRecive.getQuestionMobilesList().get(i);
            List<QuestionOptionSave> questionOptionSaveList = lookPaperQuestionMain.getQuestionOptionSaveList();
            for (int i1 = 0; i1 < questionOptionSaveList.size(); i1++) {
                QuestionOptionSave optionSave = questionOptionSaveList.get(i1);
                List<String> skipSaveList = optionSave.getQuestionSkipSaveList();
                ArrayList<String> strings = new ArrayList<String>();
                for (int i2 = 0; i2 < skipSaveList.size(); i2++) {
                    strings.add(map.get(skipSaveList.get(i2)) + "");
                }
                optionSave.setQuestionSkipSaveList(strings);

                List<String> questionChildSaveList = optionSave.getQuestionChildSaveList();

                ArrayList<String> strings2 = new ArrayList<String>();
                if (questionChildSaveList != null) {
                    for (int i2 = 0; i2 < questionChildSaveList.size(); i2++) {
                        strings2.add(map.get(questionChildSaveList.get(i2)) + "");
                    }
                    optionSave.setQuestionChildSaveList(strings2);
                }

            }


        }

        tPreDiagnosisForm.setLongestQuestionNum(questionMobilesList.size());
        tPreDiagnosisForm.setTotalQuestionNum(questionMobilesList.size());
        tPreDiagnosisForm.setShortestQuestionNum(questionMobilesList.size() - set.size() - set2.size());
        if (tPreDiagnosisForm.getCueWordStatus() == null)
        {
            tPreDiagnosisForm.setCueWordStatus(0);
        }
        tPreDiagnosisForm.setCueWordText("请根据以上预问诊问答的内容生成专病电子病历，电子病历中的内容需全部来源于预问诊问答的内容，不能进行扩充。病历分类覆盖如下：主诉、现病史、既往史、个人史、过敏史、传染病史、手术史、输血史、家族史、月经史、体格检查，缺失的内容无需额外补充");
        tPreDiagnosisForm.setCueWordTrans("，并以json格式返回，注意json格式内容不要有空格，不要有换行符号，questionClassName字段不能为空字符串，content字段如果为空字符串则填入无，中文全部使用简体中文字字体。示例如下:[{\"content\":\"这里写主诉\",\"questionClassName\":\"主诉\"},{\"content\":\"这里写现病史\",\"questionClassName\":\"现病史\"},{\"content\":\".......\",\"questionClassName\":\"既往史\"}]");
        int insert = tPreDiagnosisFormMapper.insert(tPreDiagnosisForm);
        if (insert == 0) {
            throw new RuntimeException("表单编码重复");
        }
        settPreDiagnosisStructure(lookPaperRecive, tPreDiagnosisForm, map);
        //修改传进来的预诊单id，用来返回给前端。
        lookPaperRecive.setDiaId(tPreDiagnosisForm.getDiaId());
        return ResEntity.success(lookPaperRecive);
    }


    public void settPreDiagnosisStructure(LookPaperRecive lookPaperRecive, TPreDiagnosisForm tPreDiagnosisForm, HashMap<String, Integer> map) {
        //todo 从新插入 t_pre_diagnosis_structure表
        if (StringUtils.isNotBlank(lookPaperRecive.getDiaId())) {
            QueryWrapper<TPreDiagnosisStructure> wrapper = new QueryWrapper<>();
            wrapper.eq("dia_id", lookPaperRecive.getDiaId());
            List<TPreDiagnosisStructure> list = tPreDiagnosisStructureService.list(wrapper);
            if (list.isEmpty()) {
                return;
            }
            HashMap<String, String> stringStringHashMap = new HashMap<>();
            HashMap<Integer, Integer> stringStringHashMap2 = new HashMap<>();
            for (int i = 0; i < list.size(); i++) {
                TPreDiagnosisStructure tPreDiagnosisStructure = list.get(i);
                String s = IdUtil.getSnowflake(snowflake).nextIdStr();
                stringStringHashMap.put(tPreDiagnosisStructure.getDiagnosisStructureId(), s);
                tPreDiagnosisStructure.setDiaId(tPreDiagnosisForm.getDiaId());
                tPreDiagnosisStructure.setDiagnosisStructureId(s);
            }
            if (!list.isEmpty()) {
                tPreDiagnosisStructureService.saveBatch(list);
            }

            ArrayList<Integer> preStructureContentId = new ArrayList<>();
            ArrayList<Integer> newStructureContentId = new ArrayList<>();
            //重新插入tPreDiagnosisStructureService表
            //迭代stringStringHashMap
            for (Map.Entry<String, String> entry : stringStringHashMap.entrySet()) {

                QueryWrapper<TPreDiagnosisStructureContent> wrapper1 = new QueryWrapper<>();
                wrapper1.eq("diagnosis_structure_id", entry.getKey());
                List<TPreDiagnosisStructureContent> list1 = tPreDiagnosisStructureContentService.list(wrapper1);
                for (int i = 0; i < list1.size(); i++) {
                    preStructureContentId.add(list1.get(i).getStructureContentId());
                    TPreDiagnosisStructureContent tPreDiagnosisStructureContent = list1.get(i);
                    tPreDiagnosisStructureContent.setDiagnosisStructureId(entry.getValue());
                    tPreDiagnosisStructureContent.setStructureContentId(null);
                }
                tPreDiagnosisStructureContentService.saveBatch(list1);
                for (int i = 0; i < list1.size(); i++) {
                    newStructureContentId.add(list1.get(i).getStructureContentId());
                }
            }
            for (int i = 0; i < preStructureContentId.size(); i++) {
                stringStringHashMap2.put(preStructureContentId.get(i), newStructureContentId.get(i));
            }


            //重新t_pre_diagnosis_structure_child值 更改structure_content_id值为新的值和设置主键空，重新插入。
            stringStringHashMap2.forEach((key, value) -> {
                QueryWrapper<TPreDiagnosisStructureChild> wrapper1 = new QueryWrapper<>();
                wrapper1.eq("structure_content_id", key);
                List<TPreDiagnosisStructureChild> list1 = tPreDiagnosisStructureChildService.list(wrapper1);
                for (int i1 = 0; i1 < list1.size(); i1++) {
                    TPreDiagnosisStructureChild tPreDiagnosisStructureChild = list1.get(i1);
                    if ("questionId".equals(tPreDiagnosisStructureChild.getContentType())) {
                        Integer i = map.get(tPreDiagnosisStructureChild.getContent());
                        //log.error("==================================打印i{}：{}", i,tPreDiagnosisStructureChild.getQuestionIsDel());

                        if (i == null) {
                            tPreDiagnosisStructureChild.setQuestionIsDel(1);
                        }
                        tPreDiagnosisStructureChild.setContent(i+"");
                        //修改content字段改为当前的问题id
                    } else {
                        tPreDiagnosisStructureChild.setQuestionIsDel(0);
                    }
                    tPreDiagnosisStructureChild.setStructureChild(null);
                    tPreDiagnosisStructureChild.setStructureContentId(value);
                }
                tPreDiagnosisStructureChildService.saveBatch(list1);

            });


        }

    }

    public boolean checkDisMappingIsMu(CheckDisMappingIsMu checkDisMappingIsMu) {
        int count = tPreDiagnosisFormMapper.checkDisMappingIsMu(checkDisMappingIsMu);
        if (count > 0) {
            return true;
        }
        return false;
    }
}
