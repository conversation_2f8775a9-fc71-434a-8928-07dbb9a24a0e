package com.cbkj.diagnosis.service.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.cbkj.diagnosis.beans.TAdminInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EduSendMessage {

    @Schema(description =  "健康宣教id")
    private Integer tPropagandaEduId;

    @Schema(description =  "健康宣教标题")
    private String eduTitle;

    @Schema(description =  "健康宣教发送方式1.微信2短信")
    private String  roadExecuteEventWay;

    @Schema(description =  "就传里面的 patientList.userId")
    private List<TAdminInfo> patientList;
}
