package com.cbkj.diagnosis.service.statistics.impl;

import cn.hutool.json.JSONArray;
import com.cbkj.diagnosis.beans.statistics.BlockDis;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayDisService;
import com.cbkj.diagnosis.service.statistics.factory.StatisticsStrategy;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/26 16:20
 * @Version 1.0
 */
@Service("block-index-dis")
public class BlockDisService implements StatisticsStrategy {

    private StatisticsErverDayDisService statisticsErverDayDiagnosisService;

    public BlockDisService(StatisticsErverDayDisService statisticsErverDayDiagnosisService) {

        this.statisticsErverDayDiagnosisService = statisticsErverDayDiagnosisService;
    }

    /**
     * {
     * 	"names": ["患者服务量", "预诊量", "随访量", "健康宣教"],
     * 	"lists": [{
     * 			"x": "疾病1",
     * 			"y": [1, 2, 3, 4]
     * 		        },
     *        {
     * 			"x": "疾病121",
     * 			"y": [1, 2, 3, 4]
     *        },
     *        {
     * 			"x": "疾病1112",
     * 			"y": [1, 2, 3, 4]
     *        }, {
     * 			"x": "疾病22",
     * 			"y": [1, 2, 3, 4]
     *        }
     * 	]
     *
     *
     *
     * }
     * @param statisticsVo
     * @return
     */
    @Override
    public ResEntity getData(StatisticsVo statisticsVo) {
        List<BlockDis> list = statisticsErverDayDiagnosisService.getDisList(statisticsVo);
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        String[] names = {"患者服务量","预诊量","随访量","健康宣教"};
        stringObjectHashMap.put("names",names);
        JSONArray lists = new JSONArray();
        for (BlockDis blockDis : list) {
            HashMap<String, Object> map = new LinkedHashMap<>();
            map.put("x", blockDis.getName());
            map.put("y", new JSONArray(new ArrayList<String>() {{
                add(( Integer.parseInt(blockDis.getDiaNum())  + Integer.parseInt(blockDis.getFlowNum()) )+"");
                add(blockDis.getDiaNum());
                add(blockDis.getFlowNum());
                add(blockDis.getHealthNum());
            }}));
            lists.add(map);
        }
        stringObjectHashMap.put("lists",lists);
        return ResEntity.success(stringObjectHashMap);
    }
}
