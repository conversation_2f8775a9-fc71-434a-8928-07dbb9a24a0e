package com.cbkj.diagnosis.service.statistics.impl;

import com.cbkj.diagnosis.beans.statistics.BlockTwoDetail;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayDisService;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayHealthService;
import com.cbkj.diagnosis.service.statistics.factory.StatisticsStrategy;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/29 10:14
 * @Version 1.0
 */
@Service("block-index-2")
public class BlockTwoService implements StatisticsStrategy {

    private StatisticsErverDayDisService statisticsErverDayDisService;

    private StatisticsErverDayHealthService statisticsErverDayHealthService;

    public BlockTwoService(StatisticsErverDayDisService statisticsErverDayDisService, StatisticsErverDayHealthService statisticsErverDayHealthService) {
        this.statisticsErverDayDisService = statisticsErverDayDisService;
        this.statisticsErverDayHealthService = statisticsErverDayHealthService;
    }

    @Override
    public ResEntity getData(StatisticsVo statisticsVo) {
        //预诊
        List<BlockTwoDetail> list_0 = statisticsErverDayDisService.getBlockTwoList(statisticsVo);
        //随访
        List<BlockTwoDetail> list_1 = statisticsErverDayDisService.getBlockTwoListFlow(statisticsVo);
        List<BlockTwoDetail> list_2 = statisticsErverDayHealthService.getBlockTwoList(statisticsVo);
        List<BlockTwoDetail> blockTwoDetails = mergeAndSum(list_0, list_1, list_2);
        sortByTimeDescending(blockTwoDetails);

        String[] names = {"患者服务量", "预诊量", "随访量", "健康宣教"};
        List<BlockTwoDetail>[] lists = new List[]{blockTwoDetails, list_0, list_1, list_2};

        HashMap<String, Object> map = new HashMap<>(32);
        map.put("lists",lists);
        map.put("names",names);
        return ResEntity.success(map);

    }

    public List<BlockTwoDetail> mergeAndSum(List<BlockTwoDetail>... lists) {
        Map<String, Integer> timeToSumMap = new HashMap<>(32);

        // 遍历所有列表
        for (List<BlockTwoDetail> list : lists) {
            for (BlockTwoDetail detail : list) {
                String time = detail.getX();
                Integer num = detail.getY();

                // 更新时间对应的总和
                timeToSumMap.put(time, timeToSumMap.getOrDefault(time, 0) + num);
            }
        }

        // 构建最终的合并列表
        List<BlockTwoDetail> resultList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : timeToSumMap.entrySet()) {
            BlockTwoDetail mergedDetail = new BlockTwoDetail();
            mergedDetail.setX(entry.getKey());
            mergedDetail.setY(entry.getValue());
            resultList.add(mergedDetail);
        }
        return resultList;
    }

    public  void sortByTimeDescending(List<BlockTwoDetail> list) {
        Collections.sort(list, new Comparator<BlockTwoDetail>() {
            @Override
            public int compare(BlockTwoDetail detail1, BlockTwoDetail detail2) {
                // 降序排序
                return detail2.getY().compareTo(detail1.getY());
            }
        });
    }
}
