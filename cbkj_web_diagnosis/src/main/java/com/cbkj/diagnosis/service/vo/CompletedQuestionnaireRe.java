package com.cbkj.diagnosis.service.vo;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "已填问卷管理")
public class CompletedQuestionnaireRe implements Serializable {

    @Schema(description =  "时间类型：1.填写时间、2发送时间")
    private Integer dateType;
    private String startDate;
    private String endDate;

    @Schema(description =  "问卷名称")
    private String formName;

    @Schema(description =  "随访任务id")
    private String sroadIdsstr;


    @Schema(description =  "随访任务名称。")
    private String taskName;

    @Schema(description =  "1.微信2短信3.电话。")
    private String roadExecuteEventWay;
    private String patientName;
    @Schema(description =  "患者证件号")
    private String patientCardNum;

    @Schema(description =  "疾病名")
    private String disName;
    private String diaId;

    @Schema(description =  "行数", example = "10")
    private Integer limit = 10;
    @Schema(description =  "页码", example = "1")
    private Integer page = 1;

}
