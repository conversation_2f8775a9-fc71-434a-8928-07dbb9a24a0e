package com.cbkj.diagnosis.service.monitor;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cbkj.diagnosis.beans.monitor.dto.ManifestFeeDTO;
import com.cbkj.diagnosis.beans.health.MedicalPatientRecordCost;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
public interface MedicalPatientRecordCostService extends IService<MedicalPatientRecordCost> {

    ManifestFeeDTO getRecordCostByRecordId(String recordId);
}
