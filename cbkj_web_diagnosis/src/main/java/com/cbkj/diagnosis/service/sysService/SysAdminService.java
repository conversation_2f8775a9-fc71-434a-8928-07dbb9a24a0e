package com.cbkj.diagnosis.service.sysService;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.AdminDisList;
import com.cbkj.diagnosis.beans.business.SysAdminInfoDisMapping;
import com.cbkj.diagnosis.beans.business.SysDept;
import com.cbkj.diagnosis.beans.business.SysIns;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfoRule;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.MD5Util;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.common.utils.encry.RSAEncryption;
import com.cbkj.diagnosis.mapper.business.SysDeptMapper;
import com.cbkj.diagnosis.mapper.business.SysInsMapper;
import com.cbkj.diagnosis.mapper.sysmapper.AdminInfoMapper;
import com.cbkj.diagnosis.mapper.sysmapper.AdminRuleMapper;
import com.cbkj.diagnosis.service.business.SysAdminInfoDisMappingService;
import com.cbkj.diagnosis.service.vo.QueryContent;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLDecoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service

public class SysAdminService {
    @Value("${rsa.privateKey}")
    private String privateKey;
    private final RSAEncryption rsaEncryption;
    private final AdminInfoMapper adminInfoMapper;
    private final AdminRuleMapper adminRuleMapper;
    private final SysInsMapper sysInsMapper;

    private final SysDeptMapper sysDeptMapper;
    private final SysAdminInfoDisMappingService sysAdminInfoDisMappingService;


    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;

    @Value("${change.password.reminder:90}")
    private Integer changePasswordReminder;

    SysAdminService(AdminInfoMapper adminInfoMapper,
                    AdminRuleMapper adminRuleMapper,
                    RSAEncryption rsaEncryption,
                    SysInsMapper sysInsMapper,
                    SysDeptMapper sysDeptMapper, SysAdminInfoDisMappingService sysAdminInfoDisMappingService) {
        this.adminInfoMapper = adminInfoMapper;
        this.adminRuleMapper = adminRuleMapper;
        this.rsaEncryption = rsaEncryption;
        this.sysInsMapper = sysInsMapper;
        this.sysDeptMapper = sysDeptMapper;
        this.sysAdminInfoDisMappingService = sysAdminInfoDisMappingService;
    }

    /**
     * 获取用户列表
     *
     * @param queryContent
     * @param page
     * @return
     */

    public Object getAdminList(String queryContent, Page page) {
        //PageHelper.startPage(page.getPage(), page.getLimit());
        QueryContent content = new QueryContent();
        content.setQueryContent(queryContent);
        content.setPage( (page.getPage()-1)  *  page.getLimit() );
        content.setLimit(page.getLimit() );
        List<AdminInfo> list = adminInfoMapper.getAdminList(content);
        PageHelper.clearPage();
        for (int i = 0; i < list.size(); i++) {
            AdminInfo adminInfo = list.get(i);
            adminInfo.setUserDisMappingList(adminInfoMapper.getAdminDisList(list.get(i).getUserId())) ;
        }
        //计算全部数量
        int total = adminInfoMapper.countAdminList(content);
        //当前查询数量
        int i = page.getPage() * page.getLimit();
        return Page.getLayuiData(true,"SUCCESS", total, total > i, list);
    }


    public boolean checkUserIsHas(AdminInfo adminInfo) {
        Map<String, Object> param = new HashMap<>(16);
        // param.put("id", adminInfo.getUserId());
        param.put("name", adminInfo.getUsername());

        List<Map<String, Object>> infos = adminInfoMapper.validateParam(param);
        if (null != infos && !infos.isEmpty()) {

            return true;
        }
        return false;
    }


    public List<Map<String, Object>> checkUserIsHasByEmployee(AdminInfo adminInfo) {
        Map<String, Object> param = new HashMap<>(16);
        // param.put("id", adminInfo.getUserId());
        param.put("employeeId", adminInfo.getEmployeeId());
        param.put("insCode", adminInfo.getInsCode());
        param.put("appId", adminInfo.getAppId());

        List<Map<String, Object>> infos = adminInfoMapper.validateParam(param);
//        if (null != infos && !infos.isEmpty()) {
//
//            return infos;
//        }
        return infos;
    }

    /**
     * 新建用户
     *
     * @param adminInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertAdmin(AdminInfo adminInfo) {

        if (checkUserIsHas(adminInfo)) {
            return ResEntity.error("用户存在了");
        }


        if (!StringUtils.isBlank(adminInfo.getPassword())) {
            String newPwd = URLDecoder.decode(adminInfo.getPassword());
            try {
                newPwd = rsaEncryption.dencryptByPrivateKey(newPwd, privateKey);
            } catch (Exception e) {
                return new ResEntity(false, "加密错误", null);
            }
            //newPwd = MD5Util.encode(newPwd);
            adminInfo.setPassword(newPwd);
        } else {
            //默认密码123456
            adminInfo.setPassword(MD5Util.encode("123456"));
        }
        adminInfo.setUserId(IdUtil.simpleUUID());
        adminInfo.setCreateDate(new Date());
        adminInfo.setCreateUser(AdminWebUtils.getCurrentHr().getUserId());
        adminInfo.setCreateUserName(AdminWebUtils.getCurrentHr().getNameZh());

        SysIns objectById = sysInsMapper.getObjectById(adminInfo.getInsId());
        if (objectById != null) {
            adminInfo.setInsName(objectById.getInsName());
            adminInfo.setInsCode(objectById.getInsCode());
            adminInfo.setAppId(objectById.getAppId());
        }

        SysDept objectById1 = sysDeptMapper.getObjectById(adminInfo.getDeptId());
        if (null != objectById1) {
            adminInfo.setDeptCode(objectById1.getDeptCode());
        }


//        if (StringUtils.isBlank(adminInfo.get))
        adminInfoMapper.insertUserInfo(adminInfo);
        //保存绑定疾病
        List<AdminDisList> userDisMappingList = adminInfo.getUserDisMappingList();
        if (userDisMappingList != null && userDisMappingList.size() > 0){
            List<SysAdminInfoDisMapping> sysAdminInfoDisMappingList = userDisMappingList.stream().map(
                    adminDisList -> new SysAdminInfoDisMapping(adminDisList.getDisId(), adminDisList.getDisName(), adminInfo.getUserId(),null)
            ).collect(Collectors.toList());
            if (sysAdminInfoDisMappingList.size()>0){
                sysAdminInfoDisMappingService.saveBatch(sysAdminInfoDisMappingList);
            }
        }
        //设置角色
        List<AdminRule> roles = adminInfo.getRoles();
        for (int i = 0; i < roles.size(); i++) {
            AdminRule adminRule = roles.get(i);
            AdminInfoRule adminInfoRule = new AdminInfoRule();
            adminInfoRule.setRoleId(adminRule.getRoleId());
            adminInfoRule.setUserId(adminInfo.getUserId());
            adminRuleMapper.insertSysAdminRule(adminInfoRule);
        }

        return ResEntity.success(null);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertAdminHis(AdminInfo adminInfo) {
        if (checkUserIsHas(adminInfo)) {
            return ResEntity.error("用户存在了");
        }
        adminInfo.setCreateDate(new Date());
        adminInfo.setCreateUserName("his同步");
        adminInfoMapper.insertUserInfo(adminInfo);
        //设置角色
        AdminInfoRule adminInfoRule = new AdminInfoRule();
        adminInfoRule.setRoleId("2");
        adminInfoRule.setUserId(adminInfo.getUserId());
        adminRuleMapper.insertSysAdminRule(adminInfoRule);

        return ResEntity.success(adminInfo);
    }

    /**
     * 更新用户
     *
     * @param adminInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity updateAdmin(AdminInfo adminInfo) {
        if (checkUserIsHas(adminInfo)) {
            AdminInfo userById = adminInfoMapper.getUserById(adminInfo.getUserId());
            if (!userById.getUsername().equals(adminInfo.getUsername())) {
                return ResEntity.error("用户存在了");
            }
        }
        if (!StringUtils.isBlank(adminInfo.getPassword())) {
            String newPwd = URLDecoder.decode(adminInfo.getPassword());
            try {
                newPwd = rsaEncryption.dencryptByPrivateKey(newPwd, privateKey);
            } catch (Exception e) {
                return new ResEntity(false, "加密错误", null);
            }
            //newPwd = MD5Util.encode(newPwd);
            adminInfo.setPassword(newPwd);
        }


        SysIns objectById = sysInsMapper.getObjectById(adminInfo.getInsId());
        if (objectById != null) {
            adminInfo.setInsName(objectById.getInsName());
            adminInfo.setInsCode(objectById.getInsCode());
            adminInfo.setAppId(objectById.getAppId());
        }

        SysDept objectById1 = sysDeptMapper.getObjectById(adminInfo.getDeptId());
        if (objectById1 != null) {
            adminInfo.setDeptCode(objectById1.getDeptCode());
        }


        adminInfoMapper.updateUserInfo(adminInfo);
        //删除角色
        adminRuleMapper.deleteUserRoleByUserId(adminInfo.getUserId());
        //设置角色
        List<AdminRule> roles = adminInfo.getRoles();
        for (int i = 0; i < roles.size(); i++) {
            AdminRule adminRule = roles.get(i);
            AdminInfoRule adminInfoRule = new AdminInfoRule();
            adminInfoRule.setRoleId(adminRule.getRoleId());
            adminInfoRule.setUserId(adminInfo.getUserId());
            adminRuleMapper.insertSysAdminRule(adminInfoRule);
        }

        List<AdminDisList> userDisMappingList = adminInfo.getUserDisMappingList();
        QueryWrapper<SysAdminInfoDisMapping> sysAdminInfoDisMappingQueryWrapper = new QueryWrapper<>();
        sysAdminInfoDisMappingQueryWrapper.eq("user_id", adminInfo.getUserId());
        sysAdminInfoDisMappingService.remove(sysAdminInfoDisMappingQueryWrapper);
        if (userDisMappingList != null && userDisMappingList.size() > 0){
            List<SysAdminInfoDisMapping> sysAdminInfoDisMappingList = userDisMappingList.stream().map(
                    adminDisList -> new SysAdminInfoDisMapping(adminDisList.getDisId(), adminDisList.getDisName(), adminInfo.getUserId(),null)
            ).collect(Collectors.toList());
            if (sysAdminInfoDisMappingList.size()>0){
                sysAdminInfoDisMappingService.saveBatch(sysAdminInfoDisMappingList);
            }
        }


        return ResEntity.success(null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAdmin2(AdminInfo adminInfo) {
        if (StringUtils.isBlank(adminInfo.getUserId())){
            return;
        }
        AdminInfo userById = adminInfoMapper.getUserById(adminInfo.getUserId());
        if (userById != null && StringUtils.isBlank(userById.getDeptName())) {
            adminInfoMapper.updateUserInfo(adminInfo);
        }

    }

    /**
     * 删除--------用户
     *
     * @param userId
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(String userId) {
        AdminInfo adminInfo = adminInfoMapper.getUserById(userId);
        adminInfo.setStatus("1");
        adminInfoMapper.updateUserInfo(adminInfo);
    }

    public ResEntity updatePwd(String oldPwd, String newPwd) {

        String id = AdminWebUtils.getCurrentHr().getUserId();

        if (StringUtils.isBlank(id) || StringUtils.isBlank(oldPwd) || StringUtils.isBlank(newPwd)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }

        try {
            oldPwd = rsaEncryption.dencryptByPrivateKey(oldPwd, privateKey);
            newPwd = rsaEncryption.dencryptByPrivateKey(newPwd, privateKey);
        } catch (Exception e) {
            return new ResEntity(false, "加密错误", null);
        }

        if (!this.checkPassword(newPwd)) {
            return new ResEntity(false, "密码需为数字、字母、特殊符号中的最少2种，最小8位，最大18位！", null);
        }

        AdminInfo admin = adminInfoMapper.selectByPrimaryKey(id);
        if (admin == null) {
            return new ResEntity(false, "当前用户不存在！", null);
        }
        if (!MD5Util.encode(oldPwd).equals(admin.getPassword())) {
            return new ResEntity(false, "原密码错误！", null);
        }

        newPwd = MD5Util.encode(newPwd);
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("newPwd", newPwd);
        params.put("ids", id.split(","));

        long rows = adminInfoMapper.updatePwd(params);
        return new ResEntity(true, Constant.SUCCESS_DX, rows);
    }

    /**
     * 密码规则校验
     *
     * @param str
     * @return boolean
     * <AUTHOR>
     * @date 2020/8/21
     */
    private boolean checkPassword(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }

        int a = 0;//是否是数字
        int b = 0;//师傅是字母
        int c = 0;//是否是其他符号
        for (int i = 0; i < str.length(); i++) {
            if (Character.isWhitespace(str.charAt(i))) {//是否是有空白符，不允许有空白符
                return false;
            } else if (Character.isDigit(str.charAt(i))) {
                a = 1;
            } else if (Character.isLetter(str.charAt(i))) {
                b = 1;
            } else {
                c = 1;
            }
        }

        boolean checkRule = false;
        int sum = a + b + c;
        if (sum >= 2) {
            checkRule = true;
        }
        boolean checkLength = false;
        int length = str.length();
        if (length >= 8 && length <= 18) {
            checkLength = true;
        }

        if (checkRule && checkLength) {
            return true;
        }
        return false;
    }

    /**
     * 获取角色分页列表
     *
     * @param page
     * @param roleName
     * @return
     */
    public Object getRoleListPage(Page page, String roleName) {
        AdminRule adminRule = new AdminRule();
        adminRule.setRoleName(roleName);
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<AdminRule> list = adminInfoMapper.getRoleListPage(adminRule);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 删除角色和用户关联的映射。无法删除id是1 的root管理员角色。
     *
     * @param roleId
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteRoleInfo(String roleId) {
        if (Constant.BASIC_STRING_ONE.equals(roleId)) {

        } else {
            adminInfoMapper.deleteAdminInfoRulebyRuleId(roleId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRoleInfo(AdminRule adminRule) {
        if (StringUtils.isBlank(adminRule.getRoleId())) {
            adminRule.setRoleId(IdUtil.getSnowflake(snowflake).nextIdStr());
            adminRule.setCreateDate(new Date());
            adminRule.setCreateUser(AdminWebUtils.getCurrentHr().getUserId());
            adminRule.setRnameZh(AdminWebUtils.getCurrentHr().getNameZh());
            adminInfoMapper.insertRule(adminRule);
        }

        adminInfoMapper.updateRoleInfo(adminRule);

    }

    /**
     * 获取科室列表
     */
    public List<SysDept> getDeptList(String deptName, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        SysDept sysDept = new SysDept();
        sysDept.setDeptName(deptName);
        List<SysDept> pageListByObj = sysDeptMapper.getPageListByObj(sysDept);
        PageHelper.clearPage();
        return pageListByObj;
    }

    /**
     * 获取医院名字
     */
    public List<SysIns> getIns(SysIns sysIns) {

        List<SysIns> pageListByObj = sysInsMapper.getPageListByObj(sysIns);
        return pageListByObj;

    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity changePwdNoOldBean(String ids, String newPwd) {
        if (StringUtils.isBlank(ids) || StringUtils.isBlank(newPwd) ) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        return editPWD(ids,newPwd);
    }

    private boolean checkPassword(String ids, String oldPwd) {
        //先判断旧密码是否正确

        AdminInfo userById = adminInfoMapper.getUserById(ids);
        try {
            oldPwd = rsaEncryption.dencryptByPrivateKey(oldPwd, privateKey);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (null == userById || !userById.getPassword().equals(oldPwd)) {
            return false;
        }
        return true;
    }
    public ResEntity changePwd(String ids, String newPwd, String oldPwd) {

        if (StringUtils.isBlank(ids) || StringUtils.isBlank(newPwd) || StringUtils.isBlank(oldPwd)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        boolean b = checkPassword(ids, oldPwd);
        if (!b){
            return new ResEntity(false, "原密码错误！", null);
        }
        return editPWD(ids,newPwd);
    }

    private ResEntity editPWD(String ids,String newPwd){
        try {
            newPwd = rsaEncryption.dencryptByPrivateKey(newPwd, privateKey);
        } catch (Exception e) {
            return new ResEntity(false, "加密错误", null);
        }
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("newPwd", newPwd);
        params.put("ids", ids.split(","));
        params.put("lastUpdatePwd", new Date());
        long rows = adminInfoMapper.updatePwd(params);
        return new ResEntity(true, Constant.SUCCESS_DX, rows);
    }

    public AdminInfo getAdminInfoByUserId(String userId) {
        AdminInfo userById = adminInfoMapper.getUserById(userId);
        return userById;
    }

    public AdminInfo getUserIsHasByEmployee(AdminInfo adminInfo) {
        AdminInfo userById = adminInfoMapper.getUserByEmployeeId(adminInfo);
        return userById;
    }

    public Object getDiagnosisUserList(String deptIds,Integer page,Integer limit,String doctorName) {
//        List<AdminInfo> adminInfos=  new ArrayList<>();
//        PageHelper.startPage(page, limit);
//        if (StringUtils.isNotBlank(deptIds)) {
//            String[] split = deptIds.split(",");
//            ArrayList<String> strings = new ArrayList<>(Arrays.asList(split));
//            if (strings.size() > 0) {
//                adminInfos= adminInfoMapper.getDiagnosisUserList(strings);
//            }
//        }
        List<AdminInfo> adminInfos = adminInfoMapper.getSuiFangUserList(doctorName);
        PageHelper.clearPage();
        return Page.getLayUiTablePageData(adminInfos);
    }

    public Object getSuiFangUserList(Integer page, Integer limit,String doctorName) {
        PageHelper.startPage(page, limit);
        List<AdminInfo> adminInfos = adminInfoMapper.getSuiFangUserList(doctorName);
        return Page.getLayUiTablePageData(adminInfos);
    }

    /**
     * 判断是否需要修改密码
     *
     * @param admin admin
     */
    public boolean needUpdatePwd(AdminInfo admin) {
        if (0 == changePasswordReminder) {
            return false;
        }
        Date lastUpdatePwd = (admin.getLastUpdatePwd() != null) ? admin.getLastUpdatePwd() : admin.getCreateDate();

        LocalDate today = LocalDate.now();
        //最近一次修改密码时间 往后延迟给定时间天
        LocalDate otherDate = lastUpdatePwd.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate futureDate = otherDate.plusDays(changePasswordReminder);

        if (futureDate.isAfter(today)) {
            return false;
        } else {
            return true;
        }
    }
}
