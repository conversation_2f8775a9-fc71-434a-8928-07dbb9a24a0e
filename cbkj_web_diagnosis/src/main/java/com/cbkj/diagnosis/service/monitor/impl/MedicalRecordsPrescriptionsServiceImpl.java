package com.cbkj.diagnosis.service.monitor.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.monitor.dto.PrescriptionInfoDTO;
import com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptions;
import com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem;
import com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem;
import com.cbkj.diagnosis.beans.response.MedicalRecordsPrescriptionsResponse;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsPrescriptionsMapper;
import com.cbkj.diagnosis.service.convert.MedicalRecordsPrescriptionsConvert;
import com.cbkj.diagnosis.service.monitor.MedicalRecordsPrescriptionsItemService;
import com.cbkj.diagnosis.service.monitor.MedicalRecordsPrescriptionsService;
import com.cbkj.diagnosis.service.monitor.MedicalWestPrescriptionsItemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 就诊处方 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Service
public class MedicalRecordsPrescriptionsServiceImpl extends ServiceImpl<MedicalRecordsPrescriptionsMapper, MedicalRecordsPrescriptions> implements MedicalRecordsPrescriptionsService {

    @Autowired
    private MedicalRecordsPrescriptionsMapper medicalRecordsPrescriptionsMapper;

    @Autowired
    private MedicalRecordsPrescriptionsItemService medicalRecordsPrescriptionsItemService;

    @Autowired
    private MedicalWestPrescriptionsItemService medicalWestPrescriptionsItemService;


    @Override
    public PrescriptionInfoDTO getMedicalRecordsPrescriptionsByRecordId(String recordId) {
//        ArrayList<MedicalRecordsPrescriptionsResponse> list = new ArrayList<>();
        List<MedicalRecordsPrescriptionsResponse> response = medicalRecordsPrescriptionsMapper.getMedicalRecordsPrescriptionsByRecordId(recordId);
        List<MedicalRecordsPrescriptionsResponse> response2 = medicalRecordsPrescriptionsMapper.getMedicalRecordsWestPrescriptionsByRecordId(recordId);
//        if (response != null && !response.isEmpty()){
//            list.addAll(response);
//        }
//        if (response2 != null && !response2.isEmpty()){
//            list.addAll(response2);
//        }
//        if (list.isEmpty()){
//            return new PrescriptionInfoDTO();
//        }
        List<List<MedicalRecordsPrescriptionsItem>> prescriptionsItem = new ArrayList<>();
        List<List<MedicalWestPrescriptionsItem>> westPrescriptionsItem = new ArrayList<>();

        List<List<MedicalRecordsPrescriptionsItem>> finalPrescriptionsItem = prescriptionsItem;
        List<List<MedicalWestPrescriptionsItem>> finalWestPrescriptionsItem = westPrescriptionsItem;
        if (response != null && !response.isEmpty()) {
            response.forEach(item -> {
                List<MedicalRecordsPrescriptionsItem> a = medicalRecordsPrescriptionsItemService.getMedicalRecordsPrescriptionsItemByPreId(item.getTcmPreId());
                finalPrescriptionsItem.add(a);

            });
        }
        if (response2 != null && !response2.isEmpty()) {
            response2.forEach(item -> {
                List<MedicalWestPrescriptionsItem> a = medicalWestPrescriptionsItemService.getMedicalWestPrescriptionsItemByWestPreId(item.getWestPreId());
                finalWestPrescriptionsItem.add(a);
            });
        }


        return MedicalRecordsPrescriptionsConvert.medicalRecordsConvertToDTO(response,response2, prescriptionsItem, westPrescriptionsItem);
    }
}
