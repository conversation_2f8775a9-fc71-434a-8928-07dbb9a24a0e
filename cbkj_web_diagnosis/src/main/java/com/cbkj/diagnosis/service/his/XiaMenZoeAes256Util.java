package com.cbkj.diagnosis.service.his;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 * @date 2020/04/27
 */
public class XiaMenZoeAes256Util {

    public static final String ENCRYPTION_ALGORITHM = "AES";
    public static final String CIPHER_PARAM = "AES/CBC/PKCS5Padding";

    /**
     * 自定义秘钥、偏移量加密
     * @Title: encrypt
     * @Description: AES加密
     * @param enString 用来加密的明文
     * @param enString 用来加密的密钥
     * @param enString 用来加密的偏移量
     * @throws Exception
     * @return String 加密后密文
     */
    public static String ownEncrypt(String enString, String secretKey, String ivParameter) {

        try{
            Base64 base64 = new Base64();
            //使用自定义的偏移量
            IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes());
            //使用自定义的秘钥
            SecretKeySpec key = new SecretKeySpec(secretKey.getBytes(), ENCRYPTION_ALGORITHM);

            Cipher cipher = Cipher.getInstance(CIPHER_PARAM);
            cipher.init(Cipher.ENCRYPT_MODE, key, iv);
            byte[] encrypted = cipher.doFinal(enString.getBytes("utf-8"));
            return byte2hex(encrypted).toLowerCase();
        }catch (Exception e){
            return null;
        }

    }

    /**
     * 自定义秘钥、偏移量解密
     * @Title: decrypt
     * @Description: AES解密
     * @param deString 用来解密的密文
     * @throws Exception
     * @return String 解密后的明文
     */
    public static String ownDecrypt(String deString,String secretKey, String ivParameter){
        try {
            //使用自定义的偏移量
            IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes());
            //使用自定义的秘钥
            SecretKeySpec key = new SecretKeySpec(secretKey.getBytes(), ENCRYPTION_ALGORITHM);

            Base64 base64 = new Base64();
            Cipher cipher = Cipher.getInstance(CIPHER_PARAM);
            cipher.init(Cipher.DECRYPT_MODE, key, iv);
            byte[] decrypted = hex2byte(deString);
            byte[] original = cipher.doFinal(decrypted);
            return new String(original, "utf-8");
        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * string 转byte
     * @param strhex
     * @return
     */
    private static byte[] hex2byte(String strhex) {
        if (strhex == null) {
            return null;
        }
        int l = strhex.length();
        if (l % 2 == 1) {
            return null;
        }
        byte[] b = new byte[l / 2];
        for (int i = 0; i != l / 2; i++) {
            b[i] = (byte) Integer.parseInt(strhex.substring(i * 2, i * 2 + 2),
                    16);
        }
        return b;
    }

    /**
     * byte 转 string
     * @param b
     * @return
     */
    private static String byte2hex(byte[] b) {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }
        }
        return hs.toUpperCase();
    }


    public static void main(String[] args) {
        JSONObject paramEncrypt = new JSONObject();
        paramEncrypt.put("appCode", "HC_XMSZYY_WX");
        paramEncrypt.put("telephone", "13216108997");
        paramEncrypt.put("templateId", "f16601b3b50211ee85090050569411b9");
        paramEncrypt.put("linkUrl", "https://www.xmtcm.com/pdfu/mobile/login?cbdata=bdNf0KlySD8yz0+J/YuxqJ5M+tfkFscNzNxOmVxMZceAqZ7VuoTWs2jYCiC1sLk0EJR0qNIPivlwXwS/AGm+ut8akp8dz+PRcUAXuGxV6Y1UYIDgfHps7hRp/QwdO84K1UOT3euN98OBPhrTEE63zcwvU2BWVCwy16K+FGzcm/pLjcVlLwmL2OyrOzNo/dFq70k/f67Pf+VQK9pbZfmdGPs7WxtWx8bbAxW5RRj0Xrdq0pnokRID/oEYTMQFv1Xe&jumpUrl=doctorfollowup%3Fyuzhen%3D1%26diaId%3Da49e679576c84a8282089ae70880c39d");
        paramEncrypt.put("titleParameter", null);//标题字符串，|隔开示列：”标题1|标题2”详见模板地址
//        paramEncrypt.put("contentParameter", "张三|2023-12-20 9:00:00");//内容字符串，|隔开示列：”内容1|内容2”详见模板地址
        paramEncrypt.put("contentParameter", "患者李辉1|医生李辉2|测试科室1|测试名称");//内容字符串，|隔开示列：”内容1|内容2”详见模板地址

        String result = XiaMenZoeAes256Util.ownEncrypt(paramEncrypt.toJSONString(), "16e9853fc19211ea864e005056941120", "d9a7111ea864e344");

        System.out.println(result);

        System.out.println(XiaMenZoeAes256Util.ownDecrypt(result, "16e9853fc19211ea864e005056941120", "d9a7111ea864e344"));
    }


}
