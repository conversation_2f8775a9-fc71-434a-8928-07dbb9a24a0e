package com.cbkj.diagnosis.service.his;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.TAdminInfo2;
import com.cbkj.diagnosis.beans.business.SysDept;
import com.cbkj.diagnosis.beans.business.SysIns;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisForm;
import com.cbkj.diagnosis.beans.business.TRecord;
import com.cbkj.diagnosis.beans.business.requestvo.NoEcrpy;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.health.MedicalRecordsNoEn;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.common.config.security.TokenBo;
import com.cbkj.diagnosis.common.config.security.TokenUtil;
import com.cbkj.diagnosis.common.utils.IDUtil;
import com.cbkj.diagnosis.common.utils.MD5Util;
import com.cbkj.diagnosis.controller.his.vo.QrCodeRequest;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import com.cbkj.diagnosis.mapper.business.TRecordMapper;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsMapper;
import com.cbkj.diagnosis.mapper.sysmapper.AdminRuleMapper;
import com.cbkj.diagnosis.service.sys.SysInsService;
import com.cbkj.diagnosis.service.sysService.SysAdminService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AESPKCS7Util;
import com.cbkj.diagnosis.utils.CardUtil;
import com.cbkj.diagnosis.utils.SystemConstants;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HisService {
    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;
    private final SysAdminService sysAdminService;
    private final TAdminInfoMapper tAdminInfoMapper;
    private final TokenUtil tokenUtil;
    private final SysInsService sysInsService;
    private final AdminRuleMapper adminRuleMapper;

    private final MedicalRecordsMapper medicalRecordsMapper;

    private TPreDiagnosisFormMapper tPreDiagnosisFormMapper;

    @Value("${xia.men.mobile.wx.neiwang_url}")
    private String url;

    @Value("${xia.men.mobile.wx.url}")
    private String waiwangURL;

    @Value("${sys.aes.key}")
    private String key ;

    public HisService(SysAdminService sysAdminService, TAdminInfoMapper tAdminInfoMapper, TokenUtil tokenUtil, SysInsService sysInsService, AdminRuleMapper adminRuleMapper, MedicalRecordsMapper medicalRecordsMapper, TPreDiagnosisFormMapper tPreDiagnosisFormMapper) {
        this.sysAdminService = sysAdminService;
        this.tAdminInfoMapper = tAdminInfoMapper;
        this.tokenUtil = tokenUtil;
        this.sysInsService = sysInsService;
        this.adminRuleMapper = adminRuleMapper;
        this.medicalRecordsMapper = medicalRecordsMapper;
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
    }

    @Autowired
    private TRecordMapper tRecordMapper;

    @Transactional(rollbackFor = Exception.class)
    public ResEntity decryptContent(String stime,String etime) {

        List<TRecord> list = tRecordMapper.getDecryptContent(stime,etime);
        for (int i = 0; i < list.size(); i++) {
            TRecord tRecord = list.get(i);
            tRecordMapper.updateByPrimaryKey(tRecord);
        }
        return  ResEntity.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity getUserLoginInfo(String cbdata) {
        //扁鹊登录
        if (StringUtil.isNotEmpty(cbdata)) {
            String mak = cbdata;
//            try {
            String content = null;
            try {
                content = AESPKCS7Util.decrypt(mak, key, "base64");
                log.debug("打印cbdata解密后的参数：{}", content);
            } catch (Exception e) {
                e.printStackTrace();
                log.error(e.getMessage());
                return ResEntity.error("错误的入参");
            }

            if (StringUtil.isNotEmpty(content)) {
                Map map = AESPKCS7Util.jsonToMap(content);
                log.debug("打印map解密后的映射参数："+ JSON.toJSONString(map));

                AdminInfo adminInfo = new AdminInfo();
                String appId = (String) map.get("appId");
                String insCode = (String) map.get("insCode");
                String insName = (String) map.get("insName");
                if (StringUtils.isBlank(appId)) {
                    return ResEntity.error("缺少参数appId");
                }
                if (StringUtils.isBlank(insCode)) {
                    return ResEntity.error("缺少参数insCode");
                }
                if (StringUtils.isBlank(insName)) {
                    return ResEntity.error("缺少参数insName");
                }
                Object doctorInfo = map.get("doctorInfo");
                Object patientInfo = map.get("patientInfo");
                Object recordInfo = map.get("recordInfo");

                adminInfo.setAppId(appId);


                adminInfo.setInsCode(insCode);


                adminInfo.setInsName(insName);

                //检查机构是否存在。
                SysIns sysIns = sysInsService.checkAndInsertIns(insCode, insName, appId);
                adminInfo.setInsId(sysIns.getInsId());
                if (doctorInfo != null) {
                    HashMap<String, String> doctorInfoJson = (HashMap<String, String>) doctorInfo;
                    doctorInfoJson.put("appId",appId);
                    doctorInfoJson.put("insCode",insCode);
                    ResEntity resEntity = checkAndReturnDoctor(doctorInfoJson, adminInfo);
                    if (!resEntity.getStatus()) {
                        return resEntity;
                    }
                    adminInfo = (AdminInfo) resEntity.getData();
                } else {
                    return new ResEntity(false, "缺少参数！doctorInfo", null);
                }
                TAdminInfo info = null;
                if (patientInfo != null) {
                    HashMap<String, String> patientInfoJson = (HashMap<String, String>) patientInfo;
                    String patientName = patientInfoJson.get("patientName");
                    String patientCardNumber = patientInfoJson.get("patientCardNumber");
                    String patientPhone = patientInfoJson.get("patientPhone");
                    if (StringUtils.isBlank(patientPhone)) {
                        return new ResEntity(false, "患者缺少必要参数！", null);
                    }
                    String patientCardType = patientInfoJson.get("patientCardType");
                    String healthCardNum = patientInfoJson.get("healthCardNum");

                    TAdminInfo tAdminInfo = new TAdminInfo();
                    tAdminInfo.setIdcard(patientCardNumber);
                    tAdminInfo.setMobile(patientPhone);
                    tAdminInfo.setName(patientName);
                    tAdminInfo.setPatientCardType(patientCardType);
                    tAdminInfo.setHealthCardNum(healthCardNum);
                    ResEntity userIdOrInsertUser = getUserIdOrInsertUser(tAdminInfo);
                    if (!userIdOrInsertUser.getStatus()) {
                        return userIdOrInsertUser;
                    }
                    info = (TAdminInfo) userIdOrInsertUser.getData();
                }
                if (recordInfo != null) {
                    HashMap<String, String> recordInfoJson = (HashMap<String, String>) recordInfo;
                    String visitNo = recordInfoJson.get("visitNo");
                    String insCode1 = recordInfoJson.get("insCode");
                    String insName2 = recordInfoJson.get("insName");
                    String deptCode = recordInfoJson.get("deptCode");
                    String deptName = recordInfoJson.get("deptName");
                    String recordTime = recordInfoJson.get("recordTime");
                    MedicalRecords medicalRecords = new MedicalRecords();
                    medicalRecords.setInsId(sysIns.getInsId());
                    //检查科室
                    SysDept sysDept = sysInsService.checkAndInsertDept(sysIns, deptCode, deptName);
                    medicalRecords.setVisitNo(visitNo);
                    medicalRecords.setInsCode(insCode1);
                    medicalRecords.setInsName(insName2);
                    medicalRecords.setDeptName(deptName);
                    medicalRecords.setDeptId(sysDept.getDeptId());
                    medicalRecords.setDeptCode(deptCode);
                    medicalRecords.setDeptName(deptName);
                    medicalRecords.setCreateDate(new Date());
                    adminInfo.setDeptId(sysDept.getDeptId());
                    adminInfo.setDeptCode(sysDept.getDeptCode());
                    adminInfo.setDeptName(sysDept.getDeptName());
                    sysAdminService.updateAdmin2(adminInfo);
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    try {
                        medicalRecords.setRecordTime(simpleDateFormat.parse(recordTime));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    medicalRecords.setAppId(appId);
                    medicalRecords.setDoctorId(adminInfo.getUserId());
                    medicalRecords.setDoctorName(adminInfo.getNameZh());
                    if (null != info) {
                        medicalRecords.setPatientId(info.getUserId());
                        medicalRecords.setPatientAge(info.getAge());
                        medicalRecords.setPatientPhone(info.getMobile());
                        medicalRecords.setPatientCardNumber(info.getCardNumber());
                        medicalRecords.setPatientName(info.getUserName());
                        medicalRecords.setPatientSex(info.getSex());

                    }
                    getUserIdOrInsertRecord(medicalRecords);
                }
                TokenBo tokenBo = tokenUtil.createTokenBo(adminInfo);
                return ResEntity.success(tokenBo);

            } else {
                return new ResEntity(false, "缺少参数！", null);
            }
//            } catch (Exception e) {
//                return new ResEntity(false, "缺少参数！", null);
//            }
        }
        return ResEntity.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity checkAndReturnDoctor(HashMap<String, String> doctorInfoJson, AdminInfo adminInfo) {
        String doctorName = doctorInfoJson.get("doctorName");
        String employeeId = doctorInfoJson.get("employeeId");
        String appId = doctorInfoJson.get("appId");
        String insCode = doctorInfoJson.get("insCode");

        String doctorCardNumber = doctorInfoJson.get("doctorCardNumber");
        if (StringUtils.isBlank(doctorName) ||
                StringUtils.isBlank(employeeId) ||
                StringUtils.isBlank(doctorCardNumber)) {
            return ResEntity.error("缺少医生必要参数");
        }
        //检查医生是否存在

        adminInfo.setEmployeeId(employeeId);
        adminInfo.setCertificate(doctorCardNumber);
        adminInfo.setAppId(appId);
        adminInfo.setInsCode(insCode);
        AdminInfo userIsHasByEmployee = sysAdminService.getUserIsHasByEmployee(adminInfo);
        if (userIsHasByEmployee == null) {
//            adminInfo.setUsername(Dist.getFirstPinYins(doctorName));
            adminInfo.setUsername(employeeId);
            adminInfo.setNameZh(doctorName);
            adminInfo.setEmployeeId(employeeId);
            adminInfo.setCertificate(doctorCardNumber);
            adminInfo.setSex(CardUtil.getCarSex(doctorCardNumber));
            adminInfo.setCreateDate(new Date());
            adminInfo.setPassword((MD5Util.encode("123456")));
            adminInfo.setUserId(IdUtil.getSnowflake(snowflake).nextIdStr());
            AdminInfo adminInfo22 = new AdminInfo();
            BeanUtils.copyProperties(adminInfo, adminInfo22);
            sysAdminService.insertAdminHis(adminInfo);

            //设置角色
//            AdminInfoRule adminInfoRule = new AdminInfoRule();
//            adminInfoRule.setRoleId("2");
//            adminInfoRule.setUserId(adminInfo.getUserId());
//            adminRuleMapper.insertSysAdminRule(adminInfoRule);


            return ResEntity.success(adminInfo22);
        } else {
            return ResEntity.success(userIsHasByEmployee);
        }


    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity getUserIdOrInsertUser(TAdminInfo adminInfo) {
        TAdminInfo tAdminInfo22 = new TAdminInfo();
        if (StringUtil.isNotEmpty(adminInfo.getHealthCardNum())) {
            String idcard = adminInfo.getIdcard();
            TAdminInfo2 tAdminInfo2 = new TAdminInfo2();
            tAdminInfo2.setIdCard(idcard);
            tAdminInfo2.setHealthCardNum(adminInfo.getHealthCardNum());
            tAdminInfo2.setMobile(adminInfo.getMobile());
            TAdminInfo admin = tAdminInfoMapper.getHisLoginCheckUserInfo(tAdminInfo2);

            if (admin == null) {
                adminInfo.setUserId(IDUtil.getID());
                adminInfo.setCreateTime(new Date());
                if (StringUtils.isNotEmpty(adminInfo.getIdcard()) && (adminInfo.getIdcard().length() == 15 || adminInfo.getIdcard().length() == 18)) {
                    adminInfo.setAge(CardUtil.getCarAge(adminInfo.getIdcard()));
                    adminInfo.setSex(CardUtil.getCarSex(adminInfo.getIdcard()));
                    adminInfo.setCardNumber(adminInfo.getIdcard());
                }
                if (StringUtils.isNotEmpty(adminInfo.getName())) {
                    adminInfo.setWxNiceName(adminInfo.getName());
                    adminInfo.setUserName(adminInfo.getName());
                }
                if (StringUtils.isNotEmpty(adminInfo.getMobile())) {
                    adminInfo.setMobile(adminInfo.getMobile());
                }
                //因为加密，所以要重新搞个对象
                BeanUtils.copyProperties(adminInfo, tAdminInfo22);
                tAdminInfoMapper.insert(adminInfo);
                return new ResEntity(true, "SUCCESS", tAdminInfo22);
            }
            return new ResEntity(true, "SUCCESS", admin);
        } else {
            return new ResEntity(false, "缺少居民健康卡号参数！", null);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public MedicalRecords getUserIdOrInsertRecord(MedicalRecords medicalRecords) {
        MedicalRecords a = new MedicalRecords();
        BeanUtils.copyProperties(medicalRecords, a);
        MedicalRecords medicalRecords1 = medicalRecordsMapper.selectOneByCondition(a);
        if (medicalRecords1 != null) {
            return medicalRecords;
        } else {
            medicalRecords.setRecordsId(IdUtil.getSnowflake(snowflake).nextIdStr());
            medicalRecordsMapper.insert(medicalRecords);
            return medicalRecords;
        }
    }

    public ResEntity getQrCode(QrCodeRequest qrCodeRequest) {

        TPreDiagnosisForm tPreDiagnosisForm = tPreDiagnosisFormMapper.getOneByDisId(qrCodeRequest.getDisId());
        if (null == tPreDiagnosisForm) {
            return ResEntity.error("预诊单不存在disId=" + qrCodeRequest.getDisId());
        }
        String jumpUrl = "doctorfollowup?diaId=" + tPreDiagnosisForm.getDiaId() + "&yuzhen=1" + "&diaType=1";
        try {
            jumpUrl = URLEncoder.encode(jumpUrl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        String base64 = null;
        try {
            String decode = URLDecoder.decode(qrCodeRequest.getCbdata(), "UTF-8");
            log.info("解码：{}", decode);
            base64 = AESPKCS7Util.encrypt(decode, key, "base64");
            log.info("加密：{}", base64);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        String a = url + "?cbdata=" + base64 + "&jumpUrl=" + jumpUrl;
        String waiwang = waiwangURL + "?cbdata=" + base64 + "&jumpUrl=" + jumpUrl;
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("interior", a);
        hashMap.put("exterior", waiwang);

        return ResEntity.success(hashMap);
    }

    public ResEntity encrpy(String startDate, String endDate) {
        NoEcrpy noEcrpy = new NoEcrpy();
        noEcrpy.setStartDate(startDate);
        noEcrpy.setEndDate(endDate);
        List<MedicalRecordsNoEn> noEcrpy1 = medicalRecordsMapper.getNoEcrpy(noEcrpy);
        ArrayList<MedicalRecords> medicalRecords = new ArrayList<>();
        for (int i = 0; i < noEcrpy1.size(); i++) {
            MedicalRecords medicalRecords1 = new MedicalRecords();
            BeanUtils.copyProperties(noEcrpy1.get(i), medicalRecords1);
            medicalRecords.add(medicalRecords1);
        }
        if (medicalRecords.size()>0){
            for (int i = 0; i < medicalRecords.size(); i++) {
                medicalRecordsMapper.updateByPrimaryKey(medicalRecords.get(i));
            }
        }

        return ResEntity.success();
    }
}
