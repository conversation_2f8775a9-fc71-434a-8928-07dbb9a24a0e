package com.cbkj.diagnosis.service.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Schema
@Data
@NoArgsConstructor
public class DeleteFormReVO implements Serializable {

    @Schema(description =  "预诊、随访单id")
    private String diaId;
//    @Schema(description =  "0正常 1 删除 2禁用")
//    private String status;
}
