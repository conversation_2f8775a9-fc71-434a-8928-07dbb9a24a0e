package com.cbkj.diagnosis.service.his;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.common.http.InterfaceTemplate;
import com.cbkj.diagnosis.common.http.XiaMenResEntity;
import com.cbkj.diagnosis.common.http.XiaMenTemplate;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.LogAsync;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 发送微信消息、短信消息、调用HIS接口
 */
@Component
public class XiaMenSendWXMessage {
//    @Value("${xia.men.send.wx.url}")
//    private String url ;
    private final LogAsync logAsync;
    private final XiaMenTemplate xiaMenTemplate;

    private final InterfaceTemplate interfaceTemplate;


    public XiaMenSendWXMessage(LogAsync logAsync, XiaMenTemplate xiaMenTemplate, InterfaceTemplate interfaceTemplate) {
        this.logAsync = logAsync;
        this.xiaMenTemplate = xiaMenTemplate;
        this.interfaceTemplate = interfaceTemplate;
    }


    /**
     *
     * @param mobile
     * @param titleParameter
     * @param linkUrl
     * @param contentParameter
     * @param taskExcuteTime 任务执行时间
     * @param patientCard 证件号
     * @param type 1宣教2复诊3随访
     * @return
     */
//    @Async
    public XiaMenResEntity sendWxTemplateWSDL(String mobile,String titleParameter,String linkUrl,String contentParameter,
                                              String taskExcuteTime,String patientCard,String type,String medicalCard
                                              ) {
        JSONObject paramEncrypt = new JSONObject();
//        paramEncrypt.put("appCode", "HC_XMSZYY_WX");
        paramEncrypt.put("telephone", mobile);
//        paramEncrypt.put("templateId", templateId);
        paramEncrypt.put("linkUrl", linkUrl);
        paramEncrypt.put("titleParameter", titleParameter);//标题字符串，|隔开示列：”标题1|标题2”详见模板地址
//        paramEncrypt.put("contentParameter", "张三|2023-12-20 9:00:00");//内容字符串，|隔开示列：”内容1|内容2”详见模板地址
        paramEncrypt.put("contentParameter", contentParameter);//内容字符串，|隔开示列：”内容1|内容2”详见模板地址
        paramEncrypt.put("taskExcuteTime", taskExcuteTime);
        paramEncrypt.put("patientCard", patientCard);
        paramEncrypt.put("medicalCard", medicalCard);//患者就诊卡号
        paramEncrypt.put("type", type);
        //JSONObject paramJo = new JSONObject();
        //paramJo.put("appId", "appId_Zoenet_Health");
        //需要对paramJo加密
       // paramJo.put("requestBody", XiaMenZoeAes256Util.ownEncrypt(paramEncrypt.toJSONString(), "16e9853fc19211ea864e005056941120", "d9a7111ea864e344"));
//        String s = JSON.toJSONString(paramEncrypt);
//        String[] split = contentParameter.split("\\|");
//        XiaMenResEntity post = xiaMenTemplate.callWsdlSendWxTemplate(s,split[0]);

        XiaMenResEntity post = interfaceTemplate.post("/his/wx/template/send", paramEncrypt);
        return post;

    }


}
