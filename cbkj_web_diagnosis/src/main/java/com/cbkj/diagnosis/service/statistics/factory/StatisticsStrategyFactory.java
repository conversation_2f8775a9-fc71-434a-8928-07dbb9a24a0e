package com.cbkj.diagnosis.service.statistics.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class StatisticsStrategyFactory {

    Map<String, StatisticsStrategy> bases;

    @Autowired
    public StatisticsStrategyFactory(Map<String, StatisticsStrategy> bases) {
        Assert.notNull(bases, "ParamStrategy must not be null!");
        this.bases = bases;
    }

    public StatisticsStrategy getParamStrategy(String blockId) {
        if (bases.get(blockId) == null){
            return bases.get("block-default");
        }
        return bases.get(blockId);
    }
}
