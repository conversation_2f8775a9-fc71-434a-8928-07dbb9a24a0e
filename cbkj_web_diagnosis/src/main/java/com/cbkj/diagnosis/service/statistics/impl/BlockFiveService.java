package com.cbkj.diagnosis.service.statistics.impl;

import com.cbkj.diagnosis.beans.statistics.BlockFiveDetail;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayExpensesService;
import com.cbkj.diagnosis.service.statistics.factory.StatisticsStrategy;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/29 11:44
 * @Version 1.0
 */
@Service("block-index-zlfy")
public class BlockFiveService implements StatisticsStrategy {


    private StatisticsErverDayExpensesService statisticsErverDayExpensesService;

    public BlockFiveService(StatisticsErverDayExpensesService statisticsErverDayExpensesService) {
        this.statisticsErverDayExpensesService = statisticsErverDayExpensesService;
    }

    @Override
    public ResEntity getData(StatisticsVo statisticsVo) {
        List<BlockFiveDetail> list = statisticsErverDayExpensesService.getBlockFiveList(statisticsVo);


        String[] names = {"诊疗费用分析"};
        List[] lists = new List[]{list};

        HashMap<String, Object> map = new HashMap<>(16);
        map.put("lists",lists);
        map.put("names",names);
        return ResEntity.success(map);
    }
}
