package com.cbkj.diagnosis.service.convert;

import com.alibaba.druid.util.StringUtils;
import com.cbkj.diagnosis.beans.monitor.dto.ManifestFeeDTO;
import com.cbkj.diagnosis.beans.monitor.dto.ProjectFeeDTO;
import com.cbkj.diagnosis.beans.health.MedicalPatientRecordCost;


/**
 * Created by zbh on 2024/6/11 10:15
 *
 * @description：患者诊疗费用转换器
 */
public class MedicalPatientRecordCostConvert {

    public static ManifestFeeDTO manifestFeeConvertToDTO(MedicalPatientRecordCost response) {
        if(null == response){
            return null;
        }
        ManifestFeeDTO dto = new ManifestFeeDTO();

        return dto;
    }

    public static ProjectFeeDTO feeConvertToDTO(MedicalPatientRecordCost cost) {
        if(StringUtils.isEmpty(cost.getMedicalPatientRecordActualName()) || StringUtils.isEmpty(cost.getMedicalPatientRecordCost())){
            return null;
        }
        ProjectFeeDTO dto = new ProjectFeeDTO();
        dto.setName(cost.getMedicalPatientRecordActualName());
        dto.setFee(cost.getMedicalPatientRecordCost());
        if(!StringUtils.isEmpty(cost.getMedicalPatientRecordDosage())){
            dto.setDosageForm(cost.getMedicalPatientRecordDosage());
        }
        return dto;
    }
}
