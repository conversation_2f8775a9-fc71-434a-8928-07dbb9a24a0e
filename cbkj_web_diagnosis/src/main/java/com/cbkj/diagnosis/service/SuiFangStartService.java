package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone;
import com.cbkj.diagnosis.beans.business.requestvo.GetTodayPatientList;
import com.cbkj.diagnosis.beans.business.requestvo.ListByTaskPatientsIdSearch;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsPhoneMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.common.vo.*;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

@Slf4j
@Service
public class SuiFangStartService {

    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;
    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    public SuiFangStartService(SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper, SRoadTaskPatientsMapper sRoadTaskPatientsMapper) {
        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
    }

    public Object getTodayPatientList(GetTodayPatientList getTodayPatientList) {
        if (StringUtils.isNotBlank(getTodayPatientList.getEventCodes()))
        {
            getTodayPatientList.setEventCodesList(Arrays.asList(getTodayPatientList.getEventCodes().split(",")));
        }
        getTodayPatientList.setUserId(AdminWebUtils.getCurrentHr().getUserId());
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        List<AdminRule> roles = currentHr.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            getTodayPatientList.setUserId(null);
        }


        if (null != getTodayPatientList.getToDayTaskStatus() && 1 == getTodayPatientList.getToDayTaskStatus()) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
            getTodayPatientList.setStartDate(simpleDateFormat.format(DateUtil.getToDayZeroDate()));
            getTodayPatientList.setEndDate(simpleDateFormat.format(DateUtil.getNextDate()));
        }

        //getTodayPatientList.setPhoneStatus(getTodayPatientList.getPhoneStatus());
        Integer page = getTodayPatientList.getPage();
        Integer limit = getTodayPatientList.getLimit();
        int tempCu = page * limit;
        getTodayPatientList.setPage((page - 1) * limit);
        GetTodayPatientList getTodayPatientList1 = new GetTodayPatientList();
        getTodayPatientList1.setPatientName(getTodayPatientList.getPatientName());
        getTodayPatientList1.setPatientIdcard(getTodayPatientList.getPatientIdcard());
        getTodayPatientList1.setStartDate(getTodayPatientList.getStartDate());
        getTodayPatientList1.setDiseaseName(getTodayPatientList.getDiseaseName());
        getTodayPatientList1.setEndDate(getTodayPatientList.getEndDate());
        getTodayPatientList1.setRecId(getTodayPatientList.getRecId());
        getTodayPatientList1.setSRoadTaskId(getTodayPatientList.getSRoadTaskId());
        getTodayPatientList1.setToDayTaskStatus(getTodayPatientList.getToDayTaskStatus());
        getTodayPatientList1.setUserId(getTodayPatientList.getUserId());
        getTodayPatientList1.setEventCodes(getTodayPatientList.getEventCodes());
        getTodayPatientList1.setEventCodesList(getTodayPatientList.getEventCodesList());
        List<TodayPatientList> todayPatientLists = sRoadTaskPatientsPhoneMapper.getTodayPatientList(getTodayPatientList);

        int taotalCount = sRoadTaskPatientsPhoneMapper.countTodayPatientList(getTodayPatientList1);
        //PageHelper.clearPage();
        return Page.getLayuiData(true, "SUCCESS", taotalCount, taotalCount > tempCu, todayPatientLists);
    }

    public Object getStartSuiFangInfo(GetStartSuiFangInfo getStartSuiFangInfo) {
//todo 入参应该增加电话随访表id。需要修改随访患者管理界面列表

        if (getStartSuiFangInfo.getTaskPatientsId() != null) {
            //获取随访信息
            SuiFangInfoPatientList todayPatient = sRoadTaskPatientsPhoneMapper.getSuiFangInfo(getStartSuiFangInfo);
            if (todayPatient == null) {
                return null;
            }

//            DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
//            if (StringUtils.isNotBlank(getStartSuiFangInfo.getStartDate())){
//
//
//                ZonedDateTime dateTime = ZonedDateTime.parse(getStartSuiFangInfo.getStartDate(), formatter);
//                // 自定义格式输出
//                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//                String output = dateTime.format(outputFormatter);
//
//
//                getStartSuiFangInfo.setStartDate(output);
//            }
//            if (StringUtils.isNotBlank(getStartSuiFangInfo.getEndDate())){
//
//                ZonedDateTime dateTime = ZonedDateTime.parse(getStartSuiFangInfo.getEndDate(), formatter);
//                // 自定义格式输出
//                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//                String output = dateTime.format(outputFormatter);
//
//
//                getStartSuiFangInfo.setEndDate(output);
//            }


            // SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //获取这个随访任务，注意，是界面那个任务，下所有路径的下电话随访的今天需要随访的列表
            ListByTaskPatientsIdSearch listByTaskPatientsIdSearch = new ListByTaskPatientsIdSearch();
            BeanUtils.copyProperties(getStartSuiFangInfo, listByTaskPatientsIdSearch);
//            listByTaskPatientsIdSearch.setStartDate(simpleDateFormat.format(DateUtil.getToDayZeroDate()));
//            listByTaskPatientsIdSearch.setEndDate(simpleDateFormat.format(DateUtil.getNextDate()));
            listByTaskPatientsIdSearch.setPatientId(todayPatient.getPatientId());
//

            if (null != getStartSuiFangInfo.getToDayTaskStatus() && 1 == getStartSuiFangInfo.getToDayTaskStatus()) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                getStartSuiFangInfo.setStartDate(simpleDateFormat.format(DateUtil.getToDayZeroDate()));
                getStartSuiFangInfo.setEndDate(simpleDateFormat.format(DateUtil.getNextDate()));
            }
//            getStartSuiFangInfo.setUserId(AdminWebUtils.getCurrentHr().getUserId());
//            boolean containsSearchKey = AdminWebUtils.containsSearchKey("all");
//            if (containsSearchKey) {
//                getStartSuiFangInfo.setUserId(null);
//            }
            listByTaskPatientsIdSearch.setUserId(AdminWebUtils.getCurrentHr().getUserId());
            List<AdminRule> roles = AdminWebUtils.getCurrentHr().getRoles();
            roles.forEach(role -> {
                if ("管理".equals(role.getRoleName())){
                    listByTaskPatientsIdSearch.setUserId(null);
                }
            });

            //获取所有
            List<SuiFangPatientDetail> todayPatientDetails = sRoadTaskPatientsPhoneMapper.getIncludePhoneListByTaskPatientsId(listByTaskPatientsIdSearch);

            todayPatient.setSuiFangAllList(todayPatientDetails);

            List<StartSuiFangDiaForm> startSuiFangDiaFormList = sRoadTaskPatientsPhoneMapper.getDiaFormListBySRoadTaskPatientsPhoneId(getStartSuiFangInfo.getTaskPatientsId());
            //if (startSuiFangDiaFormList.size()>0){
            todayPatient.setStartSuiFangDiaForm(startSuiFangDiaFormList);
            // }

            return todayPatient;
        }
        return null;
    }

    /**
     * 获取待随访患者的信息
     *
     * @param sRoadTaskPatientsPhoneId
     * @return
     */
//    public Object getStartSuiFangInfo(Long sRoadTaskPatientsId) {
//        //SRoadTaskPatientsPhone taskPatientsPhone = sRoadTaskPatientsPhoneMapper.getObjectById(sRoadTaskPatientsPhoneId + "");
//
//        if (sRoadTaskPatientsId != null) {
//            //获取随访信息
//            TodayPatientList todayPatient = sRoadTaskPatientsPhoneMapper.getStartSuiFangInfo(sRoadTaskPatientsId);
//
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            //获取这个随访任务，注意，是界面那个任务，下所有路径的下电话随访的今天需要随访的列表
//            ListByTaskPatientsIdSearch listByTaskPatientsIdSearch = new ListByTaskPatientsIdSearch();
////            listByTaskPatientsIdSearch.setStartDate(simpleDateFormat.format(DateUtil.getToDayZeroDate()));
////            listByTaskPatientsIdSearch.setEndDate(simpleDateFormat.format(DateUtil.getNextDate()));
//            listByTaskPatientsIdSearch.setPatientId(todayPatient.getPatientId());
//            listByTaskPatientsIdSearch.setSRoadTaskId(todayPatient.getSRoadTaskId());
//
//            List<TodayPatientDetail> todayPatientDetails = sRoadTaskPatientsPhoneMapper.getListByTaskPatientsId(listByTaskPatientsIdSearch);
//
//            todayPatient.setPhoneSuiFangAllList(todayPatientDetails);
//
//            List<StartSuiFangDiaForm> startSuiFangDiaFormList = sRoadTaskPatientsPhoneMapper.getDiaFormListBySRoadTaskPatientsPhoneId(sRoadTaskPatientsPhoneId);
//            todayPatient.setStartSuiFangDiaFormList(startSuiFangDiaFormList);
//
//            return todayPatient;
//        }
//        return null;
//    }
    public Object getSuiFangPhoneRecordList(String patientId, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());

        List<SuiFangPhoneRecordListRes> suiFangPhoneRecordListResList = sRoadTaskPatientsPhoneMapper.getSuiFangPhoneRecordList(patientId);

        PageHelper.clearPage();
        return Page.getLayUiTablePageData(suiFangPhoneRecordListResList);
    }

    public Object getStartSuiFangInfoDetails(Long sRoadTaskPatientsPhoneId) {
        return sRoadTaskPatientsPhoneMapper.getStartSuiFangInfoDetails(sRoadTaskPatientsPhoneId);
    }

    public SuiFangPatientDetail getLastestSRoadTaskPatientsId(String patientId) {
        SuiFangPatientDetail a = sRoadTaskPatientsPhoneMapper.getLastestSRoadTaskPatientsId(patientId);
        return a;
    }

    public Object getStartSuiFangInfoPatiGroup(GetTodayPatientList getTodayPatientList) {
        List<String> patiIdList = sRoadTaskPatientsPhoneMapper.getTodayPatientIdsList(getTodayPatientList);
        return ResEntity.success(patiIdList);
    }

    public Object deleteStartSuiFangInfo(Long sRoadTaskPatientsPhoneId, String taskPatientsId) {
        if (sRoadTaskPatientsPhoneId != null) {
            SRoadTaskPatientsPhone objectById = sRoadTaskPatientsPhoneMapper.getObjectById(String.valueOf(sRoadTaskPatientsPhoneId));
            if (objectById != null) {
                objectById.setPhoneStatus(3);
            }
            sRoadTaskPatientsPhoneMapper.updateByPrimaryKey(objectById);
        }
        if (taskPatientsId != null) {
            SRoadTaskPatients objectById = sRoadTaskPatientsMapper.getObjectById(taskPatientsId);
            if (objectById != null) {
                objectById.setStatus("1");
                objectById.setDeleteTime(new Date());
                objectById.setDeleteUserId(AdminWebUtils.getCurrentHr().getUserId());
                objectById.setDeleteUserName(AdminWebUtils.getCurrentHr().getNameZh());
                sRoadTaskPatientsMapper.updateByPrimaryKey(objectById);
            }
        }
        return ResEntity.success();
    }
}
