package com.cbkj.diagnosis.service.statistics.impl;

import com.cbkj.diagnosis.beans.statistics.BlockOneDetail;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayDisService;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayHealthService;
import com.cbkj.diagnosis.service.statistics.factory.StatisticsStrategy;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/26 17:58
 * @Version 1.0
 */
@Service("block-index-1")
public class BlockOneService implements StatisticsStrategy {

    private StatisticsErverDayDisService statisticsErverDayDisService;

    private StatisticsErverDayHealthService statisticsErverDayHealthService;

    public BlockOneService(StatisticsErverDayDisService statisticsErverDayDisService, StatisticsErverDayHealthService statisticsErverDayHealthService) {
        this.statisticsErverDayDisService = statisticsErverDayDisService;
        this.statisticsErverDayHealthService = statisticsErverDayHealthService;
    }

    @Override
    public ResEntity getData(StatisticsVo statisticsVo) {

        List<BlockOneDetail> list = statisticsErverDayDisService.getBlockOneList(statisticsVo);
        List<BlockOneDetail> list2 = statisticsErverDayDisService.getBlockOneList2(statisticsVo);
        //todo 健康宣教
        List<BlockOneDetail> list3 = statisticsErverDayHealthService.getBlockOneList(statisticsVo);
        //服务数量 = 随访数量+预诊数量
        List<BlockOneDetail> blockOneDetails = mergeAndSum(list, list2);

        sortByTimeDescending(blockOneDetails);
        String[] names = {"患者服务量", "预诊量", "随访量", "健康宣教"};
        List<BlockOneDetail>[] lists = new List[]{blockOneDetails, list, list2, list3};

        HashMap<String, Object> map = new HashMap<>(32);
        map.put("lists", lists);
        map.put("names", names);
        return ResEntity.success(map);
    }

    public List<BlockOneDetail> mergeAndSum(List<BlockOneDetail>... lists) {
        Map<Date, Integer> timeToSumMap = new HashMap<>();

        // 遍历所有列表
        for (List<BlockOneDetail> list : lists) {
            for (BlockOneDetail detail : list) {
                Date time = detail.getX();
                Integer num = detail.getY();
                if (num == null) {
                    num = 0;
                }
                // 更新时间对应的总和
                timeToSumMap.put(time, timeToSumMap.getOrDefault(time, 0) + num);
            }
        }

        // 构建最终的合并列表
        List<BlockOneDetail> resultList = new ArrayList<>();
        for (Map.Entry<Date, Integer> entry : timeToSumMap.entrySet()) {
            BlockOneDetail mergedDetail = new BlockOneDetail();
            mergedDetail.setX(entry.getKey());
            mergedDetail.setY(entry.getValue());
            resultList.add(mergedDetail);
        }
        return resultList;
    }

    public void sortByTimeDescending(List<BlockOneDetail> list) {
        Collections.sort(list, new Comparator<BlockOneDetail>() {
            @Override
            public int compare(BlockOneDetail detail1, BlockOneDetail detail2) {
                // 降序排序
                return detail1.getX().compareTo(detail2.getX());
            }
        });
    }

}
