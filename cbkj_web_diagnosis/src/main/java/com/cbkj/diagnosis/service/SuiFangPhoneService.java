package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone;
import com.cbkj.diagnosis.beans.business.requestvo.SRoadTaskPatientsPhoneRe;
import com.cbkj.diagnosis.beans.business.requestvo.SchedulingAllocationSavePatient;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsPhoneMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.common.vo.*;
import com.cbkj.diagnosis.service.sysService.SysAdminService;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SuiFangPhoneService {

    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;

    private final SysAdminService sysAdminService;

    public SuiFangPhoneService(SRoadTaskPatientsMapper sRoadTaskPatientsMapper, SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper, SysAdminService sysAdminService) {
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
        this.sysAdminService = sysAdminService;
    }

    public Object getList(SuiFangPhoneRe suiFangPhoneRe, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        String dateType = suiFangPhoneRe.getDateType();
        if (StringUtils.isNotBlank(dateType)) {
            if (Constant.BASIC_STRING_ONE.equals(dateType)) {
                suiFangPhoneRe.setAllotTaskStatus("0");
            }
            if (Constant.BASIC_STRING_TWO.equals(dateType)) {
                suiFangPhoneRe.setTaskExcuteStatus("8");
                suiFangPhoneRe.setAllotTaskStatus("0");
            }
            if (Constant.BASIC_STRING_THREE.equals(dateType)) {
                suiFangPhoneRe.setTaskExcuteStatus("1");
                suiFangPhoneRe.setAllotTaskStatus("0");
            }
        }

        boolean containsSearchKey = AdminWebUtils.containsSearchKey("all");

        if (!containsSearchKey) {
            String userId = AdminWebUtils.getCurrentHr().getUserId();
            //userId = null;
            //不能查看全部有权限。
            suiFangPhoneRe.setDoctorId(userId);
        }

        List<GetPhoneList> phoneList = sRoadTaskPatientsMapper.getPhoneList(suiFangPhoneRe);

        PageHelper.clearPage();

        return Page.getLayUiTablePageData(phoneList);
    }

    public Object getSchedulingList(Long taskPatientsId) {
        SRoadTaskPatients objectById = sRoadTaskPatientsMapper.getObjectById(taskPatientsId + "");
        SRoadTaskPatientsPhone patientsPhone = new SRoadTaskPatientsPhone();
        patientsPhone.setPatientId(objectById.getPatientId());
        patientsPhone.setTaskPatientsId(objectById.getTaskPatientsId());
        patientsPhone.setRoadExecuteId(objectById.getRoadExecuteId());
        List<SRoadTaskPatientsPhone> list = sRoadTaskPatientsMapper.getSchedulingList(patientsPhone);
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveScheduling(SchedulingSave schedulingSave) {
        SRoadTaskPatients sRoadTaskPatients = sRoadTaskPatientsMapper.getObjectById(schedulingSave.getTaskPatientsId() + "");
        //先删除，task_excute_status状态 是1,或者2 的待执行的。
        List<SRoadTaskPatientsPhoneRe> schedulingPhoneSaveList = schedulingSave.getSchedulingPhoneSaveList();
        HashMap<String, String> map = new HashMap<>();
        for (int i1 = 0; i1 < schedulingPhoneSaveList.size(); i1++) {
            Long sRoadTaskPatientsPhoneId1 = schedulingPhoneSaveList.get(i1).getSRoadTaskPatientsPhoneId();
            if (sRoadTaskPatientsPhoneId1 != null) {
                map.put(sRoadTaskPatientsPhoneId1 + "", i1 + "");
            }
        }

        SRoadTaskPatientsPhone sRoadTaskPatientsPhone2 = new SRoadTaskPatientsPhone();
        sRoadTaskPatientsPhone2.setRecordsId(schedulingSave.getRecordsId());
        sRoadTaskPatientsPhone2.setSRoadTaskId(sRoadTaskPatients.getSRoadTaskId());
        sRoadTaskPatientsPhone2.setRoadExecuteId(sRoadTaskPatients.getRoadExecuteId());
        //为了删除 前端删除的但是不传过来的数据，先查所有的数据，比较前端传过来的，两者一方有的就删掉
        List<TodayPatientDetail> todayPatientDetails = sRoadTaskPatientsPhoneMapper.getTodayPatientDetail(sRoadTaskPatientsPhone2);
        for (int i = 0; i < todayPatientDetails.size(); i++) {
            Long sRoadTaskPatientsPhoneId = todayPatientDetails.get(i).getSRoadTaskPatientsPhoneId();
            boolean b = map.containsKey(sRoadTaskPatientsPhoneId + "");
            if (!b) {
                SRoadTaskPatientsPhone patientsPhone = sRoadTaskPatientsPhoneMapper.getObjectById(String.valueOf(sRoadTaskPatientsPhoneId));
                if (1 == patientsPhone.getPhoneStatus() || 2 == patientsPhone.getPhoneStatus()) {
                    //删除phone表
                    SRoadTaskPatientsPhone phone = new SRoadTaskPatientsPhone();
                    phone.setSRoadTaskPatientsPhoneId(sRoadTaskPatientsPhoneId);
                    sRoadTaskPatientsPhoneMapper.deleteByPrimaryKey(phone);
                    //删除患者任务表
                    //sRoadTaskPatientsMapper.deleteTaskPatientIdKey(patientsPhone.getTaskPatientsId());
                }
            }
        }


        for (SRoadTaskPatientsPhoneRe sRoadTaskPatientsPhone : schedulingPhoneSaveList) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
            Long sRoadTaskPatientsPhoneId = sRoadTaskPatientsPhone.getSRoadTaskPatientsPhoneId();
            if (null != sRoadTaskPatientsPhoneId) {
                SRoadTaskPatientsPhone patientsPhone = sRoadTaskPatientsPhoneMapper.getObjectById(String.valueOf(sRoadTaskPatientsPhoneId));
  //              if (1 == patientsPhone.getPhoneStatus() || 2 == patientsPhone.getPhoneStatus()) {
                    //删除phone表
//                    SRoadTaskPatientsPhone phone = new SRoadTaskPatientsPhone();
//                    phone.setSRoadTaskPatientsPhoneId(sRoadTaskPatientsPhone.getSRoadTaskPatientsPhoneId());
//                    sRoadTaskPatientsPhoneMapper.deleteByPrimaryKey(phone);
//                    //删除患者任务表
//                    sRoadTaskPatientsMapper.deleteTaskPatientIdKey(patientsPhone.getTaskPatientsId());
    //            } else
                    if (8 != patientsPhone.getPhoneStatus()) {
                    //更新

                    try {
                        patientsPhone.setSuiFangTime( simpleDateFormat.parse(sRoadTaskPatientsPhone.getTaskExcuteTime()) );
                    } catch (ParseException e) {
                        try {
                            patientsPhone.setSuiFangTime( simpleDateFormat2.parse(sRoadTaskPatientsPhone.getTaskExcuteTime()) );
                        } catch (ParseException ex) {
                            throw new RuntimeException(ex);
                        }
                        //throw new RuntimeException(e);
                    }
                    //patientsPhone.setTaskExcuteStatus(2);

                    //patientsPhone.setAllotTaskStatus("1");
                    sRoadTaskPatientsPhoneMapper.updateByPrimaryKey(patientsPhone);
                    //更新
                    SRoadTaskPatients taskPatients = sRoadTaskPatientsMapper.getObjectById(String.valueOf(patientsPhone.getTaskPatientsId()));
                    //此处更新这个字段(任务时间)没有意义了，只需要从phone表获取时间
                    //taskPatients.setTaskExcuteTime(sRoadTaskPatientsPhone.getTaskExcuteTime());
                    //分配时间了，那就是说明这个任务已经已执行了，就差到日期去随访。
                    taskPatients.setTaskExcuteStatus(2);
                    sRoadTaskPatientsMapper.updateByPrimaryKey(taskPatients);
                }
            } else {
                //不存在，那就直接插入
//                SRoadTaskPatients sRoadTaskPatients1 = new SRoadTaskPatients();
//                BeanUtils.copyProperties(sRoadTaskPatients, sRoadTaskPatients1);
//                //sRoadTaskPatients1.setTaskPatientsId(null);
//                sRoadTaskPatients1.setTaskExcuteTime(sRoadTaskPatientsPhone.getTaskExcuteTime());
//                sRoadTaskPatients1.setTaskExcuteStatus(2);
//                sRoadTaskPatients1.setStatus("0");
//                sRoadTaskPatientsMapper.insert(sRoadTaskPatients1);
                //插入phone表
                SRoadTaskPatientsPhone sRoadTaskPatientsPhone1 = new SRoadTaskPatientsPhone();
                BeanUtils.copyProperties(sRoadTaskPatients, sRoadTaskPatientsPhone1);
                sRoadTaskPatientsPhone1.setAllotTaskStatus("1");
                sRoadTaskPatientsPhone1.setPhoneStatus(2);
                sRoadTaskPatientsPhone1.setTaskPatientsId(sRoadTaskPatients.getTaskPatientsId());
                try {
                    sRoadTaskPatientsPhone1.setSuiFangTime( simpleDateFormat.parse(sRoadTaskPatientsPhone.getTaskExcuteTime()) );
                } catch (ParseException e) {
                    try {
                        sRoadTaskPatientsPhone1.setSuiFangTime( simpleDateFormat2.parse(sRoadTaskPatientsPhone.getTaskExcuteTime()) );
                    } catch (ParseException ex) {
                        throw new RuntimeException(ex);
                    }
                }
                sRoadTaskPatientsPhoneMapper.insert(sRoadTaskPatientsPhone1);
            }
        }


    }

    public void saveSchedulingAllocation(SchedulingAllocationSave schedulingAllocationSave) {
        List<SchedulingAllocationSavePatient> recordsId1 = schedulingAllocationSave.getRecords();
        List<SchedulingAllocationSaveDoctor> doctors = schedulingAllocationSave.getDoctors();
        int tempNums = 0;

        int doctorSize = doctors.size();
        Date allotTime = new Date();
        //找出所有的排期列表
        //ArrayList<SRoadTaskPatientsPhone> stringArrayList = new ArrayList<>();
        HashMap<String, SRoadTaskPatients> sRoadTaskPatients = new HashMap<>();
        for (int i = 0; i < recordsId1.size(); i++) {

            SchedulingAllocationSavePatient schedulingAllocationSavePatient = recordsId1.get(i);


            tempNums = (i ) / doctorSize;
            SchedulingAllocationSaveDoctor doctor = doctors.get(i - tempNums * doctors.size());


            ArrayList<SRoadTaskPatientsPhone> a = sRoadTaskPatientsPhoneMapper.getListByRecordsId(schedulingAllocationSavePatient.getTaskPatientsId() + "");

            for (SRoadTaskPatientsPhone taskPatientsPhone : a) {
                taskPatientsPhone.setDoctorId(doctor.getDoctorId());
                taskPatientsPhone.setAllotTime(allotTime);
                taskPatientsPhone.setAllotTaskStatus("0");
                if (StringUtils.isBlank(doctor.getDoctorName())) {
                    //获取医生名字
                    AdminInfo adminInfoByUserId = sysAdminService.getAdminInfoByUserId(doctor.getDoctorId());
                    if (adminInfoByUserId != null) {
                        taskPatientsPhone.setDoctorName(adminInfoByUserId.getNameZh());
                    }
                } else {
                    taskPatientsPhone.setDoctorName(doctor.getDoctorName());
                }

                sRoadTaskPatientsPhoneMapper.updateByPrimaryKey(taskPatientsPhone);
                SRoadTaskPatients sRoadTaskPatients1 = new SRoadTaskPatients();
                sRoadTaskPatients1.setTaskPatientsId(schedulingAllocationSavePatient.getTaskPatientsId());
                sRoadTaskPatients1.setAllotTime(allotTime);
                sRoadTaskPatients1.setAllotTaskStatus("0");
                sRoadTaskPatients1.setDoctorId(taskPatientsPhone.getDoctorId());
                sRoadTaskPatients1.setDoctorName(taskPatientsPhone.getDoctorName());
                sRoadTaskPatients.put(schedulingAllocationSavePatient.getTaskPatientsId() + "", sRoadTaskPatients1);
            }

            //stringArrayList.addAll(a);
            if (sRoadTaskPatients.size()>0){
                sRoadTaskPatients.forEach((s, sRoadTaskPatients2) ->  sRoadTaskPatientsMapper.updateByPrimaryKey(sRoadTaskPatients2) );

            }

        }

//        for (int i = 0; i < stringArrayList.size(); i++) {
//            SRoadTaskPatientsPhone taskPatientsPhone = stringArrayList.get(i);
//            //SRoadTaskPatientsPhone taskPatientsPhone = sRoadTaskPatientsPhoneMapper.getOneByTaskPatientsId(taskPatientsIdList.get(i));
//            //if (taskPatientsPhone != null) {
//            tempNums = (i + 1) / doctorSize;
//            SchedulingAllocationSaveDoctor doctor = doctors.get(i - tempNums * doctors.size());
//            taskPatientsPhone.setDoctorId(doctor.getDoctorId());
//
//            if (StringUtils.isBlank(doctor.getDoctorName())) {
//                //获取医生名字
//                AdminInfo adminInfoByUserId = sysAdminService.getAdminInfoByUserId(doctor.getDoctorId());
//                if (adminInfoByUserId != null) {
//                    taskPatientsPhone.setDoctorName(adminInfoByUserId.getNameZh());
//                }
//            } else {
//                taskPatientsPhone.setDoctorName(doctor.getDoctorName());
//            }
//
//            sRoadTaskPatientsPhoneMapper.updateByPrimaryKey(taskPatientsPhone);
//
//
//            //  }
//        }
    }

    public Object getSchedulingNoList() {
        SRoadTaskPatientsPhone sRoadTaskPatientsPhone1 = new SRoadTaskPatientsPhone();
        sRoadTaskPatientsPhone1.setAllotTaskStatus("1");
        List<SRoadTaskPatientsPhone> patientsPhoneList = sRoadTaskPatientsPhoneMapper.getPageListByObj2(sRoadTaskPatientsPhone1);
        return patientsPhoneList;
    }
}
