package com.cbkj.diagnosis.service.quesionbank;


import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMutexMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.service.webapi.business.vo.LookPaperQuestionMain;
import com.cbkj.diagnosis.service.webapi.business.vo.LookPaperRecive;
import com.cbkj.diagnosis.service.webapi.business.vo.QuestionOptionSave;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/28 11:25
 * @Version 1.0
 */
@Service
public class QuestionBankService {
    private TPreDiagnosisFormMapper tPreDiagnosisFormMapper;
    private TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;
    private final TPreDiagnosisOptionMutexMapper tPreDiagnosisOptionMutexMapper;
    public QuestionBankService(TPreDiagnosisFormMapper tPreDiagnosisFormMapper, TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper, TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper, TPreDiagnosisOptionMutexMapper tPreDiagnosisOptionMutexMapper) {
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.tPreDiagnosisOptionMapper = tPreDiagnosisOptionMapper;
        this.tPreDiagnosisOptionMutexMapper = tPreDiagnosisOptionMutexMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity setPaper(LookPaperRecive lookPaperRecive) {
        tPreDiagnosisQuestionMapper.deleteByDiaId("00000000000000000000");

//        Boolean isInsert = true;
        //插入预诊主表。
        TPreDiagnosisForm tPreDiagnosisForm = new TPreDiagnosisForm();
        BeanUtils.copyProperties(lookPaperRecive, tPreDiagnosisForm);
        tPreDiagnosisForm.setCreateDate(new Date());
        tPreDiagnosisForm.setCreateUser(AdminWebUtils.getCurrentHr().getUserId());
        tPreDiagnosisForm.setCreateUsername(AdminWebUtils.getCurrentHr().getNameZh());
        tPreDiagnosisForm.setDiaId("00000000000000000000");
        tPreDiagnosisForm.setStatus("2");
        tPreDiagnosisForm.setAppId(AdminWebUtils.getCurrentHr().getAppId());
        tPreDiagnosisForm.setInsCode(AdminWebUtils.getCurrentHr().getInsCode());
        tPreDiagnosisForm.setInsId(AdminWebUtils.getCurrentHr().getInsId());

        //修改传进来的预诊单id，用来返回给前端。
        lookPaperRecive.setDiaId(tPreDiagnosisForm.getDiaId());
        //插入题目

        //k存放现在题目id，v存放现在插入的题目id
        HashMap<String, Integer> map = new HashMap<>(16);
        //k存前端过来的optionId，v存插入的optionId
        HashMap<String, Integer> map2 = new HashMap<>(16);
        List<LookPaperQuestionMain> questionMobilesList = lookPaperRecive.getQuestionMobilesList();

        ArrayList<TPreDiagnosisQuestion> tPreDiagnosisQuestions = new ArrayList<>();
        ArrayList<TPreDiagnosisOption> tPreDiagnosisOptions = new ArrayList<>();
        ArrayList<TPreDiagnosisOptionMutex> tPreDiagnosisOptionMutexArrayList = new ArrayList<>();

        for (LookPaperQuestionMain main : questionMobilesList) {
            //题目对象
            TPreDiagnosisQuestion question = new TPreDiagnosisQuestion();
            BeanUtils.copyProperties(main, question);
            question.setDiaId(tPreDiagnosisForm.getDiaId());
            tPreDiagnosisQuestions.add(question);
            tPreDiagnosisQuestionMapper.insert(question);
            map.put(main.getQuestionId(), question.getQuestionId());
            //修改传进来的问题id，用来返回给前端。
            main.setQuestionId(question.getQuestionId() + "");
            //选项列表对象
            List<QuestionOptionSave> questionOptionSaveList = main.getQuestionOptionSaveList();
            int a = 0;
            for (QuestionOptionSave optionSave : questionOptionSaveList) {
                TPreDiagnosisOption option = new TPreDiagnosisOption();
                BeanUtils.copyProperties(optionSave, option);
                option.setQuestionId(question.getQuestionId());
                option.setOptionSort(a);
                option.setOptionIdStr(optionSave.getOptionId());
                a++;
                tPreDiagnosisOptionMapper.insert(option);

                //存一下前端过来的optionId，对应存到数据中optionId是多少
                map2.put(optionSave.getOptionId(), option.getOptionId());

                tPreDiagnosisOptions.add(option);
                //修改传进来的问题id、选项id，用来返回给前端。
                optionSave.setOptionId(option.getOptionId() + "");
                optionSave.setQuestionId(question.getQuestionId() + "");
            }
        }

        for (LookPaperQuestionMain main : questionMobilesList) {

            List<QuestionOptionSave> questionOptionSaveList = main.getQuestionOptionSaveList();
            //出选项外循环。处理选项互斥。
            for (QuestionOptionSave optionSave : questionOptionSaveList) {
                List<String> optionMutexList = optionSave.getOptionMutexList();
                if (null == optionMutexList) {
                    continue;
                }
                for (String s : optionMutexList) {
                    TPreDiagnosisOptionMutex tPreDiagnosisOptionMutex = new TPreDiagnosisOptionMutex();
                    tPreDiagnosisOptionMutex.setOptionName(optionSave.getOptionName());
                    tPreDiagnosisOptionMutex.setOptionId(Integer.parseInt(optionSave.getOptionId() ));
                    tPreDiagnosisOptionMutex.setMutexOptionId(map2.get(s));
                    tPreDiagnosisOptionMutexArrayList.add(tPreDiagnosisOptionMutex);
                }
                //更新 optionMutexList
                List<String> newOptionMutexList = new ArrayList<>();
                for (String s : optionMutexList) {
                    newOptionMutexList.add(map2.get(s) + "");
                }
                optionSave.setOptionMutexList(newOptionMutexList);
            }
        }
        if (tPreDiagnosisOptionMutexArrayList.size() > 0) {
            tPreDiagnosisOptionMutexMapper.insertList(tPreDiagnosisOptionMutexArrayList);
        }
        tPreDiagnosisForm.setLongestQuestionNum(questionMobilesList.size());
        tPreDiagnosisForm.setTotalQuestionNum(questionMobilesList.size());
        tPreDiagnosisForm.setShortestQuestionNum(questionMobilesList.size());
        tPreDiagnosisForm.setShowStatus(true);
        tPreDiagnosisFormMapper.insertRe(tPreDiagnosisForm);
        return ResEntity.success(lookPaperRecive);
    }


    public Object getPaper() {
        LookPaperRecive lookPaper = tPreDiagnosisFormMapper.getLookPaper("00000000000000000000");
        return ResEntity.success(lookPaper);
    }
}
