package com.cbkj.diagnosis.service.convert;


import com.cbkj.diagnosis.beans.monitor.dto.MedicalRecordsPrescriptionsItemDTO;
import com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem;

/**
 * Created by zbh on 2024/6/6 16:51
 *
 * @description：处方信息明细转换器
 */
public class MedicalRecordsPrescriptionsItemConvert {

    public static MedicalRecordsPrescriptionsItemDTO medicalWestPrescriptionsItemConvertToDTO(MedicalRecordsPrescriptionsItem response) {
        if(null == response){
            return null;
        }
        MedicalRecordsPrescriptionsItemDTO dto = new MedicalRecordsPrescriptionsItemDTO();
        dto.setDrugDose(response.getDrugDose());
        dto.setDrugDecoctingMethod(response.getDrugDecoctingMethod());
        dto.setDrugName(response.getDrugName());
        dto.setDrugDoseUnit(response.getDrugDoseUnit());

        return dto;
    }
}
