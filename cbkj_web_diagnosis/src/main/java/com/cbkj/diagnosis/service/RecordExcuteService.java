package com.cbkj.diagnosis.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.mapper.business.*;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.business.impl.TRecordEventServiceImpl;
import com.cbkj.diagnosis.service.common.vo.QuestionListVo;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.common.vo.QuestionMobile;
import com.cbkj.diagnosis.service.statistics.StatisticsAdverse;
import com.cbkj.diagnosis.service.statistics.StatisticsDicEvent;
import com.cbkj.diagnosis.service.statistics.StatisticsDis;
import com.cbkj.diagnosis.service.statistics.StatisticsEffect;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RecordExcuteService {

    private final WebTPreDiagnosisFormService webTPreDiagnosisFormService;
    private TRecordDiaMapper tRecordDiaMapper;
    private TRecordMapper tRecordMapper;
    private SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    private TAdminInfoMapper tAdminInfoMapper;
    private TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private final TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;
    private final TRecordEventServiceImpl tRecordEventServiceImpl;
    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;
    private final StatisticsEffect statisticsEffect;
    private final StatisticsAdverse statisticsAdverse;
    private final StatisticsDis statisticsDis;
    private final StatisticsDicEvent statisticsDicEvent;
    RecordExcuteService(TRecordDiaMapper tRecordDiaMapper,
                        TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper,
                        TRecordMapper tRecordMapper,
                        TAdminInfoMapper tAdminInfoMapper, SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper,
                        SRoadTaskPatientsMapper sRoadTaskPatientsMapper, WebTPreDiagnosisFormService webTPreDiagnosisFormService, WebTPreDiagnosisFormService webTPreDiagnosisFormService1, TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper, TRecordEventServiceImpl tRecordEventServiceImpl, StatisticsEffect statisticsEffect, StatisticsAdverse statisticsAdverse, StatisticsDis statisticsDis, StatisticsDicEvent statisticsDicEvent) {
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.tRecordMapper = tRecordMapper;
        this.tAdminInfoMapper = tAdminInfoMapper;
        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.webTPreDiagnosisFormService = webTPreDiagnosisFormService1;
        this.tPreDiagnosisOptionMapper = tPreDiagnosisOptionMapper;
        this.tRecordEventServiceImpl = tRecordEventServiceImpl;
        this.statisticsEffect = statisticsEffect;
        this.statisticsAdverse = statisticsAdverse;
        this.statisticsDis = statisticsDis;
        this.statisticsDicEvent = statisticsDicEvent;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity saveQuestions(QuestionListVo questionListVo) {

        if (StringUtils.isBlank(questionListVo.getPatientId())) {
            return ResEntity.error("缺少患者");
        }
        TAdminInfo tAdminInfo = tAdminInfoMapper.getTAdminInfoById(questionListVo.getPatientId());
        TRecord tRecord = new TRecord();
        tRecord.setFormType(Constant.BASIC_STRING_TWO);
        AdminInfo adminInfo = AdminWebUtils.getCurrentHr();

        if (tAdminInfo != null) {
            tRecord.setPatientId(tAdminInfo.getUserId());
            tRecord.setPatientAge(tAdminInfo.getAge());
            tRecord.setPatientSex(tAdminInfo.getSex());
            tRecord.setPatientName(tAdminInfo.getUserName());
            tRecord.setPatientIdcard(tAdminInfo.getIdcard());
        } else {
            return ResEntity.error("未登录！");
        }
        //患者的答题
        List<QuestionMain> questionMobilesList = questionListVo.getQuestionMobilesList();

        QuestionMobile questionMobile = questionListVo.getQuestionMobile();
        //操作的医生
        tRecord.setCreateUser(adminInfo.getUserId());
        tRecord.setCreateUsername(adminInfo.getNameZh());
        tRecord.setCreateDate(new Date());
        tRecord.setRecordResource(Constant.BASIC_STRING_TWO);
        tRecord.setDiaId(questionMobile.getDiaId());
        tRecord.setPatientIdcard(tAdminInfo.getCardNumber());
        tRecord.setRecId(IdUtil.nanoId());

        tRecord.setStatus("0");

//        StringBuilder tempStr = new StringBuilder();
        //保存到明细
        int tempQuestionNum = 0;
        for (QuestionMain questionMain : questionMobilesList) {
            tempQuestionNum++;
            TRecordDia dia = new TRecordDia();
            dia.setRecId(tRecord.getRecId());
            dia.setQuestionId(questionMain.getQuestionId());
            dia.setQuestionName(questionMain.getQuestionName());
            dia.setQuestionNumber(questionMain.getQuestionNumber());
            if (StringUtils.isBlank(questionMain.getQuestionType())) {
                return ResEntity.error("questionType不能为空");
            }
            dia.setQuestionType(questionMain.getQuestionType());
            dia.setOptionIds(JSON.toJSONString(questionMain.getAnswerContentId()));
            dia.setContent(JSON.toJSONString(questionMain.getAnswerContent()));
            dia.setYear(questionMain.getYear());
            dia.setMonth(questionMain.getMonth());
            dia.setDay(questionMain.getDay());
            dia.setWeek(questionMain.getWeek());
            if (StringUtils.isNotBlank(dia.getDateUnit())) {
                if (dia.getDateUnit().contains("4")) {
                    if (StringUtils.isBlank(questionMain.getHour())) {
                        return ResEntity.error("含有题目是时间类型并且含有小时：未传hour");
                    }
                }
            }

            dia.setHour(questionMain.getHour());
            dia.setDateUnit(questionMain.getDateUnit());
            tRecordDiaMapper.insert(dia);
            String[] answerContentId = questionMain.getAnswerContentId();
            Integer[] temp = new Integer[answerContentId.length];
            for (int i = 0; i < answerContentId.length; i++) {
                temp[i] = Integer.parseInt(answerContentId[i]);
            }
            if (temp.length > 0) {
                List<HashMap<String, String>> diagnosisEventCode = tPreDiagnosisOptionMapper.getDiagnosisEventCode(temp);
                ArrayList<TRecordEvent> tRecordEvents = new ArrayList<>();
                for (HashMap<String, String> map : diagnosisEventCode) {
                    TRecordEvent tRecordEvent = new TRecordEvent();
                    tRecordEvent.setRecId(tRecord.getRecId());
                    tRecordEvent.setDicId(map.get("dicId"));
                    tRecordEvent.setDicCode(map.get("dicCode"));
                    tRecordEvent.setDicName(map.get("dicName"));
                    tRecordEvents.add(tRecordEvent);
                }
                if (!tRecordEvents.isEmpty()) {
                    tRecordEventServiceImpl.saveBatch(tRecordEvents);
                }
            }


            //前端传过来的 患者答题的集合。
//            String[] objects = questionMain.getAnswerContent();
//            String content = tPreDiagnosisQuestionMapper.getQuestionStem(questionMain.getQuestionId());
//            if (StringUtils.isNotBlank(content)) {
//                StringBuilder a = new StringBuilder();
//                for (String object : objects) {
//                    if ("4".equals(questionMain.getQuestionType())) {
//
//
//                        if (StringUtils.isNotBlank(questionMain.getYear()) && Integer.parseInt(questionMain.getYear()) != 0) {
//                            a.append(Integer.parseInt(questionMain.getYear())).append("年");
//                        }
//                        if (StringUtils.isNotBlank(questionMain.getMonth()) && Integer.parseInt(questionMain.getMonth()) != 0) {
//                            a.append(Integer.parseInt(questionMain.getMonth())).append("月");
//                        }
//                        if (StringUtils.isNotBlank(questionMain.getDay()) && Integer.parseInt(questionMain.getDay()) != 0) {
//                            a.append(Integer.parseInt(questionMain.getDay())).append("日");
//                        }
//                        if (StringUtils.isNotBlank(questionMain.getHour()) && Integer.parseInt(questionMain.getHour()) != 0) {
//                            a.append(Integer.parseInt(questionMain.getHour())).append("时");
//                        }
//
//
//                    } else if ("6".equals(questionMain.getQuestionType())) {
//                        // StringBuilder s = new StringBuilder();
//                        a.append(questionMain.getYear()).append("年").append(questionMain.getMonth()).append("月").append(questionMain.getDay()).append("日");
//                        // questionMain.setAnswerContent(new String[]{s.toString()});
//                    } else {
//                        a.append(object).append(",");
//                    }
//                }
//                String substring = a.substring(0, a.length() - 1);
//                tempStr.append(content).append(":").append(substring).append("。");
//            } else {
//                //没有题干
//                StringBuilder a = new StringBuilder();
//                for (int i = 0; i < objects.length; i++) {
//                    if (StringUtils.isNotBlank(dia.getDateUnit())) {
//                        String[] split = dia.getDateUnit().split(",");
//                        //防止越界
//                        if (i <= split.length - 1) {
//                            if ("1".equals(split[i])) {
//                                if (!"0".equals(objects[i])) {
//                                    a.append(objects[i]).append("年").append("，");
//                                }
//
//                            } else if ("2".equals(split[i])) {
//                                if (!"0".equals(objects[i])) {
//                                    a.append(objects[i]).append("月").append("，");
//                                }
//
//                            } else if ("3".equals(split[i])) {
//                                if (!"0".equals(objects[i])) {
//                                    a.append(objects[i]).append("日").append("，");
//                                }
//
//                            } else if ("4".equals(split[i])) {
//                                if (!"0".equals(objects[i])) {
//                                    a.append(objects[i]).append("小时").append("，");
//                                }
//
//                            }
//                        }
//
//                    } else {
//                        a.append(objects[i]).append(",");
//                    }
//                }
//                String substring = a.substring(0, a.length() - 1);
//                tempStr.append(substring).append("。");
//            }
//            if (tempQuestionNum % 4 == 0) {
//                // 换行
//                tempStr.append("\n");
//            }
        }
   //     tRecord.setDiaContent(tempStr.toString());

        //if (questionListVo.getSRoadTaskPatientsPhoneId() != null) {

        phoneSuiFang(tRecord, questionListVo);
        //}
//        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        //不要调整位置
        tRecordMapper.insert(tRecord);
        //需要统计数据
        //统计预诊、随访关联病种统计
//        statisticsDisWeb.statisticsDis(tRecord.getRecId(), tRecord.getDiaId(),currentHr);


        return ResEntity.success(tRecord);
    }

    /**
     * 保存随访的信息
     */
    public void phoneSuiFang(TRecord tRecord, QuestionListVo questionListVo) {
        if (tRecord != null){
            if (questionListVo.getRoadExecuteEventWay().equals(Constant.BASIC_STRING_THREE)) {
                tRecord.setRecordResource(Constant.BASIC_STRING_THREE);
            } else if (questionListVo.getRoadExecuteEventWay().equals(Constant.BASIC_STRING_FOUR)) {
                tRecord.setRecordResource(Constant.BASIC_STRING_TWO);
            } else {
                //其它
                if (StringUtils.isBlank(tRecord.getRecordResource())){
                    tRecord.setRecordResource(Constant.BASIC_STRING_FOUR);
                }
                //不为空情况，已经有来源值了。

            }
        }

        SRoadTaskPatients objectById2 = sRoadTaskPatientsMapper.getObjectById(questionListVo.getTaskPatientsId());
        if (objectById2 != null ){
            if (questionListVo.getRoadExecuteEventWay().equals(Constant.BASIC_STRING_THREE)) {
                //更新排期状态，不需要更新任务表的状态，因为，今天有没有电话待随访，不是根据患者任务表状态的。是根据所有排期是否完成
                Long sRoadTaskPatientsPhoneId = questionListVo.getSRoadTaskPatientsPhoneId();
                if (sRoadTaskPatientsPhoneId == null) {
                    //方式是电话 并且 排期id为空
                    SRoadTaskPatientsPhone sRoadTaskPatientsPhone = new SRoadTaskPatientsPhone();
                    if (objectById2.getHandSend().equals(Constant.BASIC_STRING_ONE)){
                        //手动发送的，特殊处理数据
                        sRoadTaskPatientsPhone.setRoadExecuteId(-1);
                        sRoadTaskPatientsPhone.setSRoadTaskId("-1");
                    }else {
                        sRoadTaskPatientsPhone.setRoadExecuteId(objectById2.getRoadExecuteId());
                        sRoadTaskPatientsPhone.setSRoadTaskId(objectById2.getSRoadTaskId());
                    }
                    sRoadTaskPatientsPhone.setPatientId(objectById2.getPatientId());
                    sRoadTaskPatientsPhone.setTaskPatientsId(objectById2.getTaskPatientsId());
                    sRoadTaskPatientsPhone.setDoctorId(AdminWebUtils.getCurrentHr().getUserId());
                    sRoadTaskPatientsPhone.setDoctorName(AdminWebUtils.getCurrentHr().getNameZh());
                    sRoadTaskPatientsPhone.setAllotTaskStatus("0");
                    sRoadTaskPatientsPhone.setAllotTime(new Date());
                    sRoadTaskPatientsPhone.setPhoneStatus(questionListVo.getTaskExcuteStatus());


                    sRoadTaskPatientsPhone.setRecordsId(objectById2.getRecordsId());
                    sRoadTaskPatientsPhone.setSuiFangTime(objectById2.getTaskExcuteTime());
                    if (tRecord != null && questionListVo.getTaskExcuteStatus() == 8) {
                        sRoadTaskPatientsPhone.setSuiFangFinishTime(tRecord.getCreateDate());
                        sRoadTaskPatientsPhone.setRecId(tRecord.getRecId());
                        objectById2.setRecId(tRecord.getRecId());
                        objectById2.setRoadExecuteEventWay(Constant.BASIC_STRING_THREE);
                        objectById2.setTaskExcuteStatus(8);
                        sRoadTaskPatientsMapper.updateByPrimaryKey(objectById2);
                    }
                    sRoadTaskPatientsPhone.setTaskPatientsNode(questionListVo.getTaskPatientsNode());
                    sRoadTaskPatientsPhoneMapper.insert(sRoadTaskPatientsPhone);
                } else {
                    SRoadTaskPatientsPhone objectById = sRoadTaskPatientsPhoneMapper.getObjectById(sRoadTaskPatientsPhoneId + "");
                    objectById.setPhoneStatus(questionListVo.getTaskExcuteStatus());
                    //只有完成问卷，才保存完成随访时间和设置问卷记录id
                    if (tRecord != null && questionListVo.getTaskExcuteStatus() == 8) {
                        objectById.setSuiFangFinishTime(tRecord.getCreateDate());
                        objectById.setRecId(tRecord.getRecId());
                        SRoadTaskPatients objectById1 = sRoadTaskPatientsMapper.getObjectById(objectById.getTaskPatientsId() + "");
                        objectById1.setRecId(objectById.getRecId());
                        objectById1.setRoadExecuteEventWay(Constant.BASIC_STRING_THREE);
                        sRoadTaskPatientsMapper.updateByPrimaryKey(objectById1);
                    }
                    //设置当前进行随访的医生id
                    objectById.setDoctorId(AdminWebUtils.getCurrentHr().getUserId());
                    objectById.setDoctorName(AdminWebUtils.getCurrentHr().getNameZh());
                    objectById.setTaskPatientsNode(questionListVo.getTaskPatientsNode());
                    sRoadTaskPatientsPhoneMapper.updateByPrimaryKey(objectById);
                }
            } else {
                //其它方式的，正常更新任务主表状态。
                objectById2.setTaskExcuteStatus(questionListVo.getTaskExcuteStatus());
                objectById2.setRoadExecuteEventWay(questionListVo.getRoadExecuteEventWay());
                objectById2.setTaskPatientsNode(questionListVo.getTaskPatientsNode());
                if (tRecord != null) {
                    objectById2.setRecId(tRecord.getRecId());
                }
                sRoadTaskPatientsMapper.updateByPrimaryKey(objectById2);
            }
        }else {
            if (tRecord != null){
                log.info("随访任务不存在原因：{}","是手动进行面访");
                tRecord.setRecordResource(Constant.BASIC_STRING_FOUR);
                //插入一条手动免面访的任务
                SRoadTaskPatients roadPatients = new SRoadTaskPatients();
                TPreDiagnosisForm tPreDiagnosisForm = webTPreDiagnosisFormService.getdetailById(tRecord.getDiaId());
                roadPatients.setRecId(tRecord.getRecId());
                roadPatients.setRoadExecuteEventType(tPreDiagnosisForm.getDiaType());
                roadPatients.setRoadExecuteEventContentId(tPreDiagnosisForm.getDiaId());
                roadPatients.setRoadExecuteEventContentName(tPreDiagnosisForm.getFormName());
                roadPatients.setRoadExecuteEventWay(Constant.BASIC_STRING_FOUR);
                roadPatients.setTaskExcuteTime(new Date());
                roadPatients.setPatientId(tRecord.getPatientId());
                roadPatients.setPatientAge(tRecord.getPatientAge());
                roadPatients.setPatientSex(tRecord.getPatientSex());
                roadPatients.setPatientName(tRecord.getPatientName());
                roadPatients.setRecordsId(null);
                roadPatients.setPatientCardNumber(tRecord.getPatientIdcard());
                roadPatients.setAppId(AdminWebUtils.getCurrentHr().getAppId());
                roadPatients.setInsId(AdminWebUtils.getCurrentHr().getInsId());
                roadPatients.setInsName(AdminWebUtils.getCurrentHr().getInsName());
                roadPatients.setInsCode(AdminWebUtils.getCurrentHr().getInsCode());
                roadPatients.setDeptId(AdminWebUtils.getCurrentHr().getDeptId());
                roadPatients.setDeptName(AdminWebUtils.getCurrentHr().getDeptName());
                roadPatients.setDeptCode(AdminWebUtils.getCurrentHr().getDeptCode());
                roadPatients.setStatus("0");
                roadPatients.setTaskName(null);
                roadPatients.setSRoadTaskId(null);
                roadPatients.setRoadExecuteEventTime("0");
                roadPatients.setHandSend("1");
                roadPatients.setDoctorId(AdminWebUtils.getCurrentHr().getUserId());
                roadPatients.setDoctorName(AdminWebUtils.getCurrentHr().getNameZh());
                roadPatients.setTaskExcuteStatus(8);
                sRoadTaskPatientsMapper.insert(roadPatients);
//                return roadPatients;
            }else {
                log.info("：{}","tRecord是空");
            }

        }

//        return null;

    }

}
