package com.cbkj.diagnosis.service.monitor.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem;
import com.cbkj.diagnosis.mapper.health.MedicalWestPrescriptionsItemMapper;
import com.cbkj.diagnosis.service.monitor.MedicalWestPrescriptionsItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Service
public class MedicalWestPrescriptionsItemServiceImpl extends ServiceImpl<MedicalWestPrescriptionsItemMapper, MedicalWestPrescriptionsItem> implements MedicalWestPrescriptionsItemService {

    @Autowired
    private MedicalWestPrescriptionsItemMapper medicalWestPrescriptionsItemMapper;
    @Override
    public List<MedicalWestPrescriptionsItem> getMedicalWestPrescriptionsItemByWestPreId(String recordId) {
        return medicalWestPrescriptionsItemMapper.getItemByWestPreId(recordId);
    }
}
