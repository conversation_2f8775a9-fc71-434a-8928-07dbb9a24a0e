package com.cbkj.diagnosis.service.health;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.health.MedicalMeta;
import com.cbkj.diagnosis.mapper.health.MedicalMetaMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MedicalMetaServiceImpl.java
 * @Description TODO
 * @createTime 2025年03月17日 15:08:00
 */
@Service
public class MedicalMetaServiceImpl extends ServiceImpl<MedicalMetaMapper, MedicalMeta> implements MedicalMetaService {

    @Autowired
    private MedicalMetaMapper medicalMetaMapper;

    @Cacheable(cacheNames = "setlList_getDict", key = "'dic_'+#type")
    public Map<String, String> getDict(String type) {
        QueryWrapper<MedicalMeta> queryWrapper = new QueryWrapper<>();
        List<MedicalMeta> metas = medicalMetaMapper.selectList(queryWrapper);
        Map<String, Object> basicData = metas.stream()
                .collect(Collectors.groupingBy(x -> x.getTypeCode()))
                .values()
                .stream()
                .collect(Collectors.toMap(x -> x.get(0).getTypeCode(),
                        z -> z.stream().map(x -> new MedicalMeta(x.getMetaCode(), x.getMetaName(), x.getTagType())).collect(Collectors.toList())));
        List<MedicalMeta> listType = (List<MedicalMeta>) basicData.get(type);
        if (!CollectionUtils.isEmpty(listType)) {
            return listType.stream().collect(Collectors.toMap(
                    k -> k.getMetaCode(), v -> v.getMetaName(), (o1, o2) -> o1
            ));
        }
        return new HashMap<String, String>();
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = "setlList_getDict", allEntries = true)
    })
    public void clearDict() {
    }
}
