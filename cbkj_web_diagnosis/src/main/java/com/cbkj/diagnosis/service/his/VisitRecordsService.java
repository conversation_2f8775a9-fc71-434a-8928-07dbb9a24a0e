package com.cbkj.diagnosis.service.his;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.TAdminInfo2;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.health.*;
import com.cbkj.diagnosis.beans.his.QualityControlLog;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.common.enums.IdentityDocumentType;
import com.cbkj.diagnosis.common.utils.MD5Util;
import com.cbkj.diagnosis.controller.his.vo.*;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.mapper.business.SSymptomMapper;
import com.cbkj.diagnosis.mapper.business.TDiseaseMapper;
import com.cbkj.diagnosis.mapper.business.TRecordMapper;
import com.cbkj.diagnosis.mapper.health.*;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo;
import com.cbkj.diagnosis.service.health.MedicalMetaServiceImpl;
import com.cbkj.diagnosis.service.health.MedicalRecordsDiagnosisServiceImpl;
import com.cbkj.diagnosis.service.health.MedicalRecordsExamServiceImpl;
import com.cbkj.diagnosis.service.health.MedicalRecordsLabServiceImpl;
import com.cbkj.diagnosis.service.monitor.MedicalPatientRecordCostService;
import com.cbkj.diagnosis.service.sys.SysInsService;
import com.cbkj.diagnosis.service.sysService.SysAdminService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.CardUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Log4j2
public class VisitRecordsService {

    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;

    private final SysAdminService sysAdminService;
    private final SysInsService sysInsService;

    private final TAdminInfoMapper tAdminInfoMapper;

    private final MedicalRecordsMapper medicalRecordsMapper;
    private final MedicalPatientRecordCostMapper medicalPatientRecordCostMapper;

    private final MedicalPatientRecordCostService medicalPatientRecordCostService;
    private final MedicalBasicHandleRecordMapper medicalBasicHandleRecordMapper;
    private final MedicalRecordsPrescriptionsMapper medicalRecordsPrescriptionsMapper;
    private final MedicalRecordsPrescriptionsItemMapper medicalRecordsPrescriptionsItemMapper;

    private final MedicalWestPrescriptionsMapper medicalWestPrescriptionsMapper;

    private final MedicalWestPrescriptionsItemMapper medicalWestPrescriptionsItemMapper;

//    private final TRecordMapper tRecordMapper;

//    private WebTRecordDiaService webTRecordDiaService;

    private final TDiseaseMapper tDiseaseMapper;

//    private final XiaMenTemplate xiaMenTemplate;
//    private final LogAsync logAsync;

    private final SSymptomMapper sSymptomMapper;

    //    private final TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;
    public VisitRecordsService(SysAdminService sysAdminService, SysInsService sysInsService, TAdminInfoMapper tAdminInfoMapper, MedicalRecordsMapper medicalRecordsMapper,
                               MedicalPatientRecordCostMapper medicalPatientRecordCostMapper,
                               MedicalPatientRecordCostService medicalPatientRecordCostService, MedicalBasicHandleRecordMapper medicalBasicHandleRecordMapper, MedicalRecordsPrescriptionsMapper medicalRecordsPrescriptionsMapper,
                               MedicalRecordsPrescriptionsItemMapper medicalRecordsPrescriptionsItemMapper, MedicalWestPrescriptionsMapper medicalWestPrescriptionsMapper, MedicalWestPrescriptionsItemMapper medicalWestPrescriptionsItemMapper, TRecordMapper tRecordMapper,
                               TDiseaseMapper tDiseaseMapper, SSymptomMapper sSymptomMapper) {
        this.sysAdminService = sysAdminService;
        this.sysInsService = sysInsService;
        this.tAdminInfoMapper = tAdminInfoMapper;
        this.medicalRecordsMapper = medicalRecordsMapper;
        this.medicalPatientRecordCostMapper = medicalPatientRecordCostMapper;
        this.medicalPatientRecordCostService = medicalPatientRecordCostService;
        this.medicalBasicHandleRecordMapper = medicalBasicHandleRecordMapper;
        this.medicalRecordsPrescriptionsMapper = medicalRecordsPrescriptionsMapper;
        this.medicalRecordsPrescriptionsItemMapper = medicalRecordsPrescriptionsItemMapper;
        this.medicalWestPrescriptionsMapper = medicalWestPrescriptionsMapper;
        this.medicalWestPrescriptionsItemMapper = medicalWestPrescriptionsItemMapper;
//        this.tRecordMapper = tRecordMapper;
//        this.webTRecordDiaService = webTRecordDiaService;
        this.tDiseaseMapper = tDiseaseMapper;
//        this.xiaMenTemplate = xiaMenTemplate;
//        this.logAsync = logAsync;
        this.sSymptomMapper = sSymptomMapper;
//        this.tPreDiagnosisOptionMapper = tPreDiagnosisOptionMapper;
    }


    @Autowired
    private MedicalRecordsLabServiceImpl medicalRecordsLabService;
    @Autowired
    private MedicalRecordsExamServiceImpl medicalRecordsExamService;
    @Autowired
    private MedicalRecordsDiagnosisServiceImpl medicalRecordsDiagnosisService;
    @Autowired
    private QualityControlLogServiceImpl qualityControlLogService;
    @Autowired
    private MedicalMetaServiceImpl medicalMetaService;

    /**
     * @param visitRecords
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity setVisitRecords(VisitRecords visitRecords) {

        if (null == visitRecords) {
            return ResEntity.error("缺少数据");
        }
        DoctorInfo doctorInfo = visitRecords.getDoctorInfo();
        if (null == doctorInfo) {
            return ResEntity.error("缺少医生信息");
        }
        if (StringUtils.isBlank(doctorInfo.getEmployeeId())) {
            return ResEntity.error("缺少参数:employeeId");
        }
        if (StringUtils.isBlank(visitRecords.getAppId())) {
            return ResEntity.error("缺少参数:appId");
        }
        if (StringUtils.isBlank(visitRecords.getInsCode())) {
            return ResEntity.error("缺少参数:insCode");
        }
        if (StringUtils.isBlank(visitRecords.getInsName())) {
            return ResEntity.error("缺少参数:insName");
        }
        RecordInfo recordInfo = visitRecords.getRecordInfo();
        if (null == recordInfo || StringUtils.isBlank(recordInfo.getVisitNo())) {
            return ResEntity.error("缺少病历信息！");
        }
        PatientInfo patientInfo = visitRecords.getPatientInfo();
        if (null == patientInfo) {
            return ResEntity.error("缺少患者信息！");
        }
        if (StringUtils.isBlank(patientInfo.getPatientCardType())) {
            patientInfo.setPatientCardType("01");
        } else {
            if (!IdentityDocumentType.exists(patientInfo.getPatientCardType())) {
                return ResEntity.error("患者证件类型代码错误。请检查文档规范要求");
            }
        }
        if (StringUtils.isBlank(patientInfo.getPatientCardNumber())) {
            return ResEntity.error("缺少患者证件信息！");
        }

        //数据校验
        List<QualityControlLog> logList = checkVisitRecords(visitRecords);

        String doctorName = "";
        String doctorId = "";
        AdminInfo adminInfo = new AdminInfo();
        adminInfo.setEmployeeId(doctorInfo.getEmployeeId());
        adminInfo.setAppId(visitRecords.getAppId());
        adminInfo.setInsCode(visitRecords.getInsCode());
        /**
         * 处理机构、科室
         * 如果没有医疗机构就新增
         */
        SysIns sysIns = sysInsService.checkAndInsertIns(visitRecords.getInsCode(), visitRecords.getInsName(), visitRecords.getAppId());
        adminInfo.setAppId(sysIns.getAppId());
        adminInfo.setInsId(sysIns.getInsId());
        adminInfo.setInsCode(sysIns.getInsCode());
        adminInfo.setInsName(sysIns.getInsName());
        /**
         * 处理医生
         */
        List<Map<String, Object>> infos = sysAdminService.checkUserIsHasByEmployee(adminInfo);
        if (null == infos || infos.isEmpty()) {
            adminInfo.setUserId(IdUtil.getSnowflake(snowflake).nextIdStr());
            //医生不存在，需要插入到数据库
            adminInfo.setNameZh(doctorInfo.getDoctorName());
            adminInfo.setCertificate(doctorInfo.getDoctorCardNumber());
            adminInfo.setSex(CardUtil.getCarSex(doctorInfo.getDoctorCardNumber()));
            adminInfo.setCreateDate(new Date());
            adminInfo.setPassword((MD5Util.encode("123456")));
            adminInfo.setUserId(IdUtil.getSnowflake(snowflake).nextIdStr());
            adminInfo.setUsername(doctorInfo.getEmployeeId());
            sysAdminService.insertAdminHis(adminInfo);
            doctorId = adminInfo.getUserId();
            doctorName = doctorInfo.getDoctorName();
        } else {
            doctorName = (String) infos.get(0).get("nameZh");
            doctorId = (String) infos.get(0).get("userId");
            adminInfo.setUserId(doctorId);
        }

        if (null != patientInfo) {


            //查询患者信息，是否存在，不存在就插入，存在就更新。
            TAdminInfo2 tAdminInfo2 = new TAdminInfo2();
            tAdminInfo2.setIdCard(patientInfo.getPatientCardNumber());
            tAdminInfo2.setHealthCardNum(patientInfo.getHealthCardNum());
            tAdminInfo2.setMobile(patientInfo.getPatientPhone());
            tAdminInfo2.setPatientCardType(patientInfo.getPatientCardType());
            TAdminInfo admin = null;
            if (StringUtils.isNotBlank(tAdminInfo2.getIdCard())) {
                //证件号不为空，那就查证件号
                admin = tAdminInfoMapper.getHisLoginCheckUserInfoByIdCard(tAdminInfo2);
            } else if (StringUtils.isNotBlank(tAdminInfo2.getMobile())) {
                //手机号不为空，在查手机号
                admin = tAdminInfoMapper.getHisLoginCheckUserInfo(tAdminInfo2);
            }

            //先处理诊断
            List<TDisease> tDiseases = tDiseaseMapper.getListByVo(new DiseaseVo());
            Map<String, TDisease> diseaseMap = new HashMap<>();
            for (TDisease t : tDiseases) {
                diseaseMap.put(t.getDisCode() + t.getDisType(), t);
            }
            List<SSymptom> sSymptoms = sSymptomMapper.selectListByKeyWord("");
            Map<String, SSymptom> symptomMap = new HashMap<>();
            for (SSymptom t : sSymptoms) {
                symptomMap.put(t.getSSymptomCode(), t);
            }

            List<MedicalRecordsDiagnosis> diagnosisList = new ArrayList<>();

            //处理中医诊断信息
            if(StringUtils.isNotEmpty(recordInfo.getChineseDisCode())){
                boolean flag = false;
                String[] disCodes = recordInfo.getChineseDisCode().split("\\|");
                for (int i = 0; i < disCodes.length; i++) {
                    String disCode = disCodes[i];
                    if (StringUtils.isNotEmpty(disCode)) {
                        TDisease tDisease = diseaseMap.get(disCode + "1");
                        if(null != tDisease){
                            MedicalRecordsDiagnosis diagnosis = new MedicalRecordsDiagnosis();
                            diagnosis.setId(IdUtil.getSnowflake(snowflake).nextIdStr());
                            diagnosis.setDiagnosisCode(disCode);
                            diagnosis.setDiagnosisType("2");
                            diagnosis.setDiagnosisId(tDisease.getDisId());
                            diagnosis.setDiagnosisName(tDisease.getDisName());
                            if (i == 0) {
                                recordInfo.setChineseDisCode(disCode);
                                recordInfo.setChineseDisName(tDisease.getDisName());
                                diagnosis.setDiagnosisFlag("1");

                            } else {
                                diagnosis.setDiagnosisFlag("2");
                            }
                            diagnosisList.add(diagnosis);
                        }else {
                            flag = true;
                        }
                    }
                }
                if(flag){
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), visitRecords.getAppId(), visitRecords.getInsCode(), patientInfo.getPatientCardNumber(), "recordInfo", "门（急）诊病历信息子集", "11", "chineseDisCode-中医病名代码需符合GB/T 15657字典规范", "visitNo", recordInfo.getVisitNo(), "chineseDisCode", new Date()));
                }
            }

            //处理中医证型信息
            if(StringUtils.isNotEmpty(recordInfo.getSymCode())){
                boolean flag = false;
                String[] symCodes = recordInfo.getSymCode().split("\\|");
                for (int i = 0; i < symCodes.length; i++) {
                    String symCode = symCodes[i];
                    if (StringUtils.isNotEmpty(symCode)) {
                        SSymptom sSymptom = symptomMap.get(symCode + "1");
                        if(null != sSymptom){
                            MedicalRecordsDiagnosis diagnosis = new MedicalRecordsDiagnosis();
                            diagnosis.setId(IdUtil.getSnowflake(snowflake).nextIdStr());
                            diagnosis.setDiagnosisCode(symCode);
                            diagnosis.setDiagnosisType("3");
                            diagnosis.setDiagnosisId(sSymptom.getSSymptomId());
                            diagnosis.setDiagnosisName(sSymptom.getSSymptomName());
                            if (i == 0) {
                                recordInfo.setSymCode(symCode);
                                recordInfo.setSymName(sSymptom.getSSymptomName());
                                diagnosis.setDiagnosisFlag("1");

                            } else {
                                diagnosis.setDiagnosisFlag("2");
                            }
                            diagnosisList.add(diagnosis);
                        }else {
                            flag = true;
                        }
                    }
                }

                if(flag){
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), visitRecords.getAppId(), visitRecords.getInsCode(), patientInfo.getPatientCardNumber(), "recordInfo", "门（急）诊病历信息子集", "11", "symCode-中医证候代码需符合GB/T 15657字典规范", "visitNo", recordInfo.getVisitNo(), "symCode", new Date()));
                }
            }

            //处理西医诊断信息
            if(StringUtils.isNotEmpty(recordInfo.getWestDisCode())){
                boolean flag = false;
                String[] westDisCodes = recordInfo.getWestDisCode().split("\\|");
                for (int i = 0; i < westDisCodes.length; i++) {
                    String westDisCode = westDisCodes[i];
                    if (StringUtils.isNotEmpty(westDisCode)) {
                        TDisease tDisease = diseaseMap.get(westDisCode + "2");
                        if(null != tDisease){
                            MedicalRecordsDiagnosis diagnosis = new MedicalRecordsDiagnosis();
                            diagnosis.setId(IdUtil.getSnowflake(snowflake).nextIdStr());
                            diagnosis.setDiagnosisCode(westDisCode);
                            diagnosis.setDiagnosisType("1");
                            diagnosis.setDiagnosisId(tDisease.getDisId());
                            diagnosis.setDiagnosisName(tDisease.getDisName());
                            if (i == 0) {
                                recordInfo.setWestDisCode(westDisCode);
                                recordInfo.setWestDisName(tDisease.getDisName());
                                diagnosis.setDiagnosisFlag("1");

                            } else {
                                diagnosis.setDiagnosisFlag("2");
                            }
                            diagnosisList.add(diagnosis);
                        }else {
                            flag = true;
                        }

                    }
                }
                if(flag){
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), visitRecords.getAppId(), visitRecords.getInsCode(), patientInfo.getPatientCardNumber(), "recordInfo", "门（急）诊病历信息子集", "11", "westDisCode-西医诊断代码需符合ICD-10字典规范", "visitNo", recordInfo.getVisitNo(), "westDisCode", new Date()));
                }
            }

            recordInfo.setDoctorId(doctorId);
            recordInfo.setDoctorName(doctorName);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")); // 或 GMT+8
            try {
                recordInfo.setRecordTime(simpleDateFormat.parse(recordInfo.getVisitTime()));
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            //患者不存在
            if (admin == null) {
                //插入数据库
                TAdminInfo tAdminInfo = new TAdminInfo();
                BeanUtils.copyProperties(patientInfo, tAdminInfo);
                tAdminInfo.setNation(patientInfo.getNation());
                tAdminInfo.setNationality(patientInfo.getNationality());
                tAdminInfo.setCreateTime(new Date());
                if (StringUtils.isNotBlank(patientInfo.getPatientCardNumber())) {
                    String patientCardNumber = patientInfo.getPatientCardNumber();
                    tAdminInfo.setCardNumber(patientCardNumber);
                    tAdminInfo.setAge(CardUtil.getCarAge(patientCardNumber));
                    tAdminInfo.setSex(CardUtil.getCarSex(patientCardNumber));
                    //tAdminInfo.setIdcard(patientCardNumber);
                } else {

                    tAdminInfo.setSex("1".equals(patientInfo.getPatientSex()) ? "M" : "F");
                }

                tAdminInfo.setUserName(patientInfo.getPatientName());
                tAdminInfo.setMobile(patientInfo.getPatientPhone());
                tAdminInfo.setName(patientInfo.getPatientName());
                tAdminInfo.setUserId(IdUtil.getSnowflake(snowflake).nextIdStr());


                recordInfo.setPatientId(tAdminInfo.getUserId());
                if (StringUtils.isNotBlank(patientInfo.getPatientCardNumber())) {
                    recordInfo.setPatientSex(CardUtil.getCarSex(patientInfo.getPatientCardNumber()));
                    recordInfo.setPatientAge(CardUtil.getCarAge(patientInfo.getPatientCardNumber()));
                    recordInfo.setPatientCardNumber(patientInfo.getPatientCardNumber());
                }
                recordInfo.setPatientPhone(patientInfo.getPatientPhone());

                MedicalRecords medicalRecords = recordInsertHis(visitRecords, sysIns, tAdminInfo, adminInfo);
                if (null != medicalRecords) {
                    //病历不为空
                    tAdminInfo.setLastRecordsId(medicalRecords.getRecordsId());

                } else {
                    return ResEntity.error("病历插入失败！无病历信息");
                }
                /**
                 * 更新患者最新的疾病
                 */

                tAdminInfo.setLastRecordsTime(recordInfo.getRecordTime());
                tAdminInfo.setLastWestDisName(recordInfo.getWestDisName());
                tAdminInfo.setLastChineseDisName(recordInfo.getChineseDisName());
                tAdminInfo.setLastDoctorName(doctorName);
                tAdminInfo.setLastSymName(recordInfo.getSymName());
                tAdminInfo.setLastDeptName(recordInfo.getDeptName());

                tAdminInfo.setLastDoctorId(recordInfo.getDoctorId());
                tAdminInfo.setLastRecordsTime(recordInfo.getRecordTime());
                tAdminInfoMapper.insert(tAdminInfo);

                for (MedicalRecordsDiagnosis diagnosis : diagnosisList) {
                    diagnosis.setRecordId(medicalRecords.getRecordsId());
                }
                //插入诊断信息
                medicalRecordsDiagnosisService.saveOrUpdateBatch(diagnosisList);

                for (QualityControlLog log : logList) {
                    log.setRecordId(medicalRecords.getRecordsId());
                }
                qualityControlLogService.saveBatch(logList);

                return ResEntity.success(medicalRecords.getRecordsId());
            } else {
                //存在患者
                //RecordInfo recordInfo = visitRecords.getRecordInfo();
                admin.setMedicalCard(patientInfo.getMedicalCard());
                recordInfo.setPatientId(admin.getUserId());
                if (StringUtils.isNotBlank(patientInfo.getPatientCardNumber())) {
                    recordInfo.setPatientSex(CardUtil.getCarSex(patientInfo.getPatientCardNumber()));
                    recordInfo.setPatientAge(CardUtil.getCarAge(patientInfo.getPatientCardNumber()));
                    recordInfo.setPatientCardNumber(patientInfo.getPatientCardNumber());
                }
                recordInfo.setPatientPhone(patientInfo.getPatientPhone());
                MedicalRecords medicalRecords = recordInsertHis(visitRecords, sysIns, admin, adminInfo);
                if (null != medicalRecords) {
                    admin.setLastRecordsId(medicalRecords.getRecordsId());
                } else {
                    return ResEntity.error("病历插入失败！无病历信息");

                }


                /**
                 * 更新患者最新的疾病
                 */
                admin.setLastRecordsTime(recordInfo.getRecordTime());
                admin.setLastWestDisName(recordInfo.getWestDisName());
                admin.setLastChineseDisName(recordInfo.getChineseDisName());
                admin.setLastDoctorName(doctorName);
                admin.setLastSymName(recordInfo.getSymName());
                admin.setLastDeptName(recordInfo.getDeptName());
                admin.setPatientCardType(patientInfo.getPatientCardType());
                admin.setLastDoctorId(recordInfo.getDoctorId());
                admin.setLastRecordsTime(recordInfo.getRecordTime());
                tAdminInfoMapper.updateByPrimaryKey(admin);

                //先删后插入诊断信息
                if (diagnosisList.size() > 0) {
                    for (MedicalRecordsDiagnosis diagnosis : diagnosisList) {
                        diagnosis.setRecordId(medicalRecords.getRecordsId());
                    }

                    LambdaUpdateWrapper<MedicalRecordsDiagnosis> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(MedicalRecordsDiagnosis::getRecordId, medicalRecords.getRecordsId());
                    medicalRecordsDiagnosisService.remove(updateWrapper);
                    medicalRecordsDiagnosisService.saveOrUpdateBatch(diagnosisList);
                }

                //先删后插入日志信息
                if(logList.size() > 0){
                    for (QualityControlLog log : logList) {
                        log.setRecordId(medicalRecords.getRecordsId());
                    }
                    LambdaUpdateWrapper<QualityControlLog> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(QualityControlLog::getRecordId, medicalRecords.getRecordsId());
                    qualityControlLogService.remove(updateWrapper);
                    qualityControlLogService.saveBatch(logList);
                }

                return ResEntity.success(medicalRecords.getRecordsId());
            }

        }

        return ResEntity.error("缺少患者信息");


    }

    public static void main(String[] args) {
        String str = "N63.x01,N64.500x003";
        String[] array = str.split("\\|");
        System.out.println(array.length);
        for (int i = 0; i < array.length; i++) {
            System.out.println(array[i]);
        }
    }

    /**
     * 处理病历
     *
     * @param visitRecords
     */
    public MedicalRecords recordInsertHis(VisitRecords visitRecords, SysIns sysIns, TAdminInfo tAdminInfo, AdminInfo adminInfo) {

        RecordInfo recordInfo = visitRecords.getRecordInfo();

        MedicalRecords a = new MedicalRecords();
        a.setAppId(visitRecords.getAppId());
        a.setInsCode(visitRecords.getInsCode());
        a.setVisitNo(recordInfo.getVisitNo());
        MedicalRecords medicalRecords = medicalRecordsMapper.selectOneByCondition(a);
        SysDept sysDept = sysInsService.checkAndInsertDept(sysIns, recordInfo.getDeptCode(), recordInfo.getDeptName());


        adminInfo.setDeptName(sysDept.getDeptName());
        adminInfo.setDeptCode(sysDept.getDeptCode());
        adminInfo.setDeptId(sysDept.getDeptId());
        sysAdminService.updateAdmin2(adminInfo);
        /**
         * 处理病历
         */

        if (medicalRecords != null) {
            BeanUtils.copyProperties(medicalRecords, a);
            a.setAppId(sysIns.getAppId());
            a.setInsCode(sysIns.getInsCode());
            a.setInsName(sysIns.getInsName());
            a.setInsId(sysIns.getInsId());
            BeanUtils.copyProperties(recordInfo, a);
            a.setDeptId(sysDept.getDeptId());
            a.setPatientName(tAdminInfo.getUserName());
            a.setPatientPhone(tAdminInfo.getMobile());
            a.setPatientCardNumber(tAdminInfo.getCardNumber());
            a.setPatientAge(tAdminInfo.getAge());
            a.setPatientId(tAdminInfo.getUserId());
            a.setPatientCardType(tAdminInfo.getPatientCardType());
            a.setPatientSex(tAdminInfo.getSex());
            a.setPatientHealthCardNum(tAdminInfo.getHealthCardNum());
            a.setChineseDisId(getDisId(a.getChineseDisCode(), "1"));
            a.setWestDisId(getDisId(a.getWestDisCode(), "2"));
            a.setSymId(getSymId(a.getSymCode()));
            medicalRecordsMapper.updateByPrimaryKey(a);
            a = medicalRecords;

        } else {
            BeanUtils.copyProperties(visitRecords, a);
            BeanUtils.copyProperties(recordInfo, a);

            a.setRecordsId(IdUtil.getSnowflake(snowflake).nextIdStr());
            a.setCreateDate(new Date());
            a.setDeptId(sysDept.getDeptId());
            a.setInsId(sysIns.getInsId());
            a.setInsCode(sysIns.getInsCode());
            a.setInsName(sysIns.getInsName());
            a.setAppId(visitRecords.getAppId());

            a.setPatientName(tAdminInfo.getUserName());
            a.setPatientPhone(tAdminInfo.getMobile());
            a.setPatientCardNumber(tAdminInfo.getCardNumber());
            a.setPatientAge(tAdminInfo.getAge());
            a.setPatientId(tAdminInfo.getUserId());
            a.setPatientCardType(tAdminInfo.getPatientCardType());
            a.setPatientSex(tAdminInfo.getSex());
            a.setPatientHealthCardNum(tAdminInfo.getHealthCardNum());
            a.setChineseDisId(getDisId(a.getChineseDisCode(), "1"));
            a.setWestDisId(getDisId(a.getWestDisCode(), "2"));
            a.setSymId(getSymId(a.getSymCode()));
            medicalRecordsMapper.insert(a);
        }

        /**
         * 处理处方
         */

        recordInsertHisPre(visitRecords, a.getRecordsId());

        /**
         * 处理费用
         */
        MedicalPatientRecordCost medicalPatientRecordCost;
        medicalPatientRecordCost = new MedicalPatientRecordCost();
        medicalPatientRecordCost.setRecordsId(a.getRecordsId());
        Long recordCostId = medicalPatientRecordCostMapper.selectIdByCondition(medicalPatientRecordCost);
        if ((recordCostId == null || recordCostId == 0) && visitRecords.getRecordCost() != null) {
            //插入费用
            // BeanUtils.copyProperties(visitRecords.getRecordCost(), medicalPatientRecordCost);


            //medicalPatientRecordCostMapper.insert(medicalPatientRecordCost);
            recordCostToMedicalPatientRecordCost(visitRecords.getRecordCost(), a.getRecordsId());
        }
        /**
         * 一般处置
         */

        List<GeneralTreatments> generalTreatments = visitRecords.getGeneralTreatments();
        if (generalTreatments != null) {
            //判断是否有
            MedicalBasicHandleRecord handleRecordIdQuery = new MedicalBasicHandleRecord();
            handleRecordIdQuery.setRecordsId(a.getRecordsId());
            Long aLong = medicalBasicHandleRecordMapper.selectIdByCondition(handleRecordIdQuery);
            if (aLong == null || aLong == 0) {
                ArrayList<MedicalBasicHandleRecord> list = new ArrayList<>();
                for (int i = 0; i < generalTreatments.size(); i++) {
                    MedicalBasicHandleRecord handleRecordId = new MedicalBasicHandleRecord();
                    BeanUtils.copyProperties(generalTreatments.get(i), handleRecordId);
                    handleRecordId.setRecordsId(a.getRecordsId());
                    list.add(handleRecordId);
                }
                if (list.size() > 0) {
                    medicalBasicHandleRecordMapper.insertList(list);
                }
            }
        }

        /**
         * 处理检查
         */
        List<MedicalRecordsExam> medicalRecordsExams = visitRecords.getMedicalRecordsExam();
        if (medicalRecordsExams != null) {
            for (MedicalRecordsExam exam : medicalRecordsExams) {
                exam.setExamId(IdUtil.getSnowflake(snowflake).nextIdStr());
                exam.setRecordId(a.getRecordsId());
            }

            LambdaUpdateWrapper<MedicalRecordsExam> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(MedicalRecordsExam::getRecordId, a.getRecordsId());
            medicalRecordsExamService.remove(lambdaUpdateWrapper);
            medicalRecordsExamService.saveOrUpdateBatch(medicalRecordsExams);
        }

        /**
         * 处理检验
         */
        List<MedicalRecordsLab> medicalRecordsLabs = visitRecords.getMedicalRecordsLab();
        if (medicalRecordsLabs != null) {
            for (MedicalRecordsLab lab : medicalRecordsLabs) {
                lab.setLabId(IdUtil.getSnowflake(snowflake).nextIdStr());
                lab.setRecordId(a.getRecordsId());
            }
            LambdaUpdateWrapper<MedicalRecordsLab> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(MedicalRecordsLab::getRecordId, a.getRecordsId());
            medicalRecordsLabService.remove(lambdaUpdateWrapper);
            medicalRecordsLabService.saveOrUpdateBatch(medicalRecordsLabs);
        }


        return a;
    }

    /**
     * todo 待完善
     *
     * @param recordCost
     * @param recordsId
     */
    private void recordCostToMedicalPatientRecordCost(RecordCost recordCost, String recordsId) {
        ArrayList<MedicalPatientRecordCost> arrayList = new ArrayList<>();

        MedicalPatientRecordCost medicalPatientRecordCost1 = new MedicalPatientRecordCost();
        medicalPatientRecordCost1.setRecordsId(recordsId);
        medicalPatientRecordCost1.setMedicalPatientRecordBigType("1");
        medicalPatientRecordCost1.setMedicalPatientRecordBigTypeName("综合医疗服务类");
        medicalPatientRecordCost1.setMedicalPatientRecordItemType("1.1");
//        medicalPatientRecordCost1.setMedicalPatientRecordItemTypeName("一般医疗服务费-中医辨证论治费");
        medicalPatientRecordCost1.setMedicalPatientRecordItemTypeName("一般医疗服务费");
        medicalPatientRecordCost1.setMedicalPatientRecordActualName("一般医疗服务费");
        medicalPatientRecordCost1.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getTcmServiceCost()) ? "0" : recordCost.getTcmServiceCost());
        arrayList.add(medicalPatientRecordCost1);



        MedicalPatientRecordCost tcmBianZhengCost = new MedicalPatientRecordCost();
        tcmBianZhengCost.setRecordsId(recordsId);
        tcmBianZhengCost.setMedicalPatientRecordBigType("1");
        tcmBianZhengCost.setMedicalPatientRecordBigTypeName("综合医疗服务类");
        tcmBianZhengCost.setMedicalPatientRecordItemType("1.2");
        tcmBianZhengCost.setMedicalPatientRecordItemTypeName("中医辨证论治费");
        tcmBianZhengCost.setMedicalPatientRecordActualName("中医辨证论治费");
        tcmBianZhengCost.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getTcmBianZhengCost()) ? "0" : recordCost.getTcmBianZhengCost());
        arrayList.add(tcmBianZhengCost);


        MedicalPatientRecordCost medicalPatientRecordCost21 = new MedicalPatientRecordCost();
        medicalPatientRecordCost21.setRecordsId(recordsId);
        medicalPatientRecordCost21.setMedicalPatientRecordBigType("2");
        medicalPatientRecordCost21.setMedicalPatientRecordBigTypeName("中医类");
        medicalPatientRecordCost21.setMedicalPatientRecordItemType("2.1");
        medicalPatientRecordCost21.setMedicalPatientRecordItemTypeName("中医治疗费");
        medicalPatientRecordCost21.setMedicalPatientRecordActualName("中医治疗费");
        medicalPatientRecordCost21.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getCnMedicineCost()) ? "0" : recordCost.getCnMedicineCost());
        arrayList.add(medicalPatientRecordCost21);

        MedicalPatientRecordCost medicalPatientRecordCost31 = new MedicalPatientRecordCost();
        medicalPatientRecordCost31.setRecordsId(recordsId);
        medicalPatientRecordCost31.setMedicalPatientRecordBigType("3");
        medicalPatientRecordCost31.setMedicalPatientRecordBigTypeName("诊断类");
        medicalPatientRecordCost31.setMedicalPatientRecordItemType("3.1");
        medicalPatientRecordCost31.setMedicalPatientRecordItemTypeName("影像学诊断费");
        medicalPatientRecordCost31.setMedicalPatientRecordActualName("影像学诊断费");
        medicalPatientRecordCost31.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getExaminationCost()) ? "0" : recordCost.getExaminationCost());
        arrayList.add(medicalPatientRecordCost31);

        MedicalPatientRecordCost medicalPatientRecordCost32 = new MedicalPatientRecordCost();
        medicalPatientRecordCost32.setRecordsId(recordsId);
        medicalPatientRecordCost32.setMedicalPatientRecordBigType("3");
        medicalPatientRecordCost32.setMedicalPatientRecordBigTypeName("诊断类");
        medicalPatientRecordCost32.setMedicalPatientRecordItemType("3.2");
//        medicalPatientRecordCost32.setMedicalPatientRecordItemTypeName("化验费");
//        medicalPatientRecordCost32.setMedicalPatientRecordActualName("化验费");
        medicalPatientRecordCost32.setMedicalPatientRecordItemTypeName("实验室诊断费");
        medicalPatientRecordCost32.setMedicalPatientRecordActualName("实验室诊断费");
        medicalPatientRecordCost32.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getAssayCost()) ? "0" : recordCost.getAssayCost());
        arrayList.add(medicalPatientRecordCost32);

//        MedicalPatientRecordCost medicalPatientRecordCost33 = new MedicalPatientRecordCost();
//        medicalPatientRecordCost33.setRecordsId(recordsId);
//        medicalPatientRecordCost33.setMedicalPatientRecordBigType("3");
//        medicalPatientRecordCost33.setMedicalPatientRecordBigTypeName("诊断类");
//        medicalPatientRecordCost33.setMedicalPatientRecordItemType("3.3");
//        medicalPatientRecordCost33.setMedicalPatientRecordItemTypeName("化验费");
//        medicalPatientRecordCost33.setMedicalPatientRecordActualName("化验费");
//        medicalPatientRecordCost33.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getAssayCost()) ? "0" : recordCost.getAssayCost());
//        arrayList.add(medicalPatientRecordCost33);

//        MedicalPatientRecordCost medicalPatientRecordCost34 = new MedicalPatientRecordCost();
//        medicalPatientRecordCost34.setRecordsId(recordsId);
//        medicalPatientRecordCost34.setMedicalPatientRecordBigType("3");
//        medicalPatientRecordCost34.setMedicalPatientRecordBigTypeName("诊断类");
//        medicalPatientRecordCost34.setMedicalPatientRecordItemType("3.4");
//        medicalPatientRecordCost34.setMedicalPatientRecordItemTypeName("辅助检查费");
//        medicalPatientRecordCost34.setMedicalPatientRecordActualName("辅助检查费");
//        medicalPatientRecordCost34.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getExaminationCost()) ? "0" : recordCost.getExaminationCost());
//        arrayList.add(medicalPatientRecordCost34);


        MedicalPatientRecordCost medicalPatientRecordCost41 = new MedicalPatientRecordCost();
        medicalPatientRecordCost41.setRecordsId(recordsId);
        medicalPatientRecordCost41.setMedicalPatientRecordBigType("4");
        medicalPatientRecordCost41.setMedicalPatientRecordBigTypeName("中药类");
        medicalPatientRecordCost41.setMedicalPatientRecordItemType("4.1");
        medicalPatientRecordCost41.setMedicalPatientRecordItemTypeName("中草药费");
        medicalPatientRecordCost41.setMedicalPatientRecordActualName("中草药费");
        medicalPatientRecordCost41.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getTcmCost()) ? "0" : recordCost.getTcmCost());
        arrayList.add(medicalPatientRecordCost41);

        MedicalPatientRecordCost medicalPatientRecordCost42 = new MedicalPatientRecordCost();
        medicalPatientRecordCost42.setRecordsId(recordsId);
        medicalPatientRecordCost42.setMedicalPatientRecordBigType("4");
        medicalPatientRecordCost42.setMedicalPatientRecordBigTypeName("中药类");
        medicalPatientRecordCost42.setMedicalPatientRecordItemType("4.2");
        medicalPatientRecordCost42.setMedicalPatientRecordItemTypeName("中成药费");
        medicalPatientRecordCost42.setMedicalPatientRecordActualName("中成药费");
        medicalPatientRecordCost42.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getCpdCost()) ? "0" : recordCost.getCpdCost());
        arrayList.add(medicalPatientRecordCost42);


        MedicalPatientRecordCost medicalPatientRecordCost43 = new MedicalPatientRecordCost();
        medicalPatientRecordCost43.setRecordsId(recordsId);
        medicalPatientRecordCost43.setMedicalPatientRecordBigType("4");
        medicalPatientRecordCost43.setMedicalPatientRecordBigTypeName("中药类");
        medicalPatientRecordCost43.setMedicalPatientRecordItemType("4.3");
        medicalPatientRecordCost43.setMedicalPatientRecordItemTypeName("中成药费-医疗机构中药制剂费");
        medicalPatientRecordCost43.setMedicalPatientRecordActualName("中药制剂费");
        medicalPatientRecordCost43.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getIpCost()) ? "0" : recordCost.getIpCost());
        arrayList.add(medicalPatientRecordCost43);


        MedicalPatientRecordCost medicalPatientRecordCost51 = new MedicalPatientRecordCost();
        medicalPatientRecordCost51.setRecordsId(recordsId);
        medicalPatientRecordCost51.setMedicalPatientRecordBigType("5");
        medicalPatientRecordCost51.setMedicalPatientRecordBigTypeName("西药类");
        medicalPatientRecordCost51.setMedicalPatientRecordItemType("5.1");
        medicalPatientRecordCost51.setMedicalPatientRecordItemTypeName("西药费");
        medicalPatientRecordCost51.setMedicalPatientRecordActualName("西药费");
        medicalPatientRecordCost51.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getWesDrugCost()) ? "0" : recordCost.getWesDrugCost());
        arrayList.add(medicalPatientRecordCost51);


        MedicalPatientRecordCost medicalPatientRecordCost61 = new MedicalPatientRecordCost();
        medicalPatientRecordCost61.setRecordsId(recordsId);
        medicalPatientRecordCost61.setMedicalPatientRecordBigType("6");
        medicalPatientRecordCost61.setMedicalPatientRecordBigTypeName("治疗类");
        medicalPatientRecordCost61.setMedicalPatientRecordItemType("6.1");
        medicalPatientRecordCost61.setMedicalPatientRecordItemTypeName("手术治疗费");
        medicalPatientRecordCost61.setMedicalPatientRecordActualName("手术治疗费");
        medicalPatientRecordCost61.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getTdCost()) ? "0" : recordCost.getTdCost());
//        medicalPatientRecordCost61.setMedicalPatientRecordItemTypeName("手术治疗费");
//        medicalPatientRecordCost61.setMedicalPatientRecordActualName("手术治疗费");
//        medicalPatientRecordCost61.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getSurgicalCost()) ? "0" : recordCost.getSurgicalCost());
        arrayList.add(medicalPatientRecordCost61);


//        MedicalPatientRecordCost medicalPatientRecordCost62 = new MedicalPatientRecordCost();
//        medicalPatientRecordCost62.setRecordsId(recordsId);
//        medicalPatientRecordCost62.setMedicalPatientRecordBigType("6");
//        medicalPatientRecordCost62.setMedicalPatientRecordBigTypeName("治疗类");
//        medicalPatientRecordCost62.setMedicalPatientRecordItemType("6.2");
//        medicalPatientRecordCost62.setMedicalPatientRecordItemTypeName("治疗处置金额");
//        medicalPatientRecordCost62.setMedicalPatientRecordActualName("治疗处置金额");
//        medicalPatientRecordCost62.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getTdCost()) ? "0" : recordCost.getTdCost());
//        arrayList.add(medicalPatientRecordCost62);

        MedicalPatientRecordCost medicalPatientRecordCost71 = new MedicalPatientRecordCost();
        medicalPatientRecordCost71.setRecordsId(recordsId);
        medicalPatientRecordCost71.setMedicalPatientRecordBigType("7");
        medicalPatientRecordCost71.setMedicalPatientRecordBigTypeName("其他类");
        medicalPatientRecordCost71.setMedicalPatientRecordItemType("7.1");
        medicalPatientRecordCost71.setMedicalPatientRecordItemTypeName("其他费");
        medicalPatientRecordCost71.setMedicalPatientRecordActualName("其他费");
        medicalPatientRecordCost71.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getOthersCost()) ? "0" : recordCost.getOthersCost());
        arrayList.add(medicalPatientRecordCost71);


        MedicalPatientRecordCost medicalPatientRecordCost10 = new MedicalPatientRecordCost();
        medicalPatientRecordCost10.setRecordsId(recordsId);
        medicalPatientRecordCost10.setMedicalPatientRecordBigType("10");
        medicalPatientRecordCost10.setMedicalPatientRecordBigTypeName("个人承担费");
        medicalPatientRecordCost10.setMedicalPatientRecordItemType("10");
        medicalPatientRecordCost10.setMedicalPatientRecordItemTypeName("个人承担费");
        medicalPatientRecordCost10.setMedicalPatientRecordActualName("个人承担费");
        medicalPatientRecordCost10.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getPersonalCost()) ? "0" : recordCost.getPersonalCost());
        arrayList.add(medicalPatientRecordCost10);


        MedicalPatientRecordCost medicalPatientRecordCost11 = new MedicalPatientRecordCost();
        medicalPatientRecordCost11.setRecordsId(recordsId);
        medicalPatientRecordCost11.setMedicalPatientRecordBigType("11");
        medicalPatientRecordCost11.setMedicalPatientRecordBigTypeName("医保报销金额");
        medicalPatientRecordCost11.setMedicalPatientRecordItemType("11");
        medicalPatientRecordCost11.setMedicalPatientRecordItemTypeName("医保报销金额");
        medicalPatientRecordCost11.setMedicalPatientRecordActualName("医保报销金额");
        medicalPatientRecordCost11.setMedicalPatientRecordCost(StringUtils.isBlank(recordCost.getInsuranceReimbursementCost()) ? "0" : recordCost.getInsuranceReimbursementCost());
        arrayList.add(medicalPatientRecordCost11);


//        MedicalPatientRecordCost medicalPatientRecordCost12 = new MedicalPatientRecordCost();
//        medicalPatientRecordCost12.setRecordsId(recordsId);
//        medicalPatientRecordCost12.setMedicalPatientRecordBigType("12");
//        medicalPatientRecordCost12.setMedicalPatientRecordBigTypeName("门 (急) 诊费用金额（元）");
//        medicalPatientRecordCost12.setMedicalPatientRecordItemType("12");
//        medicalPatientRecordCost12.setMedicalPatientRecordItemTypeName("门 (急) 诊费用金额（元）");
//        medicalPatientRecordCost12.setMedicalPatientRecordActualName("门 (急) 诊费用金额（元）");
//        medicalPatientRecordCost12.setMedicalPatientRecordCost(recordCost.getInsuranceReimbursementCost());
//        arrayList.add(medicalPatientRecordCost12);

//        MedicalPatientRecordCost medicalPatientRecordCost13 = new MedicalPatientRecordCost();
//        medicalPatientRecordCost12.setRecordsId(recordsId);
//        medicalPatientRecordCost12.setMedicalPatientRecordBigType("12");
//        medicalPatientRecordCost12.setMedicalPatientRecordBigTypeName("医疗费用结算方式代码");
//        medicalPatientRecordCost12.setMedicalPatientRecordItemType("12");
//        medicalPatientRecordCost12.setMedicalPatientRecordItemTypeName("医疗费用结算方式代码");
//        medicalPatientRecordCost12.setMedicalPatientRecordActualName("医疗费用结算方式代码");
//        medicalPatientRecordCost12.setMedicalPatientRecordCost(recordCost.getInsuranceReimbursementCost());
//        arrayList.add(medicalPatientRecordCost12);


        medicalPatientRecordCostService.saveBatch(arrayList);

    }

    public String getDisId(String disCode, String disType) {
        if (StringUtils.isBlank(disCode)) {
            return null;
        }
        TDisease tDisease = new TDisease();
        tDisease.setDisCode(disCode);
        tDisease.setDisType(disType);
        String disId = tDiseaseMapper.getOneByDisCode(tDisease);
        return disId;
    }

    public String getSymId(String symCode) {
        if (StringUtils.isBlank(symCode)) {
            return null;
        }
        SSymptom sSymptom = new SSymptom();
        sSymptom.setSSymptomCode(symCode);

        return sSymptomMapper.getOneBySymCode(sSymptom);
    }

    /**
     * 处方和明细处理
     *
     * @param visitRecords
     */
    public void recordInsertHisPre(VisitRecords visitRecords, String recordsId) {
        List<ChinesePrescriptions> chinesePrescriptions = visitRecords.getChinesePrescriptions();

        if (chinesePrescriptions != null && chinesePrescriptions.size() > 0) {
            /**
             * 处理中医处方
             */
            String prescriptionsId = medicalRecordsPrescriptionsMapper.selectIdByCondition(recordsId);

            if (StringUtils.isBlank(prescriptionsId)) {
                ArrayList<MedicalRecordsPrescriptions> chinesePreList = new ArrayList<>();
                ArrayList<MedicalRecordsPrescriptionsItem> chinesePreItemList = new ArrayList<>();

                for (int i = 0; i < chinesePrescriptions.size(); i++) {
                    //插入中药处方
                    MedicalRecordsPrescriptions chinesePreInsert = new MedicalRecordsPrescriptions();
                    ChinesePrescriptions chinesePrescriptions1 = chinesePrescriptions.get(i);
                    BeanUtils.copyProperties(chinesePrescriptions1, chinesePreInsert);
                    chinesePreInsert.setPrescriptionNo(chinesePrescriptions1.getPrescriptionNo());
                    chinesePreInsert.setPrescriptionsTime(chinesePrescriptions1.getPrescriptionTime());
                    chinesePreInsert.setRecordsId(recordsId);
                    chinesePreInsert.setPrescriptionsId(IdUtil.getSnowflake(snowflake).nextIdStr());
//                    chinesePreInsert.setPrescriptionsTime(chinesePrescriptions1.getPrescriptionsTime());
//                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            try {
//                recordInfo.setRecordTime(simpleDateFormat.parse(recordInfo.getVisitTime()));
//            } catch (ParseException e) {
//                throw new RuntimeException(e);
//            }

                    chinesePreList.add(chinesePreInsert);
                    //插中药处方明细
                    List<ChinesePrescriptionsItem> chinesePrescriptionDrugs = chinesePrescriptions.get(i).getChinesePrescriptionDrugs();
                    for (int i1 = 0; i1 < chinesePrescriptionDrugs.size(); i1++) {
                        MedicalRecordsPrescriptionsItem chinesePrescriptionsItem = new MedicalRecordsPrescriptionsItem();
                        BeanUtils.copyProperties(chinesePrescriptionDrugs.get(i1), chinesePrescriptionsItem);
                        chinesePrescriptionsItem.setPrescriptionsId(chinesePreInsert.getPrescriptionsId());
                        chinesePreItemList.add(chinesePrescriptionsItem);
                    }
                }
                if (!chinesePreList.isEmpty()) {
                    medicalRecordsPrescriptionsMapper.insertList(chinesePreList);
                }
                if (!chinesePreItemList.isEmpty()) {
                    medicalRecordsPrescriptionsItemMapper.insertList(chinesePreItemList);
                }
            }
        }

        /**
         * 处理西医处方和明细
         *
         */
        List<WestPrescriptions> westPrescriptions = visitRecords.getWestPrescriptions();

        if (westPrescriptions != null && westPrescriptions.size() > 0) {
            MedicalWestPrescriptions medicalWestPrescriptions = new MedicalWestPrescriptions();
            medicalWestPrescriptions.setRecordsId(recordsId);
            String westPrescriptionsIdStr = medicalWestPrescriptionsMapper.selectIdByCondition(medicalWestPrescriptions);
            if (StringUtils.isBlank(westPrescriptionsIdStr)) {
                ArrayList<MedicalWestPrescriptions> westPreList = new ArrayList<>();
                ArrayList<MedicalWestPrescriptionsItem> westPreItemList = new ArrayList<>();

                for (int i = 0; i < westPrescriptions.size(); i++) {
                    //插入西药处方
                    MedicalWestPrescriptions westPreInsert = new MedicalWestPrescriptions();
                    BeanUtils.copyProperties(westPrescriptions.get(i), westPreInsert);
                    westPreInsert.setRecordsId(recordsId);
                    westPreInsert.setWestPrescriptionsId(IdUtil.getSnowflake(snowflake).nextIdStr());
                    westPreList.add(westPreInsert);

                    List<WestPrescriptionsItem> westPrescriptionDrugs = westPrescriptions.get(i).getWestPrescriptionDrugs();
                    for (int i1 = 0; i1 < westPrescriptionDrugs.size(); i1++) {
                        MedicalWestPrescriptionsItem medicalWestPrescriptionsItem = new MedicalWestPrescriptionsItem();
                        BeanUtils.copyProperties(westPrescriptionDrugs.get(i1), medicalWestPrescriptionsItem);
                        medicalWestPrescriptionsItem.setWestPrescriptionsId(westPreInsert.getWestPrescriptionsId());
                        westPreItemList.add(medicalWestPrescriptionsItem);
                    }
                }
                if (westPreList.size() > 0) {
                    medicalWestPrescriptionsMapper.insertList(westPreList);
                }
                if (westPreItemList.size() > 0) {
                    medicalWestPrescriptionsItemMapper.insertList(westPreItemList);
                }

            }
        }

    }

    public List<QualityControlLog> checkVisitRecords(VisitRecords visitRecords) {
        List<QualityControlLog> logList = new ArrayList<>();
        Date date = new Date();
        String appId = visitRecords.getAppId();
        String insCode = visitRecords.getInsCode();
        PatientInfo patientInfo = visitRecords.getPatientInfo();
        String idCard = patientInfo.getPatientCardNumber();



        //患者信息
        if (StringUtils.isEmpty(patientInfo.getPatientName())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "patientInfo", "患者基本信息子集", "01", "patientName-患者姓名不允许为空", "patientCardType", idCard, "patientName", date));
        }
        if (StringUtils.isEmpty(patientInfo.getPatientPhone())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "patientInfo", "患者基本信息子集", "01", "patientPhone-患者电话不允许为空", "patientCardType", idCard, "patientPhone", date));
        }

        if (StringUtils.isNotEmpty(patientInfo.getPatientSex()) && null == medicalMetaService.getDict("sex").get(patientInfo.getPatientSex())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "patientInfo", "患者基本信息子集", "11", "patientSex-患者性别需符合GB/T 2261.1字典规范", "patientCardType", idCard, "patientSex", date));
        }
        if (StringUtils.isNotEmpty(patientInfo.getMaritalStatus()) && null == medicalMetaService.getDict("maritalStatus").get(patientInfo.getMaritalStatus())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "patientInfo", "患者基本信息子集", "11", "maritalStatus-婚姻状况代码需符合GB/T 2261.2字典规范", "patientCardType", idCard, "maritalStatus", date));
        }
        if (StringUtils.isNotEmpty(patientInfo.getNation()) && null == medicalMetaService.getDict("nation").get(patientInfo.getNation())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "patientInfo", "患者基本信息子集", "11", "nation-民族需符合GB/T 3304字典规范", "patientCardType", idCard, "nation", date));
        }
        if (StringUtils.isNotEmpty(patientInfo.getNationality()) && null == medicalMetaService.getDict("nationality").get(patientInfo.getNationality())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "patientInfo", "患者基本信息子集", "11", "nationality-国籍需符合GB/T 2659.1字典规范", "patientCardType", idCard, "nationality", date));
        }
        if (StringUtils.isNotEmpty(patientInfo.getEducationCode()) && null == medicalMetaService.getDict("educationCode").get(patientInfo.getEducationCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "patientInfo", "患者基本信息子集", "11", "educationCode-学历代码需符合GB/T 4568字典规范", "patientCardType", idCard, "educationCode", date));
        }
        if (StringUtils.isNotEmpty(patientInfo.getContactsRelationship()) && null == medicalMetaService.getDict("contactsRelationship").get(patientInfo.getContactsRelationship())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "patientInfo", "患者基本信息子集", "11", "contactsRelationship-家庭关系需符合GB/T 4761字典规范", "patientCardType", idCard, "contactsRelationship", date));
        }

        //病历信息
        RecordInfo recordInfo = visitRecords.getRecordInfo();
        String visitNo = recordInfo.getVisitNo();
        if (StringUtils.isEmpty(recordInfo.getDeptCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "deptCode-科室代码不允许为空", "visitNo", visitNo, "deptCode", date));
        }
        if (StringUtils.isEmpty(recordInfo.getDeptName())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "deptName-科室名称不允许为空", "visitNo", visitNo, "deptName", date));
        }
        if (StringUtils.isEmpty(recordInfo.getVisitTime())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "visitTime-就诊时间不允许为空", "visitNo", visitNo, "visitTime", date));
        }
        if (StringUtils.isEmpty(recordInfo.getInsuranceCategoryCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "insuranceCategoryCode-医疗保险类别代码不允许为空", "visitNo", visitNo, "insuranceCategoryCode", date));
        }
        if (StringUtils.isEmpty(recordInfo.getInitialDiagnosisCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "initialDiagnosisCode-初诊标志不允许为空", "visitNo", visitNo, "initialDiagnosisCode", date));
        }
//        if(StringUtils.isEmpty(recordInfo.getTreatmentCategoryCode())){
//            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo","门（急）诊病历信息子集","01","treatmentCategoryCode-治疗类别不允许为空","visitNo",visitNo,"treatmentCategoryCode", date));
//        }
//        if(StringUtils.isEmpty(recordInfo.getClinicalPathwayCode())){
//            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo","门（急）诊病历信息子集","01","clinicalPathwayCode-实施临床路径代码不允许为空","visitNo",visitNo,"clinicalPathwayCode", date));
//        }
//        if(StringUtils.isEmpty(recordInfo.getScientificResearchFlag())){
//            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo","门（急）诊病历信息子集","01","scientificResearchFlag-科研病历标志不允许为空","visitNo",visitNo,"scientificResearchFlag", date));
//        }
        if (StringUtils.isEmpty(recordInfo.getChiefComplaint())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "chiefComplaint-主诉不允许为空", "visitNo", visitNo, "chiefComplaint", date));
        }
        if (StringUtils.isEmpty(recordInfo.getPresentIllness())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "presentIllness-现病史不允许为空", "visitNo", visitNo, "presentIllness", date));
        }
        if (StringUtils.isEmpty(recordInfo.getPastHistory())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "pastHistory-既往史不允许为空", "visitNo", visitNo, "pastHistory", date));
        }
        if (StringUtils.isEmpty(recordInfo.getPersonalHistory())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "personalHistory-个人史不允许为空", "visitNo", visitNo, "personalHistory", date));
        }
//        if(StringUtils.isEmpty(recordInfo.getAllergyHistoryFlag())){
//            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo","门（急）诊病历信息子集","01","allergyHistoryFlag-过敏史标志不允许为空","visitNo",visitNo,"allergyHistoryFlag", date));
//        }

        if (StringUtils.isEmpty(recordInfo.getTongueCondition())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "tongueCondition-舌象不允许为空", "visitNo", visitNo, "tongueCondition", date));
        }
        if (StringUtils.isEmpty(recordInfo.getPulseCondition())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "pulseCondition-脉象不允许为空", "visitNo", visitNo, "pulseCondition", date));
        }
        if (StringUtils.isEmpty(recordInfo.getWestDisCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "westDisCode-西医诊断代码不允许为空", "visitNo", visitNo, "westDisCode", date));
        }
        if (StringUtils.isEmpty(recordInfo.getWestDisName())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "westDisName-西医诊断名称不允许为空", "visitNo", visitNo, "westDisName", date));
        }
        if (StringUtils.isEmpty(recordInfo.getChineseDisCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "chineseDisCode-中医病名代码不允许为空", "visitNo", visitNo, "chineseDisCode", date));
        }
        if (StringUtils.isEmpty(recordInfo.getChineseDisName())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "chineseDisName-中医病名名称不允许为空", "visitNo", visitNo, "chineseDisName", date));
        }
        if (StringUtils.isEmpty(recordInfo.getSymCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "symCode-中医证候代码不允许为空", "visitNo", visitNo, "symCode", date));
        }
        if (StringUtils.isEmpty(recordInfo.getSymName())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "symName-中医证候名称不允许为空", "visitNo", visitNo, "symName", date));
        }
        if (StringUtils.isEmpty(recordInfo.getTcmApproach())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "01", "tcmApproach-中医治则治法不允许为空", "visitNo", visitNo, "tcmApproach", date));
        }
//        if(StringUtils.isEmpty(recordInfo.getDiagnosticFlag())){
//            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo","门（急）诊病历信息子集","01","diagnosticFlag-诊断标识代码不允许为空","visitNo",visitNo,"diagnosticFlag", date));
//        }

        if (StringUtils.isNotEmpty(recordInfo.getInsuranceCategoryCode()) && null == medicalMetaService.getDict("insuranceCategoryCode").get(recordInfo.getInsuranceCategoryCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "11", "insuranceCategoryCode-医疗保险类别代码需符合CV02.01.204字典规范", "visitNo", visitNo, "insuranceCategoryCode", date));
        }
        if (StringUtils.isNotEmpty(recordInfo.getMedicalExpensesSettledCode()) && null == medicalMetaService.getDict("medicalExpensesSettledCode").get(recordInfo.getMedicalExpensesSettledCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "11", "medicalExpensesSettledCode-医疗费用结算方式代码需符合CV07.10.004字典规范", "visitNo", visitNo, "medicalExpensesSettledCode", date));
        }
        if (StringUtils.isNotEmpty(recordInfo.getOnsetSolarTermCode()) && null == medicalMetaService.getDict("onsetSolarTermCode").get(recordInfo.getOnsetSolarTermCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "11", "onsetSolarTermCode-发病节气需符合CV02.01.A11字典规范", "visitNo", visitNo, "onsetSolarTermCode", date));
        }
        if (StringUtils.isNotEmpty(recordInfo.getTreatmentCategoryCode()) && null == medicalMetaService.getDict("treatmentCategoryCode").get(recordInfo.getTreatmentCategoryCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "11", "treatmentCategoryCode-治疗类别需符合CV06.00.225字典规范", "visitNo", visitNo, "treatmentCategoryCode", date));
        }
        if (StringUtils.isNotEmpty(recordInfo.getEtiologyClassificationCode()) && null == medicalMetaService.getDict("etiologyClassification").get(recordInfo.getEtiologyClassificationCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "11", "etiologyClassificationCode-中医病因分类代码需符合CV05.01.538字典规范", "visitNo", visitNo, "etiologyClassificationCode", date));
        }
        if (StringUtils.isNotEmpty(recordInfo.getPhysicalAffectingFactorsCode()) && null == medicalMetaService.getDict("physicalAffectingFactors").get(recordInfo.getPhysicalAffectingFactorsCode())) {
            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordInfo", "门（急）诊病历信息子集", "11", "physicalAffectingFactorsCode-体质影响因素代码需符合CV05.01.543字典规范", "visitNo", visitNo, "physicalAffectingFactorsCode", date));
        }

        // 中药处方
        List<ChinesePrescriptions> chinesePrescriptions = visitRecords.getChinesePrescriptions();
        if (chinesePrescriptions != null && chinesePrescriptions.size() > 0) {
            for (ChinesePrescriptions chinesePrescription : chinesePrescriptions) {
                String prescriptionNo = chinesePrescription.getPrescriptionNo();
                if (StringUtils.isEmpty(chinesePrescription.getPrescriptionNo())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "prescriptionNo-处方编号不允许为空", "prescriptionNo", prescriptionNo, "prescriptionNo", date));
                }
                if (StringUtils.isEmpty(chinesePrescription.getPrescriptionCategory())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "prescriptionCategory-处方类别不允许为空", "prescriptionNo", prescriptionNo, "prescriptionCategory", date));
                }
                if (StringUtils.isEmpty(chinesePrescription.getPrescriptionTime()+"")) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "prescriptionTime-处方开立日期不允许为空", "prescriptionNo", prescriptionNo, "prescriptionTime", date));
                }
                if (StringUtils.isEmpty(chinesePrescription.getPrescriptionNum())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "prescriptionNum-中药剂数不允许为空", "prescriptionNo", prescriptionNo, "prescriptionNum", date));
                }
                if (StringUtils.isEmpty(chinesePrescription.getPrescribingDoctorSign())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "prescribingDoctorSign-处方开立医师签名不允许为空", "prescriptionNo", prescriptionNo, "prescribingDoctorSign", date));
                }
                if (StringUtils.isEmpty(chinesePrescription.getDecoctingFlag())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "decoctingFlag-中药代煎标志不允许为空", "prescriptionNo", prescriptionNo, "decoctingFlag", date));
                }

                //中药明细
                List<ChinesePrescriptionsItem> chinesePrescriptionsItemList = chinesePrescription.getChinesePrescriptionDrugs();
                if (null == chinesePrescriptionsItemList && chinesePrescriptionsItemList.size() > 0 ) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "chinesePrescriptionDrugs-中药处方药品明细不允许为空", "prescriptionNo", prescriptionNo, "chinesePrescriptionDrugs", date));
                }else {
                    for (ChinesePrescriptionsItem chinesePrescriptionItem : chinesePrescriptionsItemList) {
                        if (StringUtils.isEmpty(chinesePrescriptionItem.getDrugName())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "drugName-中药名称不允许为空", "prescriptionNo", prescriptionNo, "drugName", date));
                        }
                        if (StringUtils.isEmpty(chinesePrescriptionItem.getDrugDosage())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "drugDosage-中药剂型不允许为空", "prescriptionNo", prescriptionNo, "drugDosage", date));
                        }
                        if (StringUtils.isEmpty(chinesePrescriptionItem.getDrugDosage())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "drugDose-中药使用次剂量不允许为空", "prescriptionNo", prescriptionNo, "drugDose", date));
                        }
                        if (StringUtils.isEmpty(chinesePrescriptionItem.getDrugDoseUnit())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "01", "drugDoseUnit-中药使用剂量单位不允许为空", "prescriptionNo", prescriptionNo, "drugDoseUnit", date));
                        }

                        if (StringUtils.isNotEmpty(chinesePrescriptionItem.getDrugFrequencyCode()) && null == medicalMetaService.getDict("drugFrequency").get(chinesePrescriptionItem.getDrugFrequencyCode())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "11", "drugFrequencyCode-频次代码需符合CV06.00.228字典规范", "prescriptionNo", prescriptionNo, "drugFrequencyCode", date));
                        }
                        if (StringUtils.isNotEmpty(chinesePrescriptionItem.getAdministrationRouteCode()) && null == medicalMetaService.getDict("administrationRoute").get(chinesePrescriptionItem.getAdministrationRouteCode())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "11", "administrationRouteCode-用药途径代码需符合CV06.00.102字典规范", "prescriptionNo", prescriptionNo, "administrationRouteCode", date));
                        }
                        if (StringUtils.isNotEmpty(chinesePrescriptionItem.getDrugDecoctingMethod()) && null == medicalMetaService.getDict("drugDecoctingMethod").get(chinesePrescriptionItem.getDrugDecoctingMethod())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "chinesePrescription", "中药处方子集", "11", "drugDecoctingMethod-中药特殊煎煮方法需符合CV08.50.E31字典规范", "prescriptionNo", prescriptionNo, "drugDecoctingMethod", date));
                        }
                    }

                }
            }
        }

        // 西药处方
        List<WestPrescriptions> westPrescriptions = visitRecords.getWestPrescriptions();
        if (westPrescriptions != null && westPrescriptions.size() > 0) {
            for (WestPrescriptions westPrescription : westPrescriptions) {
                String prescriptionNo = westPrescription.getPrescriptionNo();
                if (StringUtils.isEmpty(westPrescription.getPrescriptionNo())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "westPrescription", "中成药/西药处方子集", "01", "prescriptionNo-处方编号不允许为空", "prescriptionNo", prescriptionNo, "prescriptionNo", date));
                }
                if (StringUtils.isEmpty(westPrescription.getPrescriptionTime()+"")) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "westPrescription", "中成药/西药处方子集", "01", "prescriptionTime-处方开立日期不允许为空", "prescriptionNo", prescriptionNo, "prescriptionTime", date));
                }
                if (StringUtils.isEmpty(westPrescription.getPrescribingDoctorSign())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "westPrescription", "中成药/西药处方子集", "01", "prescribingDoctorSign-处方开立医师签名不允许为空", "prescriptionNo", prescriptionNo, "prescribingDoctorSign", date));
                }

                //西药明细
                List<WestPrescriptionsItem> westPrescriptionsItemList = westPrescription.getWestPrescriptionDrugs();
                if (null == westPrescriptionsItemList && westPrescriptionsItemList.size() > 0 ) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "westPrescription", "中成药/西药处方子集", "01", "westPrescriptionDrugs-西药处方药品明细不允许为空", "prescriptionNo", prescriptionNo, "westPrescriptionDrugs", date));
                }else {
                    for (WestPrescriptionsItem westPrescriptionsItem : westPrescriptionsItemList) {
                        if (StringUtils.isEmpty(westPrescriptionsItem.getDrugName())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "westPrescription", "中成药/西药处方子集", "01", "drugName-药物名称不允许为空", "prescriptionNo", prescriptionNo, "drugName", date));
                        }
                        if (StringUtils.isEmpty(westPrescriptionsItem.getDrugSpecifications())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "westPrescription", "中成药/西药处方子集", "01", "drugSpecifications-药物规格不允许为空", "prescriptionNo", prescriptionNo, "drugSpecifications", date));
                        }
                        if (StringUtils.isEmpty(westPrescriptionsItem.getDrugDose())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "westPrescription", "中成药/西药处方子集", "01", "drugDose-药物使用次剂量不允许为空", "prescriptionNo", prescriptionNo, "drugDose", date));
                        }
                        if (StringUtils.isEmpty(westPrescriptionsItem.getDrugDoseUnit())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "westPrescription", "中成药/西药处方子集", "01", "drugDoseUnit-药物使用剂量单位不允许为空", "prescriptionNo", prescriptionNo, "drugDoseUnit", date));
                        }

                        if (StringUtils.isNotEmpty(westPrescriptionsItem.getDrugFrequencyCode()) && null == medicalMetaService.getDict("drugFrequency").get(westPrescriptionsItem.getDrugFrequencyCode())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "westPrescription", "中成药/西药处方子集", "11", "drugFrequencyCode-频次代码需符合CV06.00.228字典规范", "prescriptionNo", prescriptionNo, "drugFrequencyCode", date));
                        }
                        if (StringUtils.isNotEmpty(westPrescriptionsItem.getAdministrationRouteCode()) && null == medicalMetaService.getDict("administrationRoute").get(westPrescriptionsItem.getAdministrationRouteCode())) {
                            logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "westPrescription", "中成药/西药处方子集", "11", "administrationRouteCode-用药途径代码需符合CV06.00.102字典规范", "prescriptionNo", prescriptionNo, "administrationRouteCode", date));
                        }
                    }

                }
            }
        }

        //一般处置记录
        List<GeneralTreatments> generalTreatments = visitRecords.getGeneralTreatments();
        if(null != generalTreatments && generalTreatments.size() > 0){
            for(GeneralTreatments generalTreatment : generalTreatments){
                String operationCode = generalTreatment.getOperationCode();
                if (StringUtils.isEmpty(generalTreatment.getOperationCode())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "generalTreatment", "一般治疗处置记录子集", "01", "operationCode-操作编码不允许为空", "operationCode", operationCode, "operationCode", date));
                }
                if (StringUtils.isEmpty(generalTreatment.getOperationName())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "generalTreatment", "一般治疗处置记录子集", "01", "operationName-操作名称不允许为空", "operationCode", operationCode, "operationName", date));
                }
                if (StringUtils.isEmpty(generalTreatment.getOperationTime()+"")) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "generalTreatment", "一般治疗处置记录子集", "01", "operationTime-操作日期时间不允许为空", "operationCode", operationCode, "operationTime", date));
                }
            }
        }

        //诊疗费用
        RecordCost recordCost = visitRecords.getRecordCost();
        if(null != recordCost){
            if (StringUtils.isEmpty(recordCost.getTotalCost())) {
                logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordCost", "诊疗费用子集", "01", "totalCost-总费用不允许为空", "patientCardType", idCard, "totalCost", date));
            }
        }

        //检查
        List<MedicalRecordsExam> medicalRecordsExams =  visitRecords.getMedicalRecordsExam();
        if(null != medicalRecordsExams && medicalRecordsExams.size() > 0){
            for (MedicalRecordsExam medicalRecordsExam :medicalRecordsExams){
                String applyNo = medicalRecordsExam.getApplyNo();
                if (StringUtils.isEmpty(medicalRecordsExam.getApplyNo())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordExam", "检查子集", "01", "applyNo-电子申请单编号不允许为空", "电子申请单编号", applyNo, "applyNo", date));
                }
                if (StringUtils.isEmpty(medicalRecordsExam.getApplyDeptName())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordExam", "检查子集", "01", "applyDeptName-检查申请科室不允许为空", "电子申请单编号", applyNo, "applyDeptName", date));
                }
                if (StringUtils.isEmpty(medicalRecordsExam.getApplyDoctorName())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordExam", "检查子集", "01", "applyDoctorName-检查申请医生签名不允许为空", "电子申请单编号", applyNo, "applyDoctorName", date));
                }
                if (StringUtils.isEmpty(medicalRecordsExam.getExamTime()+"")) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordExam", "检查子集", "01", "examTime-检查日期时间不允许为空", "电子申请单编号", applyNo, "examTime", date));
                }
            }
        }

        //检验
        List<MedicalRecordsLab> medicalRecordsLabs =  visitRecords.getMedicalRecordsLab();
        if(null != medicalRecordsLabs && medicalRecordsLabs.size() > 0){
            for (MedicalRecordsLab medicalRecordsLab :medicalRecordsLabs){
                String applyNo = medicalRecordsLab.getApplyNo();
                if (StringUtils.isEmpty(medicalRecordsLab.getApplyNo())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordLab", "检验子集", "01", "applyNo-电子申请单编号不允许为空", "电子申请单编号", applyNo, "applyNo", date));
                }
                if (StringUtils.isEmpty(medicalRecordsLab.getApplyDeptName())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordExam", "检查子集", "01", "applyDeptName-检验申请科室不允许为空", "电子申请单编号", applyNo, "applyDeptName", date));
                }
                if (StringUtils.isEmpty(medicalRecordsLab.getApplyDoctorName())) {
                    logList.add(new QualityControlLog(IdUtil.getSnowflake(snowflake).nextIdStr(), appId, insCode, idCard, "recordExam", "检查子集", "01", "applyDoctorName-检验申请医生签名不允许为空", "电子申请单编号", applyNo, "applyDoctorName", date));
                }
            }
        }

        return logList;
    }


//    public Object getPreDiagnosis(GetPreDiagnosis getPreDiagnosis) {
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        //PageHelper.startPage(getPreDiagnosis.getPage(), getPreDiagnosis.getLimit());
//        GetPageListByObjNew2 getPageListByObjNew2 = new GetPageListByObjNew2();
//        BeanUtils.copyProperties(getPreDiagnosis, getPageListByObjNew2);
//        //getPageListByObjNew2.setStartDate(simpleDateFormat.format(DateUtil.getNextDate(-1)));
//        // getPageListByObjNew2.setEndDate(simpleDateFormat.format(DateUtil.getNextDate(0)));
//        List<TRecordHis> list = tRecordMapper.getPageListByObjNew2(getPageListByObjNew2);
//        //PageHelper.clearPage();
//        if (list.size() > 0) {
//            TRecordHis tRecordHis = list.get(0);
//            ResEntity prePaper = webTRecordDiaService.getPrePaper(tRecordHis.getRecId());
//
//            tRecordHis.setCreateDateFormat(simpleDateFormat.format(tRecordHis.getCreateDate()));
//            if (prePaper.getStatus()) {
//                if (null != prePaper.getData()) {
//                    List<QuestionMain> data = (List<QuestionMain>) prePaper.getData();
//                    for (int i = 0; i < data.size(); i++) {
//                        QuestionMain questionMain = data.get(i);
//                        String dateUnit = questionMain.getDateUnit();
//                        if (StringUtils.isNotBlank(dateUnit)){
//                            //        //时间回答选项id
//                            StringBuilder s = new StringBuilder();
//                            if (StringUtils.isNotBlank(questionMain.getYear()) && Integer.parseInt(questionMain.getYear()) != 0){
//                                s.append(Integer.parseInt(questionMain.getYear())).append("年");
//                            }
//                            if (StringUtils.isNotBlank(questionMain.getMonth()) && Integer.parseInt(questionMain.getMonth()) != 0){
//                                s.append( Integer.parseInt(questionMain.getMonth())).append("月");
//                            }
//                            if (StringUtils.isNotBlank(questionMain.getDay()) && Integer.parseInt(questionMain.getDay()) != 0) {
//                                s.append( Integer.parseInt(questionMain.getDay())).append("日");
//                            }
//                            if (StringUtils.isNotBlank(questionMain.getHour()) && Integer.parseInt(questionMain.getHour()) != 0) {
//                                s.append( Integer.parseInt(questionMain.getHour())).append("时");
//                            }
//                            questionMain.setAnswerContent(new String[]{s.toString()});
//                        }
//                        if ("6".equals(questionMain.getQuestionType())){
//                            StringBuilder s = new StringBuilder();
//                            s.append(questionMain.getYear()).append("年").append(questionMain.getMonth()).append("月").append(questionMain.getDay()).append("日");
//                            questionMain.setAnswerContent(new String[]{s.toString()});
//                        }
//                        //预诊、随访问题单选，多选配置项新增病历转换术语：
//                        //非必填，如“否”选项，可以配置：否认慢性病史；
//                        //用于专业病历术语回填。如配置了该项内容，结构化回传题目选项内容，优先取该项配置值
//                        if ( "2".equals(questionMain.getQuestionType()) || "3".equals(questionMain.getQuestionType())){
//                            String[] answerContentContentId = questionMain.getAnswerContentId();
//                            String[] answerContent = questionMain.getAnswerContent();
//                            List<QuestionOption> questionOptionList = questionMain.getQuestionOptionList();
//
//                            for (int i1 = 0; i1 < answerContentContentId.length; i1++) {
//                                for (int i2 = 0; i2 < questionOptionList.size(); i2++) {
//                                    String optionId = questionOptionList.get(i2).getOptionId();
//                                    String optionStructureValue = questionOptionList.get(i2).getOptionStructureValue();
//                                    if (optionId.equals(answerContentContentId[i1]) && StringUtils.isNotBlank(optionStructureValue) ){
//                                        answerContent[i1] = optionStructureValue;
//                                    }
//                                }
//                            }
//                        }
//
//
//
//                    }
//                    tRecordHis.setQuestionList(data);
//                }
//            }
//
//            try {
//                JAXBContext context = JAXBContext.newInstance(TRecordHis.class);
//                Marshaller marshaller = context.createMarshaller();
//                marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
//                // 将Java对象转换为XML字符串
//                StringWriter writer = new StringWriter();
//                marshaller.marshal(tRecordHis, writer);
//                String xmlString = writer.toString();
//                XiaMenResEntity xiaMenResEntity = xiaMenTemplate.callWsdlService(xmlString,tRecordHis.getPatientName());
//
////                SysLogInterface log = new SysLogInterface();
////                log.setId(IDUtil.getID());
////                log.setPatientName(tRecordHis.getPatientName());
////                log.setCreateTime(new Date());
////                log.setInterfaceName("callWsdlService");
////                log.setInterfaceDesc("推送患者预诊数据至HIS:recId="+tRecordHis.getRecId());
////                log.setInterfaceToken("");
////                log.setInterfaceParams(xmlString);
////                log.setResultStatus(xiaMenResEntity.getSuccess()? "1" : "0");
////                log.setResultMeseage(xiaMenResEntity.getMessage());
////                if (xiaMenResEntity.getData() != null) {
////                    log.setResultData(JSONObject.toJSONString(xiaMenResEntity.getData()));
////                }
////                logAsync.logRecord(log);
//
//                if (xiaMenResEntity.getSuccess()) {
//                    return ResEntity.success(xiaMenResEntity.getData());
//                } else {
//                    return ResEntity.error(xiaMenResEntity.getMessage());
//                }
//
//            } catch (JAXBException e) {
//                e.printStackTrace();
//            }
//        }
//
//
//        //return Page.getLayUiTablePageData(list);
//
//        return ResEntity.error("未找到数据："+ JSON.toJSONString(getPreDiagnosis));
//    }
}
