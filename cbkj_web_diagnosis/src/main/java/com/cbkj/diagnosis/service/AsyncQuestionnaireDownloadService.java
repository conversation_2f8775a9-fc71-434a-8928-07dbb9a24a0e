package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreRe;
import com.cbkj.diagnosis.common.utils.PerformanceMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 异步问卷下载服务
 * 实现真正的异步处理，提升用户体验
 * 
 * <AUTHOR>
 * @date 2024/12/23
 * @version 2.0 - 异步优化版本
 */
@Service
@Slf4j
public class AsyncQuestionnaireDownloadService {
    
    @Autowired
    private CompletedQuestionnairePreDiagnosisService questionnaireService;
    
    @Autowired
    @Qualifier("excelGenerationExecutor")
    private Executor excelGenerationExecutor;
    
    /**
     * 异步下载问卷数据
     * 立即返回任务ID，后台处理完成后通知用户
     */
    @Async("excelGenerationExecutor")
    public CompletableFuture<String> downloadQuestionnaireAsync(
            CompletedQuestionnairePreRe request, 
            String taskId) {
        
        long startTime = PerformanceMonitor.startTimer("async_download_questionnaire");
        
        try {
            log.info("开始异步处理问卷下载任务: {}", taskId);
            
            // 更新任务状态为处理中
            updateTaskStatus(taskId, "PROCESSING", "正在生成Excel文件...");
            
            // 执行实际的下载逻辑
            // 注意：这里需要修改原方法以支持异步处理
            processQuestionnaireDownload(request, taskId);
            
            // 更新任务状态为完成
            updateTaskStatus(taskId, "COMPLETED", "文件生成完成，可以下载");
            
            log.info("异步问卷下载任务完成: {}", taskId);
            return CompletableFuture.completedFuture(taskId);
            
        } catch (Exception e) {
            log.error("异步问卷下载任务失败: {}", taskId, e);
            updateTaskStatus(taskId, "FAILED", "文件生成失败: " + e.getMessage());
            return CompletableFuture.failedFuture(e);
        } finally {
            PerformanceMonitor.endTimer("async_download_questionnaire", startTime);
        }
    }
    
    /**
     * 处理问卷下载的核心逻辑
     */
    private void processQuestionnaireDownload(CompletedQuestionnairePreRe request, String taskId) {
        try {
            // 这里需要修改原有的下载方法，使其支持文件存储而不是直接响应
            // 可以将文件保存到临时目录，然后提供下载链接
            
            // 生成临时文件路径
            String tempFilePath = generateTempFilePath(taskId);
            
            // 调用优化后的下载方法（需要修改为支持文件输出）
            // questionnaireService.downloadQuestionnaireToFile(request, tempFilePath);
            
            // 记录文件信息到数据库或缓存
            recordDownloadFile(taskId, tempFilePath);
            
        } catch (Exception e) {
            log.error("处理问卷下载失败", e);
            throw new RuntimeException("处理问卷下载失败", e);
        }
    }
    
    /**
     * 更新任务状态
     */
    private void updateTaskStatus(String taskId, String status, String message) {
        try {
            // 这里可以更新数据库中的任务状态
            // 或者使用Redis等缓存存储任务状态
            log.info("任务状态更新 - ID: {}, 状态: {}, 消息: {}", taskId, status, message);
            
            // 可以通过WebSocket或SSE推送状态更新给前端
            // notificationService.sendTaskUpdate(taskId, status, message);
            
        } catch (Exception e) {
            log.error("更新任务状态失败", e);
        }
    }
    
    /**
     * 生成临时文件路径
     */
    private String generateTempFilePath(String taskId) {
        String tempDir = System.getProperty("java.io.tmpdir");
        String fileName = "questionnaire_" + taskId + "_" + System.currentTimeMillis() + ".zip";
        return tempDir + "/" + fileName;
    }
    
    /**
     * 记录下载文件信息
     */
    private void recordDownloadFile(String taskId, String filePath) {
        try {
            // 记录文件信息到数据库或缓存
            // 包括文件路径、生成时间、过期时间等
            log.info("记录下载文件 - 任务ID: {}, 文件路径: {}", taskId, filePath);
            
        } catch (Exception e) {
            log.error("记录下载文件信息失败", e);
        }
    }
    
    /**
     * 获取任务状态
     */
    public TaskStatus getTaskStatus(String taskId) {
        try {
            // 从数据库或缓存中获取任务状态
            // 这里返回模拟数据
            return new TaskStatus(taskId, "PROCESSING", "正在处理中...", 50);
            
        } catch (Exception e) {
            log.error("获取任务状态失败", e);
            return new TaskStatus(taskId, "FAILED", "获取状态失败", 0);
        }
    }
    
    /**
     * 任务状态封装类
     */
    public static class TaskStatus {
        private String taskId;
        private String status;
        private String message;
        private int progress;
        
        public TaskStatus(String taskId, String status, String message, int progress) {
            this.taskId = taskId;
            this.status = status;
            this.message = message;
            this.progress = progress;
        }
        
        // Getters and Setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public int getProgress() { return progress; }
        public void setProgress(int progress) { this.progress = progress; }
    }
}
