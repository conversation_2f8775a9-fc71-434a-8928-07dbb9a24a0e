package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.common.utils.ZipUtils;
import com.cbkj.diagnosis.common.utils.excel.ExportExcel;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.mapper.business.TRecordDiaMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/20 09:31
 * @Version 1.0
 */
@Service
public class CompletedQuestionnairePreDiagnosisService {

    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;
    private final TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private final TRecordDiaMapper tRecordDiaMapper;
    public CompletedQuestionnairePreDiagnosisService(SRoadTaskPatientsMapper sRoadTaskPatientsMapper, TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper, TRecordDiaMapper tRecordDiaMapper) {
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.tRecordDiaMapper = tRecordDiaMapper;
    }

    public Object getList(CompletedQuestionnairePreRe completedQuestionnaireRe, Page page) {
        if (StringUtils.isNotBlank(completedQuestionnaireRe.getDisIds())){
            completedQuestionnaireRe.setDisIdsList(completedQuestionnaireRe.getDisIds().split(","));
        }
        if (null != page) {
            PageHelper.startPage(page.getPage(), page.getLimit());
        }

        List<CompletedQuestionnairePreDiagnosisListRes> excelCompletedQuestionnaireList = getExcelCompletedQuestionnaireList(completedQuestionnaireRe);
        if (null != page) {
            PageHelper.clearPage();
        }
        //判断excelCompletedQuestionnaireList中的chineseDisName字段空的话就赋值 预问诊记录
        excelCompletedQuestionnaireList.forEach(item->{
            if (StringUtils.isBlank(item.getChineseDisName())){
                item.setChineseDisName("预问诊记录");
            }else {
                //去掉字符串中最后一个斜杠符号 /
                item.setChineseDisName(item.getChineseDisName().substring(0,item.getChineseDisName().length()-1));
            }
        });
        return Page.getResEntityPageData(excelCompletedQuestionnaireList);
    }

    public void downloadQuestionnaire(CompletedQuestionnairePreRe completedQuestionnaireRe, HttpServletResponse httpServletResponse) {
        if (StringUtils.isNotBlank(completedQuestionnaireRe.getDisIds())){
            completedQuestionnaireRe.setDisIdsList(completedQuestionnaireRe.getDisIds().split(","));
        }
        completedQuestionnaireRe.setDiaId(null);
        CompletedQuestionnairePreRe completedQuestionnaireCoreRe = new CompletedQuestionnairePreRe();
        BeanUtils.copyProperties(completedQuestionnaireRe,completedQuestionnaireCoreRe);
        if ( StringUtils.isNotBlank(completedQuestionnaireCoreRe.getStartDate())){
            completedQuestionnaireCoreRe.setStartDate(  completedQuestionnaireCoreRe.getStartDate().substring(0,10)+" 00:00:00"  );
            if ( StringUtils.isNotBlank(completedQuestionnaireCoreRe.getEndDate())){
                completedQuestionnaireCoreRe.setEndDate(  completedQuestionnaireCoreRe.getEndDate().substring(0,10)+" 23:59:59"  );
            }
        }



        //按照diaId分类进行导出
        List<CompletedQuestionnairePreCount> countList = sRoadTaskPatientsMapper.getCountQuestionnaireList(completedQuestionnaireCoreRe);
        //存储每一种问卷也就是按照diaId的excel文档字节码数组
        ArrayList<byte[]> bytes = new ArrayList<byte[]>();
        //每个excel文档的名称
        StringBuilder fNames= new StringBuilder();
        //设置excel每页的最大数据，否则就增加当前excel的shell
        int maxCount = 1000;
        HashMap<String, List<TPreDiagnosisQuestion>> stringListHashMap = new HashMap<>();
        for (int i = 0; i < countList.size(); i++) {
            SXSSFWorkbook workbook = new SXSSFWorkbook(100);
            CompletedQuestionnairePreCount completedQuestionnairePreCount = countList.get(i);
            Integer totalCount = completedQuestionnairePreCount.getNum();
            String diaId = completedQuestionnairePreCount.getDiaId();
            fNames.append(completedQuestionnairePreCount.getChineseDisName()).append("-").append(diaId).append(",");
            LinkedHashMap<String, String> headers = new LinkedHashMap<>();
            headers.put("id", "序号");
            headers.put("finishTime", "预诊填写时间");
            headers.put("patientName","姓名");
            headers.put("patientSex","性别");
            headers.put("patientAge", "年龄");
            headers.put("patientCardNum", "身份证号");
            headers.put("chineseDisName", "预诊内容");
            ExportExcel<Object> exportExcel = new ExportExcel<>();
            if (totalCount > maxCount) {
                int number = (totalCount % maxCount) == 0 ? totalCount
                        / maxCount : totalCount / maxCount + 1;
                for (int i2 = 0; i2 < number; i2++) {
                    PageHelper.startPage(i2+1, maxCount);
                    //获取这个问卷的完整的问题
                    List<TPreDiagnosisQuestion> tPreDiagnosisQuestionList= null;
                    if (!stringListHashMap.containsKey(diaId)){
                        tPreDiagnosisQuestionList = tPreDiagnosisQuestionMapper.getAllQuestionListByDiaId(diaId);
                        stringListHashMap.put(diaId,tPreDiagnosisQuestionList);
                    }else {
                        tPreDiagnosisQuestionList = stringListHashMap.get(diaId);
                    }

                    //取当前问卷id的所有患者信息
                    completedQuestionnaireCoreRe.setDiaId(diaId);
                    List<CompletedQuestionnaireListExcel> list = sRoadTaskPatientsMapper.getExcelCompletedPreQuestionnaireList(completedQuestionnaireCoreRe);
                    //把患者的这个问卷的所有问题的答题找出来。
//                    for (CompletedQuestionnaireListExcel completedQuestionnaireListExcel : list) {
//                        completedQuestionnaireListExcel.setTRecordDiasList(tRecordDiaMapper.getObjectByRecId(completedQuestionnaireListExcel.getRecId()));
//                    }
                    // 生成一个表格
                    SXSSFSheet sheet = workbook.createSheet();
                    exportExcel.packDataCell(sheet, 0, headers, list, tPreDiagnosisQuestionList);
                    try {
                        //每创建完成一个sheet页就把数据刷新到磁盘
                        sheet.flushRows();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    PageHelper.clearPage();
                }
                try {
                    byte[] excelBytes = convertToByteArray(workbook);
                    //  excelBytes 中包含了 SXSSFWorkbook 对象的内容
                    // 将 excelBytes 保存到文件、发送给客户端等
                    bytes.add(excelBytes);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }finally {
                    //需要关闭 workbook
                    workbook.dispose();
                }
            }else {
                completedQuestionnaireCoreRe.setDiaId(diaId);
                List<CompletedQuestionnaireListExcel> list = sRoadTaskPatientsMapper.getExcelCompletedPreQuestionnaireList(completedQuestionnaireCoreRe);
                //把患者的这个问卷的所有问题的答题找出来。
//                for (CompletedQuestionnaireListExcel completedQuestionnaireListExcel : list) {
//                    completedQuestionnaireListExcel.setTRecordDiasList(tRecordDiaMapper.getObjectByRecId(completedQuestionnaireListExcel.getRecId()));
//                }
                //获取这个问卷的完整的问题
                List<TPreDiagnosisQuestion> tPreDiagnosisQuestionList = tPreDiagnosisQuestionMapper.getAllQuestionListByDiaId(diaId);
                // 生成一个表格
                SXSSFSheet sheet = workbook.createSheet();
                exportExcel.packDataCell(sheet, 0, headers, list, tPreDiagnosisQuestionList);
                try {
                    byte[] excelBytes = convertToByteArray(workbook);
                    //  excelBytes 中包含了 SXSSFWorkbook 对象的内容
                    // 将 excelBytes 保存到文件、发送给客户端等
                    bytes.add(excelBytes);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }finally {
                    //需要关闭 workbook
                    workbook.dispose();
                }
            }
        }
        //打包输出。
        ZipUtils.exposeZip("预诊单原始数据",httpServletResponse,bytes, fNames.toString().split(","));

    }
    public static byte[] convertToByteArray(SXSSFWorkbook workbook) throws IOException {

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            workbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        }
    }

    public List<CompletedQuestionnairePreDiagnosisListRes> getExcelCompletedQuestionnaireList(CompletedQuestionnairePreRe completedQuestionnaireRe) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        completedQuestionnaireRe.setUserId(currentHr.getUserId());
        List<AdminRule> roles = currentHr.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            completedQuestionnaireRe.setUserId(null);
        }

        if ( StringUtils.isNotBlank(completedQuestionnaireRe.getStartDate())){
            completedQuestionnaireRe.setStartDate(  completedQuestionnaireRe.getStartDate().substring(0,10)+" 00:00:00"  );
        }
        if ( StringUtils.isNotBlank(completedQuestionnaireRe.getEndDate())){
            completedQuestionnaireRe.setEndDate(  completedQuestionnaireRe.getEndDate().substring(0,10)+" 23:59:59"  );
        }
        return sRoadTaskPatientsMapper.getCompletedQuestionnairePreDiagnosisList(completedQuestionnaireRe);
    }
}
