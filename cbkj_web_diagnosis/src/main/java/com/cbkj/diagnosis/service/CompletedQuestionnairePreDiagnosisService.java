package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.common.utils.*;
import com.cbkj.diagnosis.common.utils.excel.ExportExcel;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.mapper.business.TRecordDiaMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.github.pagehelper.PageHelper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @Description 预诊问卷下载服务 - 性能优化版本
 * @Date 2024/12/20 09:31
 * @Version 2.0 - 性能优化版本
 */
@Service
@Log4j2
public class CompletedQuestionnairePreDiagnosisService {

    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;
    private final TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private final TRecordDiaMapper tRecordDiaMapper;
private final PreDiagnosisListCacheService preDiagnosisListCacheService;
    // 问卷问题缓存，避免重复查询
    private final Map<String, List<TPreDiagnosisQuestion>> questionCache = new ConcurrentHashMap<>();

    // 线程池，用于并行处理
    private final ExecutorService executorService = Executors.newFixedThreadPool(
        Math.min(Runtime.getRuntime().availableProcessors(), 8)
    );

    public CompletedQuestionnairePreDiagnosisService(SRoadTaskPatientsMapper sRoadTaskPatientsMapper,
                                                     TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper,
                                                     TRecordDiaMapper tRecordDiaMapper, PreDiagnosisListCacheService preDiagnosisListCacheService) {
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.preDiagnosisListCacheService = preDiagnosisListCacheService;
    }

    public Object getList(CompletedQuestionnairePreRe completedQuestionnaireRe, Page page) {
        if (StringUtils.isNotBlank(completedQuestionnaireRe.getDisIds())){
            completedQuestionnaireRe.setDisIdsList(completedQuestionnaireRe.getDisIds().split(","));
        }
        if (null != page) {
            PageHelper.startPage(page.getPage(), page.getLimit());
        }

        List<CompletedQuestionnairePreDiagnosisListRes> excelCompletedQuestionnaireList = getExcelCompletedQuestionnaireList(completedQuestionnaireRe);
        if (null != page) {
            PageHelper.clearPage();
        }
        //判断excelCompletedQuestionnaireList中的chineseDisName字段空的话就赋值 预问诊记录
        excelCompletedQuestionnaireList.forEach(item->{
            if (StringUtils.isBlank(item.getChineseDisName())){
                item.setChineseDisName("预问诊记录");
            }else {
                //去掉字符串中最后一个斜杠符号 /
                item.setChineseDisName(item.getChineseDisName().substring(0,item.getChineseDisName().length()-1));
            }
        });
        return Page.getResEntityPageData(excelCompletedQuestionnaireList);
    }



    /**
     * 批量预加载问卷问题数据 - 极致优化版本
     */
    private void preloadQuestionData(List<CompletedQuestionnairePreCount> countList) {
        long startTime = PerformanceMonitor.startTimer("preload_questions");

        // 获取所有需要查询的diaId
        Set<String> diaIds = countList.stream()
            .map(CompletedQuestionnairePreCount::getDiaId)
            .collect(Collectors.toSet());

        // 过滤掉已缓存的diaId
        Set<String> uncachedDiaIds = diaIds.stream()
            .filter(diaId -> !questionCache.containsKey(diaId))
            .collect(Collectors.toSet());

        if (uncachedDiaIds.isEmpty()) {
            log.info("所有问卷问题数据已缓存，跳过预加载");
            return;
        }

        // 批量查询所有未缓存的问卷问题
        Map<String, List<TPreDiagnosisQuestion>> batchQuestions = batchLoadQuestionsByDiaIds(uncachedDiaIds);

        // 批量更新缓存
        questionCache.putAll(batchQuestions);

        PerformanceMonitor.endTimer("preload_questions", startTime);
        log.info("批量预加载完成，新加载 {} 个问卷的问题数据，缓存总数: {}",
            uncachedDiaIds.size(), questionCache.size());
    }

    /**
     * 批量查询问卷问题数据 - 减少数据库连接次数
     */
    private Map<String, List<TPreDiagnosisQuestion>> batchLoadQuestionsByDiaIds(Set<String> diaIds) {
        long startTime = PerformanceMonitor.startTimer("batch_load_questions");

        try {
            // 分批查询，避免IN子句过长
            List<String> diaIdList = new ArrayList<>(diaIds);
            Map<String, List<TPreDiagnosisQuestion>> result = new ConcurrentHashMap<>();

            int batchSize = 100; // 每批查询100个
            for (int i = 0; i < diaIdList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, diaIdList.size());
                List<String> batchDiaIds = diaIdList.subList(i, endIndex);

                // 批量查询这一批的问题数据
                List<TPreDiagnosisQuestion> batchQuestions = tPreDiagnosisQuestionMapper.getAllQuestionsByDiaIds(batchDiaIds);

                // 按diaId分组
                Map<String, List<TPreDiagnosisQuestion>> groupedQuestions = batchQuestions.stream()
                    .collect(Collectors.groupingBy(TPreDiagnosisQuestion::getDiaId));

                result.putAll(groupedQuestions);
            }

            PerformanceMonitor.recordDatabaseQuery("batch_questions",
                System.currentTimeMillis() - startTime, result.values().stream().mapToInt(List::size).sum());

            return result;
        } catch (Exception e) {
            log.error("批量查询问卷问题失败，降级到单个查询", e);
            // 降级到单个查询
            return diaIds.stream()
                .collect(Collectors.toMap(
                    diaId -> diaId,
                    this::getQuestionsByDiaIdCached
                ));
        }
    }

    private List<TPreDiagnosisQuestion> getQuestionsByDiaIdCached(String s) {
        return preDiagnosisListCacheService.getQuestionsByDiaIdCached(s);
    }

    public void downloadQuestionnaire(CompletedQuestionnairePreRe completedQuestionnaireRe, HttpServletResponse httpServletResponse) {
        long overallStartTime = PerformanceMonitor.startTimer("download_questionnaire_overall");
        PerformanceMonitor.recordMemoryUsage("download_start");

        try {
            // 参数预处理
            if (StringUtils.isNotBlank(completedQuestionnaireRe.getDisIds())){
                completedQuestionnaireRe.setDisIdsList(completedQuestionnaireRe.getDisIds().split(","));
            }
            completedQuestionnaireRe.setDiaId(null);
            CompletedQuestionnairePreRe completedQuestionnaireCoreRe = new CompletedQuestionnairePreRe();
            BeanUtils.copyProperties(completedQuestionnaireRe,completedQuestionnaireCoreRe);
            if ( StringUtils.isNotBlank(completedQuestionnaireCoreRe.getStartDate())){
                completedQuestionnaireCoreRe.setStartDate(  completedQuestionnaireCoreRe.getStartDate().substring(0,10)+" 00:00:00"  );
                if ( StringUtils.isNotBlank(completedQuestionnaireCoreRe.getEndDate())){
                    completedQuestionnaireCoreRe.setEndDate(  completedQuestionnaireCoreRe.getEndDate().substring(0,10)+" 23:59:59"  );
                }
            }

            //按照diaId分类进行导出
            long queryStartTime = PerformanceMonitor.startTimer("db_query_count");
            List<CompletedQuestionnairePreCount> countList = sRoadTaskPatientsMapper.getCountQuestionnaireList(completedQuestionnaireCoreRe);
            PerformanceMonitor.endTimer("db_query_count", queryStartTime);

            if (countList.isEmpty()) {
                log.warn("没有找到符合条件的问卷数据");
                return;
            }

            // 预加载所有问卷的问题数据
            preloadQuestionData(countList);

            // 使用并行处理优化性能
            downloadQuestionnaireOptimized(countList, completedQuestionnaireCoreRe, httpServletResponse);

        } catch (Exception e) {
            log.error("下载问卷失败", e);
            throw new RuntimeException("下载问卷失败", e);
        } finally {
            PerformanceMonitor.endTimer("download_questionnaire_overall", overallStartTime);
            PerformanceMonitor.recordMemoryUsage("download_end");
            PerformanceMonitor.printPerformanceReport();
        }
    }
    /**
     * 优化版本的下载方法，使用并行处理
     */
    private void downloadQuestionnaireOptimized(List<CompletedQuestionnairePreCount> countList,
                                               CompletedQuestionnairePreRe completedQuestionnaireCoreRe,
                                               HttpServletResponse httpServletResponse) {

        long startTime = PerformanceMonitor.startTimer("parallel_excel_generation");

        // 并行生成Excel数据
        List<CompletableFuture<ExcelData>> futures = countList.stream()
            .map(count -> CompletableFuture.supplyAsync(() ->
                generateExcelData(count, completedQuestionnaireCoreRe), executorService))
            .collect(Collectors.toList());

        // 等待所有Excel生成完成并收集结果
        List<ExcelData> excelDataList = futures.stream()
            .map(CompletableFuture::join)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        PerformanceMonitor.endTimer("parallel_excel_generation", startTime);
        PerformanceMonitor.addToCounter("excel_files_generated", excelDataList.size());

        // 流式生成压缩包
        generateZipFileStreaming(excelDataList, httpServletResponse);
    }

    /**
     * Excel数据封装类
     */
    private static class ExcelData {
        private final String fileName;
        private byte[] data;

        public ExcelData(String fileName, byte[] data) {
            this.fileName = fileName;
            this.data = data;
        }

        public String getFileName() { return fileName; }
        public byte[] getData() { return data; }
        public void clearData() { this.data = null; } // 释放内存
    }
    /**
     * 生成单个Excel文件数据 - 极致优化版本
     */
    private ExcelData generateExcelData(CompletedQuestionnairePreCount count,
                                       CompletedQuestionnairePreRe completedQuestionnaireCoreRe) {
        long startTime = PerformanceMonitor.startTimer("single_excel_generation");

        try {
            String diaId = count.getDiaId();
            Integer totalCount = count.getNum();
            String fileName = count.getChineseDisName() + "-" + diaId;

            // 动态调整每页数据量，根据数据总量优化
            int maxCount = calculateOptimalPageSize(totalCount);

            // 创建工作簿，优化内存窗口大小
            SXSSFWorkbook workbook = new SXSSFWorkbook(Math.min(maxCount / 10, 100));

            // 设置表头
            LinkedHashMap<String, String> headers = createOptimizedHeaders();

            // 使用优化的Excel生成器
            OptimizedExcelGenerator excelGenerator = new OptimizedExcelGenerator();
            // 从缓存获取问卷问题数据
            List<TPreDiagnosisQuestion> tPreDiagnosisQuestionList = questionCache.get(diaId);
            if (tPreDiagnosisQuestionList == null) {
                tPreDiagnosisQuestionList = getQuestionsByDiaIdCached(diaId);
            }

            // 设置查询参数
            CompletedQuestionnairePreRe queryParam = new CompletedQuestionnairePreRe();
            BeanUtils.copyProperties(completedQuestionnaireCoreRe, queryParam);
            queryParam.setDiaId(diaId);

            // 根据数据量决定是否分页处理
            if (totalCount > maxCount) {
                int pageCount = (totalCount % maxCount) == 0 ? totalCount / maxCount : totalCount / maxCount + 1;

                for (int page = 0; page < pageCount; page++) {
                    PageHelper.startPage(page + 1, maxCount);
                    try {
                        List<CompletedQuestionnaireListExcel> list = sRoadTaskPatientsMapper.getExcelCompletedPreQuestionnaireList(queryParam);

                        // 生成sheet
                        SXSSFSheet sheet = workbook.createSheet();
                        excelGenerator.batchWriteData(sheet, 0, headers, list, tPreDiagnosisQuestionList);

                        // 及时刷新数据到磁盘，释放内存
                        sheet.flushRows();

                    } finally {
                        PageHelper.clearPage();
                    }
                }
            } else {
                // 小数据量直接处理
                List<CompletedQuestionnaireListExcel> list = sRoadTaskPatientsMapper.getExcelCompletedPreQuestionnaireList(queryParam);

                SXSSFSheet sheet = workbook.createSheet();
                excelGenerator.batchWriteData(sheet, 0, headers, list, tPreDiagnosisQuestionList);
            }

            // 转换为字节数组
            byte[] excelBytes = convertToByteArray(workbook);
            PerformanceMonitor.recordExcelGeneration(diaId, System.currentTimeMillis() - startTime, totalCount);

            return new ExcelData(fileName, excelBytes);

        } catch (Exception e) {
            log.error("生成Excel失败, diaId: {}", count.getDiaId(), e);
            return null;
        } finally {
            // 确保workbook被正确释放
            try {
                // workbook.dispose(); // 这里不能dispose，因为还需要返回数据
            } catch (Exception e) {
                log.warn("释放workbook失败", e);
            }
        }
    }
    /**
     * 流式生成压缩包，优化内存使用
     */
    private void generateZipFileStreaming(List<ExcelData> excelDataList, HttpServletResponse response) {
        long startTime = PerformanceMonitor.startTimer("zip_generation");

        String downloadFilename = "预诊单原始数据" +
            com.cbkj.diagnosis.common.utils.DateUtil.getDateFormats(
                com.cbkj.diagnosis.common.utils.DateUtil.YYYYMMDD, null) + ".zip";

        try {
            // 设置响应头
            String encodedFilename = java.net.URLEncoder.encode(downloadFilename, "UTF-8");
            String contentDisposition = "filename*=utf-8''" +
                java.net.URLEncoder.encode(downloadFilename, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;" + contentDisposition + ";filename=" + encodedFilename);
        } catch (Exception e) {
            log.error("设置下载文件名失败", e);
        }

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        long totalSize = 0;

        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            String dateStr = com.cbkj.diagnosis.common.utils.DateUtil.getDateFormats(
                com.cbkj.diagnosis.common.utils.DateUtil.YYYYMMDD, null);

            for (ExcelData excelData : excelDataList) {
                if (excelData == null || excelData.getData() == null) {
                    continue;
                }

                String safeName = excelData.getFileName().replace("/", "") + dateStr + ".xlsx";
                ZipEntry entry = new ZipEntry(safeName);

                try {
                    zos.putNextEntry(entry);
                    zos.write(excelData.getData());
                    zos.closeEntry();

                    totalSize += excelData.getData().length;

                    // 立即释放内存中的数据
                    excelData.clearData();

                } catch (IOException e) {
                    log.error("写入ZIP条目失败: {}", safeName, e);
                }
            }

            zos.flush();

        } catch (IOException e) {
            log.error("生成ZIP文件失败", e);
            throw new RuntimeException("ZIP导出错误", e);
        }

        PerformanceMonitor.recordZipGeneration(System.currentTimeMillis() - startTime,
            excelDataList.size(), totalSize);
        PerformanceMonitor.endTimer("zip_generation", startTime);

        log.info("压缩包生成完成，包含 {} 个文件，总大小: {} MB",
            excelDataList.size(), totalSize / 1024 / 1024);
    }

    /**
     * 优化的Excel转字节数组方法
     */
    public static byte[] convertToByteArray(SXSSFWorkbook workbook) throws IOException {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            workbook.write(byteArrayOutputStream);
            byte[] result = byteArrayOutputStream.toByteArray();

            // 立即释放workbook资源
            workbook.dispose();

            return result;
        }
    }

    /**
     * 计算最优分页大小
     */
    private int calculateOptimalPageSize(int totalCount) {
        if (totalCount <= 500) return totalCount; // 小数据量不分页
        if (totalCount <= 2000) return 500;       // 中等数据量
        if (totalCount <= 5000) return 1000;      // 大数据量
        return 1500; // 超大数据量
    }

    /**
     * 创建优化的表头
     */
    private LinkedHashMap<String, String> createOptimizedHeaders() {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("id", "序号");
        headers.put("finishTime", "预诊填写时间");
        headers.put("patientName","姓名");
        headers.put("patientSex","性别");
        headers.put("patientAge", "年龄");
        headers.put("patientCardNum", "身份证号");
        headers.put("chineseDisName", "预诊内容");
        return headers;
    }

    /**
     * 优化的Excel生成器内部类
     */
    private static class OptimizedExcelGenerator {

        /**
         * 批量写入数据到Excel，减少单次写入开销
         */
        public void batchWriteData(SXSSFSheet sheet, int startRow,
                                 LinkedHashMap<String, String> headers,
                                 List<CompletedQuestionnaireListExcel> dataList,
                                 List<TPreDiagnosisQuestion> questions) {

            // 使用ExportExcel的优化版本
            ExportExcel<Object> exportExcel = new ExportExcel<>();
            exportExcel.packDataCell(sheet, startRow, headers, dataList, questions);
        }
    }

    /**
     * 资源清理方法
     */
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            log.info("线程池已关闭");
        }
        questionCache.clear();
        log.info("缓存已清理");
    }

    public List<CompletedQuestionnairePreDiagnosisListRes> getExcelCompletedQuestionnaireList(CompletedQuestionnairePreRe completedQuestionnaireRe) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        completedQuestionnaireRe.setUserId(currentHr.getUserId());
        List<AdminRule> roles = currentHr.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            completedQuestionnaireRe.setUserId(null);
        }

        if ( StringUtils.isNotBlank(completedQuestionnaireRe.getStartDate())){
            completedQuestionnaireRe.setStartDate(  completedQuestionnaireRe.getStartDate().substring(0,10)+" 00:00:00"  );
        }
        if ( StringUtils.isNotBlank(completedQuestionnaireRe.getEndDate())){
            completedQuestionnaireRe.setEndDate(  completedQuestionnaireRe.getEndDate().substring(0,10)+" 23:59:59"  );
        }
        return sRoadTaskPatientsMapper.getCompletedQuestionnairePreDiagnosisList(completedQuestionnaireRe);
    }
}
