package com.cbkj.diagnosis.service;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.beans.task.SRoadTask;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.common.utils.PerformanceMonitor;
import com.cbkj.diagnosis.common.utils.ZipUtils;
import com.cbkj.diagnosis.common.utils.excel.ExportExcel;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.mapper.business.TRecordDiaMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.vo.CompletedQuestionnaireRe;
import com.github.pagehelper.PageHelper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
@Log4j2
public class CompletedQuestionnaireService {


    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    private final TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private final TRecordDiaMapper tRecordDiaMapper;

    private final SRoadTaskMapper sRoadTaskMapper;

    public CompletedQuestionnaireService(SRoadTaskPatientsMapper sRoadTaskPatientsMapper, TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper, TRecordDiaMapper tRecordDiaMapper, SRoadTaskMapper sRoadTaskMapper) {
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.sRoadTaskMapper = sRoadTaskMapper;
    }

    public Object getList(CompletedQuestionnaireRe completedQuestionnaireRe, Page page) {
        log.info("completedQuestionnaireRe入参{}", JSON.toJSONString(completedQuestionnaireRe));
        CompletedQuestionnaireCoreRe completedQuestionnaireCoreRe = new CompletedQuestionnaireCoreRe();
        if (StringUtils.isNotBlank(completedQuestionnaireRe.getSroadIdsstr())) {
            completedQuestionnaireCoreRe.setSRoadTaskIdList(completedQuestionnaireRe.getSroadIdsstr().split(","));
        }
        completedQuestionnaireCoreRe.setPage( (page.getPage()-1) * page.getLimit());
        completedQuestionnaireCoreRe.setLimit(page.getLimit());
        //PageHelper.startPage(page.getPage(), page.getLimit());

        BeanUtils.copyProperties(completedQuestionnaireRe, completedQuestionnaireCoreRe);
        if (StringUtils.isNotBlank(completedQuestionnaireCoreRe.getStartDate())) {
            completedQuestionnaireCoreRe.setStartDate(completedQuestionnaireCoreRe.getStartDate().substring(0, 10) + " 00:00:00");
        }
        if (StringUtils.isNotBlank(completedQuestionnaireCoreRe.getEndDate())) {
            completedQuestionnaireCoreRe.setEndDate(completedQuestionnaireCoreRe.getEndDate().substring(0, 10) + " 23:59:59");
        }
//        if (StringUtils.isNotBlank(completedQuestionnaireRe.getSRoadTaskId())) {
//            SRoadTask objectById = sRoadTaskMapper.getObjectById(completedQuestionnaireRe.getSRoadTaskId());
//            if (objectById != null) {
//                completedQuestionnaireCoreRe.setTaskName(objectById.getTaskName());
//            }
//        }
        //不一定能关联到任务 所以删除下main代码。主动发送的没有任务id
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        completedQuestionnaireCoreRe.setCreateUserId(currentHr.getUserId());
        List<AdminRule> roles = currentHr.getRoles();

        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            completedQuestionnaireCoreRe.setCreateUserId(null);
        }

        List<CompletedQuestionnaireListRes> completedQuestionnaireListResList =
                sRoadTaskPatientsMapper.getCompletedQuestionnaireList(completedQuestionnaireCoreRe);
        int totalNum = sRoadTaskPatientsMapper.getCountNumCompletedQuestionnaireList(completedQuestionnaireCoreRe);
        //PageHelper.clearPage();

        return Page.getLayuiData(true, "SUCCESS", totalNum, totalNum > page.getLimit() * page.getPage(), completedQuestionnaireListResList);

//        return Page.getLayUiTablePageData(completedQuestionnaireListResList);
    }


    public void downloadQuestionnaire(CompletedQuestionnaireRe questionnaireRequest, HttpServletResponse response) {
        long overallStartTime = PerformanceMonitor.startTimer("download_questionnaire_overall");
        PerformanceMonitor.recordMemoryUsage("download_start");

        CompletedQuestionnaireCoreRe completedQuestionnaireCoreRe = new CompletedQuestionnaireCoreRe();
        BeanUtils.copyProperties(questionnaireRequest, completedQuestionnaireCoreRe);
        //Excel标题
        //String fileName = "中草药处方明细数据统计";
        if (StringUtils.isNotBlank(completedQuestionnaireCoreRe.getStartDate())) {
            completedQuestionnaireCoreRe.setStartDate(completedQuestionnaireCoreRe.getStartDate().substring(0, 10) + " 00:00:00");
            if (StringUtils.isNotBlank(completedQuestionnaireCoreRe.getEndDate())) {
                completedQuestionnaireCoreRe.setEndDate(completedQuestionnaireCoreRe.getEndDate().substring(0, 10) + " 23:59:59");
            }
        }
        if (StringUtils.isNotBlank(questionnaireRequest.getSroadIdsstr())) {
            SRoadTask objectById = sRoadTaskMapper.getObjectById(questionnaireRequest.getSroadIdsstr());
            if (objectById != null) {
                completedQuestionnaireCoreRe.setTaskName(objectById.getTaskName());
            }
        }
        completedQuestionnaireCoreRe.setCreateUserId(null);
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if (currentHr == null) {
            log.error("下载随访原始问卷：当前鉴权用户过期");
            return;
        }
        completedQuestionnaireCoreRe.setCreateUserId(currentHr.getUserId());
        List<AdminRule> roles = currentHr.getRoles();

        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            completedQuestionnaireCoreRe.setCreateUserId(null);
        }
        List<CompletedQuestionnaireCount> countCompletedQuestionnaireList = sRoadTaskPatientsMapper.getCountCompletedQuestionnaireList(completedQuestionnaireCoreRe);

        // 优化：预先批量查询所有问卷的问题数据，避免在循环中重复查询
        Map<String, List<TPreDiagnosisQuestion>> questionCache = new HashMap<>();
        for (CompletedQuestionnaireCount count : countCompletedQuestionnaireList) {
            String diaId = count.getDiaId();
            if (!questionCache.containsKey(diaId)) {
                List<TPreDiagnosisQuestion> questions = getQuestionListWithCache(diaId);
                questionCache.put(diaId, questions);
            }
        }

        // 使用并行处理优化性能
        downloadQuestionnaireOptimized(countCompletedQuestionnaireList, completedQuestionnaireCoreRe, questionCache, response);

        PerformanceMonitor.endTimer("download_questionnaire_overall", overallStartTime);
        PerformanceMonitor.recordMemoryUsage("download_end");
        PerformanceMonitor.printPerformanceReport();
    }

    /**
     * 优化版本的下载方法，使用异步并行处理
     * @param countCompletedQuestionnaireList 分类列表
     * @param completedQuestionnaireCoreRe      入参查询参数
     * @param questionCache      问卷原始数据
     */
    private void downloadQuestionnaireOptimized(List<CompletedQuestionnaireCount> countCompletedQuestionnaireList,
                                               CompletedQuestionnaireCoreRe completedQuestionnaireCoreRe,
                                               Map<String, List<TPreDiagnosisQuestion>> questionCache,
                                               HttpServletResponse response) {

        // 创建线程池，根据CPU核心数设置线程数
        int threadCount = Math.min(Runtime.getRuntime().availableProcessors(), countCompletedQuestionnaireList.size());
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        try {
            // 创建异步任务列表
            List<CompletableFuture<ExcelData>> futures = new ArrayList<>();

            for (int i = 0; i < countCompletedQuestionnaireList.size(); i++) {
                final int index = i;
                final CompletedQuestionnaireCount completedQuestionnaireCount = countCompletedQuestionnaireList.get(i);

                // 创建异步任务
                CompletableFuture<ExcelData> future = CompletableFuture.supplyAsync(() -> {
                    return generateExcelData(completedQuestionnaireCount, completedQuestionnaireCoreRe, questionCache);
                }, executor);

                futures.add(future);
            }

            // 等待所有任务完成并收集结果
            List<ExcelData> excelDataList = new ArrayList<>();
            for (CompletableFuture<ExcelData> future : futures) {
                try {
                    ExcelData excelData = future.get();
                    if (excelData != null) {
                        excelDataList.add(excelData);
                    }
                } catch (Exception e) {
                    log.error("生成Excel数据时发生错误", e);
                }
            }

            // 生成压缩包
            generateZipFile(excelDataList, response);

        } finally {
            executor.shutdown();
        }
    }

    /**
     * 生成单个Excel数据的方法
     */
    private ExcelData generateExcelData(CompletedQuestionnaireCount completedQuestionnaireCount,
                                       CompletedQuestionnaireCoreRe completedQuestionnaireCoreRe,
                                       Map<String, List<TPreDiagnosisQuestion>> questionCache) {
        try {
            // 声明一个工作薄
            SXSSFWorkbook workbook = new SXSSFWorkbook(100);
            Integer totalCount = completedQuestionnaireCount.getNum();
            String diaId = completedQuestionnaireCount.getDiaId();
            String fileName = completedQuestionnaireCount.getFormName() + "-" + diaId;

            LinkedHashMap<String, String> headers = new LinkedHashMap<>();
            headers.put("id", "序号");
            headers.put("finishTime", "完成时间");
            headers.put("sendTime", "发送时间");
            headers.put("patientName", "姓名");
            headers.put("patientSex", "性别");
            headers.put("patientCardNum", "身份证");
            headers.put("formName", "随访内容");
            headers.put("patientAge", "年龄");
            ExportExcel<Object> exportExcel = new ExportExcel<>();
            int maxCount = 1000;
            // 创建一个新的查询对象，避免线程安全问题
            CompletedQuestionnaireCoreRe queryParam = new CompletedQuestionnaireCoreRe();
            BeanUtils.copyProperties(completedQuestionnaireCoreRe, queryParam);
            queryParam.setDiaId(diaId);

            //如果导出数据量大于设定的最大数据量，则分页处理
            if (totalCount > maxCount) {
                int number = (totalCount % maxCount) == 0 ? totalCount / maxCount : totalCount / maxCount + 1;
                for (int i2 = 0; i2 < number; i2++) {
                    PageHelper.startPage(i2 + 1, maxCount);
                    //取当前问卷id的所有患者信息
                    List<CompletedQuestionnaireListExcel> list = sRoadTaskPatientsMapper.getExcelCompletedQuestionnaireList(queryParam);
                    PageHelper.clearPage();
                    //从缓存中获取这个问卷的完整的问题，避免重复查询
                    List<TPreDiagnosisQuestion> tPreDiagnosisQuestionList = questionCache.get(diaId);

                    // 生成一个表格
                    SXSSFSheet sheet = workbook.createSheet();
                    exportExcel.packDataCell(sheet, 0, headers, list, tPreDiagnosisQuestionList);
                    try {
                        //每创建完成一个sheet页就把数据刷新到磁盘
                        sheet.flushRows();
                    } catch (IOException e) {
                        log.error("刷新sheet数据到磁盘失败", e);
                    }
                }
            } else {
                //取当前问卷id的所有患者信息
                List<CompletedQuestionnaireListExcel> list = sRoadTaskPatientsMapper.getExcelCompletedQuestionnaireList(queryParam);
                //从缓存中获取这个问卷的完整的问题，避免重复查询
                List<TPreDiagnosisQuestion> tPreDiagnosisQuestionList = questionCache.get(diaId);

                // 生成一个表格
                SXSSFSheet sheet = workbook.createSheet();
                exportExcel.packDataCell(sheet, 0, headers, list, tPreDiagnosisQuestionList);
            }

            try {
                byte[] excelBytes = convertToByteArray(workbook);
                return new ExcelData(fileName, excelBytes);
            } catch (IOException e) {
                log.error("转换Excel为字节数组失败", e);
                return null;
            } finally {
                //需要关闭 workbook
                workbook.dispose();
            }

        } catch (Exception e) {
            log.error("生成Excel数据失败", e);
            return null;
        }
    }


    /**
     * 生成压缩包文件 - 优化内存使用版本
     */
    private void generateZipFile(List<ExcelData> excelDataList, HttpServletResponse response) {
        // 使用流式处理，避免在内存中存储所有Excel字节数组
        generateZipFileStreaming(excelDataList, response);
    }

    /**
     * 流式生成压缩包，优化内存使用
     */
    private void generateZipFileStreaming(List<ExcelData> excelDataList, HttpServletResponse response) {
        String downloadFilename = "随访问卷/量表原始数据" + com.cbkj.diagnosis.common.utils.DateUtil.getDateFormats(com.cbkj.diagnosis.common.utils.DateUtil.YYYYMMDD, null) + ".zip";

        try {
            downloadFilename = java.net.URLEncoder.encode(downloadFilename, "UTF-8");
            String downloadFilenameNew = "filename*=utf-8''" + java.net.URLEncoder.encode(downloadFilename, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;" + downloadFilenameNew + ";filename=" + downloadFilename);
        } catch (Exception e) {
            log.error("设置下载文件名失败", e);
        }

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        try (java.util.zip.ZipOutputStream zos = new java.util.zip.ZipOutputStream(response.getOutputStream())) {
            String yyyyMMdd = com.cbkj.diagnosis.common.utils.DateUtil.getDateFormats(com.cbkj.diagnosis.common.utils.DateUtil.YYYYMMDD, null);

            for (ExcelData excelData : excelDataList) {
                String safeName = excelData.getFileName().replace("/", "") + yyyyMMdd + ".xlsx";
                java.util.zip.ZipEntry entry = new java.util.zip.ZipEntry(safeName);

                try {
                    zos.putNextEntry(entry);
                    zos.write(excelData.getData());
                    zos.closeEntry();

                    // 立即释放内存中的数据
                    excelData.clearData();
                } catch (IOException e) {
                    log.error("写入ZIP条目失败: {}", safeName, e);
                }
            }
        } catch (IOException e) {
            log.error("生成ZIP文件失败", e);
            throw new RuntimeException("ZIP导出错误", e);
        }
    }

    /**
     * Excel数据封装类 - 支持内存优化
     */
    private static class ExcelData {
        private final String fileName;
        private byte[] data;

        public ExcelData(String fileName, byte[] data) {
            this.fileName = fileName;
            this.data = data;
        }

        public String getFileName() {
            return fileName;
        }

        public byte[] getData() {
            return data;
        }

        /**
         * 清理数据，释放内存
         */
        public void clearData() {
            this.data = null;
        }
    }

    /**
     * 带缓存的问题列表查询方法
     * 缓存时间设置为30分钟，避免问卷结构变更时数据不一致
     */
    @Cacheable(value = "questionnaireQuestions", key = "#diaId", unless = "#result == null")
    public List<TPreDiagnosisQuestion> getQuestionListWithCache(String diaId) {
        return tPreDiagnosisQuestionMapper.getAllQuestionListByDiaId(diaId);
    }

    public static byte[] convertToByteArray(SXSSFWorkbook workbook) throws IOException {

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            workbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        }
    }


    public void createSheet(SXSSFSheet sheet, SXSSFWorkbook workbook, String fileName) {
        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 20);

        //标题样式
        CellStyle titleCellStyle = ExportExcel.createTitleCellStyle(workbook);
        //普通样式
        CellStyle normalCellStyle = ExportExcel.createNormalCellStyle(workbook);

        //标题行
        SXSSFRow row = sheet.createRow(0);
        SXSSFCell cell1 = row.createCell(0);
        cell1.setCellValue(new XSSFRichTextString(fileName));
        cell1.setCellStyle(titleCellStyle);

        // 合并单元格
        CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 18);
        sheet.addMergedRegion(cra);

        //第2行
        SXSSFRow row2 = sheet.createRow(1);
        SXSSFCell cell20 = row2.createCell(0);
//        cell20.setCellValue(new XSSFRichTextString("统计时间：" + prescriptionStatisticsVO.getPreTimeStart() + " ~ " + prescriptionStatisticsVO.getPreTimeEnd()));
        cell20.setCellStyle(normalCellStyle);

        //合并单元格
        CellRangeAddress cra21 = new CellRangeAddress(1, 1, 0, 18);
        sheet.addMergedRegion(cra21);
    }


}
