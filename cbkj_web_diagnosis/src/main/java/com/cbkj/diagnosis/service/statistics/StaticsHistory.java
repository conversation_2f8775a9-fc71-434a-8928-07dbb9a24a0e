package com.cbkj.diagnosis.service.statistics;

import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth;
import com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData;
import com.cbkj.diagnosis.service.statistics.impl.StatisticsErverDayDisServiceImpl;
import com.cbkj.diagnosis.service.statistics.impl.StatisticsErverDayHealthServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/10 10:46
 * @Version 1.0
 */
@Service
public class StaticsHistory {
    public StaticsHistory(StatisticsErverDayDisServiceImpl statisticsErverDayDisService, StatisticsErverDayHealthServiceImpl statisticsErverDayHealthService) {
        this.statisticsErverDayDisService = statisticsErverDayDisService;
        this.statisticsErverDayHealthService = statisticsErverDayHealthService;
    }
    private final StatisticsErverDayDisServiceImpl statisticsErverDayDisService;
    private final StatisticsErverDayHealthServiceImpl statisticsErverDayHealthService;

    @Transactional(rollbackFor = Exception.class)
    public void staticsHistoryData(StaticsHistoryData staticsHistoryData){
        List<StatisticsErverDayDis> listYZ =  statisticsErverDayDisService.staticsHistoryYZ(staticsHistoryData);
        List<StatisticsErverDayDis> listSF =  statisticsErverDayDisService.staticsHistorySF(staticsHistoryData);
        List<StatisticsErverDayHealth> listHealth =  statisticsErverDayHealthService.staticsHistoryHealth(staticsHistoryData);

        List<StatisticsErverDayDis> insertList = new ArrayList<>();
        listYZ.forEach(item -> {
            if(item.getId() == null){
                item.setDiagnosisNum(item.getCurrentNum() );
                item.setTotalNum(item.getCurrentNum() );
                item.setRecordsNum(0);
                item.setFollowNum(0);
                listSF.forEach(itemSF -> {
                    if (itemSF.getCreateTime().getTime() == item.getCreateTime().getTime() && itemSF.getId() == null){

                        item.setFollowNum(itemSF.getCurrentNum() );

                        Integer totalNum = item.getTotalNum();
                        item.setTotalNum( itemSF.getCurrentNum()+totalNum );

                    }
                });
                insertList.add(item);
            }
        });

        listSF.forEach(itemSF -> {
            boolean present = listYZ.stream().anyMatch(item -> item.getCreateTime().getTime() == itemSF.getCreateTime().getTime());
            if (!present && itemSF.getId() == null){
                itemSF.setDiagnosisNum(0 );
                itemSF.setTotalNum(itemSF.getCurrentNum() );
                itemSF.setFollowNum(itemSF.getCurrentNum());
                itemSF.setRecordsNum(0);
                insertList.add(itemSF);
            }
        });
        statisticsErverDayDisService.saveBatch(insertList);
        statisticsErverDayHealthService.saveBatch(listHealth);
    }
}
