package com.cbkj.diagnosis.service.statistics.impl;

import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.service.statistics.factory.StatisticsStrategy;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 默认的服务
 * @param [statisticsVo]
 * {@link ResEntity}
 * @date 2024/1/29 16:32
 * @version 1.0
 */
@Service("block-default")
public class BlockDefaultService implements StatisticsStrategy {
    @Override
    public ResEntity getData(StatisticsVo statisticsVo) {
        return ResEntity.error("错误的入参");
    }
}
