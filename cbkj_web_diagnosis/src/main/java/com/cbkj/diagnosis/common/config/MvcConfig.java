package com.cbkj.diagnosis.common.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.TaskExecutor;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * <AUTHOR>
 */
@Configuration
public class MvcConfig implements WebMvcConfigurer {

    @Value("${file.address}")
    private String localtion;

    @Value("${root.preview}")
    private String preview;

//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//    }



    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/" + preview + "**").addResourceLocations("file:" + localtion);
//        registry.addResourceHandler("/swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
//        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
        WebMvcConfigurer.super.addResourceHandlers(registry);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加自定义的 token 拦截器
//        registry.addInterceptor(tokenInterceptor(tokenUtil, objectMapper))
//                .addPathPatterns("/healthy/**")
//                .excludePathPatterns("/preview/**", "/sys/help/download");

        // 添加设置 Referrer-Policy 头的拦截器
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
                response.setHeader("Referrer-Policy", "no-referrer");
                return true;
            }
        }).addPathPatterns("/**"); // 根据需要调整路径模式
        // 添加设置 X-Content-Type-Options 头的拦截器
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
                response.setHeader("X-Content-Type-Options", "nosniff");
                return true;
            }
        }).addPathPatterns("/**"); // 根据需要调整路径模式
    }



    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer.favorParameter(true).parameterName("format")
//                .ignoreAcceptHeader(true)
                .defaultContentType(MediaType.APPLICATION_JSON)
                .mediaType("xml", MediaType.APPLICATION_XML)
                .mediaType("json", MediaType.APPLICATION_JSON);
    }


    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        configurer.setTaskExecutor(taskExecutor());  // 设置线程池:ml-citation{ref="7" data="citationList"}
        configurer.setDefaultTimeout(30_000);  // 超时时间(ms):ml-citation{ref="6" data="citationList"}
    }

//    @Bean
    public AsyncTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);  // 核心线程数:ml-citation{ref="7" data="citationList"}
        executor.setMaxPoolSize(50);   // 最大线程数
        executor.setQueueCapacity(100); // 任务队列容量
        executor.initialize();
        return executor;
    }


}