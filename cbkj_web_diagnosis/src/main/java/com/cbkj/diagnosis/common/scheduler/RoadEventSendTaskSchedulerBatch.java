package com.cbkj.diagnosis.common.scheduler;


import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.common.http.XiaMenResEntity;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.his.XiaMenSendWXMessage;
import com.cbkj.diagnosis.service.webtask.TaskRedisService;
import com.cbkj.diagnosis.utils.AESPKCS7Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.LockSupport;

@Slf4j
@Service
public class RoadEventSendTaskSchedulerBatch {
    private static final int EVER_COUNT = 500;
    public static AtomicInteger connentCount = new AtomicInteger(0);

    private final PlatformTransactionManager transactionManager;

    private final TaskRedisService taskRedisService;
    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;
    private final XiaMenSendWXMessage xiaMenSendWXMessage;

    private final TAdminInfoMapper tAdminInfoMapper;

    private final MedicalRecordsMapper medicalRecordsMapper;
    @Value("${xia.men.mobile.wx.url}")
    private String url;
    @Value("${sys.aes.key}")
    private String key ;
    public RoadEventSendTaskSchedulerBatch(PlatformTransactionManager transactionManager, TaskRedisService taskRedisService, SRoadTaskPatientsMapper sRoadTaskPatientsMapper, XiaMenSendWXMessage xiaMenSendWXMessage, TAdminInfoMapper tAdminInfoMapper, MedicalRecordsMapper medicalRecordsMapper) {
        this.transactionManager = transactionManager;

        this.taskRedisService = taskRedisService;
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.xiaMenSendWXMessage = xiaMenSendWXMessage;
        this.tAdminInfoMapper = tAdminInfoMapper;
        this.medicalRecordsMapper = medicalRecordsMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSchedule() {
        if (connentCount.addAndGet(1) != 1) {
            log.info("任务执行-当前有其它线程占用");
            return;
        }

        ArrayList<String> arrivedKey = taskRedisService.getArrivedKey();
        log.info("任务执行-01");
        for (String next : arrivedKey) {
            //更新
            log.info("当前更新任务id:{}", next);

            sRoadTaskPatientsMapper.upDateSchedulerTaskStatusById(next);
            //http://pretest.tcmbrain.com:233/pdfu/mobile/healtheducation?roadExecuteEventContentId=17&taskPatientsId=203&token=123 宣教
            //http://pretest.tcmbrain.com:233/pdfu/mobile/rememberfollowup?token=123 复诊
            //http://pretest.tcmbrain.com:233/pdfu/mobile/guide?taskPatientsId=237&diaId=81fb1dcd0fd7479a913882d7d4e66ac5&token=123 随访问卷
            //todo 需要根据任务方式：1.微信2短信3.电话 （微信：调用his微信接口发送。2短信 调用his短信接口。

            SRoadTaskPatients sRoadTaskPatients = sRoadTaskPatientsMapper.getObjectById(next);
//            sRoadTaskPatients.setTaskExcuteStatus(Integer.parseInt(Constant.BASIC_STRING_ONE));
//            sRoadTaskPatientsMapper.updateByPrimaryKey(sRoadTaskPatients);
            if (sRoadTaskPatients != null && Constant.BASIC_STRING_ONE.equals(sRoadTaskPatients.getRoadExecuteEventWay())) {
                TAdminInfo tAdminInfoById = tAdminInfoMapper.getTAdminInfoById(sRoadTaskPatients.getPatientId());
                MedicalRecords objectById = medicalRecordsMapper.getObjectById(sRoadTaskPatients.getRecordsId());
                //SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                //组装cbdata字段值。
                if ( null == objectById){
                    log.error("当前更新任务MedicalRecords查询不存在id:{}", next);
                    continue;
                }
                HashMap<String, String> map = new HashMap<>();
                map.put("appId", objectById.getAppId());
                map.put("insCode", objectById.getInsCode());
                map.put("insName", objectById.getInsName());
                map.put("timestamp", System.currentTimeMillis() + "");
                map.put("userName", tAdminInfoById.getUserName());
                map.put("mobile", tAdminInfoById.getMobile());
                map.put("healthCardNum", tAdminInfoById.getHealthCardNum());
                String mak = JSON.toJSONString(map);
                String base64 = "";
                try {
                    base64 = AESPKCS7Util.encrypt(mak, key, "base64");
                } catch (Exception e) {
                    log.error("cbdata加密错误：" + e.getMessage());
                }
                XiaMenResEntity re = null;
                if (Constant.BASIC_STRING_ONE.equals(sRoadTaskPatients.getRoadExecuteEventType())) {

                    String temp = null;
                    try {
                        temp = URLEncoder.encode("healtheducation?roadExecuteEventContentId="+sRoadTaskPatients.getRoadExecuteEventContentId()+"&taskPatientsId="+sRoadTaskPatients.getTaskPatientsId(),"UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                    //宣教 XiaMenZoeAes256Util
                    re = xiaMenSendWXMessage.sendWxTemplateWSDL(tAdminInfoById.getMobile(),  null,
                            url + "?cbdata=" + base64 + "&jumpUrl="+temp,
                            //入院科室|入院时间|宣教内容
                            objectById.getPatientName() + "|" + objectById.getDoctorName() + "|" + objectById.getDeptName() + "|" + sRoadTaskPatients.getRoadExecuteEventContentName(),
                            com.cbkj.diagnosis.common.utils.DateUtil.getDateFormats( "yyyy-MM-dd HH:mm:ss",sRoadTaskPatients.getTaskExcuteTime()),
                            sRoadTaskPatients.getPatientCardNumber(),Constant.BASIC_STRING_ONE,tAdminInfoById.getMedicalCard()
                            );
                } else if (Constant.BASIC_STRING_THREE.equals(sRoadTaskPatients.getRoadExecuteEventType())) {
                    //复诊
                    re = xiaMenSendWXMessage.sendWxTemplateWSDL(tAdminInfoById.getMobile(),  null,
                            url + "?cbdata=" + base64 + "&jumpUrl=rememberfollowup",
                            //就诊人|就诊日期
                            objectById.getPatientName() + "|" + objectById.getDoctorName() + "|" + objectById.getDeptName() + "|" + sRoadTaskPatients.getRoadExecuteEventContentName(),
                            com.cbkj.diagnosis.common.utils.DateUtil.getDateFormats( "yyyy-MM-dd HH:mm:ss",sRoadTaskPatients.getTaskExcuteTime()),
                            sRoadTaskPatients.getPatientCardNumber(),Constant.BASIC_STRING_TWO,tAdminInfoById.getMedicalCard()
                            );
                } else {
                    String a = "doctorfollowup?taskPatientsId="+sRoadTaskPatients.getTaskPatientsId()+"&diaId="+sRoadTaskPatients.getRoadExecuteEventContentId();
                    String temp = null;
                    try {
                        temp = URLEncoder.encode(a,"UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                    //随访问卷||自测量表
                    re = xiaMenSendWXMessage.sendWxTemplateWSDL(tAdminInfoById.getMobile(),  null,
                            url + "?cbdata=" + base64 + "&jumpUrl="+temp,
                            //就诊人姓名|医生姓名|科室名称|问诊单名称
                            objectById.getPatientName() + "|" + objectById.getDoctorName() + "|" + objectById.getDeptName() + "|" + sRoadTaskPatients.getRoadExecuteEventContentName(),
                            com.cbkj.diagnosis.common.utils.DateUtil.getDateFormats( "yyyy-MM-dd HH:mm:ss",sRoadTaskPatients.getTaskExcuteTime()),
                            sRoadTaskPatients.getPatientCardNumber(),Constant.BASIC_STRING_THREE,tAdminInfoById.getMedicalCard()
                            );
                }
                if (re.getSuccess() || re.getCode() != 0) {
                    log.error("任务执行发送微信消息失败，错误信息：{}", re.getMessage());
                }
            }

            // ...
        }
        taskRedisService.removeKey(arrivedKey);
        log.info("处理除了电话随访任务外的其它任务是否到应该推送了");
        //不正常结束，就一直锁着
        connentCount.decrementAndGet();
        log.info("任务执行-解锁线程");

    }

    public static void main(String[] args) {
        HashMap<String, String> map = new HashMap<>();
        map.put("appId", "100016");
        map.put("insCode", "1001");
        map.put("insName", "浙江中医药大学附属第二医院(浙江省新华医院)");
        map.put("timestamp", System.currentTimeMillis() + "");
        map.put("userName", "患者老六");
        map.put("mobile", "13175815320");
       // map.put("healthCardNum", "13175815320");
        String mak = JSON.toJSONString(map);
        String base64 = "";
        try {
            base64 = AESPKCS7Util.encrypt(mak, "OUT3041JT51Y7HF2", "base64");
            //随访
            //String jumpUrl = "doctorfollowup?taskPatientsId="+"403"+"&diaId="+"9b8539acc143457697d2dd455262d84f";
            //预诊
            String jumpUrl = "doctorfollowup?diaId=" + "e4eae59d1edc4146ac8eb8cf71617162" + "&yuzhen=1" + "&diaType=1";
            String temp = null;
            try {
                temp = URLEncoder.encode(jumpUrl,"UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }


            String u = "?cbdata=" + base64 + "&jumpUrl="+temp;
            System.out.println(u);
        } catch (Exception e) {
            log.error("cbdata加密错误：" + e.getMessage());
        }
    }

    private void notifyAllThread(List<Thread> unfinishedList, int totalSize, boolean isForce) {
        if (isForce || unfinishedList.size() >= totalSize) {
            log.info("唤醒当前所有休眠线程，线程数={}，总线程数={},是否强制={}", unfinishedList.size(), totalSize, isForce);
            for (Thread thread : unfinishedList) {
                log.info("当前线程={}被唤醒", thread.getName());
                LockSupport.unpark(thread);
            }
        }
    }



}
