package com.cbkj.diagnosis.common.http;


import com.cbkj.diagnosis.common.utils.Constant;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应实体
 */
@Data
public class XiaMenResEntity implements Serializable {

    private static final long serialVersionUID = -1439573222637546375L;
    private boolean success = true;
    private int code = 0;
    private String message;
    private Object data;

    public XiaMenResEntity() {

    }

    public XiaMenResEntity(boolean success, Object data) {
        this.success = success;
        this.data = data;
    }


    public XiaMenResEntity(boolean success, String message, Object data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }

    public XiaMenResEntity(boolean success, int code, String message, Object data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static XiaMenResEntity entity(boolean status, String message, Object data) {
        return new XiaMenResEntity(status, message, data);
    }

    public static XiaMenResEntity success(Object data) {
        return new XiaMenResEntity(true, Constant.SUCCESS_DX, data);
    }
    public static XiaMenResEntity success() {
        return new XiaMenResEntity(true, Constant.SUCCESS_DX, null);
    }

    public static XiaMenResEntity error(String message) {
        return new XiaMenResEntity(false, message, null);
    }

    public static XiaMenResEntity error(int code, String message) {
        return new XiaMenResEntity(false, code, message, null);
    }

    public boolean getSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}

