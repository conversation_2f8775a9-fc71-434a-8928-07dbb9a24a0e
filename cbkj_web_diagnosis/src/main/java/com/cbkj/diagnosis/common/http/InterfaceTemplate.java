package com.cbkj.diagnosis.common.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/29 14:34
 * @Version 1.0
 */
@Component
@Slf4j
public class InterfaceTemplate extends RestTemplate {

    @Value("${diagnosis.interface.url}")
    private String interfaceUrl;
    private final RestTemplate restTemplate;

    public InterfaceTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public XiaMenResEntity post(String path, JSONObject params) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set("interface_token","123456");
        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);
        try {
            XiaMenResEntity xiaMenResEntity = restTemplate.postForObject(interfaceUrl+path, request, XiaMenResEntity.class);
            log.info("【调用接口服务】成功 --- URL:{}, request:{}, response:{}", interfaceUrl+path, request, JSON.toJSONString(xiaMenResEntity));
            return xiaMenResEntity;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【调用接口服务】异常 --- URL:{}, request:{}, error:{}", interfaceUrl+path, request, e);
        }
        return XiaMenResEntity.error("调用接口服务异常");
    }
}
