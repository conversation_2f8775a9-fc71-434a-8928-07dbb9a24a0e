package com.cbkj.diagnosis.common.utils;

import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.common.config.security.TokenBo;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Created by sang on 2017/12/30.
 */
@UtilityClass
public class AdminWebUtils {

    /**
     * 获取当前登录管理员
     *
     * @return
     */
    public AdminInfo getCurrentHr() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication) {
            Object obj = authentication.getPrincipal();
            if (obj instanceof TokenBo) {
                return ((TokenBo) obj).getAdmin();
            }

            if (obj instanceof AdminInfo) {
                return (AdminInfo) obj;
            }
        }
        return null;
    }
    public Set<String> getRolesMenuStr() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication) {
            Object obj = authentication.getPrincipal();
            if (obj instanceof TokenBo) {
                return ((TokenBo) obj).getRolesMenuStr();
            }
        }
        return null;
    }


    /**
     * 搜索是否包含数据搜索的某个权限
     *
     * @param searchWord
     * @return
     */
    public boolean containsSearchKey(String searchWord) {

        List<AdminRule> roles = getCurrentHr().getRoles();
        for (int i = 0; i < roles.size(); i++) {
            String searchData = roles.get(i).getSearchData();
            if (StringUtils.isBlank(searchData)) {
                continue;
            }
            boolean contains = searchData.contains(searchWord);
            if (contains) {
                return true;
            }
        }

        return false;
    }


    /**
     * 判断当前登录管理员是否神级管理员
     */
    public boolean getSuper() {
        AdminInfo adminInfo = getCurrentHr();
        if (null != adminInfo) {
            AdminRule adminRule = adminInfo.getRoles().stream().filter(rule -> rule.getRoleId().equals(Constant.ROLEGRADE)).findAny().orElse(null);
            return null != adminRule;
        }
        return false;
    }


    /**
     * 获取真实IP
     *
     * @param request
     * @return
     */
    public String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }


}