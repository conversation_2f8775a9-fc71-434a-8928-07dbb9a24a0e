package com.cbkj.diagnosis.common.utils;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> 徐亨期
 * @Description : 可以放一些公共的方法
 * @updateTime : 2019/12/12 16:30
 */

public class CommonUtil {
    /**
     * @param o : Page.getLayUiTablePageData(list)的结果
     * @Description :   将 Page.getLayUiTablePageData(list)转换为只有 count和respList的map
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/12/12 16:31
     */
    public static Map<String, Object> pageToMap(Object o) {
        Map<String, Object> result = (Map<String, Object>) o;
        Map<String, Object> respM = new HashMap<>();
        respM.put("count", result.get("count"));
        respM.put("respList", result.get("data"));
        return respM;
    }



    /**
     * @return 获取当前机器的IP
     */
    public static String getLocalIP() {
        InetAddress addr = null;
        try {
            addr = InetAddress.getLocalHost();
        } catch (Exception e) {
            e.printStackTrace();
        }


        byte[] ipAddr = addr.getAddress();
        String ipAddrStr = "";
        for (int i = 0; i < ipAddr.length; i++) {
            if (i > 0) {
                ipAddrStr += ".";
            }
            ipAddrStr += ipAddr[i] & 0xFF;
        }
        return ipAddrStr;
    }

}