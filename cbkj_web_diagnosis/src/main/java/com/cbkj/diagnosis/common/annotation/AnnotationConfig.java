package com.cbkj.diagnosis.common.annotation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.beans.SysLogInterface;
import com.cbkj.diagnosis.common.exception.ExceptionEnum;
import com.cbkj.diagnosis.common.exception.ExceptionUtils;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.IDUtil;
import com.cbkj.diagnosis.mapper.sysmapper.AdminMenuMapper;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.LogAsync;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;

@Slf4j
@Aspect
@Component
public class AnnotationConfig {

    private final AdminMenuMapper adminMenuMapper;

    public AnnotationConfig(AdminMenuMapper adminMenuMapper, LogAsync logAsync) {
        this.adminMenuMapper = adminMenuMapper;
        this.logAsync = logAsync;
    }
    private final LogAsync logAsync;


    @Pointcut("execution(public * com.cbkj.diagnosis.controller..*.*(..))")
    public void web() {
    }

    @Around("@annotation(logAnnotaion)")
    public Object arround(ProceedingJoinPoint joinPoint,LogAnnotaion logAnnotaion) throws Throwable {
        String params;
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Method method = getMethod(joinPoint);
        String methodName = joinPoint.getSignature().getName();
        Object o = joinPoint.proceed();
        if ("handleFileUpload".equals(methodName) || "sendPreDiagnosis".equals(methodName)){
            return o;
        }
        //params = JSONObject.toJSONString(joinPoint.getArgs()[0]);



        params = JSONObject.toJSONString(joinPoint.getArgs());
        if (null != method && o instanceof ResEntity) {

            String descpt = null;
            //Object logObj = method.getAnnotation(LogAnnotaion.class);
           // if (null != logObj) {

             //   LogAnnotaion logAnnotaion = ((LogAnnotaion) logObj);
                if (logAnnotaion.isWrite()) {
                    descpt = logAnnotaion.value();
                }
            //}

            if (StringUtils.isNotBlank(descpt)) {

                ResEntity entity = (ResEntity) o;
                String methodType = request.getMethod();
                Map<String, String[]> paramMap = request.getParameterMap();


                if ("GET".equals(methodType)) {
                    params = getParams(paramMap);
                }

                SysLogInterface log = new SysLogInterface();
                log.setId(IDUtil.getID());
                log.setAppId(getParam(paramMap, "appId"));
                log.setInsCode(getParam(paramMap, "insCode"));
                log.setDoctorId(getParam(paramMap, "doctorId"));
                log.setDoctorName(getParam(paramMap, "doctorName"));
                log.setPatientId(getParam(paramMap, "patientId"));
                log.setPatientName(getParam(paramMap, "patientName"));
                log.setCreateTime(new Date());
                log.setInterfaceName(methodName);
                log.setInterfaceDesc(descpt);
                log.setInterfaceToken(getParam(paramMap, "token"));
                log.setInterfaceParams(params);
                log.setResultStatus(entity.getStatus() ? "1" : "0");
                log.setResultMeseage(entity.getMessage());
                if (entity.getData() != null) {
                    log.setResultData(JSONObject.toJSONString(entity.getData()));
                }

                logAsync.logRecord(log);

            }
        }
        return o;
    }

    private static String getParam(Map<String, String[]> paramMap, String key) {
        String[] values = paramMap.get(key);
        if (values != null && values.length > 0) {
            StringBuilder sb = new StringBuilder();
            for (String value : values) {
                if (StringUtils.isNotBlank(value)) {
                    sb.append(value).append(",");
                }
            }
            return sb.substring(0, sb.length());
        }
        return "";
    }
    private static String getParams(Map<String, String[]> paramMap) {
        StringBuffer sb = new StringBuffer(" ");
        for (String key : paramMap.keySet()) {
            String[] values = paramMap.get(key);
            for (int i = 0; i < values.length; i++) {
                String value = values[i];
                if (StringUtils.isNotBlank(value)) {
                    sb.append(key).append(":").append(value).append(",");
                }
            }
        }
        return sb.substring(0, sb.length());
    }

    @Before("@annotation(requiresMenus)")
    public void before(JoinPoint joinPoint,RequiresMenus requiresMenus) {
        Method method = getMethod(joinPoint);
        if (null == method) {
            ExceptionUtils.throwCustomRuntimeException(ExceptionEnum.ACCESS_DENIED);
            return;
        }
//        boolean annotationPresent = method.isAnnotationPresent(RequiresMenus.class);
//        if (!annotationPresent) {
//            return;
//        }
//        RequiresMenus annotation = method.getAnnotation(RequiresMenus.class);
        if (requiresMenus != null && AdminWebUtils.getCurrentHr() != null) {

            String[] value = requiresMenus.value();
            if (value.length == 0) {
                //无内容不需要拦截
                return;
            }
            if (Constant.ADMIN.equals(AdminWebUtils.getCurrentHr().getUsername())) {
                return;
            }
            //用户拥有的菜单id集合
            Set<String> menuSetByUid = AdminWebUtils.getRolesMenuStr();
            if (menuSetByUid == null || menuSetByUid.size() == 0) {
                log.error("@RequiresMenus 拦截没有登录信息");
                ExceptionUtils.throwCustomRuntimeException(ExceptionEnum.ACCESS_DENIED);
            }

            //判断menuSetByUid中是否有这个value值
            boolean b = menuSetByUid.containsAll(new HashSet<>(Arrays.asList(value)));
            if (!b) {
                log.error("@RequiresMenus 拦截没有要求的菜单权限 ：" + JSON.toJSONString(value) + "--账户:" + AdminWebUtils.getCurrentHr().getUsername());
                ExceptionUtils.throwCustomRuntimeException(ExceptionEnum.ACCESS_DENIED);
            }
        }


    }


    /**
     * 获取方法
     *
     * @param joinPoint joinPoint
     * @return Method
     */
    private Method getMethod(JoinPoint joinPoint) {

        Method[] methods = joinPoint.getTarget().getClass().getMethods();
        String methodName = joinPoint.getSignature().getName();
        Object[] objs = joinPoint.getArgs();

        return Arrays.stream(methods).filter(method ->
                        (method.getName().equals(methodName) && method.getParameterTypes().length == objs.length))
                .findAny()
                .orElse(null);

    }
}
