//package com.cbkj.diagnosis.common.utils;
//
//import lombok.extern.slf4j.Slf4j;
//
//import javax.servlet.http.HttpServletResponse;
//import java.io.*;
//import java.nio.ByteBuffer;
//import java.nio.channels.Channels;
//import java.nio.channels.ReadableByteChannel;
//import java.nio.channels.WritableByteChannel;
//import java.util.List;
//import java.util.zip.Deflater;
//import java.util.zip.ZipEntry;
//import java.util.zip.ZipOutputStream;
//
///**
// * 高性能ZIP工具类
// * 针对大数据量Excel文件压缩的性能优化
// *
// * <AUTHOR>
// * @date 2024/12/23
// * @version 2.0 - 极致性能优化版本
// */
//@Slf4j
//public class HighPerformanceZipUtils {
//
//    // 缓冲区大小 - 64KB，平衡内存使用和性能
//    private static final int BUFFER_SIZE = 64 * 1024;
//
//    // 压缩级别 - 使用最快压缩
//    private static final int COMPRESSION_LEVEL = Deflater.BEST_SPEED;
//
//    /**
//     * 高性能流式ZIP生成
//     * 使用NIO和优化的压缩算法
//     */
//    public static void generateHighPerformanceZip(String zipFileName,
//                                                 List<ExcelFileData> excelFiles,
//                                                 HttpServletResponse response) {
//
//        long startTime = PerformanceMonitor.startTimer("high_performance_zip");
//
//        try {
//            // 设置响应头
//            setResponseHeaders(response, zipFileName);
//
//            // 使用缓冲输出流
//            BufferedOutputStream bufferedOut = new BufferedOutputStream(
//                response.getOutputStream(), BUFFER_SIZE);
//
//            try (ZipOutputStream zipOut = createOptimizedZipOutputStream(bufferedOut)) {
//
//                long totalSize = 0;
//                int fileCount = 0;
//
//                for (ExcelFileData excelFile : excelFiles) {
//                    if (excelFile == null || excelFile.getData() == null) {
//                        continue;
//                    }
//
//                    // 创建ZIP条目
//                    ZipEntry entry = createOptimizedZipEntry(excelFile.getFileName());
//                    zipOut.putNextEntry(entry);
//
//                    // 使用NIO高效写入数据
//                    writeDataWithNIO(zipOut, excelFile.getData());
//
//                    zipOut.closeEntry();
//
//                    totalSize += excelFile.getData().length;
//                    fileCount++;
//
//                    // 立即释放内存
//                    excelFile.clearData();
//
//                    // 定期刷新，避免内存积累
//                    if (fileCount % 5 == 0) {
//                        zipOut.flush();
//                    }
//                }
//
//                zipOut.finish();
//                bufferedOut.flush();
//
//                PerformanceMonitor.recordZipGeneration(
//                    System.currentTimeMillis() - startTime, fileCount, totalSize);
//
//                log.info("高性能ZIP生成完成: {} 个文件, 总大小: {} MB, 耗时: {} ms",
//                    fileCount, totalSize / 1024 / 1024,
//                    System.currentTimeMillis() - startTime);
//
//            }
//
//        } catch (IOException e) {
//            log.error("高性能ZIP生成失败", e);
//            throw new RuntimeException("ZIP生成失败", e);
//        } finally {
//            PerformanceMonitor.endTimer("high_performance_zip", startTime);
//        }
//    }
//
//    /**
//     * 创建优化的ZIP输出流
//     */
//    private static ZipOutputStream createOptimizedZipOutputStream(OutputStream out) {
//        ZipOutputStream zipOut = new ZipOutputStream(out);
//
//        // 设置压缩级别为最快
//        zipOut.setLevel(COMPRESSION_LEVEL);
//
//        // 设置压缩方法
//        zipOut.setMethod(ZipOutputStream.DEFLATED);
//
//        return zipOut;
//    }
//
//    /**
//     * 创建优化的ZIP条目
//     */
//    private static ZipEntry createOptimizedZipEntry(String fileName) {
//        ZipEntry entry = new ZipEntry(fileName);
//
//        // 设置时间戳
//        entry.setTime(System.currentTimeMillis());
//
//        // 设置压缩方法
//        entry.setMethod(ZipEntry.DEFLATED);
//
//        return entry;
//    }
//
//    /**
//     * 使用NIO高效写入数据
//     */
//    private static void writeDataWithNIO(ZipOutputStream zipOut, byte[] data) throws IOException {
//        try (ByteArrayInputStream bais = new ByteArrayInputStream(data);
//             ReadableByteChannel inputChannel = Channels.newChannel(bais);
//             WritableByteChannel outputChannel = Channels.newChannel(zipOut)) {
//
//            ByteBuffer buffer = ByteBuffer.allocateDirect(BUFFER_SIZE);
//
//            while (inputChannel.read(buffer) != -1) {
//                buffer.flip();
//                outputChannel.write(buffer);
//                buffer.clear();
//            }
//        }
//    }
//
//    /**
//     * 设置响应头
//     */
//    private static void setResponseHeaders(HttpServletResponse response, String fileName) {
//        try {
//            String encodedFilename = java.net.URLEncoder.encode(fileName, "UTF-8");
//            String contentDisposition = "filename*=utf-8''" +
//                java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
//
//            response.setHeader("Content-Disposition",
//                "attachment;" + contentDisposition + ";filename=" + encodedFilename);
//            response.setContentType("application/octet-stream;charset=UTF-8");
//            response.setCharacterEncoding("UTF-8");
//
//            // 设置缓存控制
//            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
//            response.setHeader("Pragma", "no-cache");
//            response.setDateHeader("Expires", 0);
//
//        } catch (Exception e) {
//            log.error("设置响应头失败", e);
//        }
//    }
//
//    /**
//     * Excel文件数据封装类
//     */
//    public static class ExcelFileData {
//        private final String fileName;
//        private byte[] data;
//
//        public ExcelFileData(String fileName, byte[] data) {
//            this.fileName = fileName;
//            this.data = data;
//        }
//
//        public String getFileName() {
//            return fileName;
//        }
//
//        public byte[] getData() {
//            return data;
//        }
//
//        public void clearData() {
//            this.data = null;
//        }
//
//        public long getSize() {
//            return data != null ? data.length : 0;
//        }
//    }
//}
