package com.cbkj.diagnosis.common.scheduler;


import com.cbkj.diagnosis.beans.business.SRoadTaskHandleMapping;
import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone;
import com.cbkj.diagnosis.beans.business.SRoadTaskPresMapping;
import com.cbkj.diagnosis.beans.business.SRoadTaskWestMapping;
import com.cbkj.diagnosis.beans.task.SRoadExecute;
import com.cbkj.diagnosis.beans.task.SRoadExecuteContents;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.beans.task.webvo.UpdateOrInsertRoad;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.business.*;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.sysService.RedisService;
import com.cbkj.diagnosis.service.webapi.business.SRoadTaskPatientsOtherService;
import com.cbkj.diagnosis.service.webapi.business.vo.RoadEventTaskSchedulerRes;
import com.cbkj.diagnosis.common.MyThreadPoolTaskScheduler;
import com.cbkj.diagnosis.service.webtask.WebRoadService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.LockSupport;

@Slf4j
@Service
public class RoadEventTaskSchedulerBatch {
    private static final int EVER_COUNT = 500;
    private final PlatformTransactionManager transactionManager;
    //    @Resource
//    @Qualifier("newAsyncExecutor")
//    private ThreadPoolTaskExecutor ioDense;
    public static AtomicInteger connentCount = new AtomicInteger(0);
    @Autowired
    private MyThreadPoolTaskScheduler threadPoolTaskScheduler;

    private final WebRoadService webRoadService;

    private final SRoadTaskPatientsOtherService sRoadTaskPatientsOtherService;

    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;
    private final SRoadTaskPresMappingMapper sRoadTaskPresMappingMapper;
    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;

    private final SRoadTaskPatientsMappingMapper sRoadTaskPatientsMappingMapper;

    private final SRoadTaskWestMappingMapper sRoadTaskWestMappingMapper;
    private final SRoadTaskHandleMappingMapper sRoadTaskHandleMappingMapper;

    private final RedisService redisService;

    public RoadEventTaskSchedulerBatch(PlatformTransactionManager transactionManager,
                                       WebRoadService webRoadService,
                                       SRoadTaskPatientsOtherService sRoadTaskPatientsOtherService,
                                       SRoadTaskPatientsMapper sRoadTaskPatientsMapper,
                                       SRoadTaskPresMappingMapper sRoadTaskPresMappingMapper,
                                       SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper,
                                       SRoadTaskPatientsMappingMapper sRoadTaskPatientsMappingMapper,
                                       SRoadTaskWestMappingMapper sRoadTaskWestMappingMapper,
                                       SRoadTaskHandleMappingMapper sRoadTaskHandleMappingMapper,
                                       RedisService redisService) {
        this.transactionManager = transactionManager;
        this.webRoadService = webRoadService;
        this.sRoadTaskPatientsOtherService = sRoadTaskPatientsOtherService;
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.sRoadTaskPresMappingMapper = sRoadTaskPresMappingMapper;
        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
        this.sRoadTaskPatientsMappingMapper = sRoadTaskPatientsMappingMapper;
        this.sRoadTaskWestMappingMapper = sRoadTaskWestMappingMapper;
        this.sRoadTaskHandleMappingMapper = sRoadTaskHandleMappingMapper;
        this.redisService = redisService;
    }


    public void batchSchedule() {

        if (connentCount.addAndGet(1) != 1) {
            return;
        }
//         获取需要生成任务的数据列表。
        ArrayList<RoadEventTaskSchedulerRes> roadEventTaskSchedulerRes1 = new ArrayList<>();

        List<RoadEventTaskSchedulerRes> addList = sRoadTaskPatientsMappingMapper.getRoadEventTaskSchedulerList();
        //西药处方，先不用。
        //List<RoadEventTaskSchedulerRes> addList2 = sRoadTaskPatientsMappingMapper.getRoadEventTaskSchedulerWestPreList();
        //适宜技术，先删除
        //List<RoadEventTaskSchedulerRes> addList3 = sRoadTaskPatientsMappingMapper.getRoadEventTaskSchedulerHandlePreList();

        if (addList.size() > 0) {
            roadEventTaskSchedulerRes1.addAll(addList);
        }
//        if (addList2.size() > 0 ){
//            roadEventTaskSchedulerRes1.addAll(addList2);
//        }
//        if (addList3.size() > 0 ){
//            roadEventTaskSchedulerRes1.addAll(addList3);
//        }

        if (!CollectionUtils.isEmpty(roadEventTaskSchedulerRes1)) {
            log.info("MyThreadPoolTaskScheduler此刻最大线程数：{}", threadPoolTaskScheduler.getExecutor().getMaxPoolSize());
            //定义局部变量，是否成功、顺序标识、等待线程队列
            AtomicBoolean isSuccess = new AtomicBoolean(true);
            AtomicInteger cur = new AtomicInteger(1);
            List<Thread> unfinishedList = new ArrayList<>();
            //谷歌提供工具类切分集合--import com.google.common.collect.Lists;
            List<List<RoadEventTaskSchedulerRes>> partition = Lists.partition(roadEventTaskSchedulerRes1, EVER_COUNT);
            int totalSize = partition.size();
            //多线程处理开始
            CompletableFuture<Void> future =
                    CompletableFuture.allOf(partition.stream().map(addPartitionList -> CompletableFuture.runAsync(() -> {
                        //Spring事务内部由ThreadLocal存储事务绑定信息，因此需要每个线程新开一个事务
                        DefaultTransactionDefinition defGo = new DefaultTransactionDefinition();
                        defGo.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                        TransactionStatus statusGo = transactionManager.getTransaction(defGo);
                        int curInt = cur.getAndIncrement();
                        try {
                            log.info("当前是第{}个线程开始启动，线程名={}", curInt, Thread.currentThread().getName());
                            //处理加入到任务
                            //baseInfoService.getBaseMapper().batchSchedule(addPartitionList);

                            ArrayList<SRoadTaskPatients> sRoadTaskPatients = new ArrayList<>();
                            List<SRoadTaskPresMapping> sRoadTaskPresMappingArrayList = new ArrayList<>();
                            List<SRoadTaskWestMapping> sRoadTaskPresMappingArrayList2 = new ArrayList<>();
                            List<SRoadTaskHandleMapping> sRoadTaskPresMappingArrayList3 = new ArrayList<>();

                            for (int i = 0; i < addPartitionList.size(); i++) {
                                RoadEventTaskSchedulerRes roadEventTaskSchedulerRes = addPartitionList.get(i);
                                UpdateOrInsertRoad roadDetailById = webRoadService.getRoadDetailById(addPartitionList.get(i).getSRoadId());

                                if (StringUtils.isNotBlank(roadEventTaskSchedulerRes.getPrescriptionsId())) {
                                    SRoadTaskPresMapping sRoadTaskPresMapping = new SRoadTaskPresMapping();
                                    sRoadTaskPresMapping.setSRoadTaskId(roadEventTaskSchedulerRes.getSRoadTaskId());
                                    sRoadTaskPresMapping.setPatientId(roadEventTaskSchedulerRes.getPatientId());
                                    sRoadTaskPresMapping.setPrescriptionsId(roadEventTaskSchedulerRes.getPrescriptionsId());
                                    sRoadTaskPresMappingArrayList.add(sRoadTaskPresMapping);
                                }
                                if (StringUtils.isNotBlank(roadEventTaskSchedulerRes.getWestPrescriptionsId())) {
                                    SRoadTaskWestMapping sRoadTaskPresMapping = new SRoadTaskWestMapping();
                                    sRoadTaskPresMapping.setSRoadTaskId(roadEventTaskSchedulerRes.getSRoadTaskId());
                                    sRoadTaskPresMapping.setPatientId(roadEventTaskSchedulerRes.getPatientId());
                                    sRoadTaskPresMapping.setWestPrescriptionsId(roadEventTaskSchedulerRes.getWestPrescriptionsId());
                                    sRoadTaskPresMappingArrayList2.add(sRoadTaskPresMapping);
                                }
                                if (StringUtils.isNotBlank(roadEventTaskSchedulerRes.getHandleRecordId())) {
                                    SRoadTaskHandleMapping sRoadTaskPresMapping = new SRoadTaskHandleMapping();
                                    sRoadTaskPresMapping.setSRoadTaskId(roadEventTaskSchedulerRes.getSRoadTaskId());
                                    sRoadTaskPresMapping.setPatientId(roadEventTaskSchedulerRes.getPatientId());
                                    sRoadTaskPresMapping.setHandleRecordId(roadEventTaskSchedulerRes.getHandleRecordId());
                                    sRoadTaskPresMappingArrayList3.add(sRoadTaskPresMapping);
                                }

                                List<SRoadExecute> sRoadExecuteList = roadDetailById.getSRoadExecuteList();
                                for (int i1 = 0; i1 < sRoadExecuteList.size(); i1++) {
                                    SRoadExecute sRoadExecute = sRoadExecuteList.get(i1);
                                    //排除路径事件是2的：就诊后
                                    if (sRoadExecute.getRoadExecuteEvent().equals(Constant.BASIC_STRING_TWO)) {
                                        continue;
                                    }
                                    SRoadTaskPatients roadPatients = new SRoadTaskPatients();
                                    BeanUtils.copyProperties(sRoadExecute, roadPatients);
                                    List<SRoadExecuteContents> sRoadExecuteContentsList = sRoadExecute.getSRoadExecuteContentsList();
                                    for (int i2 = 0; i2 < sRoadExecuteContentsList.size(); i2++) {
                                        SRoadExecuteContents executeContents = sRoadExecuteContentsList.get(i2);
                                        BeanUtils.copyProperties(executeContents, roadPatients);
                                        roadPatients.setPatientId(roadEventTaskSchedulerRes.getPatientId());
                                        roadPatients.setPatientAge(roadEventTaskSchedulerRes.getPatientAge());
                                        roadPatients.setPatientSex(roadEventTaskSchedulerRes.getPatientSex());
                                        roadPatients.setPatientName(roadEventTaskSchedulerRes.getPatientName());
                                        roadPatients.setRecordsId(roadEventTaskSchedulerRes.getRecordsId());
                                        roadPatients.setPatientCardNumber(roadEventTaskSchedulerRes.getPatientCardNumber());

                                        roadPatients.setAppId(roadEventTaskSchedulerRes.getAppId());
                                        roadPatients.setInsCode(roadEventTaskSchedulerRes.getInsCode());
                                        roadPatients.setInsId(roadEventTaskSchedulerRes.getInsId());
                                        roadPatients.setInsName(roadEventTaskSchedulerRes.getInsName());
                                        roadPatients.setInsCode(roadEventTaskSchedulerRes.getInsCode());
                                        roadPatients.setDeptId(roadEventTaskSchedulerRes.getDeptId());
                                        roadPatients.setDeptName(roadEventTaskSchedulerRes.getDeptName());
                                        roadPatients.setDeptCode(roadEventTaskSchedulerRes.getDeptCode());
                                        roadPatients.setDeptId(roadEventTaskSchedulerRes.getDeptId());

                                        roadPatients.setStatus("0");
                                        roadPatients.setTaskName(roadEventTaskSchedulerRes.getTaskName());
                                        roadPatients.setSRoadTaskId(roadEventTaskSchedulerRes.getSRoadTaskId());
                                        roadPatients.setTaskExcuteStatus(1);
                                        roadPatients.setRoadExecuteEventTime(sRoadExecute.getRoadExecuteTime());
                                        roadPatients.setHandSend("0");
                                        roadPatients.setDoctorId(roadEventTaskSchedulerRes.getCreateUserId());
                                        roadPatients.setDoctorName(roadEventTaskSchedulerRes.getCreateUserName());
                                        try {
                                            String prescriptionNum = roadEventTaskSchedulerRes.getPrescriptionNum();
                                            if (StringUtils.isNotBlank(prescriptionNum) && StringUtils.isNumeric(prescriptionNum)) {
                                                if (sRoadTaskPatientsOtherService.checkJIuZhenHouTimeIsArrived(roadPatients, Integer.parseInt(prescriptionNum))) {
                                                    roadPatients.setTaskExcuteStatus(2);
                                                }
                                            } else {
                                                if (sRoadTaskPatientsOtherService.checkJIuZhenHouTimeIsArrived(roadPatients, 0)) {
                                                    roadPatients.setTaskExcuteStatus(2);
                                                }
                                            }

                                        } catch (ParseException e) {
                                            throw new RuntimeException(e);
                                        }
                                        sRoadTaskPatients.add(roadPatients);


                                    }

                                }


                            }

                            if (sRoadTaskPatients.size() > 0) {
                                sRoadTaskPatientsMapper.insertList(sRoadTaskPatients);

                                ArrayList<SRoadTaskPatientsPhone> sRoadTaskPatientsPhones = new ArrayList<>();
                                for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                                    if (sRoadTaskPatients1.getRoadExecuteEventWay().equals(Constant.BASIC_STRING_THREE) &&

                                            (sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_TWO) ||
                                                    sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_FOUR)
                                            )
                                    ) {
                                        sRoadTaskPatientsPhones.add(sRoadTaskPatientsOtherService.newSRoadTaskPatientsPhone(sRoadTaskPatients1));

                                    }
                                }
                                if (sRoadTaskPatientsPhones.size() > 0) {
                                    sRoadTaskPatientsPhoneMapper.insertList(sRoadTaskPatientsPhones);

                                    for (SRoadTaskPatientsPhone sRoadTaskPatients1 : sRoadTaskPatientsPhones) {
                                        if (sRoadTaskPatients1.getPhoneStatus() == 1) {
                                            redisService.putTaskWait1("2", sRoadTaskPatients1.getSRoadTaskPatientsPhoneId() + "", sRoadTaskPatients1.getSuiFangTime());
                                        }

                                    }
                                }
                                for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                                    // if (sRoadTaskPatients1.getTaskExcuteStatus() == 1) {

                                    redisService.putTaskWait1("1", sRoadTaskPatients1.getTaskPatientsId() + "", sRoadTaskPatients1.getTaskExcuteTime());
                                    //}

                                }


                            }
                            //设置 表: s_road_task_pres_mapping
                            if (sRoadTaskPresMappingArrayList.size() > 0) {
                                sRoadTaskPresMappingMapper.insertList(sRoadTaskPresMappingArrayList);
                            }
                            if (sRoadTaskPresMappingArrayList2.size() > 0) {
                                sRoadTaskWestMappingMapper.insertList(sRoadTaskPresMappingArrayList2);
                            }
                            if (sRoadTaskPresMappingArrayList3.size() > 0) {
                                sRoadTaskHandleMappingMapper.insertList(sRoadTaskPresMappingArrayList3);
                            }
                            //不正常结束，就一直锁着
                            connentCount.decrementAndGet();


                            log.info("当前是第{}个线程完成批量插入，开始加入等待队列，线程名={}", curInt, Thread.currentThread().getName());
                            //ArrayList线程不安全，多线程会出现数据覆盖，体现为数据丢失
                            synchronized (unfinishedList) {
                                unfinishedList.add(Thread.currentThread());
                            }
                            log.info("当前是第{}个线程已加入队列，开始休眠，线程名={}", curInt, Thread.currentThread().getName());
                            notifyAllThread(unfinishedList, totalSize, false);
                            LockSupport.park();
                            if (isSuccess.get()) {
                                log.info("当前是第{}个线程提交，线程名={}", curInt, Thread.currentThread().getName());
                                transactionManager.commit(statusGo);
                            } else {
                                log.info("当前是第{}个线程回滚，线程名={}", curInt, Thread.currentThread().getName());
                                transactionManager.rollback(statusGo);
                            }
                        } catch (Exception e) {
                            log.error("当前是第{}个线程出现异常，线程名={}", curInt, Thread.currentThread().getName(), e);
                            transactionManager.rollback(statusGo);
                            isSuccess.set(false);
                            notifyAllThread(unfinishedList, totalSize, true);
                        }
                    }, threadPoolTaskScheduler.getExecutor())).toArray(CompletableFuture[]::new));
            future.join();
        }
    }

    private void notifyAllThread(List<Thread> unfinishedList, int totalSize, boolean isForce) {
        if (isForce || unfinishedList.size() >= totalSize) {
            log.info("唤醒当前所有休眠线程，线程数={}，总线程数={},是否强制={}", unfinishedList.size(), totalSize, isForce);
            for (Thread thread : unfinishedList) {
                log.info("当前线程={}被唤醒", thread.getName());
                LockSupport.unpark(thread);
            }
        }
    }

}
