package com.cbkj.diagnosis.common.scheduler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.common.openfeign.OCRModelClient;
import com.cbkj.diagnosis.common.openfeign.reqAndres.OCRModelRes;
import com.cbkj.diagnosis.common.openfeign.reqAndres.OcrResponseDTO;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.LocalMultipartFile;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.mapper.business.TRecordDiaMapper;
import com.cbkj.diagnosis.mapper.business.TRecordMapper;
import com.cbkj.diagnosis.service.TPropagandaEduService;
import com.cbkj.diagnosis.service.business.TRecordDiaImageService;
import com.cbkj.diagnosis.service.sysService.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/5 13:44
 * @Version 1.0
 */

@Slf4j
@Component
public class TaskListener {
    @Value("${file.address}")
    private String location;
    @Value("${ocr.api.token}")
    private String ocrApiToken;

    @Value("${root.preview}")
    private String preview;
    private final RedisService redisService;

    private final TRecordDiaMapper tRecordDiaMapper;
    private final TRecordMapper tRecordMapper;

    private final TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;

    private final TPropagandaEduService tPropagandaEduService;

    private final TRecordDiaImageService tRecordDiaImageService;

    private final OCRModelClient ocrModelClient;

    public TaskListener(RedisService redisService, TRecordDiaMapper tRecordDiaMapper, TRecordMapper tRecordMapper, TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper, TPropagandaEduService tPropagandaEduService, TRecordDiaImageService tRecordDiaImageService, OCRModelClient ocrModelClient) {
        this.redisService = redisService;
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.tRecordMapper = tRecordMapper;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.tPropagandaEduService = tPropagandaEduService;
        this.tRecordDiaImageService = tRecordDiaImageService;
        this.ocrModelClient = ocrModelClient;
    }


    public Integer task() {
        log.info("开始执行任务");
        String andRemoveBusiness = redisService.getAndRemoveBusiness();
        //todo 处理 andRemoveBusiness
        String recId = andRemoveBusiness.split(":")[0];
        TRecord objectById = tRecordMapper.getObjectById(recId);
        if (null == objectById) {
            return 1;
        }
        String patientId = objectById.getPatientId();
        List<TRecordDia> objectByRecId = tRecordDiaMapper.getObjectByRecId(recId);

        if (objectByRecId.isEmpty()) {
            return 1;
        }

        ArrayList<String> stringArrayList = new ArrayList<>();
        for (TRecordDia questionMain : objectByRecId) {
            List<String> strings = JSON.parseArray(questionMain.getOptionIds(), String.class);
            stringArrayList.addAll(strings);
        }
        if (stringArrayList.isEmpty()) {
            return 1;
        }
        List<HashMap<String, String>> hashMaps = tPreDiagnosisQuestionMapper.getfollowUpVisitIdListByQuestionId(stringArrayList);
        if (hashMaps.isEmpty()) {
            return 1;
        }
        hashMaps.forEach(hashMap -> {
            EduSendMessage eduSendMessage = new EduSendMessage();
            eduSendMessage.setEduTitle(hashMap.get("eduTitle"));
            eduSendMessage.setTPropagandaEduId(Integer.valueOf(hashMap.get("followUpVisitId")));
            eduSendMessage.setRoadExecuteEventType(Constant.BASIC_STRING_THREE);
            TAdminInfo tAdminInfo = new TAdminInfo();
            tAdminInfo.setUserId(patientId);
            eduSendMessage.setPatientList(Collections.singletonList(tAdminInfo));
            tPropagandaEduService.sendMessage(eduSendMessage);
        });
        //todo 业务处理结束 返回 1
        log.info("结束执行任务");
        return 1;

    }

    public Integer task2() {
        log.info("开始执行任务");

        String andRemoveAiImage = redisService.getAndRemoveAiImage();

//根据recId获取数据
        QueryWrapper<TRecordDiaImage> recId = new QueryWrapper<TRecordDiaImage>();
        recId.eq("rec_id", andRemoveAiImage);
        List<TRecordDiaImage> list = tRecordDiaImageService.list(recId);
        if (!list.isEmpty()) {
            list.stream().forEach(tRecordDiaImage -> {
                String imageUrl = tRecordDiaImage.getImageUrl();
                //把 imageUrl 中的 preview 替换成 location
                imageUrl = imageUrl.replace(preview, location);
                File dest = new File(imageUrl);
                //转成 MultiPartFile
                try {
                    LocalMultipartFile file = new LocalMultipartFile("file", dest);
                    OCRModelRes ocrModelRes = ocrModelClient.ocrImageUpload(file, "admin-admin", ocrApiToken);
                    if (ocrModelRes == null){
                        log.error("上传图片到dify服务失败recId={}", andRemoveAiImage);
                        return;
                    }
                    tRecordDiaImage.setAiUrl(ocrModelRes.getSource_url());
                    //调用另外一个dify接口获取识别图片的内容
                    HashMap<String, Object> map = getStringObjectHashMap(ocrModelRes.getSource_url());
                    OcrResponseDTO ocrResponseDTO = ocrModelClient.ocrResult(map, ocrApiToken);
                    if (ocrResponseDTO == null){
                        log.error("调用dify接口获取图片内容失败recId={}", andRemoveAiImage);
                        log.error("调用dify接口获取图片内容失败recId response={}", JSON.toJSONString(ocrResponseDTO));
                        return;
                    }
                    String summary = null;
                    try {
                         summary = ocrResponseDTO.getData().getOutputs().getResult().get(0).getResult().getSummary();
                    }catch (Exception e){
                        log.error("调用dify接口获取图片Summary字段内容失败recId={}", andRemoveAiImage);
                        log.error("调用dify接口获取图片Summary字段内容容失败 response={}", JSON.toJSONString(ocrResponseDTO));
                        return;
                    }
                    tRecordDiaImage.setAiContent(summary);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }

            });
            tRecordDiaImageService.updateBatchById(list);
        }


        return 1;
    }

    private static HashMap<String, Object> getStringObjectHashMap(String aiUrl) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("response_mode", "blocking");
        map.put("user", "admin-admin");
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("document_url", aiUrl);
        map.put("inputs", objectObjectHashMap);
        return map;
    }

    @EventListener(DiagnosisApiApplication.class)
    public void startTaskThread() {
        new Thread(() -> {
            while (true) {
                try {
                    Integer result = task();
                    if (result != 1) {
                        //直接退出不要继续这个线程
                        break;
                    }
                } catch (Exception e) {
                    log.error("task-thread-error. {}", e.getMessage());
                    break;
                }
            }
        }, "task-thread").start();
    }

    @EventListener(DiagnosisApiApplication.class)
    public void startAiImageThread() {
        new Thread(() -> {
            while (true) {
                try {
                    Integer result = task2();
                    if (result != 1) {
                        //直接退出不要继续这个线程
                        break;
                    }
                } catch (Exception e) {
                    log.error("task-thread-error. {}", e.getMessage());
                    break;
                }
            }
        }, "task-thread").start();
    }
}
