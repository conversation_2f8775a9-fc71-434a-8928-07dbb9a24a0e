package com.cbkj.diagnosis.common.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@EnableScheduling
public class RoadEventTaskScheduler {


    private final RoadEventTaskSchedulerBatch roadEventTaskSchedulerBatch;
    private final RoadEventSendTaskSchedulerBatch roadEventSendTaskSchedulerBatch;


    public RoadEventTaskScheduler(
            RoadEventTaskSchedulerBatch roadEventTaskSchedulerBatch, RoadEventSendTaskSchedulerBatch roadEventSendTaskSchedulerBatch) {
        this.roadEventTaskSchedulerBatch = roadEventTaskSchedulerBatch;
        this.roadEventSendTaskSchedulerBatch = roadEventSendTaskSchedulerBatch;
    }

    /**
     * 处理中医、西医、适宜技术
     * 每晚十一点执行，放入缓存。由下面的定时任务去看 是否看执行。
     */
//    @Scheduled(cron = "0 0/1 * * * ?")
    @Scheduled(cron = "0 0 23 * * ?")
    public void task() {
        //使用CompletableFuture默认的线程池ForkJoinPool
        roadEventTaskSchedulerBatch.batchSchedule();
    }



    /**
     * 处理除了电话随访任务外的其它任务是否到应该推送了。
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void taskSend() {

        roadEventSendTaskSchedulerBatch.batchSchedule();
    }



}
