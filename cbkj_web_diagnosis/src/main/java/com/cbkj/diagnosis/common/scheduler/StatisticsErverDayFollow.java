package com.cbkj.diagnosis.common.scheduler;

import com.cbkj.diagnosis.common.utils.DateUtil;
//import com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayDiagnosisMapper;
import com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayFollowMapper;
//import com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayHealthMapper;
import com.cbkj.diagnosis.beans.statistics.PastSevenDaysRe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@EnableScheduling
@Transactional(rollbackFor = Exception.class)
public class StatisticsErverDayFollow {


    private final StatisticsErverDayFollowMapper statisticsErverDayFollowMapper;
//    private final StatisticsErverDayDiagnosisMapper statisticsErverDayDiagnosisMapper;
//    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;
//    private final StatisticsErverDayHealthMapper statisticsErverDayHealthMapper;

    public StatisticsErverDayFollow(StatisticsErverDayFollowMapper statisticsErverDayFollowMapper
//            , StatisticsErverDayDiagnosisMapper statisticsErverDayDiagnosisMapper, SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper, StatisticsErverDayHealthMapper statisticsErverDayHealthMapper
    ) {
        this.statisticsErverDayFollowMapper = statisticsErverDayFollowMapper;
//        this.statisticsErverDayDiagnosisMapper = statisticsErverDayDiagnosisMapper;
//        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
//        this.statisticsErverDayHealthMapper = statisticsErverDayHealthMapper;
    }

    /**
     * 统计昨天一天的随访和预诊人数
     */
    @Scheduled(cron = "0 5 0 * * ?")
    public void statisticsEverDayFollowData() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        PastSevenDaysRe pastSevenDaysRe = new PastSevenDaysRe();
        pastSevenDaysRe.setStartDate(simpleDateFormat.format(DateUtil.getNextDate(-1)));
        statisticsErverDayFollowMapper.insertStatisticsEverDayFollowDataPage(pastSevenDaysRe);
        /**
         * 统计昨日随访完成数量，不从任务表取，从实际的随访记录表取。
         */
//        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        pastSevenDaysRe.setStartDate2(simpleDateFormat2.format(DateUtil.getNextDate(-1)));
//        pastSevenDaysRe.setEndDate(simpleDateFormat2.format(DateUtil.getNextDate(0)));
//        statisticsErverDayFollowMapper.insertStatisticsEverDayFollowDataPage(pastSevenDaysRe);

        /**
         * 统计昨日预诊完成数量，不从任务表取，从实际的随访预诊记录表取。
         */
//        statisticsErverDayDiagnosisMapper.insertStatisticsEverDayData(pastSevenDaysRe);
        /**
         * 健康宣教
         */
        //statisticsErverDayHealthMapper.insertStatisticsEverDayData(pastSevenDaysRe);
    }


}
