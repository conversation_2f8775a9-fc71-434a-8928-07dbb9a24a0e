package com.cbkj.diagnosis.common.config;


import org.flywaydb.core.Flyway;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/17 16:46
 * @Version 1.0
 */
@Configuration
public class FlywayConfig {
    @Bean
    public Flyway flyway(DataSource dataSource) {
        Flyway flyway = Flyway.configure()
                .dataSource(dataSource)
                .locations("classpath:dbUp")  // 指定迁移脚本的位置
                .baselineOnMigrate(true)  // 指定迁移脚本的位置
                .baselineVersion("1.1")  // baseline-version
                .validateOnMigrate(false) //禁用临时校验
                .outOfOrder(true)
                .load();
        flyway.repair();
        flyway.migrate(); // 自动执行迁移
        return flyway;
    }
}
