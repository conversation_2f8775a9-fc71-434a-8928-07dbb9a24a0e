package com.cbkj.diagnosis.common.config.security;


import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * SecurityVo
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/7/12
 */
@Data
@NoArgsConstructor
public class TokenBo implements Serializable {

    /**
     * tokenKey
     */
    private String tokenKey;

    /**
     * authorization
     */
    private String authorization;

    /**
     * 医联体ID
     */
    private String adminId;


    /**
     * 医生
     */
    private AdminInfo admin;

    private Set<String> rolesMenuStr;


    /**
     * 上次请求时间
     */
    private Date lastRequestTime;

    public TokenBo(String tokenKey, String authorization, String adminId) {
        this.tokenKey = tokenKey;
        this.authorization = authorization;
        this.adminId = adminId;

    }

    public TokenBo(String tokenKey, String authorization, String adminId,  AdminInfo admin,Set<String> menuSetByUid,Date lastRequestTime) {
        this.tokenKey = tokenKey;
        this.authorization = authorization;
        this.adminId = adminId;
        this.admin = admin;
        this.rolesMenuStr = menuSetByUid;
        this.lastRequestTime = lastRequestTime;
    }

    public TokenBo(String tokenKey, String adminId,  AdminInfo admin) {
        this.tokenKey = tokenKey;
        this.adminId = adminId;
        this.admin = admin;
    }
}
