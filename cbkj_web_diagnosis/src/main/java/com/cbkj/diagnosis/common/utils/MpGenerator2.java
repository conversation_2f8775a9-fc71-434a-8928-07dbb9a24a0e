package com.cbkj.diagnosis.common.utils;


import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

/**
 * <p>
 * 代码生成器演示
 * </p>
 */
public class MpGenerator2 {
	/**
	 * <p>
	 * MySQL 生成演示
	 * </p>
	 */
	public static void main(String[] args) {
		// 代码生成器
		new AutoGenerator(new DataSourceConfig
				//.Builder("**************************************************************************************************",
				//.Builder("*******************************************************************************************************",
				.Builder("**********************************************************************************************************************************************************",
				"root", "qb123456")
				.build()
		)
				.global(new GlobalConfig.Builder()
						.outputDir("D://git_workspace//pre_diagnosis//cbkj_diagnosis_main")
						.author("zjh")
//						.enableSwagger() //自动swagger注解

						.dateType(DateType.ONLY_DATE)
						.fileOverride()
						.build()
				)
				.packageInfo(new PackageConfig.Builder()// 包配置
						.parent("com.cbkj.diagnosis")
						.controller("controller")
						.entity("beans.business")
						.service("service.business")
						.serviceImpl("service.business.impl")
						.mapper("mapper.business")
						.xml("resources.mapper.business")
						.build()
				)
				.strategy(new StrategyConfig.Builder()// 策略配置
						//.enableCapitalMode()// 全局大写命名
						//.addExclude("databasechangelog", "databasechangeloglock")// 排除生成的表 sys_user
						.addInclude("t_pre_diagnosis_question_range") // 对应表 ntc_dictionary_info
						//.addTablePrefix("cpt_") // 表前缀
						//.addFieldPrefix("tb_") // 字段前缀
						.enableSkipView() //跳过视图

						.controllerBuilder()
						.enableRestStyle() //@RestController 注解
						//.enableHyphenStyle() //url中驼峰转连字符
						//.superClass(IBaseController.class)
						.formatFileName("%sController")

						.entityBuilder()
						.enableLombok()// lombok 模型
						//.enableChainModel() // 链式操作
						//.enableSerialVersionUID()
						//.enableRemoveIsPrefix() 去掉字段前边的is
						//.superClass(BaseEntity.class)
						//.addSuperEntityColumns("id", "createDate", "createId", "updateDate", "updateId") // 自定义实体，公共字段
						.naming(NamingStrategy.underline_to_camel)
						.enableTableFieldAnnotation()

						.serviceBuilder()
						.formatServiceFileName("%sService")
						.formatServiceImplFileName("%sServiceImpl")

						.mapperBuilder()
						.formatMapperFileName("%sMapper")
						.formatXmlFileName("%sMapper")
						.enableBaseResultMap()
						.enableBaseColumnList()
						.build()
				)
				.template(new TemplateConfig.Builder()
						// .entity("templates\\entity\\entity.java.vm")
						// .controller("templates\\controller\\controller.java.vm")
						// .service(
						//         "templates\\service\\service.java.vm",
						//         "templates\\service\\impl\\serviceImpl.java.vm"
						// )
						// .mapper("templates\\persistent\\mapper.java.vm")
						// .mapperXml("templates\\persistent\\xml\\mapper.xml.vm")
						.build()
				)
				.execute(new FreemarkerTemplateEngine());
	}
}