package com.cbkj.diagnosis.common.config.security;

import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminMenu;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.mapper.sysmapper.AdminMenuMapper;
import com.cbkj.diagnosis.service.sysService.AdminRuleService;
import com.cbkj.diagnosis.service.sysService.RedisService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Clock;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.DefaultClock;
import io.jsonwebtoken.impl.TextCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019年12月13日 14:35:00
 */
@Component
public class TokenUtil implements Serializable {
    private static final long serialVersionUID = -3301605591108950415L;

    @Resource
    private RedisService redisService;

    @Resource
    private AdminRuleService adminRuleService;
    private final AdminMenuMapper adminMenuMapper;

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private Long expiration;

    private Clock clock = DefaultClock.INSTANCE;

    public static final String TOKEN_PREFIX = "PRE_AI_TOKEN_";

    @Value("${login.one}")
    private boolean loginOne;

    @Autowired
    public TokenUtil(AdminMenuMapper adminMenuMapper) {
        this.adminMenuMapper = adminMenuMapper;
    }

    //    public String createTokenKey(String adminId, String appId, String insCode) {
//        return TOKEN_PREFIX + adminId + "_" + appId + "_" + insCode;
//    }
    public String createTokenKey(String adminId) {
        return TOKEN_PREFIX + adminId;
    }

    /**
     * 创建Token
     *
     * @param registerId registerId
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
//    public TokenBo createTokenBo(String registerId) {
//
//        //TRegister register = redisService.getRegisterById(registerId);
//        String tokenKey = createTokenKey(null, null, null) + "_" + registerId;
//        String authorization = generateToken(tokenKey);
////        String tokenKey = TOKEN_PREFIX + register.getDoctorId() + "_" + register.getAppId() + "_" + register.getInsCode() + "_" + registerId;
//        TokenBo tokenBo = new TokenBo(tokenKey, authorization, (String) null, (String) null, (String) null);
//        tokenBo.setRegister(register);
//        AdminInfo admin = register.getAdminInfo();
//        if (admin == null) {
//            admin = redisService.loadUserByUserId(register.getDoctorId());
//        }
//        tokenBo.setAdmin(admin);
//        tokenBo.setPractice(redisService.loadAdminPracticeByUserId(register.getDoctorId(), register.getAppId(), register.getInsCode()));
//
//        redisService.putToken(tokenKey, tokenBo);
//        return tokenBo;
//    }
    public Set<String> getMenuSetByUid(String uid) {
        Set<String> menuSet = new HashSet<String>();
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("menuTypes", "1");
        //共三级菜单，不要第一级菜单
        List<AdminMenu> lisM = adminMenuMapper.getMenuByPID(params);
        if (null != lisM) {
            for (AdminMenu menu : lisM) {
                menuSet.add(menu.getMenuId());
            }
            return menuSet;
        }
        return new HashSet<>();
    }

    /**
     * 创建token
     *
     * @param admin
     * @param practice
     * @return
     */
    public TokenBo createTokenBo(AdminInfo admin) {
        //获取这个人所有角色信息。
        List<AdminRule> adminRuleInfo = adminRuleService.getAdminRuleInfo(admin.getUserId());
        admin.setRoles(adminRuleInfo);
        String tokenKey = createTokenKey(admin.getUserId());
        String authorization = generateToken(tokenKey);
        Set<String> menuSetByUid = getMenuSetByUid(admin.getUserId());
        TokenBo tokenBo = new TokenBo(tokenKey, authorization, admin.getUserId(), admin, menuSetByUid,new Date());

        redisService.putToken(tokenKey, tokenBo);

        setSecurityContext(tokenBo);
        return tokenBo;
    }

    /**
     * 更新Token
     *
     * @param tokenBo tokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public void updateTokenBo(TokenBo tokenBo) {

        redisService.putToken(tokenBo.getTokenKey(), tokenBo);
    }

    /**
     * 更新Token
     *
     * @param tokenBo    tokenBo
     * @param registerId registerId
     * <AUTHOR>
     * @date 2021/7/12
     */
    public void updateTokenBo(TokenBo tokenBo, String registerId) {

//        tokenBo.setRegister(redisService.getRegisterById(registerId));
//
//        redisService.putToken(tokenBo.getTokenKey(), tokenBo);
    }

    /**
     * 获取Token
     *
     * @param tokenKey tokenKey
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public TokenBo getSecurityRegister(String tokenKey) {
        return redisService.getToken(tokenKey);
    }


    /**
     * 登录信息交给Security管理
     *
     * @param tokenBo tokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public void setSecurityContext(TokenBo tokenBo) {
        if (tokenBo != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            AdminInfo admin = tokenBo.getAdmin();
            UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(tokenBo, null, admin.getAuthorities());

            SecurityContextHolder.getContext().setAuthentication(auth);
        }
    }



    public String generateToken(String key) {
        return doGenerateToken(new HashMap<>(), key);
    }

    private String doGenerateToken(Map<String, Object> claims, String subject) {

        final Date createdDate = clock.now();
        final Date expirationDate = calculateExpirationDate(createdDate);
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(createdDate)
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    private Date calculateExpirationDate(Date createdDate) {
        return new Date(createdDate.getTime() + expiration);
    }

    public String getKeyFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret).setAllowedClockSkewSeconds(0)
                .parseClaimsJws(token)
                .getBody();
    }

    private Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(clock.now());
    }

    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    public Map<String, Object> getKeyAndExp(String token) {
        Map<String, Object> map = new HashMap<>();
        String base64UrlEncodedPayload = token.split("\\.")[1];
        String payload = TextCodec.BASE64URL.decodeToString(base64UrlEncodedPayload);
        if (payload.charAt(0) == '{' && payload.charAt(payload.length() - 1) == '}') {
            JSONObject object = JSONObject.parseObject(payload);
            Object sub = object.get("sub");
            Object exp = object.get("exp");
            Object iat = object.get("iat");
            map.put("key", sub);
            map.put("expirationTime", ((Number) exp).longValue());
            map.put("loginTime", ((Number) iat).longValue());

        }
        return map;
    }

    public boolean getLoginOne() {
        return loginOne;
    }

    public void deleteToken(String tokenKey) {
        redisService.removeKey(tokenKey);
    }
}
