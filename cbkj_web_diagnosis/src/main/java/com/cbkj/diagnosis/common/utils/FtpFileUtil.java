package com.cbkj.diagnosis.common.utils;

import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileInputStream;
import java.io.InputStream;

/**
 * ftp 文件上传下载
 */
@UtilityClass
public class FtpFileUtil {

    @Autowired
    private FTPClient ftpClient;

    /**
     * Description: 向FTP服务器上传文件
     *
     * @param url      FTP服务器hostname
     * @param port     FTP服务器端口
     * @param username FTP登录账号
     * @param password FTP登录密码
     * @param path     FTP服务器保存目录,如果是根目录则为“/”
     * @param filename 上传到FTP服务器上的文件名
     * @param input    本地文件输入流
     * @return 成功返回true，否则返回false
     * @Version1.0
     */
    @SneakyThrows
    public boolean uploadFile(String url, int port, String username, String password, String path, String filename, InputStream input) {

        FTPClient ftpClient = new FTPClient();
        ftpClient.setControlEncoding(Constant.ENCODED);
        ftpClient.connect(url, port);
        ftpClient.login(username, password);

        int replyCode = ftpClient.getReplyCode();
        ftpClient.setDataTimeout(120000);
        ftpClient.setFileType(FTP.BINARY_FILE_TYPE);//设置为二进制文件

        if (!FTPReply.isPositiveCompletion(replyCode)) {
            ftpClient.disconnect();
            System.out.println("FTP连接失败");
        } else {
            System.out.println("FTP连接成功");
        }
        //本地文件流
        InputStream in = new FileInputStream("F://love.txt");
        //指定写入目录,注意:假如指定的目录不存在,会直接上传到根目录,假如存在，就上传到指定路径
//		ftpClient.changeWorkingDirectory("test2");
        //远程文件名
        String removePath = new String("./test2/love8.txt".getBytes(Constant.ENCODED), "iso-8859-1");
        //上传
        ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
        if (ftpClient.storeFile(removePath, in)) {
            System.out.println("文件上传成功");
        } else {
            System.out.println("文件上传失败");
        }
        //关闭文件流
        in.close();
        //关闭连接
        if (ftpClient != null) {
            ftpClient.logout();
            ftpClient.disconnect();
        }
        return false;
    }

}