package com.cbkj.diagnosis.common.config;

import com.cbkj.diagnosis.common.config.security.AuthorizationTokenFilter;
import com.cbkj.diagnosis.common.config.security.TokenUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 *
 * <AUTHOR>
 */
@Configuration
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    @Value("${root.preview}")
    private String preview;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TokenUtil tokenUtil;

    @Value("${jwt.token}")
    private String tokenHeader;

    @Value("${jwt.expirationExt}")
    private Long expirationExt;


    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers("/login","/public/getBanner","/randomImage/**","/doc.html", "/swagger-ui.html", "/swagger-ui.html/**", "/webjars/**",
                "/swagger-resources/**", "/barcode/**", "/v2/api-docs","/his/**",
                 "/error/**", "/noAuth/**", "/" + preview + "**",
                //"/pre/questionnaire/download/questionnaire",
                "/his/**","/sys/help/download"
                //,"/suifang/questionnaire/download/questionnaire"
                ,"/block/staticsHistory","/swagger-ui.html","/swagger-ui/**","/v3/**"
//                ,"/test","test2","/diagnosis/aiSymptomAnalysis/getAISymptomAnalysis"
        );
    }

//    @Override
//    protected void configure(HttpSecurity http) throws Exception {
//
////        http.headers().frameOptions().disable();
//
//        http.cors().and().csrf().disable().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
//                .and().authorizeRequests().anyRequest().authenticated()
//                .and().addFilterAfter(getAuthorizationTokenFilter(), UsernamePasswordAuthenticationFilter.class);
//    }
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.cors() // 配置CORS
                .and()
                .csrf().disable() // 禁用CSRF保护
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS) // 无状态会话管理
                .and()
                .authorizeRequests().anyRequest().authenticated() // 所有请求都需要认证
                .and()
                .addFilterAfter(getAuthorizationTokenFilter(), UsernamePasswordAuthenticationFilter.class) // 添加自定义过滤器
                .headers() // 配置HTTP头
                .httpStrictTransportSecurity() // 配置HSTS
                .maxAgeInSeconds(16070400) //
                .includeSubDomains(true) // 包括子域名
                .preload(true)
                .and()
                .xssProtection()
                .block(true)// 启用XSS保护并阻止页面渲染
                .and()
                .contentSecurityPolicy("default-src 'self'");
//                .and()
//                .frameOptions()
//                .deny(); // 允许预加载
//        ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry expressionInterceptUrlRegistry = http.authorizeRequests();
//        expressionInterceptUrlRegistry.antMatchers("").hasAuthority("2");
    }

    private AuthorizationTokenFilter getAuthorizationTokenFilter() {
        return new AuthorizationTokenFilter(tokenUtil, objectMapper, tokenHeader, expirationExt);
    }
}