package com.cbkj.diagnosis.common.exception;


import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


/**
 * 1、新建一个Class,这里取名为GlobalDefaultExceptionHandler
 * 2、在class上添加注解，@ControllerAdvice;
 * 3、在class中添加一个方法
 * 4、在方法上添加@ExcetionHandler拦截相应的异常信息；
 * 5、如果返回的是View -- 方法的返回值是ModelAndView;
 * 6、如果返回的是String或者是Json数据，那么需要在方法上添加@ResponseBody注解.
 */
@ControllerAdvice
public class GlobalDefaultExceptionHandler {


    /**
     * 全局日志 UnknownAccountException
     */
    private Logger logger = LoggerFactory.getLogger(GlobalDefaultExceptionHandler.class);


    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResEntity defaultExceptionHandler(HttpServletRequest req, Exception e) {
        //BaseResp baseResp = new BaseResp();
        logger.warn("==============全局异常开始========================================================");
        e.printStackTrace();
        String url = req.getRequestURL().toString();
        String method = req.getMethod();
        String uri = req.getRequestURI();
        String queryString = req.getQueryString();
        logger.error(String.format("请求参数, url: %s, method: %s, uri: %s, params: %s", url, method, uri, queryString));
        if (e.getMessage() == null && e.getCause() != null && e.getCause().getMessage() != null) {
            logger.error(e.getCause().getMessage());
            return ResEntity.error(e.getCause().getMessage());
        } else {

            logger.error(e.getMessage());
            return ResEntity.error("系统错误"+e.getMessage());
        }
    }

}
