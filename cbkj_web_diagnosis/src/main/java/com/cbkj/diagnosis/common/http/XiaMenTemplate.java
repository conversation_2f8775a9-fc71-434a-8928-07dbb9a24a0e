package com.cbkj.diagnosis.common.http;

import com.alibaba.fastjson.JSONObject;
import com.cbkj.diagnosis.beans.SysLogInterface;
import com.cbkj.diagnosis.common.utils.IDUtil;
import com.cbkj.diagnosis.utils.LogAsync;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import javax.xml.soap.*;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * http请求组件
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class XiaMenTemplate extends RestTemplate {

    private final LogAsync logAsync;



    public XiaMenTemplate(LogAsync logAsync) {
        this.logAsync = logAsync;
    }


//    public XiaMenResEntity post(String path, JSONObject params) {
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
//        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);
//        XiaMenResEntity xiaMenResEntity = null;
//        try {
//            xiaMenResEntity = restTemplate.postForObject(path, request, XiaMenResEntity.class);
//            log.info("【厦门接口】成功 --- URL:{}, request:{}, response:{}", path, request, JSON.toJSONString(xiaMenResEntity));
//        } catch (Exception e) {
//            log.error("【厦门接口】异常 --- URL:{}, request:{}, error:{}", path, request, e);
//        }
//        return xiaMenResEntity;
//    }

    /**
     * 推送患者预诊数据数据到HIS
     *
     * @param params
     * @return
     */
//    public XiaMenResEntity callWsdlService(String params,String patientName) {
//        String interfaceParams = null;
//        try {
//            // 创建SOAP连接
//            SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
//            SOAPConnection soapConnection = soapConnectionFactory.createConnection();
//
//            ByteArrayOutputStream outputStream2 = new ByteArrayOutputStream();
////            for (String param : params) {
//            SOAPMessage soapRequest = createSoapRequest(params);
//
//            soapRequest.writeTo(outputStream2);
//            interfaceParams = outputStream2.toString(String.valueOf(StandardCharsets.UTF_8));
//            outputStream2.close();
//            // 发送SOAP消息并获取响应
//            log.info("打印HIS接收预诊信息接口地址："+xmlPushurl);
//            SOAPMessage soapResponse = soapConnection.call(soapRequest, xmlPushurl);
//
//            // 处理响应，
//            //System.out.println("SOAP Response:");
//
//            // 获取整个SOAP响应的XML内容
//            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//            soapResponse.writeTo(outputStream);
//            String responseXml = outputStream.toString();
//            outputStream.close();
//            log.info("==================打印开始");
//            log.info("打印-推送患者预诊数据至HIS返回数据："+responseXml);
//            log.info("==================打印结束");
////            }
//            // 关闭连接
//            soapConnection.close();
//            XiaMenResEntity success = XiaMenResEntity.success("推送患者预诊数据至HIS返回消息" + responseXml);
//
//            recordWX(patientName, success, interfaceParams,"推送患者预诊数据至HIS");
//            return success;
//        } catch (Exception e) {
//            e.printStackTrace();
//            XiaMenResEntity error = XiaMenResEntity.error("推送患者预诊数据至HIS报错：" + e.getMessage());
//            recordWX(patientName, error, interfaceParams,"推送患者预诊数据至HIS");
//            return error;
//        }
//    }

    /**
     * 创建 SOAP 请求消息
     */
    private SOAPMessage createSoapRequest(String paramXml) {
        SOAPMessage message = null;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 创建一个SOAP消息工厂
            MessageFactory factory = MessageFactory.newInstance();
            message = factory.createMessage();
            // 创建一个SOAP消息体
            SOAPPart soapPart = message.getSOAPPart();
            SOAPEnvelope envelope = soapPart.getEnvelope();
            envelope.addNamespaceDeclaration("zys", "http://www.zysoft.com.cn/");
            SOAPBody body = envelope.getBody();
            // 创建CallInterface元素
            SOAPElement callInterfaceElement = body.addChildElement("CallInterface", "zys");
            // 创建msgHeader元素并添加CDATA内容
            String msgHeaderCDATA = "<![CDATA[<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                    "<root>\n" +
                    "  <serverName>SendPrediagInfo</serverName>" +
                    "  <format>xml</format>" +
                    "  <callOperator>预问诊</callOperator>" +
                    "  <certificate>qA2H54Wdr+/vo35wy3G+1g==</certificate>" +
                    "  <msgNo></msgNo>" +
                    "  <sendTime>"+simpleDateFormat.format(new Date())+"</sendTime>" +
                    "  <sendCount>1</sendCount>" +
                    "</root>]]>";
            SOAPElement msgHeaderElement = callInterfaceElement.addChildElement("msgHeader", "zys");
            msgHeaderElement.addTextNode(msgHeaderCDATA);
            SOAPElement msgBody = callInterfaceElement.addChildElement("msgBody", "zys");
            msgBody.addTextNode("<![CDATA[" + paramXml + "]]>");
            // 将SOAP消息转换为XML格式并打印输出
            //message.writeTo(System.out);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return message;

    }


//    public XiaMenResEntity callWsdlSendWxTemplate(String paramJSON,String patientName) {
//        String interfaceParams = "";
//        try {
//            // 创建SOAP连接
//            SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
//            SOAPConnection soapConnection = soapConnectionFactory.createConnection();
//
//            ByteArrayOutputStream outputStream2 = new ByteArrayOutputStream();
////            for (String param : params) {
//            SOAPMessage soapRequest = createSoapWxRequest(paramJSON);
//            soapRequest.writeTo(outputStream2);
//            interfaceParams = outputStream2.toString(String.valueOf(StandardCharsets.UTF_8));
//            outputStream2.close();
////            log.info("==================打印开始");
////            log.info("=================="+s);
////            log.info("==================打印结束");
//            // 发送SOAP消息并获取响应
//            SOAPMessage soapResponse = soapConnection.call(soapRequest, sendWXTemplateUrl);
//
//            // 处理响应，
//            //  System.out.println("SOAP Response:");
//
//            // 获取整个SOAP响应的XML内容
//            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//            soapResponse.writeTo(outputStream);
//            String responseXml = outputStream.toString();
//            outputStream.close();
////            }
//            // 关闭连接
//            soapConnection.close();
////            log.info("==================打印开始");
////            log.info("发送微信模板消息HIS返回消息内容：" + responseXml);
////            log.info("==================打印开始");
//            XiaMenResEntity success = XiaMenResEntity.success("发送微信模板消息成功" + responseXml);
//            recordWX(patientName, success, interfaceParams,"发送微信模板消息");
//            return success;
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            XiaMenResEntity error = XiaMenResEntity.error("发送微信模板消息失败：" + e.getMessage());
//            recordWX(patientName, error, interfaceParams,"发送微信模板消息");
//            return error;
//        }
//    }

    public void recordWX(String patientName,XiaMenResEntity post,String interfaceParams,String interfaceDesc){

        SysLogInterface log = new SysLogInterface();
        log.setId(IDUtil.getID());
        log.setPatientName(patientName);
        log.setCreateTime(new Date());
        log.setInterfaceName("callWsdlService");
        log.setInterfaceDesc(interfaceDesc);
        log.setInterfaceToken("");
        log.setInterfaceParams(interfaceParams);
        log.setResultStatus(post.getSuccess()? "1" : "0");
        log.setResultMeseage(post.getMessage());
        if (post.getData() != null) {
            log.setResultData(JSONObject.toJSONString(post.getData()));
        }
        logAsync.logRecord(log);
    }

    /**
     * @param paramJSON 列子：{
     *                  "telephone": "13216108997",
     *                  "templateId": "f16601b3b50211ee85090050569411b9",
     *                  "linkUrl": "跳转地址",
     *                  "titleParameter": "预问诊",
     *                  "contentParameter": "李辉|张医生|内科测试|腰痹预问诊单",
     *                  "remarkParameter": "",
     *                  "linkParameter": ""
     *                  }
     * @return
     */
    private SOAPMessage createSoapWxRequest(String paramJSON) {
        SOAPMessage message = null;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 创建一个SOAP消息工厂
            MessageFactory factory = MessageFactory.newInstance();
            message = factory.createMessage();
            // 创建一个SOAP消息体
            SOAPPart soapPart = message.getSOAPPart();
            SOAPEnvelope envelope = soapPart.getEnvelope();
            envelope.addNamespaceDeclaration("zys", "http://www.zysoft.com.cn/");
            SOAPBody body = envelope.getBody();
            // 创建CallInterface元素
            SOAPElement callInterfaceElement = body.addChildElement("CallInterface", "zys");
            // 创建msgHeader元素并添加CDATA内容
            String msgHeaderCDATA = "<![CDATA[\n" +
                    "         <?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<root>\n" +
                    "<serverName>SendMsgToWxByTpl</serverName>\n" +
                    "<format>xml</format>\t\n" +
                    "<callOperator></callOperator>\n" +
                    "<certificate>p5Ve1FsT7z6bqEGn0EcUvA== </certificate>\n" +
                    "</root>\n" +
                    "         ]]>";
            SOAPElement msgHeaderElement = callInterfaceElement.addChildElement("msgHeader", "zys");
            msgHeaderElement.addTextNode(msgHeaderCDATA);
            SOAPElement msgBody = callInterfaceElement.addChildElement("msgBody", "zys");
            msgBody.addTextNode("<![CDATA[" + paramJSON + "]]>");
            // 将SOAP消息转换为XML格式并打印输出
            //message.writeTo(System.out);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return message;

    }


/**
 * <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:zys="http://www.zysoft.com.cn/">
 *    <soapenv:Header/>
 *    <soapenv:Body>
 *       <zys:CallInterface>
 *          <!--Optional:-->
 *          <zys:msgHeader>
 *          <![CDATA[
 *          <?xml version="1.0" encoding="utf-8"?>
 * 	      <root>
 * 	        <serverName>SendPrediagInfo</serverName>
 * 	        <format>xml</format>
 * 	        <callOperator>预问诊</callOperator>
 * 	        <certificate>qA2H54Wdr+/vo35wy3G+1g==</certificate>
 * 	        <msgNo></msgNo>
 * 	        <sendTime>2024-03-04 10:47:03</sendTime>
 * 	        <sendCount>1</sendCount>
 * 	      </root>
 * 	      ]]>
 *          </zys:msgHeader>
 *          <!--Optional:-->
 *          <zys:msgBody>
 *           <![CDATA[
 *
 *          	      ]]>
 *          </zys:msgBody>
 *       </zys:CallInterface>
 *    </soapenv:Body>
 * </soapenv:Envelope>
 */
}