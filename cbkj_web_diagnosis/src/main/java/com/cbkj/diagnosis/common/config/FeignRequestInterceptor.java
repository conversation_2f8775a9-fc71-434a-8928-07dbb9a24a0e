package com.cbkj.diagnosis.common.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24 17:48
 * @Version 1.0
 */
@Component
public class FeignRequestInterceptor implements RequestInterceptor {
    private static final Logger log = LoggerFactory.getLogger(FeignRequestInterceptor.class);

    @Override
    public void apply(RequestTemplate template) {
        String fullUrl = template.feignTarget().url() + template.url();
        log.info("Feign Request URL: {} {}", template.method(), fullUrl);
        // 可追加查询参数打印

        log.info("Feign Request Query Params: {}", template.queries());
    }
}

