package com.cbkj.diagnosis.common.utils;

import lombok.extern.log4j.Log4j2;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控工具类
 * 用于监控下载压缩包功能的性能指标
 */
@Log4j2
public class PerformanceMonitor {
    
    private static final ConcurrentHashMap<String, AtomicLong> counters = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicLong> timers = new ConcurrentHashMap<>();
    
    /**
     * 开始计时
     */
    public static long startTimer(String operation) {
        long startTime = System.currentTimeMillis();
        log.debug("开始执行操作: {}", operation);
        return startTime;
    }
    
    /**
     * 结束计时并记录
     */
    public static void endTimer(String operation, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        timers.computeIfAbsent(operation, k -> new AtomicLong(0)).addAndGet(duration);
        log.info("操作 {} 耗时: {}ms", operation, duration);
    }
    
    /**
     * 增加计数器
     */
    public static void incrementCounter(String metric) {
        counters.computeIfAbsent(metric, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    /**
     * 增加计数器指定值
     */
    public static void addToCounter(String metric, long value) {
        counters.computeIfAbsent(metric, k -> new AtomicLong(0)).addAndGet(value);
    }
    
    /**
     * 获取计数器值
     */
    public static long getCounter(String metric) {
        return counters.getOrDefault(metric, new AtomicLong(0)).get();
    }
    
    /**
     * 获取平均耗时
     */
    public static long getAverageTime(String operation) {
        AtomicLong totalTime = timers.get(operation);
        AtomicLong count = counters.get(operation + "_count");
        if (totalTime != null && count != null && count.get() > 0) {
            return totalTime.get() / count.get();
        }
        return 0;
    }
    
    /**
     * 记录内存使用情况
     */
    public static void recordMemoryUsage(String operation) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        log.info("操作 {} 内存使用情况 - 总内存: {}MB, 已用内存: {}MB, 空闲内存: {}MB", 
                operation, 
                totalMemory / 1024 / 1024,
                usedMemory / 1024 / 1024,
                freeMemory / 1024 / 1024);
        
        addToCounter(operation + "_memory_used", usedMemory);
    }
    
    /**
     * 打印性能报告
     */
    public static void printPerformanceReport() {
        log.info("=== 性能监控报告 ===");
        
        // 打印计数器
        log.info("计数器统计:");
        counters.forEach((key, value) -> {
            log.info("  {}: {}", key, value.get());
        });
        
        // 打印计时器
        log.info("耗时统计:");
        timers.forEach((key, value) -> {
            AtomicLong count = counters.get(key + "_count");
            if (count != null && count.get() > 0) {
                log.info("  {} - 总耗时: {}ms, 平均耗时: {}ms, 执行次数: {}", 
                        key, value.get(), value.get() / count.get(), count.get());
            } else {
                log.info("  {} - 总耗时: {}ms", key, value.get());
            }
        });
        
        log.info("=== 报告结束 ===");
    }
    
    /**
     * 清空所有统计数据
     */
    public static void reset() {
        counters.clear();
        timers.clear();
        log.info("性能监控数据已重置");
    }
    
    /**
     * 记录数据库查询性能
     */
    public static void recordDatabaseQuery(String queryType, long duration, int resultCount) {
        endTimer("db_query_" + queryType, System.currentTimeMillis() - duration);
        addToCounter("db_query_" + queryType + "_count", 1);
        addToCounter("db_query_" + queryType + "_results", resultCount);
        
        log.debug("数据库查询 {} 耗时: {}ms, 结果数量: {}", queryType, duration, resultCount);
    }
    
    /**
     * 记录Excel生成性能
     */
    public static void recordExcelGeneration(String diaId, long duration, int dataCount) {
        endTimer("excel_generation", System.currentTimeMillis() - duration);
        incrementCounter("excel_generation_count");
        addToCounter("excel_data_count", dataCount);
        
        log.debug("Excel生成 {} 耗时: {}ms, 数据量: {}", diaId, duration, dataCount);
    }
    
    /**
     * 记录压缩包生成性能
     */
    public static void recordZipGeneration(long duration, int fileCount, long totalSize) {
        endTimer("zip_generation", System.currentTimeMillis() - duration);
        incrementCounter("zip_generation_count");
        addToCounter("zip_file_count", fileCount);
        addToCounter("zip_total_size", totalSize);
        
        log.info("压缩包生成耗时: {}ms, 文件数量: {}, 总大小: {}MB", 
                duration, fileCount, totalSize / 1024 / 1024);
    }
}
