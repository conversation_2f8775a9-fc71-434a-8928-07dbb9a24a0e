package com.cbkj.diagnosis.common.config;


import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.models.OpenAPI;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.handler.MappedInterceptor;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
// 替换 @EnableSwagger2 + @EnableSwaggerBootstrapUI
@SecurityScheme(
        name = "Authorization",
        type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER,
        description = "JWT 认证"
)
public class Swagger2Config {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("医生端接口API")
                        .description("医生端接口API文档")
                        .version("2.0.3"))
                .addSecurityItem(new SecurityRequirement()
                        .addList("Authorization"));  // 全局生效:ml-citation{ref="3,7" data="citationList"}
    }
    // 关键：请求头注入处理器
    @Bean
    public OperationCustomizer customize() {
        return (operation, handlerMethod) -> {
            operation.addSecurityItem(new SecurityRequirement()
                    .addList("Authorization"));  // 接口级生效:ml-citation{ref="6,8" data="citationList"}
            return operation;
        };
    }
//    @Bean
//    public Docket createRestApi() {
//        return new Docket(DocumentationType.SWAGGER_2)
//                .apiInfo(apiInfo())
//                .select()
//                .apis(RequestHandlerSelectors.basePackage("com.cbkj.diagnosis.controller"))
//                .paths(PathSelectors.any())
//                .build()
//                .securitySchemes(securitySchemesNew())
//                .securityContexts(securityContexts());
//    }
//
//    private ApiInfo apiInfo() {
//        return new ApiInfoBuilder()
//                .title("接口API")
//                .description("接口API文档")
//                .termsOfServiceUrl("")
//                .version("1.0")
//                .build();
//    }

//    private List<SecurityScheme> securitySchemesNew() {
//        List<SecurityScheme> apiKeys = new ArrayList<>();
//        apiKeys.add(new ApiKey("Authorization", "Authorization", "header"));
//        return apiKeys;
//    }
//    private List<ApiKey> securitySchemes() {
//        List<ApiKey> apiKeys = new ArrayList<>();
//        apiKeys.add(new ApiKey("Authorization", "Authorization", "header"));
//        return apiKeys;
//    }

//    private List<SecurityContext> securityContexts() {
//        List<SecurityContext> securityContexts = new ArrayList<>();
//        securityContexts.add(SecurityContext.builder()
//                .securityReferences(defaultAuth())
//                .forPaths(PathSelectors.regex("^(?!auth).*$")).build());
//        return securityContexts;
//    }

//    private List<SecurityReference> defaultAuth() {
//        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
//        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
//        authorizationScopes[0] = authorizationScope;
//        List<SecurityReference> securityReferences = new ArrayList<>();
//        securityReferences.add(new SecurityReference("Authorization", authorizationScopes));
//        return securityReferences;
//    }
//
    @Value("${swagger.basic.username:admin}")
    private String username;
    @Value("${swagger.basic.password:admin}")
    private String password;
    /* 必须在此处配置拦截器,要不然拦不到swagger的静态资源 */
    @Bean
    @ConditionalOnProperty(name = "swagger.basic.enable", havingValue = "true")
    public MappedInterceptor getMappedInterceptor() {
        return new MappedInterceptor(new String[]{"/doc.html", "/v3/api-docs/**"},
                new SwaggerInterceptor(username, password));
    }

}