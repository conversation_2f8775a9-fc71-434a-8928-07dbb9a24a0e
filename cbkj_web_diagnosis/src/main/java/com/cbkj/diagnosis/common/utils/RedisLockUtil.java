package com.cbkj.diagnosis.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 使用RedisTemplate简单实现分布式锁
 * * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2020/11/4 09:58
 */
@Slf4j
@Component
public class RedisLockUtil {
    /*** 分布式锁固定前缀 ***/
    private static final String REDIS_LOCK = "REDIS_LOCK:";
    /*** 分布式锁过期时间 ***/
    private static final Integer EXPIRE_TIME = 30;
    /*** redis返回成功 ***/
    private static final Long SUCCESS = 1L;
    @Autowired
    private RedisTemplate redisTemplate;


    /**
     * 加锁
     * 默认过期时间30s
     *
     * @param lockKey 加锁唯一标识
     * @param value   释放锁唯一标识（建议使用线程ID作为value）
     * @return [true: 加锁成功; false: 加锁失败]
     */
    public boolean lock(String lockKey, String value) {
        return lock(lockKey, value, EXPIRE_TIME);
    }

    /**
     * 加锁
     *
     * @param lockKey    加锁唯一标识
     * @param value      释放锁唯一标识（建议使用线程ID作为value）
     * @param expireTime 超时时间（单位：S）
     * @return [true: 加锁成功; false: 加锁失败]
     */
    public boolean lock(String lockKey, String value, Integer expireTime) {
        try {

            String script = "if redis.call('setNx',KEYS[1],ARGV[1]) == 1 " +
                    "then return redis.call('expire',KEYS[1],ARGV[2]) " +
                    "else return 0 " +
                    "end";
            RedisScript<Long> redisScript = new DefaultRedisScript<>(script, Long.class);

            Object result = redisTemplate.execute(redisScript, Collections.singletonList(REDIS_LOCK + lockKey), value, expireTime);

            if (SUCCESS.equals(result)) {

                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 释放锁
     *
     * @param lockKey 加锁唯一标识
     * @param value   释放锁唯一标识（建议使用线程ID作为value）
     * @return [true: 释放锁成功; false: 释放锁失败]
     */
    public boolean unLock(String lockKey, String value) {
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] " +
                "then return redis.call('del', KEYS[1]) " +
                "else return 0 " +
                "end";

        RedisScript<Long> redisScript = new DefaultRedisScript<>(script, Long.class);

        Object result = redisTemplate.execute(redisScript, Collections.singletonList(REDIS_LOCK + lockKey), value);
        if (SUCCESS.equals(result)) {
            return true;
        }

        return false;
    }
}