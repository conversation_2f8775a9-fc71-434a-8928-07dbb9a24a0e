//package com.cbkj.diagnosis.common.utils;
//
//import net.sf.json.JSONObject;
//
//import java.lang.reflect.Field;
//import java.lang.reflect.InvocationTargetException;
//import java.lang.reflect.Method;
//import java.lang.reflect.Type;
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @version 1.0.0
// * @date 2019年07月23日 16:53:00
// */
//public class MapListToBeanList {
//
//    public static <T> List<T> convertMapListToBeanList(List<Map<String, Object>> mapList, Class<T> clazz){
//
//        List<T> list= new ArrayList<>();
//        for(Map map:mapList){
//            T obj = (T) JSONObject.toBean(JSONObject.fromObject(map),clazz);
//            list.add(obj);
//        }
//        return list;
//    }
//
//}