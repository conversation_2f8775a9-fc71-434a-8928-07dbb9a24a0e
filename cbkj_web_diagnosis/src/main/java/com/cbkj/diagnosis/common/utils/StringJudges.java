//package com.cbkj.diagnosis.common.utils;
//
//import org.apache.commons.lang.StringUtils;
//
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
///**
// * <AUTHOR>
// * @version 1.0.0
// * @date 2019年07月29日 10:01:00
// */
//public class StringJudges {
//
//    public static Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
//    /**
//     * 判断字符串中是否包含中文
//     * @param str
//     * 待校验字符串
//     * @return 是否为中文
//     * @warn 不能校验是否为中文标点符号
//     */
//    public static boolean isContainChinese(String str) {
//        if (StringUtils.isBlank(str)) {
//            return false;
//        }
//
//        Matcher m = p.matcher(str);
//        if (m.find()) {
//            return true;
//        }
//        return false;
//    }
//
//
//}