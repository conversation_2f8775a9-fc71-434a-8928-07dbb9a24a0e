//package com.cbkj.diagnosis.common.utils;
//
//import com.cbkj.diagnosis.beans.sysBeans.Logentity;
//import com.cbkj.diagnosis.beans.sysBeans.OperationLog;
//import com.cbkj.diagnosis.service.sysService.LogentityService;
//import com.cbkj.diagnosis.service.sysService.OperationLogService;
//import com.cbkj.diagnosis.service.sysService.RedisService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Component;
//
//
//@Component
//public class LogAsync {
//
//    @Autowired
//    private LogentityService logentityService;
//    @Autowired
//    private RedisService redisService;
//
//    @Autowired
//    private OperationLogService operationLogService;
//
//    @Async
//    public void logRecord(Logentity logentity) {
//        logentityService.insert(logentity);
//    }
//
//    @Async
//    public void logCZRecord(OperationLog operationLog) {
//        operationLogService.insert(operationLog);
//    }
//
//}