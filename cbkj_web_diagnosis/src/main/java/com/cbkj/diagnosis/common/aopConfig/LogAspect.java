package com.cbkj.diagnosis.common.aopConfig;

import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class LogAspect {
    @Pointcut("execution(public * com.cbkj.diagnosis.controller..*.*(..))")
    public void web() {
    }


    @Before("web()")
    public void before(JoinPoint joinPoint) {
        StringBuilder sb = new StringBuilder();
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null;
        if (attributes != null) {
            request = attributes.getRequest();
        }
        StringBuffer url = null;
        String ip = null;
        String methodName = null;
        if (request != null) {
            url = request.getRequestURL();
            methodName = request.getMethod();
            ip = request.getRemoteAddr();

        }
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if (null != currentHr) {
            sb.append(currentHr.getNameZh());
        }
        sb.append(",ip=").append(ip);
        sb.append(",url=").append(url);
        sb.append(",method=").append(methodName).append(",");
        if (request != null) {
            sb.append(getParams(request, joinPoint));
        }
        log.info(sb.toString());
        Method method = getMethod(joinPoint);
        if (null == method) {
            return;
        }

    }

    @AfterReturning(returning = "reo", pointcut = "web()")
    public void doAfterReturning(JoinPoint joinPoint, Object reo) {
        StringBuilder sb = new StringBuilder("params:[");
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null;
        if (attributes != null) {
            request = attributes.getRequest();
        }
        StringBuffer url = null;
        String ip = null;
        String methodName = null;
        if (request != null) {
            url = request.getRequestURL();
            methodName = request.getMethod();
            ip = request.getRemoteAddr();
        }
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if (null != currentHr) {
            sb.append(currentHr.getNameZh()).append("--id=").append(currentHr.getUserId());
        }
        sb.append(",ip=").append(ip);
        sb.append(",url=").append(url);
        sb.append(",method=").append(methodName).append(",");
        if (request != null) {
            sb.append(getParams(request, joinPoint));
        }
        sb.append("]; result:[");
        sb.append(com.alibaba.fastjson.JSONObject.toJSONString(reo));
        sb.append("]");
        log.info(sb.toString());
    }

    private Method getMethod(JoinPoint joinPoint) {
        Method[] methods = joinPoint.getTarget().getClass().getMethods();
        String methodName = joinPoint.getSignature().getName();
        Object[] objs = joinPoint.getArgs();
        return Arrays.stream(methods).filter(method ->
                        (method.getName().equals(methodName) && method.getParameterTypes().length == objs.length))
                .findAny()
                .orElse(null);

    }

    private static String getParams(HttpServletRequest request, JoinPoint joinPoint) {
        String methodType = request.getMethod();
        Map<String, String[]> paramMap = request.getParameterMap();
        StringBuilder sb = new StringBuilder(" ");
        if ("POST".equals(methodType)) {
            try {
                Object[] args = joinPoint.getArgs();
                //参数名
                String[] argNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();

                for (int i = 0; i < argNames.length; i++) {
                    sb.append(argNames[i]).append(":").append(com.alibaba.fastjson.JSONObject.toJSONString(args[i])).append(",");
                }
            } catch (Exception e) {
                //sb.append("LogAspect-JSON解析失败：").append(e.getMessage());
            }
        } else {
            for (String key : paramMap.keySet()) {
                String[] values = paramMap.get(key);
                for (int i = 0; i < values.length; i++) {
                    String value = values[i];
                    if (StringUtils.isNotBlank(value)) {
                        sb.append(key).append(":").append(value).append(",");
                    }
                }
            }
        }
        return sb.toString();
    }

}
