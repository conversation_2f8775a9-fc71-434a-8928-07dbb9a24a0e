package com.cbkj.diagnosis.controller.sysController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cbkj.diagnosis.beans.business.SysSettingInfo;
import com.cbkj.diagnosis.mapper.business.SysSettingInfoMapper;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Tag(name = "配置管理", description = "配置管理")
@Slf4j
@RestController
public class SysSettingInfoController {

    @Autowired
    private SysSettingInfoMapper sysSettinginfoMapper;


    @RequestMapping(value = "getSetting/info", method = RequestMethod.GET)
    @ResponseBody
    public Object getSetting() {
        LambdaQueryWrapper<SysSettingInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysSettingInfo::getId, "1");
        return sysSettinginfoMapper.selectOne(queryWrapper);
    }

    @RequestMapping(value = "setSetting/info", method = RequestMethod.POST)
    @ResponseBody
    public Object setSetting(@RequestBody SysSettingInfo sysSettingInfo) {
        sysSettinginfoMapper.updateById(sysSettingInfo);
        return ResEntity.success();
    }
}
