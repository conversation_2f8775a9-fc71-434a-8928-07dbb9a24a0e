package com.cbkj.diagnosis.controller.business;


import com.cbkj.diagnosis.beans.business.TSymptom;
import com.cbkj.diagnosis.beans.business.TSymptomClass;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.webapi.business.WebTSymptomService;
import com.cbkj.diagnosis.service.webapi.business.vo.GetSymptomPageListRe;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/propaganda/symptom")
//@Api(value = "症状管理界面的接口", tags = "症状管理界面的接口")
@Tag(name = "症状管理界面的接口", description = "症状管理界面的接口")
public class WebSymptomController {


    private WebTSymptomService webTSymptomService;

    WebSymptomController(WebTSymptomService webTSymptomService) {
        this.webTSymptomService = webTSymptomService;
    }

    /**
     * 获取症状列表分页
     *
     * @param getSymptomPageListRe
     * @param page
     * @return
     */
    @GetMapping(value = "getSymptomPageList")
    //@ApiOperation(value = "症状列表", notes = "症状列表")
    @ResponseBody
    public Object getSymptomPageList(@ParameterObject GetSymptomPageListRe getSymptomPageListRe, @ParameterObject Page page) {
        return webTSymptomService.getSymptomPageList(getSymptomPageListRe, page);
    }

    @GetMapping(value = "getSymptomClassPageList")
    //@ApiOperation(value = "症状类别列表-树状", notes = "症状类别列表-树状")
    @ResponseBody
    public Object getSymptomClassPageList() {
        List<TSymptomClass> list = webTSymptomService.getSymptomClassPageList();

        return ResEntity.success(list);
    }


    @PostMapping(value = "saveSymptom")
    //@ApiOperation(value = "新增症状", notes = "新增症状")
    @ResponseBody
    public Object saveSymptom(@RequestBody TSymptom tSymptom) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        tSymptom.setCreateUsername(currentHr.getNameZh());
        tSymptom.setCreateUser(currentHr.getUserId());
        TSymptom tSymptom1 = webTSymptomService.saveSymptom(tSymptom);
        return ResEntity.success(tSymptom1);

    }

    @PostMapping(value = "editSymptom")
    //@ApiOperation(value = "修改症状", notes = "新修改症状")
    @ResponseBody
    public Object editSymptom(@RequestBody TSymptom tSymptom) {

        webTSymptomService.editSymptom(tSymptom);
        return ResEntity.success(tSymptom);

    }

    @GetMapping(value = "deleteSymptom")
    //@ApiOperation(value = "删除症状", notes = "删除症状")
    @ResponseBody
    public Object deleteSymptom(String symptomId) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        webTSymptomService.deleteSymptom(symptomId, currentHr.getNameZh(), currentHr.getUserId());
        return ResEntity.success();

    }


}
