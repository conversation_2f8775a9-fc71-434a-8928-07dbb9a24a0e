package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreDiagnosisListRes;
import com.cbkj.diagnosis.beans.business.TBusinessEdition;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.CompletedQuestionnairePreDiagnosisService;
import com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreRe;
//import io.swagger.annotations.Api;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.annotations.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 已填写预诊问卷管理
 * @Date 2024/12/20 09:21
 * @Version 1.0
 */
//@Api(value = "诊前管理--已填写预诊问卷管理", tags = "诊前管理-已填写预诊问卷管理")
@Tag(name = "诊前管理--已填写预诊问卷管理", description = "诊前管理--已填写预诊问卷管理")
@Controller
@RequestMapping("/pre/questionnaire")
public class CompletedQuestionnairePreDiagnosisController {
    private final HttpServletResponse httpServletResponse;
    private final CompletedQuestionnairePreDiagnosisService completedQuestionnaireService;

    public CompletedQuestionnairePreDiagnosisController(HttpServletResponse httpServletResponse, CompletedQuestionnairePreDiagnosisService completedQuestionnaireService) {
        this.httpServletResponse = httpServletResponse;
        this.completedQuestionnaireService = completedQuestionnaireService;
    }

    @GetMapping(value = "getQuestionnairePageList")
    @Operation(description = "获取已填问卷列表", summary = "获取已填问卷列表", responses = {
            @ApiResponse(description = "获取已填问卷列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CompletedQuestionnairePreDiagnosisListRes.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getList(@ParameterObject CompletedQuestionnairePreRe completedQuestionnaireRe, @ParameterObject Page page) {

        return completedQuestionnaireService.getList(completedQuestionnaireRe, page);

    }


    @GetMapping(value = "download/questionnaire")
    @Operation(description = "根据查询条件导出问卷数据（Excel格式），管理员可查看全部数据", summary = "获取已填问卷列表下载", responses = {
            @ApiResponse(description = "文件下载成功", responseCode = "200",
                    content = @Content(
                            mediaType = "application/octet-stream",
                            schema = @Schema(type = "string", format = "binary")
                    )
            )
    }
    )
//    @ResponseBody
    public String getDownloadQuestionnaire(@ParameterObject CompletedQuestionnairePreRe completedQuestionnaireRe) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        completedQuestionnaireRe.setUserId(currentHr.getUserId());
        List<AdminRule> roles = currentHr.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            completedQuestionnaireRe.setUserId(null);
        }
        completedQuestionnaireService.downloadQuestionnaire(completedQuestionnaireRe, httpServletResponse);
        return null;
    }


}


