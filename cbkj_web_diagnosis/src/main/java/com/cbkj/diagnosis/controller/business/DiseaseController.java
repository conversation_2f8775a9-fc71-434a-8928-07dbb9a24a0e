package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.beans.business.TDisease;
import com.cbkj.diagnosis.beans.business.TSymptom;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.DiseaseSymptomService;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo2;
import com.cbkj.diagnosis.service.webapi.business.WebTDiseaseService;
import com.cbkj.diagnosis.service.webapi.business.vo.LookPaperRecive;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
//@Api(value = "疾病-证型", tags = "疾病-证型")
@Tag(name = "疾病-证型", description = "疾病-证型")
@RequestMapping("diagnosis/dis")
public class DiseaseController {

    private final WebTDiseaseService tDiseaseService;
    private final DiseaseSymptomService diseaseSymptomService;

    public DiseaseController(WebTDiseaseService tDiseaseService, DiseaseSymptomService diseaseSymptomService) {
        this.tDiseaseService = tDiseaseService;
        this.diseaseSymptomService = diseaseSymptomService;
    }


    @GetMapping(value = "getDiseaseList")
    //@ApiOperation(value = "获取中西医疾病分页列表", notes = "获取中西医疾病分页列表",response = TDisease.class)
    @Operation(description = "获取中西医疾病分页列表", summary = "获取中西医疾病分页列表",responses = {
            @ApiResponse(description = "获取中西医疾病分页列表",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TDisease.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getDiseaseList(DiseaseVo2 diseaseVo, Page page) {
        return tDiseaseService.getDiseaseList2(diseaseVo,page);
    }


    @GetMapping(value = "getDiseaseSymptomList")
    //@ApiOperation(value = "获取证型列表-注意区分问题选项的症状", notes = "获取证型列表-注意区分问题选项的症状",response = TSymptom.class)
    @Operation(description = "获取证型列表-注意区分问题选项的症状", summary = "获取证型列表-注意区分问题选项的症状",responses = {
            @ApiResponse(description = "获取证型列表-注意区分问题选项的症状",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TSymptom.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getDiseaseSymptomList(String keyWord, Page page) {
        Object symptomList = diseaseSymptomService.getDiseaseSymptomList(keyWord, page);
        return symptomList;
    }
}




