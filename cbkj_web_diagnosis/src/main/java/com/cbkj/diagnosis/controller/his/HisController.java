package com.cbkj.diagnosis.controller.his;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.common.annotation.LogAnnotaion;
import com.cbkj.diagnosis.controller.his.vo.QrCodeRequest;
import com.cbkj.diagnosis.controller.his.vo.VisitRecords;
import com.cbkj.diagnosis.service.health.MedicalMetaServiceImpl;
import com.cbkj.diagnosis.service.his.HisService;
import com.cbkj.diagnosis.service.his.VisitRecordsService;
import com.cbkj.diagnosis.service.statistics.StatisticsExpenses;
import com.cbkj.diagnosis.service.webtask.WebRoadTaskPatient;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
//@Api(value = "三方接口", tags = "三方接口")
@Tag(name = "三方接口", description = "三方接口")
@RequestMapping("his/")
public class HisController {

    private final StatisticsExpenses statisticsExpenses;
    private final HisService hisService;
    private final VisitRecordsService visitRecordsService;

    private final WebRoadTaskPatient webRoadTaskPatient;

    public HisController(StatisticsExpenses statisticsExpenses, HisService hisService, VisitRecordsService visitRecordsService, WebRoadTaskPatient webRoadTaskPatient) {
        this.statisticsExpenses = statisticsExpenses;
        this.hisService = hisService;
        this.visitRecordsService = visitRecordsService;
        this.webRoadTaskPatient = webRoadTaskPatient;
    }

    @Autowired
    private MedicalMetaServiceImpl medicalMetaService;

    @GetMapping(value = "credentials/logon")
    //@ApiOperation(value = "获取三方登录所需凭证", notes = "获取三方登录所需凭证")
    @ResponseBody
    @LogAnnotaion(value = "获取三方登录所需凭证", isWrite = true)
    public ResEntity getUserLoginInfo(String cbdata) {

        return hisService.getUserLoginInfo(cbdata);

    }

    @GetMapping(value = "record/decryptContent")
    //@ApiOperation(value = "解密", notes = "解密")
    @ResponseBody
    @LogAnnotaion(value = "解密", isWrite = false)
    public ResEntity decryptContent(String sTime,String eTime) {

        return hisService.decryptContent(sTime,eTime);

    }

    @PostMapping(value = "/records/async")
    //@ApiOperation(value = "同步就诊记录", notes = "同步就诊记录")
    @ResponseBody
    @LogAnnotaion(value = "同步就诊记录", isWrite = true)
    public Object setVisitRecords(@RequestBody VisitRecords visitRecords) {
        ResEntity resEntity = visitRecordsService.setVisitRecords(visitRecords);
        if (resEntity.getStatus() && resEntity.getData() != null){
            String recordId = (String)resEntity.getData();
            //异步处理当前同步过来的病历是否要加入哪些自动任务
            webRoadTaskPatient.newRecordsCreateTask(recordId);

            statisticsExpenses.writeReadFromRedis(recordId,visitRecords.getAppId(),visitRecords.getInsCode(),visitRecords.getInsName(),null);
        }
        resEntity.setData(null);
        return resEntity;
    }

    @PostMapping(value = "/meta/clearDict")
    //@ApiOperation(value = "清除数据元缓存", notes = "清除数据元缓存")
    @ResponseBody
    @LogAnnotaion(value = "清除数据元缓存", isWrite = false)
    public Object clearDict() {
        medicalMetaService.clearDict();
        return ResEntity.success("清除成功");
    }

    @PostMapping(value = "/meta/getDict")
    //@ApiOperation(value = "获取缓存数据元测试", notes = "获取缓存数据元测试")
    @ResponseBody
    @LogAnnotaion(value = "获取缓存数据元测试", isWrite = false)
    public Object getDict(String dictType) {
        Map<String, String> map = medicalMetaService.getDict(dictType);
        return ResEntity.success(map);
    }


//    @PostMapping(value = "/prediagnosis/push")
//    //@ApiOperation(value = "推送患者预诊表单数据到HIS", notes = "推送患者预诊表单数据到HIS")
//    @ResponseBody
//    //@LogAnnotaion(value = "推送患者预诊表单数据到HIS",isWrite = true)
//    public Object sendPreDiagnosis(@RequestBody GetPreDiagnosis getPreDiagnosis) {
//        if (StringUtils.isBlank(getPreDiagnosis.getRecId())
//        ){
//            return ResEntity.error("缺少必要参数,请检查文档核对入参");
//        }
//        return visitRecordsService.getPreDiagnosis(getPreDiagnosis);
//    }

    @GetMapping(value = "credentials/qrCode", produces = "application/json")
    //@ApiOperation(value = "云系统-获取三方获取预诊地址", notes = "云系统-获取三方获取预诊地址")
    @ResponseBody
//    @LogAnnotaion(value = "获取三方获取预诊地址", isWrite = true)
    public ResEntity getQrCode(QrCodeRequest qrCodeRequest) {
        log.info("获取三方获取预诊地址入参：{}", JSON.toJSONString(qrCodeRequest));
        if (StringUtils.isBlank(qrCodeRequest.getDisId())) {
            return ResEntity.error("缺少必要参数disId");
        }
        return hisService.getQrCode(qrCodeRequest);
    }

//    @GetMapping(value = "encrpy/record")
//    //@ApiOperation(value = "加密未加密的病历-后面删除这个接口", notes = "加密未加密的病历-后面删除这个接口")
//    @ResponseBody
//    public ResEntity encrpy(String startDate, String endDate) {
//        return hisService.encrpy(startDate, endDate);
//    }


}
