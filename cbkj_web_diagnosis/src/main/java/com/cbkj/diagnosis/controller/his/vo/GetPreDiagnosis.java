package com.cbkj.diagnosis.controller.his.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.annotations.ApiParam;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description =  "获取患者预诊信息列表")
public class GetPreDiagnosis {
    @Schema(description =  "应用id",required = true)
    private String appId;
    @Schema(description =  "机构代码",required = true)
    private String insCode;
    @Schema(description =  "患者电话",required = true)
    private String mobile;
    @Schema(description =  "患者身份证",required = false)
    private String cardNumber;
    @Schema(description =  "证件类型",required = false)
    private String cardType;
    @Schema(description =  "健康卡号",required = true)
    private String healthCardNum;
    @Schema(description =  "页码")
    private Integer page;
    @Schema(description =  "条数")
    private Integer limit;
    private String recId;
}
