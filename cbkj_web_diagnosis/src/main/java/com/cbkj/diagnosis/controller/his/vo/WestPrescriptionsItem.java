package com.cbkj.diagnosis.controller.his.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WestPrescriptionsItem {

    @Schema(description =  "药物使用次剂量")
    private String drugDose;

    @Schema(description =  "药物使用剂量单位")
    private String drugDoseUnit;

    @Schema(description =  "药物使用频次")
    private String drugFrequency;

    @Schema(description =  "药物名称")
    private String drugName;

    @Schema(description =  "药物规格")
    private String drugSpecifications;

    @Schema(description =  "药物剂型")
    private String drugDosage;

    @Schema(description =  "用药途径")
    private String administrationRoute;

    @Schema(description =  "药物使用总剂量")
    private String drugTotalDosage;

    /**
     * 20250304 新增
     */
    @Schema(description =  "药物使用频次代码")
    private String drugFrequencyCode;

    @Schema(description =  "用药途径代码")
    private String administrationRouteCode;
}
