package com.cbkj.diagnosis.controller.business;


import com.cbkj.diagnosis.beans.business.TDisease;
import com.cbkj.diagnosis.beans.business.TRecord;
import com.cbkj.diagnosis.beans.business.TSymptom;
import com.cbkj.diagnosis.beans.business.adviceVo.GetAdviceListRes;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.webapi.business.WebTDiseaseService;
import com.cbkj.diagnosis.service.webapi.business.WebTRecordDiaService;
import com.cbkj.diagnosis.service.webapi.business.WebTRecordService;
import com.cbkj.diagnosis.service.webapi.business.WebTSymptomService;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo;
import com.cbkj.diagnosis.service.common.vo.PreListReVo;
import com.cbkj.diagnosis.service.webapi.business.vo.PreResultDifferent;
import com.cbkj.diagnosis.service.webapi.business.vo.WebTSymptomReVo;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
//@Api(value = "预诊信息管理", tags = "预诊信息管理")
@Tag(name = "预诊信息管理", description = "预诊信息管理")
@RequestMapping("diagnosis/preInfo")
public class TDiseaseController {

    private final WebTRecordService tRecordService;
    private final WebTDiseaseService tDiseaseService;
    private final WebTRecordDiaService tRecordDiaService;
    private final WebTSymptomService tSymptomService;


    TDiseaseController(
            WebTRecordService tRecordService,
            WebTDiseaseService tDiseaseService, WebTRecordDiaService tRecordDiaService,
            WebTSymptomService tSymptomService

    ) {
        this.tRecordService = tRecordService;
        this.tDiseaseService = tDiseaseService;
        this.tRecordDiaService = tRecordDiaService;
        this.tSymptomService = tSymptomService;

    }


    @GetMapping(value = "getPages")
    //@ApiOperation(value = "预诊信息诊前信息列表", notes = "预诊信息诊前信息列表", response = TRecord.class)
    @Operation(description = "预诊信息诊前信息列表", summary = "预诊信息诊前信息列表",responses = {
            @ApiResponse(description = "预诊信息诊前信息列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TRecord.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPreList(PreListReVo preListReVo, Page page) {

        Object pageDatas = tRecordService.getPageDatas(preListReVo, page, AdminWebUtils.getCurrentHr().getUserId());
        return pageDatas;
    }

    @GetMapping(value = "getOne")
    //@ApiOperation(value = "预诊信息诊前信息-单个", notes = "预诊信息诊前信息", response = TRecord.class)
    @Operation(description = "预诊信息诊前信息-单个", summary = "预诊信息诊前信息-单个",responses = {
            @ApiResponse(description = "预诊信息诊前信息-单个",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TRecord.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPreOne(String recId) {

        return tRecordService.getPreOne(recId);
    }

    @GetMapping(value = "getPreResultDifferent")
    //@ApiOperation(value = "预诊信息诊前信息与上次同病种差异", notes = "预诊信息诊前信息与上次同病种差异", response = TRecord.class)
    @Operation(description = "预诊信息诊前信息与上次同病种差异", summary = "预诊信息诊前信息与上次同病种差异",responses = {
            @ApiResponse(description = "预诊信息诊前信息与上次同病种差异",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TRecord.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPreResultDifferent(String patientId, String diaId,String recId) {
        ArrayList<PreResultDifferent> pageDatas = tRecordService.getPreResultDifferent(patientId, diaId,recId);
        return ResEntity.success(pageDatas);
    }

    @GetMapping(value = "getPrePaper")
    //@ApiOperation(value = "查看患者问诊单-预诊、随访", notes = "查看患者问诊单-预诊、随访", response = QuestionMain.class)
    @Operation(description = "查看患者问诊单-预诊、随访", summary = "查看患者问诊单-预诊、随访",responses = {
            @ApiResponse(description = "查看患者问诊单-预诊、随访",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = QuestionMain.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPrePaper(String recId) {
        Object pageDatas = tRecordDiaService.getPrePaper(recId);
        return pageDatas;
    }

    @GetMapping(value = "getDiseaseList")
    //@ApiOperation(value = "获取疾病列表", notes = "获取疾病列表", response = TDisease.class)
    @Operation(description = "获取疾病列表", summary = "获取疾病列表",responses = {
            @ApiResponse(description = "获取疾病列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TDisease.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getDiseaseList(@ParameterObject DiseaseVo diseaseVo) {
        List<TDisease> diseaseList = tDiseaseService.getDiseaseList(diseaseVo);
        return ResEntity.success(diseaseList);
    }


    @GetMapping(value = "getSymptomList")
    //@ApiOperation(value = "获取症状列表", notes = "获取症状列表", response = TSymptom.class)
    @Operation(description = "获取症状列表", summary = "获取症状列表",responses = {
            @ApiResponse(description = "获取症状列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TSymptom.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getSymptom(@ParameterObject WebTSymptomReVo webTSymptomReVo, @ParameterObject Page page) {
        Object symptomList = tSymptomService.getSymptomList(webTSymptomReVo, page);
        return symptomList;
    }


}