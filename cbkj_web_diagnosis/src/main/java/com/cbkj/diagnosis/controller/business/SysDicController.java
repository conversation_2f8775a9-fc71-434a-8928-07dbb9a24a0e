package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.service.sysService.SysWebDicService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/30 13:17
 * @Version 1.0
 */
//@Api(value ="字典数据", tags = "字典数据")
@Tag(name = "字典数据", description = "字典数据")
@RestController
@RequestMapping("sys/dic")
public class SysDicController {

    private final SysWebDicService sysWebDicService;

    public SysDicController(SysWebDicService sysWebDicService) {
        this.sysWebDicService = sysWebDicService;
    }


    @GetMapping(value = "getEleDisDicList")
    //@ApiOperation(value = "获取电子病历分类维护", notes = "获取电子病历分类维护")
    public ResEntity getEleDisDicList() {
        return sysWebDicService.getEleDisDicList();
    }

    @GetMapping(value = "getDiagnosisDicList")
    //@ApiOperation(value = "随访患者管理增加随访详情详情页-随访方式", notes = "随访患者管理增加随访详情详情页-随访方式")
    public ResEntity getDiagnosisDicList() {
        return sysWebDicService.getDiagnosisDicList();
    }

}
