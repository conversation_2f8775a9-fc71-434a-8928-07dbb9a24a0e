package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.beans.business.TSymptom;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.service.health.WebHealthService;
import com.cbkj.diagnosis.beans.health.vo.GetMedicalRecordsDetails;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 */
@Controller
//@Api(value = "健康档案", tags = "健康档案")
@Tag(name = "健康档案", description = "健康档案")
@RequestMapping("health")
public class HealthController {

    private final WebHealthService webHealthService;

    HealthController(WebHealthService webHealthService) {

        this.webHealthService = webHealthService;
    }

    @PostMapping(value = "getMedicalRecordsDetails")
    //@ApiOperation(value = "健康档案详情", notes = "健康档案详情")
    @Operation(description = "健康档案详情", summary = "健康档案详情",responses = {
            @ApiResponse(description = "健康档案详情",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = MedicalRecords.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getMedicalRecordsDetails(GetMedicalRecordsDetails medicalRecordsDetails) {
        webHealthService.getMedicalRecordsDetails(medicalRecordsDetails);
        return null;
    }
}
