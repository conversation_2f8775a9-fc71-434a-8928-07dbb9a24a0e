package com.cbkj.diagnosis.controller.statistics;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/24 10:52
 * @Version 1.0
 */

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.SysAdminInfoDisMapping;
import com.cbkj.diagnosis.beans.business.TDisease;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMapping;
import com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.business.TDiseaseMapper;
import com.cbkj.diagnosis.service.TFormService;
import com.cbkj.diagnosis.service.business.impl.SysAdminInfoDisMappingServiceImpl;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisDisMappingServiceImpl;
import com.cbkj.diagnosis.service.impl.SysDicServiceImpl;
import com.cbkj.diagnosis.service.statistics.StaticsHistory;
import com.cbkj.diagnosis.service.statistics.factory.StatisticsStrategyFactory;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
//@Api(value = "首页数据接口", tags = "首页数据接口", position = 1)
@Tag(name = "首页数据接口", description = "首页数据接口")
@RestController
public class StatisticsController {

    private final StatisticsStrategyFactory statisticsStrategyFactory;
    private final TFormService tFormService;
    private final StaticsHistory staticsHistory;
    private final SysAdminInfoDisMappingServiceImpl sysAdminInfoDisMappingService;
    private final TDiseaseMapper tDiseaseMapper;
    public StatisticsController(StatisticsStrategyFactory statisticsStrategyFactory, TFormService tFormService, StaticsHistory staticsHistory,
                                SysAdminInfoDisMappingServiceImpl sysAdminInfoDisMappingService, TDiseaseMapper tDiseaseMapper) {
        this.statisticsStrategyFactory = statisticsStrategyFactory;
        this.tFormService = tFormService;
        this.staticsHistory = staticsHistory;
        this.sysAdminInfoDisMappingService = sysAdminInfoDisMappingService;
        this.tDiseaseMapper = tDiseaseMapper;
    }

    //@ApiOperation(value = "获取首页数据", notes = "获取首页数据")
    @GetMapping("/block/data")
    public ResEntity getStatisticsDiagnosis(StatisticsVo statisticsVo) {

        if (statisticsVo == null || StringUtils.isBlank(statisticsVo.getBlockId())) {
            return ResEntity.error("参数不能为空");
        }
        if (Constant.BASIC_STRING_ZERO.equals(statisticsVo.getDisCode())){
            statisticsVo.setDisCode(null);
        }
        statisticsVo.setUserId(AdminWebUtils.getCurrentHr().getUserId());
        List<AdminRule> roles = AdminWebUtils.getCurrentHr().getRoles();
        roles.forEach(role -> {
            if ("管理".equals(role.getRoleName())){
                statisticsVo.setUserId(null);
            }
        });
        LambdaQueryWrapper<SysAdminInfoDisMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysAdminInfoDisMapping::getUserId,statisticsVo.getUserId());
        List<SysAdminInfoDisMapping> list = sysAdminInfoDisMappingService.list(wrapper);
        if (!list.isEmpty()){
            StringBuilder stringBuilder = new StringBuilder();
            list.forEach(a -> {
                TDisease objectById = tDiseaseMapper.getObjectById(a.getDisId());
                if (objectById != null){
                    stringBuilder.append(objectById.getDisCode()).append(",");
                }

            });
            statisticsVo.setDisCodes(stringBuilder.toString());
        }
        //分解
//        String insCode = statisticsVo.getInsCode();
//        if (StringUtils.isNotBlank(insCode)){
//            String[] split = insCode.split("-");
//            statisticsVo.setAppId(split[0]);
//            statisticsVo.setInsCode(split[1]);
//            statisticsVo.setInsName(split[2]);
//        }
        return statisticsStrategyFactory.getParamStrategy(statisticsVo.getBlockId()).getData(statisticsVo);
    }

    //@ApiOperation(value = "获取首页疾病下拉数据", notes = "获取首页疾病")
    @GetMapping("/block/disease")
    public Object getDoctorMappingDisList(String disName) {
        return ResEntity.success(tFormService.getDoctorDocDisCodeList(disName));
    }

    //@ApiOperation(value = "统计历史-预诊、随访、宣教（无法细分到机构和疾病，只能到日期）", notes = "统计历史-预诊、随访、宣教（无法细分到机构和疾病，只能到日期）")
    @GetMapping("/block/staticsHistory")
    public ResEntity staticsHistory(StaticsHistoryData staticsHistoryData) {
        if (staticsHistoryData == null || StringUtils.isBlank(staticsHistoryData.getStartDate()) || StringUtils.isBlank(staticsHistoryData.getEndDate())){
            return ResEntity.error("参数不能为空");
        }
        staticsHistory.staticsHistoryData(staticsHistoryData);
        return ResEntity.success();
    }
}
