package com.cbkj.diagnosis.controller.webtask;

import com.cbkj.diagnosis.beans.task.SRoad;
import com.cbkj.diagnosis.beans.task.webvo.UpdateOrInsertRoad;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.webtask.WebRoadService;
import com.cbkj.diagnosis.service.webtask.vo.UpdateRoadStatusRe;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
//@Api(value = "随访路径-相关接口", tags = "随访路径相关接口")
@Tag(name = "随访路径-相关接口", description = "随访路径-相关接口")
@RestController
@RequestMapping("webapi/road")
public class WebRoadController {

    private WebRoadService webRoadService;

    WebRoadController(WebRoadService webRoadService) {
        this.webRoadService = webRoadService;
    }

    @GetMapping(value = "updateRoadStatus")
    //@ApiOperation(value = "更新随访路径状态", notes = "更新随访路径状态")
    @Operation(description = "更新随访路径状态", summary = "更新随访路径状态",responses = {
            @ApiResponse(description = "更新随访路径状态",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object updateRoadStatus(@ParameterObject UpdateRoadStatusRe updateRoadStatusRe) {

        webRoadService.updateRoadStatus(updateRoadStatusRe);
        return ResEntity.success();
    }


    @GetMapping(value = "getPageRoadList")
    //@ApiOperation(value = "随访路径列表", notes = "随访路径列表", response = SRoad.class)
    @Operation(description = "随访路径列表", summary = "随访路径列表",responses = {
            @ApiResponse(description = "随访路径列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SRoad.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPageRoadList(String sRoadName, Page page) {
        return (webRoadService.getPageRoadList(sRoadName, page));
    }


    @GetMapping(value = "getRoadDetailById")
    //@ApiOperation(value = "随访路径详情", notes = "随访路径列表详情",response = UpdateOrInsertRoad.class)
    @Operation(description = "随访路径详情", summary = "随访路径详情",responses = {
            @ApiResponse(description = "随访路径详情",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = UpdateOrInsertRoad.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getRoadDetailById(String sRoadIid) {
        UpdateOrInsertRoad updateOrInsertRoad = webRoadService.getRoadDetailById(sRoadIid);
        return ResEntity.success(updateOrInsertRoad);
    }


    @PostMapping(value = "updateOrInsertRoad")
    //@ApiOperation(value = "新增 更新 随访路径", notes = "新增 更新 随访路径", response = UpdateOrInsertRoad.class)
    @Operation(description = "新增 更新 随访路径", summary = "新增 更新 随访路径",responses = {
            @ApiResponse(description = "新增 更新 随访路径",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = UpdateOrInsertRoad.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object updateOrInsertRoad(@RequestBody UpdateOrInsertRoad updateOrInsertRoad) {
        ResEntity resEntity = webRoadService.updateOrInsertRoad(updateOrInsertRoad);
        return resEntity;
    }


}
