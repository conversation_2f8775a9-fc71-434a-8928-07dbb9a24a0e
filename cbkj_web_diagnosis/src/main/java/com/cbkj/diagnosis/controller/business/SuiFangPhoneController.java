package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.SuiFangPhoneService;
import com.cbkj.diagnosis.service.common.vo.GetPhoneList;
import com.cbkj.diagnosis.service.common.vo.SchedulingAllocationSave;
import com.cbkj.diagnosis.service.common.vo.SchedulingSave;
import com.cbkj.diagnosis.service.common.vo.SuiFangPhoneRe;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
//@Api(value = "（萧山）电话随访患者", tags = "（萧山）电话随访患者")
@Tag(name = "（萧山）电话随访患者", description = "（萧山）电话随访患者")
@RequestMapping("suifang/phone")
public class SuiFangPhoneController {


    private final SuiFangPhoneService suiFangPhoneService;

    public SuiFangPhoneController(SuiFangPhoneService suiFangPhoneService) {
        this.suiFangPhoneService = suiFangPhoneService;
    }

    @GetMapping(value = "/list")
    //@ApiOperation(value = "获取电话随访患者列表", notes = "获取电话随访患者列表",response = GetPhoneList.class)
    @ResponseBody
    public Object getList(@ParameterObject SuiFangPhoneRe suiFangPhoneRe, @ParameterObject Page page) {
        return suiFangPhoneService.getList(suiFangPhoneRe, page);
    }


    @GetMapping(value = "scheduling/list")
    //@ApiOperation(value = "获取电话随访-随访排期", notes = "获取电话随访-随访排期",response = SRoadTaskPatientsPhone.class)
    @ResponseBody
    public Object getSchedulingList(Long taskPatientsId) {
        if (taskPatientsId == null) {
            return ResEntity.error("不能为空");
        }
        return ResEntity.success(suiFangPhoneService.getSchedulingList(taskPatientsId));
    }


//    @GetMapping(value = "scheduling/no/list")
//    //@ApiOperation(value = "分配患者-获取未分配的患者信息-返回这个患者的这个任务下的这个路径下的多条排期", notes = "分配患者-获取未分配的患者信息-返回这个患者的这个任务下的这个路径下的多条排期")
//    @ResponseBody
//    public Object getSchedulingNoList() {
//        return ResEntity.success(suiFangPhoneService.getSchedulingNoList());
//    }


    @PostMapping(value = "scheduling/save")
    //@ApiOperation(value = "电话随访患者排期保存", notes = "电话随访患者排期保存")
    @ResponseBody
    public Object saveScheduling(@RequestBody SchedulingSave schedulingSave) {
        suiFangPhoneService.saveScheduling(schedulingSave);
        return ResEntity.success();
    }


    @PostMapping(value = "scheduling/batch/save")
    //@ApiOperation(value = "电话随访患者排期批量保存", notes = "电话随访患者排期批量保存")
    @ResponseBody
    public Object saveBatchScheduling(@RequestBody List<SchedulingSave> schedulingSave) {
        for (int i = 0; i < schedulingSave.size(); i++) {
            suiFangPhoneService.saveScheduling(schedulingSave.get(i));
        }
        return ResEntity.success();
    }


    @PostMapping(value = "scheduling/allocation/save")
    //@ApiOperation(value = "患者电话随访任务分配给医生", notes = "患者电话随访任务分配给医生")
    @ResponseBody
    public Object saveSchedulingAllocation(@RequestBody SchedulingAllocationSave schedulingAllocationSave) {
        suiFangPhoneService.saveSchedulingAllocation(schedulingAllocationSave);
        return ResEntity.success();
    }


}
