package com.cbkj.diagnosis.controller.sysController;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TSysParam;
import com.cbkj.diagnosis.beans.sysExt.LoginBean;
import com.cbkj.diagnosis.common.config.security.TokenBo;
import com.cbkj.diagnosis.common.openfeign.TestFeignClient;
import com.cbkj.diagnosis.common.utils.RandImageUtil;
import com.cbkj.diagnosis.service.LoginService;
import com.cbkj.diagnosis.service.business.impl.TSysParamServiceImpl;
import com.cbkj.diagnosis.service.sysService.RedisService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import com.cbkj.diagnosis.common.utils.Constant;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * //@Api(value = "登录接口", tags = "登录接口", position = 3)
 *
 * @Controller
 * @Slf4j
 **/
@Tag(name = "登录接口", description = "登录接口")
@Slf4j
@RestController
public class LoginController {
    private final RedisService redisService;
    private final LoginService loginService;
    private final TestFeignClient testFeignClient;
    private static final String BASE_CHECK_CODES = "qwertyuiplkjhgfdsazxcvbnmQWERTYUPLKJHGFDSAZXCVBNM1234567890";
    private final TSysParamServiceImpl tsysParamService;


    LoginController(RedisService redisService, LoginService loginService, TestFeignClient testFeignClient, TSysParamServiceImpl tsysParamService) {
        this.redisService = redisService;
        this.loginService = loginService;
        this.testFeignClient = testFeignClient;
        this.tsysParamService = tsysParamService;
    }

    @RequestMapping(value = "test", method = RequestMethod.GET)
//    @Operation(summary = "test", description = "test")
    @ApiResponse(description = "test",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ResEntity.class)
            )
    )
    @ResponseBody
//    @RequiresMenus(value = {"1001001"})
    public Object test(String key) {
        return testFeignClient.test(key);
    }

    @RequestMapping(value = "test2", method = RequestMethod.GET)
    //@ApiOperation(value = "test2", notes = "test2")
//    @Operation(summary  = "测试test2", description = "测试test2")
    @ApiResponse(description = "测试test2",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ResEntity.class)
            )
    )
    @ResponseBody
//    @RequiresMenus(value = {"100100"})
    public ResEntity test2() {
        return ResEntity.success("你好");
    }

    @RequestMapping(value = "login", method = RequestMethod.POST)
//    @ApiResponse()
    @ResponseBody
    public ResEntity login(@RequestBody LoginBean loginBean) {
        ResEntity res = loginService.login(loginBean);
        if (!res.getStatus()) {
            return loginService.accountNumberLock(loginBean.getName(), res);
        }
        return res;
    }

    @RequestMapping(value = "exit", method = RequestMethod.GET)
    @Operation(description = "退出", summary = "退出",responses = {
            @ApiResponse( description = "退出")
    }
    )
    @ResponseBody
    public ResEntity exit() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication) {
            Object obj = authentication.getPrincipal();
            if (obj instanceof TokenBo) {
                redisService.clearRedisCache(null, "pre-diagnosis-token::" + ((TokenBo) obj).getTokenKey());
            }
        }
        return ResEntity.success(null);
    }

    /**
     * 后台生成图形验证码 ：有效
     */
    @GetMapping(value = "/randomImage")
    @Operation(description = "获取图像验证码,参数路径key在登录中会再次需要这个值", summary = "获取图像验证码,参数路径key在登录中会再次需要这个值",responses = {
            @ApiResponse(description = "获取图像验证码,参数路径key在登录中会再次需要这个值",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public ResEntity randomImage(HttpServletResponse response, @RequestParam("key") String key) {
        try {
            String redisPrefix = Constant.CACHE_PREFIX + ":verification::";

            String code = RandomUtil.randomString(BASE_CHECK_CODES, 4);
            String lowerCaseCode = code.toLowerCase();
            String realKey = lowerCaseCode + key;


            redisService.set(redisPrefix + realKey, lowerCaseCode, 5, TimeUnit.MINUTES);
            String base64 = RandImageUtil.generate(code);
            return ResEntity.success(base64);
        } catch (Exception e) {
            return ResEntity.error(e.getMessage());
        }
    }

    @RequestMapping(value = "getParam", method = RequestMethod.GET)
    @Operation(description = "获取参数配置", summary = "获取参数配置",responses = {
            @ApiResponse(description = "获取参数配置",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TSysParam.class)
                    )
            )
    }
    )
    @ResponseBody
    public ResEntity<List<TSysParam>> getParam() {
        QueryWrapper<TSysParam> tSysParamQueryWrapper = new QueryWrapper<>();
        tSysParamQueryWrapper.eq("status", "0");
        List<TSysParam> list = tsysParamService.list(tSysParamQueryWrapper);
        return new ResEntity<List<TSysParam>>(true, list);
    }
}
