package com.cbkj.diagnosis.controller.his.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RecordCost {
    /**
     * 项目大类子类代码，。前是大类代码，点后是子类，合起来确定唯一。：
     * 1.1 一般医疗服务费-中医辨证论治费，2.1 中医治疗费，
     * 3.1 影像学诊断费 3.2 实验室诊断费，
     * 4.1 中草药费 、4.2 中成药费 4.3 中成药费-医疗机构中药制剂费，
     * 5.1 西药费，
     * 6.1 手术治疗费，
     * 7.1 其他费，煎药费用金额
     * 10 个人承担费
     * 11 医保报销金额
     * 12 门 (急) 诊费用金额（元）
     * 13 医疗费用结算方式代码
     * 14
     */
    @Schema(description =  "患者诊疗费用")
    private Long recordCostId;

    @Schema(description =  "就诊记录id")
    private String recordsId;


    @Schema(description =  "综合医疗服务类-中医辨证论治费")
    private String tcmBianZhengCost;
    @Schema(description =  "综合医疗服务类-中医医疗服务项目收费")
    private String tcmServiceCost;

    @Schema(description =  "中药类-中草药费")
    private String tcmCost;

    @Schema(description =  "中药类-中成药费")
    private String cpdCost;

    @Schema(description =  "中药类-中成药费-医疗机构中药制剂费")
    private String ipCost;

    @Schema(description =  "治疗处置金额")
    private String tdCost;

    @Schema(description =  "辅助检查费")
    private String examinationCost;

    @Schema(description =  "化验费")
    private String assayCost;

    @Schema(description =  "西药类-西药费")
    private String wesDrugCost;

    @Schema(description =  "其他类-其他费")
    private String othersCost;

    @Schema(description =  "煎药费用金额")
    private String decoctingCost;

    @Schema(description =  "个人承担费用金额")
    private String personalCost;

    @Schema(description =  "门诊医保报销金额")
    private String insuranceReimbursementCost;

    @Schema(description =  "中医类-中医治疗费")
    private String cnMedicineCost;

    @Schema(description =  "诊断类-影像学诊断费")
    private String imagingCost;

    @Schema(description =  "诊断类-实验室诊断费")
    private String laboratoryCost;

    @Schema(description =  "治疗类-手术治疗费")
    private String surgicalCost;

    @Schema(description =  "本次患者就诊所支付的各项费用，单位为元")
    private String totalCost;
}
