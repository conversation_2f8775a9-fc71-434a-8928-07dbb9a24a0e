package com.cbkj.diagnosis.controller.his.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class ChinesePrescriptions {
    @Schema(description =  "处方id")
    private String prescriptionNo;

    @Schema(description =  "中药处方名")
    private String prescriptionName;

    @Schema(description =  "中药剂数（剂")
    private String prescriptionNum;

    @Schema(description =  "中医服法")
    private String chinesePrescriptionUsage;

    @Schema(description =  "中医频次")
    private String chinesePrescriptionRate;

    @Schema(description =  "就诊记录id")
    private String recordsId;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "处方开立日期")
    private Date prescriptionTime;

    @Schema(description =  "处方有效天数")
    private String prescriptionsEffectiveDays;

    @Schema(description =  "处方中药金额")
    private String chinesePrescriptionCost;

    @Schema(description =  "处方类别")
    private String prescriptionCategory;

    @Schema(description =  "中药煎煮法")
    private String decoctingMethod;

    @Schema(description =  "中药用药方法")
    private String medicationMethod;

    @Schema(description =  "中药服用要求")
    private String medicationRequirements;

    @Schema(description =  "中药用药禁忌")
    private String medicationContraindications;

    @Schema(description =  "处方开立医师签名")
    private String prescribingDoctorSign;

    @Schema(description =  "处方审核药剂师签名")
    private String reviewedDoctorSign;

    @Schema(description =  "处方属性")
    private String prescriptionPropertieCode;

    @Schema(description =  "协定处方标志")
    private String cipherPrescription;

    @Schema(description =  "中药代煎标志")
    private String decoctingFlag;

    @Schema(description =  "使用医疗机构中药制剂标志")
    private String preparationFlag;

    private List<ChinesePrescriptionsItem> chinesePrescriptionDrugs;

}
