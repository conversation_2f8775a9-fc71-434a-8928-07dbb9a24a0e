package com.cbkj.diagnosis.controller.sysController;

import com.cbkj.diagnosis.beans.business.SysIns;
import com.cbkj.diagnosis.beans.sysBeans.*;
import com.cbkj.diagnosis.common.annotation.RequiresMenus;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.sysService.AdminMenuService;
import com.cbkj.diagnosis.service.sysService.AdminRuleService;
import com.cbkj.diagnosis.service.sysService.SysAdminService;
import com.cbkj.diagnosis.service.webapi.business.WebTDiseaseService;
import com.cbkj.diagnosis.service.webapi.business.vo.MySelfDisList;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//@Api(value = "(医生)用户、角色接口", tags = "用户、角色接口", position = 3)
@Tag(name = "(医生)用户、角色接口", description = "(医生)用户、角色接口")
@RestController
@RequestMapping("webapi/admininfo")
public class AdminInfoController {
    private final AdminRuleService adminRuleService;

    private final AdminMenuService adminMenuService;
    private final SysAdminService sysAdminService;
    private final WebTDiseaseService webTDiseaseService;

    AdminInfoController(AdminRuleService adminRuleService, AdminMenuService adminMenuService, SysAdminService sysAdminService, WebTDiseaseService webTDiseaseService) {
        this.adminRuleService = adminRuleService;
        this.adminMenuService = adminMenuService;
        this.sysAdminService = sysAdminService;
        this.webTDiseaseService = webTDiseaseService;
    }
    @PreAuthorize("hasRole('普通用户')")
    @GetMapping("test.do")
    public Object getTest(){

        return ResEntity.success();
    }

    @RequiresMenus(value = {"199901"})
    @PostMapping(value = "updateRoleInfo")
    //@ApiOperation(value = "新增 更新 角色", notes = "新增 更新 角色")
    @ResponseBody
    public Object updateRoleInfo(@RequestBody AdminRule adminRule) {
        if (Constant.BASIC_STRING_ONE.equals(adminRule.getRoleId())) {
            return ResEntity.error("无法修改root角色");
        }

        sysAdminService.updateRoleInfo(adminRule);
        return ResEntity.success();
    }

//    @RequiresMenus(value = {"199901"})
    @GetMapping(value = "getRoleListPage")
    //@ApiOperation(value = "获取角色分页", notes = "获取角色分页")
    @ResponseBody
    public Object getRoleListPage(Page page, String roleName) {
        return sysAdminService.getRoleListPage(page, roleName);
    }

    /**
     * 删除角色和用户关联的映射。无法删除id是1 的root管理员角色。
     *
     * @param roleId
     */
    @RequiresMenus(value = {"199901"})
    @GetMapping(value = "deleteRoleInfo")
    //@ApiOperation(value = "删除角色", notes = "删除角色")
    @ResponseBody
    public Object deleteRoleInfo(String roleId) {
        if (Constant.BASIC_STRING_ONE.equals(roleId)) {
            return ResEntity.error("无法删除root角色");
        }
        sysAdminService.deleteRoleInfo(roleId);
        return ResEntity.success();
    }

//    @RequiresMenus(value = {"199902"})
    @GetMapping(value = "getUserList")
    //@ApiOperation(value = "获取用户列表", notes = "获取用户列表")
    @ResponseBody
    public Object getUserList(String deptName, String keyWord, Page page) {
        if (StringUtils.isNotBlank(deptName)) {
            return sysAdminService.getAdminList(deptName, page);
        }

        return sysAdminService.getAdminList(keyWord, page);
    }

    @GetMapping(value = "getDiagnosisUserList")
    //@ApiOperation(value = "获取随访任务就诊医生列表", notes = "获取随访任务就诊医生列表")
    @ResponseBody
    public Object getDiagnosisUserList(String deptIds,Integer page,Integer limit,String doctorName) {
        return sysAdminService.getDiagnosisUserList(deptIds, page, limit,doctorName);
    }
    @GetMapping(value = "getSuiFangUserList")
    //@ApiOperation(value = "获取随访任务随访医生列表", notes = "获取随访任务随访医生列表")
    @ResponseBody
    public Object getSuiFangUserList(Integer page,Integer limit,String doctorName) {
        return sysAdminService.getSuiFangUserList( page, limit,doctorName);
    }


    @RequiresMenus(value = {"199902"})
    @GetMapping(value = "deleteUser")
    //@ApiOperation(value = "删除用户", notes = "删除用户")
    @ResponseBody
    public Object deleteUser(String userId) {
        if (StringUtils.isBlank(userId)) {
            return ResEntity.error("缺少入参");
        }
        sysAdminService.deleteUser(userId);
        return ResEntity.success(null);
    }

    @RequiresMenus(value = {"199902"})
    @PostMapping(value = "insertUserinfo")
    //@ApiOperation(value = "新建用户", notes = "新建用户")
    @ResponseBody
    public Object insertUserinfo(@RequestBody AdminInfo adminInfo) {
        return sysAdminService.insertAdmin(adminInfo);
    }

    @RequiresMenus(value = {"199902"})
    @PostMapping(value = "updateUserinfo")
    //@ApiOperation(value = "更新用户", notes = "更新用户")
    @ResponseBody
    public Object updateUserinfo(@RequestBody AdminInfo adminInfo) {
        return sysAdminService.updateAdmin(adminInfo);

    }

    @GetMapping(value = "getDeptList")
    //@ApiOperation(value = "获取科室列表", notes = "获取科室列表")
    @ResponseBody
    public Object getDeptList(String deptName, Page page) {
        return ResEntity.success(sysAdminService.getDeptList(deptName, page));
    }

    @GetMapping(value = "getIns")
    //@ApiOperation(value = "获取机构名字-一家部署一个，就一个名字", notes = "获取机构名字")
    @ResponseBody
    public Object getIns() {

        List<SysIns> ins = sysAdminService.getIns(new SysIns());
        return ResEntity.success(ins);
    }


    @RequestMapping(value = "rule/authority/findMenu", method = RequestMethod.GET)
    //@ApiOperation(value = "加载角色菜单", notes = "加载角色菜单")
    @ResponseBody
    public Object findMenu(String id) {
        return adminRuleService.findMenuByMID(id, "2", true);
    }

    @RequestMapping(value = "rule/getAdminRuleMenu", method = RequestMethod.POST)
    //@ApiOperation(value = "获取角色菜单配置信息", notes = "获取角色菜单配置信息")
    @ResponseBody
    public Object getAdminRuleMenu(String roleId) {
        return adminRuleService.getAdminRuleMenu(roleId);
    }

    @RequiresMenus(value = {"199901"})
    @RequestMapping(value = "rule/saveAdminRuleMenu", method = RequestMethod.POST)
    //@ApiOperation(value = "保存角色菜单配置信息", notes = "保存角色菜单配置信息")

    @ResponseBody
    public Object saveAdminRuleMenu(@RequestBody AdminRuleMenuVo adminRuleMenuVo) {
        return adminRuleService.saveAdminRuleMenu(adminRuleMenuVo);
    }
    @RequiresMenus(value = {"199901"})
    @RequestMapping(value = "rule/authority", method = RequestMethod.GET)
    //@ApiOperation(value = "权限设置菜单", notes = "加载角色菜单")
    @ResponseBody
    public Object authority(String menuIds, String roleId) {
        return adminRuleService.insertauthority(menuIds, roleId);
    }

    @RequiresMenus(value = {"199901"})
    @RequestMapping(value = "rule/getPages", method = RequestMethod.GET)
    //@ApiOperation(value = "分页获取角色", notes = "分页获取角色", response = AdminRule.class)
    @ResponseBody
    public Object getApps(String keyWord, Page page) {
        return adminRuleService.getPageDatas(keyWord, page);
    }

    @RequiresMenus(value = {"199901"})
    @RequestMapping(value = "rule/findObj", method = RequestMethod.GET)
    //@ApiOperation(value = "获取角色详情", notes = "获取角色详情")
    @ResponseBody
    public Object getObj(String id) {
        return adminRuleService.findObj(id);
    }
    @RequiresMenus(value = {"199901"})
    @RequestMapping(value = "rule/insert", method = RequestMethod.POST)
    //@ApiOperation(value = "新增角色", notes = "新增角色")
    @ResponseBody
    public Object insert(@RequestBody AdminRuleVo adminRuleVo) {
        return adminRuleService.insert(adminRuleVo);
    }
    @RequiresMenus(value = {"199901"})
    @RequestMapping(value = "rule/update", method = RequestMethod.POST)
    //@ApiOperation(value = "修改角色", notes = "修改角色")
    @ResponseBody
    public Object update(@RequestBody AdminRuleVo adminRuleVo) {
        return adminRuleService.update(adminRuleVo);
    }
    @RequiresMenus(value = {"199901"})
    @RequestMapping(value = "rule/deleteLis", method = RequestMethod.GET)
    //@ApiOperation(value = "删除角色", notes = "删除角色")
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return adminRuleService.deleteLis(ids);
    }


    @RequestMapping(value = "getLoginAdminInfo", method = RequestMethod.GET)
    //@ApiOperation(value = "获取登陆用户信息", notes = "加载用户信息")
    @ResponseBody
    public Object getLoginAdminInfo() {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if (currentHr == null) {
            return new ResEntity(false, "请先登录", null);
        }
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("name", currentHr.getUsername());
            result.put("nameZh", currentHr.getNameZh());
            result.put("userId", currentHr.getUserId());
            result.put("expiredPassword", sysAdminService.needUpdatePwd(currentHr));
            result.put("roles", adminRuleService.getAdminRuleInfo(currentHr.getUserId()));
            result.put("menus", adminMenuService.getMenuByUID(currentHr.getUserId()).getData());

            result.put("mobile", currentHr.getPhone());
        } catch (Exception e) {
            e.printStackTrace();
        }
        result.put("sex", currentHr.getSex());

        return new ResEntity(true, Constant.SUCCESS_DX, result);
    }


    @RequestMapping(value = "changePwd", method = RequestMethod.POST)
    //@ApiOperation(value = "用户重置密码", notes = "用户重置密码")
    @ResponseBody
    public Object changePwd(@RequestBody ChangePwdBean changePwdBean) {
        if (StringUtils.isBlank(changePwdBean.getUserId())) {
            changePwdBean.setUserId(AdminWebUtils.getCurrentHr().getUserId());
        }
        return sysAdminService.changePwd(changePwdBean.getUserId(), URLDecoder.decode(changePwdBean.getUserPwd()), URLDecoder.decode(changePwdBean.getAuthPwd()));

    }

    @RequestMapping(value = "changeNoOldPwd", method = RequestMethod.POST)
    //@ApiOperation(value = "用户重置密码-不需要原密码", notes = "用户重置密码-不需要原密码")
    @ResponseBody
    public Object changeNoOldPwd(@RequestBody ChangePwdNoOldBean changePwdNoOldBean) {
        if (StringUtils.isBlank(changePwdNoOldBean.getUserId())) {
            return ResEntity.error("缺少userId");
        }
        try {
            return sysAdminService.changePwdNoOldBean(changePwdNoOldBean.getUserId(), URLDecoder.decode(changePwdNoOldBean.getUserPwd(), "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

    }

    @RequestMapping(value = "getAdminDisList", method = RequestMethod.GET)
    //@ApiOperation(value = "获取全部中医病种(中医病种+全科)", notes = "获取全部中医病种(中医病种+全科)")
    @ResponseBody
    public ResEntity getAdminDisList(String disName) {
        return ResEntity.success(webTDiseaseService.getAdminDisList(disName));
    }

    @RequestMapping(value = "getMySelfDisList", method = RequestMethod.GET)
    //@ApiOperation(value = "获取当前账号的中医病种", notes = "获取当前账号的中医病种")
    @ResponseBody
    public ResEntity getMySelfDisList(String disName) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        String userId = currentHr.getUserId();
        List<AdminRule> roles = currentHr.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            userId = null;
        }
        return ResEntity.success(webTDiseaseService.getMySelfDisList(disName,userId));
    }


}
