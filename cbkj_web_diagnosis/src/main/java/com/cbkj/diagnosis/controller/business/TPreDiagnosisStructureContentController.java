package com.cbkj.diagnosis.controller.business;


import com.cbkj.diagnosis.beans.diagnosisstructure.EmrList;
import com.cbkj.diagnosis.beans.diagnosisstructure.SavePageEmr;
import com.cbkj.diagnosis.beans.emr.EmrOptionStructure;
import com.cbkj.diagnosis.beans.emr.SaveOptionList;
import com.cbkj.diagnosis.common.annotation.RequiresMenus;
import com.cbkj.diagnosis.service.EmrService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
//@Api(value = "预诊emr病历模板", tags = "预诊emr病历模板")
@Tag(name = "预诊emr病历模板", description = "预诊emr病历模板")
@RestController
@RequestMapping("/structureContent")
public class TPreDiagnosisStructureContentController {


    private final EmrService emrService;

    public TPreDiagnosisStructureContentController(EmrService emrService) {
        this.emrService = emrService;
    }

    @RequiresMenus(value = {"100500"})
    @PostMapping(value = "savePageEmr")
    //@ApiOperation(value = "保存emr病历模板", notes = "保存emr病历模板")
    @ResponseBody
    public Object savePageEmr(@RequestBody SavePageEmr savePageEmr) {
        return emrService.savePageEmr(savePageEmr.getEmrLists());
    }

    @RequiresMenus(value = {"100500"})
    @GetMapping(value = "getPageEmr")
    //@ApiOperation(value = "获取预诊问卷的emr病历模板", notes = "获取预诊问卷的emr病历模板")
    @ResponseBody
    public Object getPageEmr(String diaId) {
        if (diaId == null) {
            return ResEntity.error("参数diaId不能为空");
        }
        return emrService.getPageEmr(diaId);
    }

    /**
     * 单选题、多选题 获取选项的转化术语信息
     */
    @RequiresMenus(value = {"100500"})
    @GetMapping(value = "getOptionStructureList")
    //@ApiOperation(value = "获取单选题、多选题 选项转化术语信息,其它类型题目的标题", notes = "获取单选题、多选题 选项转化术语信息,其它类型题目的标题", response = EmrOptionStructure.class)
    @ResponseBody
    public Object getOptionList(String questionId) {
        return emrService.getOptionList(questionId);
    }

    /**
     * 保存单选提、多选题的选项的转化术语信息
     */
    @RequiresMenus(value = {"100500"})
    @PostMapping(value = "saveOptionStructureList")
    //@ApiOperation(value = "保存单选提、多选题的选项的转化术语信息", notes = "保存单选提、多选题的选项的转化术语信息")
    @ResponseBody
    public Object saveOptionList(@RequestBody SaveOptionList emrOptionStructure) {
        return emrService.saveOptionList(emrOptionStructure);
    }

}
