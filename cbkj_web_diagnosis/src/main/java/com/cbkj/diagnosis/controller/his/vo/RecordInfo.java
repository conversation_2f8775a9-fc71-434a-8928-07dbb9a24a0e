package com.cbkj.diagnosis.controller.his.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class RecordInfo {
    @Schema(description =  "就诊医生名字")
    private String doctorName;

    @Schema(description =  "医生ID")
    private String doctorId;

    @Schema(description =  "就诊科室名称")
    private String deptName;

    private String appId;

    @Schema(description =  "机构代码")
    private String insCode;

    @Schema(description =  "机构名")
    private String insName;

    @Schema(description =  "患者就诊科室id")
    private String deptId;
    private String deptCode;

    @Schema(description =  "主诉")
    private String chiefComplaint;

    @Schema(description =  "现病史")
    private String presentIllness;

    @Schema(description =  "既往史")
    private String pastHistory;

    @Schema(description =  "个人史")
    private String personalHistory;

    @Schema(description =  "家族史")
    private String familyHistory;

    @Schema(description =  "体格检查")
    private String physicalExamination;

    @Schema(description =  "中医疾病名")
    private String chineseDisName;

    @Schema(description =  "西医疾病名")
    private String westDisName;

    @Schema(description =  "中医疾病id")
    private String chineseDisId;

    @Schema(description =  "西医疾病id")
    private String westDisId;

    @Schema(description =  "证型名称")
    private String symName;

    @Schema(description =  "证型id")
    private String symId;

    @Schema(description =  "治疗意见")
    private String recordsAdvice;

  //  @Schema(description =  "就诊时间")
    private Date recordTime;
    private String visitTime;
   // private Date visitTimeDate;

//    @Schema(description =  "创建日期")
//    private Date createDate;

    @Schema(description =  "诊次")
    private Integer recordsTimes;


    @Schema(description =  "患者名字")
    private String patientName;

    @Schema(description =  "患者身份id（t_admin_info）")
    private String patientId;

    @Schema(description =  "患者性别")
    private String patientSex;

    @Schema(description =  "患者手机号")
    private String patientPhone;

    @Schema(description =  "患者年龄")
    private String patientAge;

    @Schema(description="患者证件")
    private String patientCardNumber;

    @Schema(description =  "his就诊号")
    private String visitNo;

    @Schema(description =  "城乡居民健康档案编号")
    private String healthFilesNum;

    @Schema(description =  "身份证件类别代码")
    private String patientCardType;

    @Schema(description =  "居民健康卡号")
    private String patientHealthCardNum;

    @Schema(description =  "医疗保险类别代码")
    private String insuranceTypeCode;

    @Schema(description =  "婚姻状况代码")
    private String maritalStatus;

    @Schema(description =  "民族")
    private String nation;

    @Schema(description =  "联系人姓名")
    private String contactsName;

    @Schema(description =  "联系人电话号码")
    private String contactsPhone;

    @Schema(description =  "国籍代码")
    private String nationality;

    @Schema(description =  "学历代码")
    private String educationCode;

    @Schema(description =  "联系人与患者的关系代码")
    private String contactsAndPatientRelationship;

    @Schema(description =  "中医基本舌象诊断信息")
    private String chineseBaseTongueInfo;

    @Schema(description =  "中医基本脉象诊断信息")
    private String chineseBasePulse;

    @Schema(description =  "辅助检查结果")
    private String auxiliaryInspectionResults;

    @Schema(description =  "辨证依据")
    private String syndromeDifferentiationBasis;

    @Schema(description =  "治则治法")
    private String tcmApproach;

    @Schema(description =  "中医病因分类")
    private String etiologyClassification;

    @Schema(description =  "中医辨证方法")
    private String syndromeDifferentiationMethod;

    @Schema(description =  "中医基本病机")
    private String tcmPathogenesis;

    @Schema(description =  "体质影响因素")
    private String physicalAffectingFactors;

    @Schema(description =  "中医发病类型")
    private String tcmIncidentsType;

    @Schema(description =  "中医发病形式")
    private String tcmIncidentsForm;

    @Schema(description =  "检查检验项目信息")
    private String inspectionInfoString;

    @Schema(description =  "医师签名")
    private String yiShiSign;

    @Schema(description =  "职业(患者当前职业范畴的完整描述)")
    private String occupation;

    @Schema(description =  "发病节气")
    private String onsetSolarTermCode;

    @Schema(description =  "初诊标志 Y是 N否")
    private String initialDiagnosisCode;

    @Schema(description =  "治疗类别")
    private String treatmentCategoryCode;

    @Schema(description =  "实施临床路径")
    private String clinicalPathwayCode;

    @Schema(description =  "科研病历标志")
    private String scientificResearchFlag;

    @Schema(description =  "过敏史标志")
    private String allergyHistoryFlag;

    @Schema(description =  "过敏史")
    private String allergyHistory;

    @Schema(description =  "药物过敏标志")
    private String allergicDrugFlag;

    @Schema(description =  "过敏药物")
    private String allergicDrug;

    @Schema(description =  "传染病历标志")
    private String infectiousHistoryFlag;

    @Schema(description =  "传染病史")
    private String infectiousHistory;

    @Schema(description =  "预防接种史")
    private String vaccinationHistory;

    @Schema(description =  "手术史")
    private String surgicalHistory;

    @Schema(description =  "输血史")
    private String bloodTransfusionHistory;

    @Schema(description =  "怀孕标志")
    private String pregnancyFlag;

    @Schema(description =  "哺乳期标志")
    private String sucklingPeriodFlag;

    @Schema(description =  "婚育史")
    private String obstetricHistory;

    @Schema(description =  "月经史")
    private String menstrualHistory;

    @Schema(description =  "身高")
    private String height;

    @Schema(description =  "体重")
    private String weight;

    @Schema(description =  "BMI")
    private String bmi;

    @Schema(description =  "辅助检查项目")
    private String auxiliaryInspectionItems;

    @Schema(description =  "中医基本症状描述")
    private String symptomDescription;

    @Schema(description =  "中医基本舌象诊断信息")
    private String tongueCondition;

    @Schema(description =  "中医基本脉象诊断信息")
    private String pulseCondition;

    @Schema(description =  "西医诊断代码")
    private String westDisCode;

    @Schema(description =  "中医病名代码")
    private String chineseDisCode;

    @Schema(description =  "中医证候代码")
    private String symCode;

    @Schema(description =  "诊断标识代码")
    private String diagnosticFlag;

    /**
     * 20250304 新增
     */
    @Schema(description =  "医疗费用结算方式代码")
    private String medicalExpensesSettledCode;

    @Schema(description =  "中医病因分类代码")
    private String etiologyClassificationCode;

    @Schema(description =  "中医证候分类代码")
    private String physicalAffectingFactorsCode;

    /**
     * 20250311 新增
     */
    @Schema(description =  "医疗保险类别代码")
    private String insuranceCategoryCode;
}
