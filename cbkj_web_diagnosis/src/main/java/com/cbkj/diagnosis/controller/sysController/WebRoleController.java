//package com.cbkj.diagnosis.controller.sysController;
//
//import com.cbkj.diagnosis.beans.sysBeans.AdminRuleMenuVo;
//import com.cbkj.diagnosis.service.sysService.AdminRuleService;
////import io.swagger.annotations.Api;
//import io.swagger.v3.oas.annotations.media.Schema;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.ResponseBody;
//
///**
// * <AUTHOR>
// */
////@Api(value = "菜单接口", tags = "菜单接口", position = 4)
//@Controller
//@RequestMapping("admininfo")
//public class WebRoleController {
//
//    private final AdminRuleService adminRuleService;
//
//    public WebRoleController(AdminRuleService adminRuleService) {
//        this.adminRuleService = adminRuleService;
//    }
//
//    @RequestMapping(value = "rule/authority/findMenu", method = RequestMethod.GET)
//    //@ApiOperation(value = "加载角色菜单", notes = "加载角色菜单")
//    @ResponseBody
//    public Object findMenu(String id) {
//        return adminRuleService.findMenuByMID(id, "2", true);
//    }
//
//    @RequestMapping(value = "rule/getAdminRuleMenu", method = RequestMethod.POST)
//    //@ApiOperation(value = "获取角色菜单配置信息", notes = "获取角色菜单配置信息")
//    @ResponseBody
//    public Object getAdminRuleMenu(String roleId) {
//        return adminRuleService.getAdminRuleMenu(roleId);
//    }
//
//    @RequestMapping(value = "rule/saveAdminRuleMenu", method = RequestMethod.POST)
//    //@ApiOperation(value = "保存角色菜单配置信息", notes = "保存角色菜单配置信息")
//
//    @ResponseBody
//    public Object saveAdminRuleMenu(@RequestBody AdminRuleMenuVo adminRuleMenuVo) {
//        return adminRuleService.saveAdminRuleMenu(adminRuleMenuVo);
//    }
//
//
//
//}
