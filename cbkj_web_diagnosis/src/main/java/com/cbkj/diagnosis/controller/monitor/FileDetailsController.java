package com.cbkj.diagnosis.controller.monitor;

import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.webapi.business.WebTRecordDiaService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/5 11:11
 * @Version 1.0
 */
@Controller
//@Api(value = "档案详情", tags = "档案详情")
@Tag(name = "档案详情", description = "档案详情")
@RequestMapping("archives/")
public class FileDetailsController {

    private final WebTRecordDiaService tRecordDiaService;

    public FileDetailsController(WebTRecordDiaService tRecordDiaService) {
        this.tRecordDiaService = tRecordDiaService;
    }

    @GetMapping(value = "diagnosis/pati/info")
    //@ApiOperation(value = "查看患者问诊单-预诊、随访", notes = "查看患者问诊单-预诊、随访", response = QuestionMain.class)
    @ResponseBody
    public Object getPrePaper(String recId) {
        Object pageDatas = tRecordDiaService.getPrePaper(recId);
        return pageDatas;
    }


    @GetMapping(value = "edu/details")
    //@ApiOperation(value = "健康宣教详情", notes = "健康宣教详情")
    @ResponseBody
    public Object eduDetails(Integer tPropagandaEduId) {
        return ResEntity.success(tRecordDiaService.eduDetails(tPropagandaEduId));
    }
}
