package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.common.vo.AiSuiFangListRe;
import com.cbkj.diagnosis.service.common.vo.SuiFangPreListReVo;
import com.cbkj.diagnosis.service.vo.PhoneTaskPageList;
import com.cbkj.diagnosis.service.webapi.business.vo.FaceListVo;
import com.cbkj.diagnosis.service.webapi.business.vo.PhoneTaskPageListCore;
import com.cbkj.diagnosis.service.webapi.business.vo.PhoneTaskPageListReturnVo;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.cbkj.diagnosis.service.webapi.business.WebTRecordService;

import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
//@Schema(description = "预诊随访的面访列表、智能随访明细")
@Tag(name = "预诊随访的面访列表", description = "预诊随访的面访列表")
@RequestMapping("suifang/detail")
public class SuiFangExcuteDetailController {

    private WebTRecordService webTRecordService;

    SuiFangExcuteDetailController(WebTRecordService webTRecordService){
        this.webTRecordService = webTRecordService;
    }

    @GetMapping(value = "getFacePageList")
    //@ApiOperation(value = "医生面访列表", notes = "医生面访列表")
    @Operation(description = "医生面访列表", summary = "医生面访列表",responses = {
            @ApiResponse(description = "医生面访列表",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = FaceListVo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getFacePageList(@ParameterObject SuiFangPreListReVo suiFangPreListReVo, @ParameterObject Page page) {
        return webTRecordService.getFacePageList(suiFangPreListReVo,page);

    }



//    @GetMapping(value = "getTaskPageList")
//    //@ApiOperation(value = "智能随访执行明细列表-上面的筛选条件-内容-是查随访、自测量表、健康宣教", notes = "智能随访执行明细列表-上面的筛选条件-内容-是查随访、自测量表、健康宣教")
//    @ResponseBody
//    public Object getTaskPageList(AiSuiFangListRe aiSuiFangListRe , Page page) {
//
//        return ResEntity.success(null);
//    }


    @GetMapping(value = "getTaskPageList")
    //@ApiOperation(value = "智能随访执行明细列表", notes = "智能随访执行明细列表")
    @Operation(description = "智能随访执行明细列表", summary = "智能随访执行明细列表",responses = {
            @ApiResponse(description = "智能随访执行明细列表",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SRoadTaskPatients.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getTaskPageList(@ParameterObject AiSuiFangListRe aiSuiFangListRe , @ParameterObject Page page) {
        // 不一定有关联到任务 所以下面代码删除
//        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
//        List<AdminRule> roles = currentHr.getRoles();
//        aiSuiFangListRe.setCreateUserId(currentHr.getUserId());
//        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
//            aiSuiFangListRe.setCreateUserId(null);
//        }
        return webTRecordService.getTaskPageList(aiSuiFangListRe,page);

    }


    @GetMapping(value = "getPhoneTaskPageList")
    //@ApiOperation(value = "萧山电话随访明细", notes = "萧山电话随访明细",response = PhoneTaskPageListReturnVo.class)
    @Operation(description = "萧山电话随访明细", summary = "萧山电话随访明细",responses = {
            @ApiResponse(description = "萧山电话随访明细",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = PhoneTaskPageListReturnVo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPhoneTaskPageList(@ParameterObject PhoneTaskPageList phoneTaskPageList , @ParameterObject Page page) {
        PhoneTaskPageListCore phoneTaskPageListCore = new PhoneTaskPageListCore();
        BeanUtils.copyProperties(phoneTaskPageList,phoneTaskPageListCore);
        return webTRecordService.getPhoneTaskPageList(phoneTaskPageListCore,page);

    }

//    @GetMapping(value = "getTaskPageList")
//    //@ApiOperation(value = "电话随访详情", notes = "智能随访执行明细列表")
//    @ResponseBody
//    public Object getTaskPageList(AiSuiFangListRe aiSuiFangListRe , Page page) {
//        return webTRecordService.getTaskPageList(aiSuiFangListRe,page);
//
//    }


//    @GetMapping(value = "patients/task/add")
//    //@ApiOperation(value = "患者加入随访任务-不需要病历直接加入任务并且发送执行", notes = "患者加入随访任务-不需要病历直接加入任务并且发送执行")
//    @ResponseBody
//    public Object addPatientToTask(String patientIds,String sroadTaskId) {
//        return webTRecordService.addPatientsToTask(patientIds,sroadTaskId);
//    }

}
