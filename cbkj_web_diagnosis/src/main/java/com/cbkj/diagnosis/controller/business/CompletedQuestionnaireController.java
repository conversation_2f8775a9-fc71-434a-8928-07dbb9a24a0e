package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.beans.business.CompletedQuestionnaireListRes;
import com.cbkj.diagnosis.beans.business.TBusinessEdition;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.CompletedQuestionnaireService;
import com.cbkj.diagnosis.service.vo.CompletedQuestionnaireRe;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;

@Controller
//@Api(value = "已填写问卷管理", tags = "已填写问卷管理")
@Tag(name = "已填写问卷管理", description = "已填写问卷管理")
@RequestMapping("suifang/questionnaire")
public class CompletedQuestionnaireController {

    private final HttpServletResponse httpServletResponse;
    private final CompletedQuestionnaireService completedQuestionnaireService;

    public CompletedQuestionnaireController(HttpServletResponse httpServletResponse, CompletedQuestionnaireService completedQuestionnaireService) {
        this.httpServletResponse = httpServletResponse;
        this.completedQuestionnaireService = completedQuestionnaireService;
    }

    @GetMapping(value = "getQuestionnairePageList", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(description = "获取已填问卷列表", summary = "获取已填问卷列表", responses = {
            @ApiResponse(description = "获取已填问卷列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CompletedQuestionnaireListRes.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getList(@ParameterObject CompletedQuestionnaireRe completedQuestionnaireRe) {
        Page page = new Page(completedQuestionnaireRe.getPage(), completedQuestionnaireRe.getLimit());
        return completedQuestionnaireService.getList(completedQuestionnaireRe, page);

    }


//    @GetMapping(value = "download/questionnaire", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @GetMapping(value = "download/questionnaire")
    //@ApiOperation(value = "获取已填随访问卷列表下载", notes = "获取已填随访问卷列表下载",produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @Operation(
            summary = "下载已填问卷数据",
            description = "根据查询条件导出问卷数据为Excel文件",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Excel文件流",
                            content = @Content(//application/octet-stream
                                    mediaType = "application/zip",
                                    schema = @Schema(type = "string", format = "binary")
                            )
                    )
            }
    )
//    @ResponseBody
    public String getDownloadQuestionnaire(@ParameterObject CompletedQuestionnaireRe completedQuestionnaireRe) {

        completedQuestionnaireService.downloadQuestionnaire(completedQuestionnaireRe, httpServletResponse);
        return null;
    }
}
