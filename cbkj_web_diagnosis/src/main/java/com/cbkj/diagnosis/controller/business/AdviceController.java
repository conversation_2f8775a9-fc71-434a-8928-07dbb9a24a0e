package com.cbkj.diagnosis.controller.business;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/2 9:56
 * @Version 1.0
 */
import com.cbkj.diagnosis.beans.business.TBusinessProposal;
import com.cbkj.diagnosis.beans.business.adviceVo.*;
import com.cbkj.diagnosis.beans.sysBeans.AdminMenu;
import com.cbkj.diagnosis.common.annotation.RequiresMenus;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.service.advice.AdviceSerivice;
import com.cbkj.diagnosis.service.business.impl.TBusinessProposalServiceImpl;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;

@Controller
//@Api(value = "意见建议接口", tags = "意见建议接口")
@Tag(name = "意见建议接口", description = "意见建议接口")
public class AdviceController {

    private final TBusinessProposalServiceImpl proposalService;

    private final AdviceSerivice adviceSerivice;

    public AdviceController(TBusinessProposalServiceImpl proposalService, AdviceSerivice adviceSerivice) {
        this.proposalService = proposalService;
        this.adviceSerivice = adviceSerivice;
    }


    @RequestMapping(value = "advice/list", method = RequestMethod.GET)
    @ResponseBody
    @RequiresMenus({"199903"})
    //@ApiOperation(value = "管理员获取问题建议咨询列表", notes = "获取问题建议咨询列表", response = GetAdviceListRes.class)
    @Operation(description = "管理员获取问题建议咨询列表", summary = "管理员获取问题建议咨询列表",responses = {
            @ApiResponse(description = "管理员获取问题建议咨询列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = GetAdviceListRes.class)
                    )
            )
    }
    )
    public Object getAdviceList(@ParameterObject com.cbkj.diagnosis.common.utils.Page page, @ParameterObject GetAdviceListReq getAdviceListReq) {
        return adviceSerivice.getAdviceList(page, getAdviceListReq,null);
    }

    @RequestMapping(value = "advice/detail", method = RequestMethod.GET)
    @ResponseBody
    @RequiresMenus({"199903"})
    //@ApiOperation(value = "管理员获取问题建议咨询详情", notes = "管理员获取问题建议咨询详情", response = TBusinessProposalRes.class)
    @Operation(description = "管理员获取问题建议咨询详情", summary = "管理员获取问题建议咨询详情",responses = {
            @ApiResponse(description = "管理员获取问题建议咨询详情",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TBusinessProposalRes.class)
                    )
            )
    }
    )
    public ResEntity getAdviceDetail(Long id) {
        TBusinessProposalRes adviceDetail = adviceSerivice.getAdviceDetail(id, null);
        return ResEntity.success(adviceDetail);
    }

    @RequestMapping(value = "advice/delete", method = RequestMethod.GET)
    @ResponseBody
    @RequiresMenus({"199903"})
    //@ApiOperation(value = "管理员删除问题建议咨询", notes = "管理员删除问题建议咨询", response = ResEntity.class)
    @Operation(description = "管理员删除问题建议咨询", summary = "管理员删除问题建议咨询",responses = {
            @ApiResponse(description = "管理员删除问题建议咨询",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    public ResEntity deleteAdvice(Long id) {
        adviceSerivice.deleteAdvice(id, null);
        return ResEntity.success();
    }


    @RequestMapping(value = "advice/fix", method = RequestMethod.POST)
    @ResponseBody
    @RequiresMenus({"199903"})
    //@ApiOperation(value = "管理员处理问题建议咨询", notes = "管理员处理问题建议咨询", response = ResEntity.class)
    @Operation(description = "管理员处理问题建议咨询", summary = "管理员处理问题建议咨询",responses = {
            @ApiResponse(description = "管理员处理问题建议咨询",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    public ResEntity fixAdvice(@RequestBody FixAdvice fixAdvice) {
        if (StringUtils.isBlank(fixAdvice.getProposalReceiveOpinion())){
            return ResEntity.error("请输入处理意见");
        }
        if (fixAdvice.getId() == null){
            return ResEntity.error("参数错误");
        }
        adviceSerivice.fixAdvice(fixAdvice,null);
        return ResEntity.success();
    }


    @RequestMapping(value = "advice/user/save", method = RequestMethod.POST)
    @ResponseBody
    //@ApiOperation(value = "医生创建问题建议咨询", notes = "医生创建问题建议咨询", response = TBusinessProposal.class)
    @Operation(description = "医生创建问题建议咨询", summary = "医生创建问题建议咨询",responses = {
            @ApiResponse(description = "医生创建问题建议咨询",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TBusinessProposal.class)
                    )
            )
    }
    )
    public ResEntity userSaveAdvice(@RequestBody AdviceSave adviceSave) {
        return proposalService.saveAdvice(adviceSave, AdminWebUtils.getCurrentHr().getUserId(),AdminWebUtils.getCurrentHr().getNameZh(),1);

    }

    @RequestMapping(value = "advice/user/update", method = RequestMethod.POST)
    @ResponseBody
    //@ApiOperation(value = "医生编辑保存问题建议咨询", notes = "医生编辑保存问题建议咨询", response = TBusinessProposal.class)
    @Operation(description = "医生编辑保存问题建议咨询", summary = "医生编辑保存问题建议咨询",responses = {
            @ApiResponse(description = "医生编辑保存问题建议咨询",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TBusinessProposal.class)
                    )
            )
    }
    )
    public ResEntity userUpdateAdvice(@RequestBody AdviceSave adviceSave) {
        if (adviceSave.getId() == null){
            return ResEntity.error("参数错误");
        }
        return proposalService.userUpdateAdvice(adviceSave,AdminWebUtils.getCurrentHr().getUserId(),AdminWebUtils.getCurrentHr().getNameZh(),1);
    }

    @RequestMapping(value = "advice/user/delete", method = RequestMethod.GET)
    @ResponseBody
    //@ApiOperation(value = "医生删除自己的问题建议咨询", notes = "医生删除自己的问题建议咨询", response = ResEntity.class)
    @Operation(description = "医生删除自己的问题建议咨询", summary = "医生删除自己的问题建议咨询",responses = {
            @ApiResponse(description = "医生删除自己的问题建议咨询",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    public ResEntity userDeleteAdvice(Long id) {
        if (id == null){
            return ResEntity.error("参数错误");
        }
        adviceSerivice.deleteAdvice(id, AdminWebUtils.getCurrentHr().getUserId());
        return ResEntity.success();
    }


    @RequestMapping(value = "advice/user/list", method = RequestMethod.GET)
    @ResponseBody
    //@ApiOperation(value = "医生查看我的问题建议咨询列表", notes = "医生查看我的问题建议咨询列表", response = GetAdviceListRes.class)
    @Operation(description = "医生查看我的问题建议咨询列表", summary = "医生查看我的问题建议咨询列表",responses = {
            @ApiResponse(description = "医生查看我的问题建议咨询列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = GetAdviceListRes.class)
                    )
            )
    }
    )
    public Object userLookAdvice(@ParameterObject com.cbkj.diagnosis.common.utils.Page page, @ParameterObject GetAdviceListReq getAdviceListReq) {
        return adviceSerivice.getAdviceList(page, getAdviceListReq,AdminWebUtils.getCurrentHr().getUserId());
    }

    @RequestMapping(value = "advice/user/detail", method = RequestMethod.GET)
    @ResponseBody
    //@ApiOperation(value = "医生查看我的问题建议咨询详情", notes = "医生查看我的问题建议咨询详情", response = TBusinessProposalRes.class)
    @Operation(description = "医生查看我的问题建议咨询详情", summary = "医生查看我的问题建议咨询详情",responses = {
            @ApiResponse(description = "医生查看我的问题建议咨询详情",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TBusinessProposalRes.class)
                    )
            )
    }
    )
    public ResEntity userLookAdviceDetail(Long id) {
        TBusinessProposalRes adviceDetail = adviceSerivice.getAdviceDetail(id, AdminWebUtils.getCurrentHr().getUserId());
        return ResEntity.success(adviceDetail);
    }


}
