package com.cbkj.diagnosis.controller.his.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ChinesePrescriptionsItem {
    @Schema(description =  "药名称")
    private String drugName;

    @Schema(description =  "药代码")
    private String drugCode;

    @Schema(description =  "中药使用次剂量")
    private String drugDose;

    @Schema(description =  "中药特殊煎煮方法")
    private String teShuZhuFa;

    @Schema(description =  "中药剂型")
    private String drugDosage;

    @Schema(description =  "中药使用剂量单位")
    private String drugDoseUnit;

    @Schema(description =  "中药使用频次")
    private String drugFrequency;

    @Schema(description =  "中药用药途径")
    private String administrationRoute;

    @Schema(description =  "中药使用总剂量")
    private String drugTotalDosage;

    @Schema(description =  "医保用药级别代码")
    private String drugLevelCode;

    @Schema(description =  "中药材及饮片类别")
    private String drugCategoryCode;

    @Schema(description =  "中药特殊煎煮方法")
    private String drugDecoctingMethod;

    @Schema(description =  "君臣佐使分类代码")
    private String mmagCode;

    @Schema(description =  "处方药品组号")
    private String drugOrder;

    /**
     * 20250304 新增
     */
    @Schema(description =  "药物使用频次代码")
    private String drugFrequencyCode;

    @Schema(description =  "用药途径代码")
    private String administrationRouteCode;
}
