package com.cbkj.diagnosis.controller.business;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TBusinessAnnex;
import com.cbkj.diagnosis.beans.business.adviceVo.GetAdviceListReq;
import com.cbkj.diagnosis.beans.business.adviceVo.GetAdviceListRes;
import com.cbkj.diagnosis.beans.request.SystemHelp;
import com.cbkj.diagnosis.common.annotation.RequiresMenus;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.TBusinessAnnexMapper;
import com.cbkj.diagnosis.service.advice.SystemHelpService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.java.Log;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/3 10:20
 * @Version 1.0
 */
@Log
@RestController
//@Api(value = "系统帮助", tags = "系统帮助")
@Tag(name = "系统帮助", description = "系统帮助")
public class SystemHelpController {
    @Value("${file.address}")
    private String localtion;

    @Value("${root.preview}")
    private String preview;
    private final SystemHelpService systemHelpService;
    private final TBusinessAnnexMapper tBusinessAnnexMapper;

    private final HttpServletResponse httpServletResponse;

    public SystemHelpController(SystemHelpService systemHelpService, TBusinessAnnexMapper tBusinessAnnexMapper, HttpServletResponse httpServletResponse) {
        this.systemHelpService = systemHelpService;
        this.tBusinessAnnexMapper = tBusinessAnnexMapper;
        this.httpServletResponse = httpServletResponse;
    }

    @RequestMapping(value = "sys/help/list", method = RequestMethod.GET)
    @ResponseBody
    //@ApiOperation(value = "系统帮助列表", notes = "系统帮助列表", response = GetAdviceListRes.class)
    public Object getSystemHelpList(@ParameterObject SystemHelp systemHelp, Page page) {
        return systemHelpService.getSystemHelpList(systemHelp,page);
    }

    @RequestMapping(value = "sys/help/download", method = RequestMethod.GET)
    public ResEntity getFileByUrl(String fileUrl){

        QueryWrapper<TBusinessAnnex> wrapper = new QueryWrapper<>();
        wrapper.eq("annex_path", fileUrl).in("annex_type",2,3);
        TBusinessAnnex tBusinessAnnex = tBusinessAnnexMapper.selectOne(wrapper);

        if (tBusinessAnnex == null){
            return ResEntity.error("未找到资源");
        }
        if (fileUrl.startsWith(preview)) {
            fileUrl = fileUrl.replace(preview, localtion);
        }
        log.info("fileUrl:" + fileUrl);
        File file = new File(fileUrl);
        if (file.exists()) {
            try {
                //为文件重新设置名字，采用数据库内存储的文件名称
                String fileName = tBusinessAnnex.getAnnexName();
                fileName = new String(fileName.getBytes("UTF-8"), "ISO_8859_1");

                if (StringUtils.isNotBlank(tBusinessAnnex.getAnnexSuffixName())) {
                    httpServletResponse.setContentType(tBusinessAnnex.getAnnexSuffixName());
                } else {
                    httpServletResponse.setContentType("application/octet-stream");
                }
                httpServletResponse.setHeader("Content-Length", file.length() + "");
                httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + fileName);

                BufferedInputStream br = new BufferedInputStream(new FileInputStream(file));
                OutputStream os = httpServletResponse.getOutputStream();
                byte[] b = new byte[1024];
                while (br.read(b) != -1) {
                    os.write(b);
                }
                br.close();
                os.flush();
                os.close();


                return ResEntity.success();

            } catch (IOException e) {
                e.printStackTrace();
                return ResEntity.entity(false, "下载帮助手册失败!", fileUrl);
            }
        } else {
            return ResEntity.entity(false, "找不到该文件!", fileUrl);
        }
    }
}
