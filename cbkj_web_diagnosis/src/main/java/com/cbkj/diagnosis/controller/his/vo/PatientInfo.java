package com.cbkj.diagnosis.controller.his.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PatientInfo {
    @Schema(description =  "患者证件类型呢")
    private String patientCardType;
    @Schema(description =  "患者证件号码")
    private String patientCardNumber;

    @Schema(description =  "患者健康卡号",required = true)
    private String healthCardNum;

    @Schema(description =  "城乡居民健康档案编号")
    private String healthFilesNum;

    @Schema(description =  "患者名字",required = true)
    private String patientName;
    @Schema(description =  "患者电话",required = true)
    private String patientPhone;
    @Schema(description =  "患者出生日期")
    private String patientBirthday;

    @Schema(description =  "患者性别：1男2女")
    private String patientSex;
    @Schema(description =  "婚姻状况代码：10未婚 20已婚 21初婚 22在婚 23复婚 30丧偶 40离婚 90 未说明")
    private String maritalStatus;

    @Schema(description =  "名族")
    private String nation;

    @Schema(description =  "国籍代码--新增")
    private String nationality;

    @Schema(description =  "教育代码：1小学 2初中 3高中 4中等职业 5.专科 6本科7硕士8博士")
    private String educationCode;
    @Schema(description =  "联系人名")
    private String contactsName;

    @Schema(description =  "联系人电话")
    private String contactsPhone;

    @Schema(description =  "联系人关系1\n" +
            "配偶\n" +
            "2\n" +
            "子\n" +
            "3\n" +
            "女\n" +
            "4\n" +
            "孙子、孙女或外孙子、外孙女\n" +
            "5\n" +
            "父母\n" +
            "6\n" +
            "祖父母或外祖父母\n" +
            "7\n" +
            "兄、弟、姐、妹\n" +
            "99\n" +
            "其他")
    private String contactsRelationship;

    @Schema(description =  "患者就诊卡号")
    private String medicalCard;
}
