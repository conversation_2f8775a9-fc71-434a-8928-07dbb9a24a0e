package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.service.quesionbank.QuestionBankService;
import com.cbkj.diagnosis.service.webapi.business.vo.LookPaperRecive;
//import io.swagger.annotations.Api;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/28 14:00
 * @Version 1.0
 */

@Controller
//@Api(value = "题库", tags = "题库")
@Tag(name = "题库", description = "题库")
@RequestMapping("questionBank")
public class QuestionBankController {

    private QuestionBankService questionBankService;

    public QuestionBankController(QuestionBankService questionBankService) {
        this.questionBankService = questionBankService;
    }

    @PostMapping(value = "setPaper")
    @Operation(description = "保存题库", summary = "保存题库",responses = {
            @ApiResponse(description = "保存题库",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = LookPaperRecive.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object setPaper(@RequestBody LookPaperRecive lookPaperRecive) {
        return questionBankService.setPaper(lookPaperRecive);
    }

    @GetMapping(value = "getPaper")
    @Operation(description = "获取题库", summary = "获取题库",responses = {
            @ApiResponse(description = "获取题库",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = LookPaperRecive.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPaper() {
        return questionBankService.getPaper();
    }

}
