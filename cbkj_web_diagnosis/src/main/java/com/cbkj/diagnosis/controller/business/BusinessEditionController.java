package com.cbkj.diagnosis.controller.business;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TBusinessEdition;
import com.cbkj.diagnosis.beans.business.TBusinessProposal;
import com.cbkj.diagnosis.beans.business.adviceVo.AdviceSave;
import com.cbkj.diagnosis.beans.business.adviceVo.GetAdviceListRes;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.service.business.impl.TBusinessEditionServiceImpl;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/3 16:59
 * @Version 1.0
 */
@RestController
@Tag(name = "系统版本升级信息", description = "系统版本升级信息")
public class BusinessEditionController {

    private final TBusinessEditionServiceImpl tBusinessEditionService;


    public BusinessEditionController(TBusinessEditionServiceImpl tBusinessEditionService) {
        this.tBusinessEditionService = tBusinessEditionService;
    }

    @RequestMapping(value = "system/version/list", method = RequestMethod.GET)
    @ResponseBody
    @Operation(description = "系统版本升级信息", summary = "系统版本升级信息",responses = {
            @ApiResponse(description = "系统版本升级信息",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TBusinessEdition.class)
                    )
            )
    }
    )
    public ResEntity getSystemVersionList() {
        QueryWrapper<TBusinessEdition> tBusinessEditionQueryWrapper = new QueryWrapper<>();
        tBusinessEditionQueryWrapper.eq("is_del", 0).orderByDesc("edition_time");
        List<TBusinessEdition> list = tBusinessEditionService.list(tBusinessEditionQueryWrapper);
        return ResEntity.success(list);

    }

}
