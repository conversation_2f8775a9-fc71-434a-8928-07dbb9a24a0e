//package com.cbkj.diagnosis.controller.monitor;
//
//import com.cbkj.diagnosis.beans.business.TDisease;
//import com.cbkj.diagnosis.service.webapi.business.WebTDiseaseService;
//import com.cbkj.diagnosis.sysBeans.ResEntity;
////import io.swagger.annotations.Api;
//import io.swagger.v3.oas.annotations.media.Schema;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @Description TODO
// * @Date 2024/8/14 9:45
// * @Version 1.0
// */
////@Api(value = "监测-首页数据接口", tags = "首页数据接口", position = 1)
//@RestController
//public class StatisticsController {
//
//    private final WebTDiseaseService webTDiseaseService;
//
//    public StatisticsController(WebTDiseaseService webTDiseaseService) {
//        this.webTDiseaseService = webTDiseaseService;
//    }
//
//    //@ApiOperation(value = "获取首页疾病下拉数据", notes = "获取首页疾病")
//    @GetMapping("/block/disease")
//    public ResEntity getDiseaseList() {
//        List<TDisease> list = webTDiseaseService.getDiseaseDiagnosisMappingList();
//        return ResEntity.success(list);
//    }
//}
