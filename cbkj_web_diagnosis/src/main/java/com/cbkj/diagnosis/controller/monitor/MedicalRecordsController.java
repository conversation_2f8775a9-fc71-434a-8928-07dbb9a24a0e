package com.cbkj.diagnosis.controller.monitor;



import com.cbkj.diagnosis.beans.business.TRecord;
import com.cbkj.diagnosis.beans.monitor.dto.FileDetailsConsultationDTO;
import com.cbkj.diagnosis.beans.monitor.dto.PatientInfoDTO;
import com.cbkj.diagnosis.beans.monitor.dto.RespDTO;
import com.cbkj.diagnosis.beans.monitor.vo.GetPatientInfo;
import com.cbkj.diagnosis.facade.MedicalRecordsFacade;
//import io.swagger.annotations.Api;
import com.cbkj.diagnosis.service.monitor.impl.MedicalRecordsServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 就诊记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@RestController("/medicalRecords")
//@Api(value = "就诊记录",tags = "就诊记录")
@Tag(name = "就诊记录", description = "就诊记录")
public class MedicalRecordsController {


    private final MedicalRecordsFacade medicalRecordsFacade;
    private final MedicalRecordsServiceImpl medicalRecordsService;

    public MedicalRecordsController(MedicalRecordsFacade medicalRecordsFacade, MedicalRecordsServiceImpl medicalRecordsService) {
        this.medicalRecordsFacade = medicalRecordsFacade;
        this.medicalRecordsService = medicalRecordsService;
    }

    @GetMapping("/detail")
    @Operation(description = "档案详情-就诊", summary = "档案详情-就诊",responses = {
            @ApiResponse(description = "档案详情-就诊",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = FileDetailsConsultationDTO.class)
                    )
            )
    }
    )
    @ResponseBody
    public RespDTO<FileDetailsConsultationDTO> detail(String recordId) {
        FileDetailsConsultationDTO dto = medicalRecordsService.detail(recordId);
        return RespDTO.onSuc(dto);

    }

    @GetMapping("/get/patient/info")
    @Operation(description = "档案详情主界面顶部、侧边栏数据", summary = "档案详情主界面顶部、侧边栏数据",responses = {
            @ApiResponse(description = "档案详情主界面顶部、侧边栏数据",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = PatientInfoDTO.class)
                    )
            )
    }
    )
    @ResponseBody
    public RespDTO<PatientInfoDTO> getPatientInfo(@ParameterObject GetPatientInfo getPatientInfo) {

        PatientInfoDTO dto = medicalRecordsFacade.getPatientInfo(getPatientInfo);
        return RespDTO.onSuc(dto);

    }

}

