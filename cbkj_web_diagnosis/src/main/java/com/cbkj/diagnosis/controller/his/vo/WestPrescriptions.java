package com.cbkj.diagnosis.controller.his.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class WestPrescriptions {
    @Schema(description =  "处方号")
    private String prescriptionNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "处方开立日期(示例：2023-11-21 15:10:10)")
    private Date prescriptionTime;

    @Schema(description =  "处方有效天数")
    private String prescriptionsEffectiveDays;

    @Schema(description =  "处方备注信息")
    private String prescriptionRemark;

    @Schema(description =  "处方开立医师签名")
    private String prescribingDoctorSign;

    @Schema(description =  "处方审核药剂师签名")
    private String reviewedDoctorSign;

    private List<WestPrescriptionsItem> westPrescriptionDrugs;
}
