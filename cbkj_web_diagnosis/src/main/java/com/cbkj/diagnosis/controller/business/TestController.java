//package com.cbkj.diagnosis.controller.business;
//
//import com.cbkj.diagnosis.sysBeans.ResEntity;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//@RestController
//@RequestMapping("测试权限root")
//public class TestController {
//
//
//    @GetMapping(value = "one")
//    @PreAuthorize("@permission.hasRole('root权限')")
//    public Object test(){
//
//        return ResEntity.success(null);
//    }
//}
