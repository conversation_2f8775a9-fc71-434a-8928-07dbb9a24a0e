package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreDiagnosisListRes;
import com.cbkj.diagnosis.beans.business.SysAdminInfoDisMapping;
import com.cbkj.diagnosis.beans.business.SysDic;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisForm;
import com.cbkj.diagnosis.common.annotation.RequiresMenus;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.SysDicMapper;
import com.cbkj.diagnosis.service.LookPaperService;
import com.cbkj.diagnosis.service.SysDicService;
import com.cbkj.diagnosis.service.TFormService;
import com.cbkj.diagnosis.beans.dao.CheckDisMappingIsMu;
import com.cbkj.diagnosis.service.vo.DeleteFormReVO;
import com.cbkj.diagnosis.service.WebTPreDiagnosisFormService;
import com.cbkj.diagnosis.service.vo.DoctorMappingDis;
import com.cbkj.diagnosis.service.webapi.business.vo.LookPaperRecive;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import com.cbkj.diagnosis.service.webapi.business.vo.PaperListReVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller
//@Api(value = "预诊单管理", tags = "预诊单管理")
@Tag(name = "预诊单管理", description = "预诊单管理")
@RequestMapping("diagnosis/paper")
public class DiagnosisPageController {


    private WebTPreDiagnosisFormService webTPreDiagnosisFormService;
    private LookPaperService lookPaperService;
    private TFormService tFormService;

    private final SysDicService sysDicService;

    DiagnosisPageController(WebTPreDiagnosisFormService webTPreDiagnosisFormService,
                            LookPaperService lookPaperService,
                            TFormService tFormService, SysDicService sysDicService) {
        this.webTPreDiagnosisFormService = webTPreDiagnosisFormService;
        this.lookPaperService = lookPaperService;
        this.tFormService = tFormService;
        this.sysDicService = sysDicService;
    }

    @GetMapping (value = "lookPaper")
    //@ApiOperation(value = "查看预诊单子-预诊单详情", notes = "查看预诊单子-预诊单详情",response = LookPaperRecive.class)
    @Operation(description = "查看预诊单子-预诊单详情", summary = "查看预诊单子-预诊单详情",responses = {
            @ApiResponse(description = "查看预诊单子-预诊单详情",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = LookPaperRecive.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object lookPaper(String diaId) {
        if (StringUtils.isBlank(diaId)) {
            return ResEntity.error("缺少字段");
        }
        LookPaperRecive lookPaper = webTPreDiagnosisFormService.getLookPaper(diaId);
        return ResEntity.success(lookPaper);
    }


    @GetMapping (value = "getPaperList")
    //@ApiOperation(value = "查看预诊/随访单(随访里面还有两个类型：1.随访问卷2.自测量表)列表", notes = "查看预诊/随访单(随访里面还有两个类型：1.随访问卷2.自测量表)列表",response = TPreDiagnosisForm.class)
    @Operation(description = "查看预诊/随访单(随访里面还有两个类型：1.随访问卷2.自测量表)列表", summary = "查看预诊/随访单(随访里面还有两个类型：1.随访问卷2.自测量表)列表",responses = {
            @ApiResponse(description = "查看预诊/随访单(随访里面还有两个类型：1.随访问卷2.自测量表)列表",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TPreDiagnosisForm.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPaperList(@ParameterObject PaperListReVo paperListReVo,
                               @ParameterObject Page page) {
        Object paperList = webTPreDiagnosisFormService.getPaperList(paperListReVo, page);
        return paperList;
    }


    @GetMapping (value = "deletePaper")
    //@ApiOperation(value = "删除预诊、随访单子", notes = "删除预诊单子",response = ResEntity.class)
    @Operation(description = "删除预诊、随访单子", summary = "删除预诊、随访单子",responses = {
            @ApiResponse(description = "删除预诊、随访单子",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    @RequiresMenus(value = {"100500"})
    public Object deletePaper(@ParameterObject DeleteFormReVO deleteFormReVO) {
        return tFormService.deletePaper(deleteFormReVO);
    }

    @GetMapping (value = "changeShowStatus")
    //@ApiOperation(value = "变更预诊单/问卷量表展示按钮状态", notes = "变更预诊单/问卷量表展示按钮状态",response = ResEntity.class)
    @Operation(description = "变更预诊单/问卷量表展示按钮状态", summary = "变更预诊单/问卷量表展示按钮状态",responses = {
            @ApiResponse(description = "变更预诊单/问卷量表展示按钮状态",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    @RequiresMenus(value = {"100500"})
    public Object changeShowStatus(String diaId, boolean status) {
        TPreDiagnosisForm tPreDiagnosisForm = tFormService.changeShowStatus(diaId, status);
        return ResEntity.success(tPreDiagnosisForm);
    }


    @GetMapping (value = "getDoctorMappingDisList")
    //@ApiOperation(value = "随访和宣教管理获取当前账户绑定的中医病种", notes = "随访和宣教管理获取当前账户绑定的中医病种",response = ResEntity.class)
    @Operation(description = "随访和宣教管理获取当前账户绑定的中医病种", summary = "随访和宣教管理获取当前账户绑定的中医病种",responses = {
            @ApiResponse(description = "随访和宣教管理获取当前账户绑定的中医病种",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getDoctorMappingDisList(String disName) {
         List<SysAdminInfoDisMapping> list = tFormService.getDoctorMappingDisList(disName);
        return ResEntity.success(list);
    }

    @GetMapping (value = "getSuiFangType")
    @Operation(description = "获取预诊、随访（问卷/量表）题目的类别下拉字典内容", summary = "获取预诊、随访（问卷/量表）题目的类别下拉字典内容",responses = {
            @ApiResponse(description = "获取预诊、随访（问卷/量表）题目的类别下拉字典内容",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getSuiFangType() {
        List<SysDic> suiFangType = sysDicService.getSuiFangType();
        return ResEntity.success(suiFangType);
    }

    @ApiOperationSupport(author = "zjh")
    @GetMapping (value = "getOptionEventCode")
    //@ApiOperation(value = "获取随访（问卷/量表）选项的事件下拉字典内容", notes = "获取随访（问卷/量表）选项的事件下拉字典内容",response = ResEntity.class)
    @Operation(description = "获取随访（问卷/量表）选项的事件下拉字典内容", summary = "获取随访（问卷/量表）选项的事件下拉字典内容",responses = {
            @ApiResponse(description = "获取随访（问卷/量表）选项的事件下拉字典内容",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getOptionEventCode() {
        List<SysDic> optionEventCode = sysDicService.getOptionEventCode();
        return ResEntity.success(optionEventCode);
    }


    @PostMapping(value = "setPaper")
//    //@ApiOperation(value = "新建预诊/随访单子（更新预诊会删除原先的预诊单，重新插入新的，" +
//            "所以每次接口返回diaId、questionId、optionId都会更改。返回更改后的id数据）",
//            notes = "新建预诊/随访单子（更新预诊会删除原先的预诊/随访单，重新插入新的，所以每次接口返回diaId、questionId、optionId都会更改。）返回更改后的id数据"
//    ,response = LookPaperRecive.class
//    )
    @Operation(description = "新建预诊/随访单子（更新预诊会删除原先的预诊单，重新插入新的，\" +\n" +
            "//            \"所以每次接口返回diaId、questionId、optionId都会更改。返回更改后的id数据", summary = "新建预诊/随访单子",responses = {
            @ApiResponse(description = "新建预诊/随访单子",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = LookPaperRecive.class)
                    )
            )
    }
    )
    @ResponseBody
    @RequiresMenus(value = {"100500"})
    public Object setPaper(@RequestBody LookPaperRecive lookPaperRecive) {
        if (lookPaperRecive.getDiaType().equals(Constant.BASIC_STRING_ONE) ){
            if (lookPaperRecive.getChineseDis() == null || lookPaperRecive.getChineseDis().size() == 0){
                return ResEntity.error("专科预诊单，必须设置疾病id");
            }
        }
        if (StringUtils.isBlank(lookPaperRecive.getFormType())){
            return ResEntity.error("必须设置问卷的类型formType");
        }
        if (StringUtils.isBlank(lookPaperRecive.getFormType())){
            return ResEntity.error("formType字段必传");
        }
//        if (lookPaperRecive.getFormType().equals(Constant.BASIC_STRING_ONE)){
            //判断除自己外疾病是否被绑定了。
            for (int i = 0; i < lookPaperRecive.getChineseDis().size(); i++) {
                CheckDisMappingIsMu checkDisMappingIsMu = new CheckDisMappingIsMu();
                checkDisMappingIsMu.setDisId(lookPaperRecive.getChineseDis().get(i).getId());
                if (StringUtils.isBlank(checkDisMappingIsMu.getDisId())){
                    checkDisMappingIsMu.setDisId("-1");
                }
                checkDisMappingIsMu.setDiaId(lookPaperRecive.getDiaId());
                checkDisMappingIsMu.setFormType(lookPaperRecive.getFormType());
                boolean hasMore = lookPaperService.checkDisMappingIsMu(checkDisMappingIsMu);
                if (hasMore){
                    return ResEntity.error("疾病:"+lookPaperRecive.getChineseDis().get(i).getName()+"已被绑定，请先解除绑定");
                }
            }

//        }
        return lookPaperService.setPaper(lookPaperRecive);

    }


    @ApiOperationSupport(author = "zjh")
    @GetMapping (value = "changeCueWordStatus")
    @Operation(description = "改变随访表单开启大模型开关状态", summary = "改变随访表单开启大模型开关状态",responses = {
            @ApiResponse(description = "改变随访表单开启大模型开关状态",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object changeCueWordStatus(
            @Schema(description = "随访表单id", example = "1") String diaId,
                                      @Schema(description = "大模型开关状态0开1关", example = "0") Integer cueWordStatus) {
        return tFormService.changeCueWordStatus(diaId,cueWordStatus);

    }
}
