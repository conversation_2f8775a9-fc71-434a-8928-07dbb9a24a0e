package com.cbkj.diagnosis.controller.monitor;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cbkj.diagnosis.beans.his.QualityControlLog;
import com.cbkj.diagnosis.beans.monitor.dto.RespDTO;
import com.cbkj.diagnosis.beans.monitor.vo.DataSourceList;
import com.cbkj.diagnosis.beans.request.ClosedRequest;
import com.cbkj.diagnosis.beans.request.ClosedStatusRequest;
import com.cbkj.diagnosis.beans.request.ResourceListRequest;
import com.cbkj.diagnosis.beans.task.webvo.UpdateOrInsertRoad;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.his.QualityControlLogMapper;
import com.cbkj.diagnosis.service.monitor.impl.MedicalRecordsServiceImpl;
import com.github.pagehelper.PageHelper;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/5 9:24
 * @Version 1.0
 */
//@Api(value = "数据资源", tags = "数据资源")
@Tag(name = "数据资源", description = "数据资源")
@RestController
public class DataSourceController {

    @Autowired
    private MedicalRecordsServiceImpl medicalRecordsServiceImpl;

    @Autowired
    private QualityControlLogMapper qualityControlLogMapper;

    //@ApiOperation(value = "获取数据资源列表", notes = "获取数据资源列表",response = DataSourceList.class)
    @GetMapping("/datasource/list")
    public Object getDataSourceList(@ParameterObject ResourceListRequest request, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<DataSourceList> dataSourceList = medicalRecordsServiceImpl.getDataSourceList(request);
        return Page.getLayUiTablePageData(dataSourceList);
    }


    @GetMapping("/caseSelection/list")
    public Object getCaseSelectionList(@ParameterObject ResourceListRequest request, Page page) {
        return medicalRecordsServiceImpl.getCaseSelectionList(request,page);
    }

    @GetMapping("/caseClosed/list")
    public Object getCaseClosedList(@ParameterObject ResourceListRequest request, Page page) {
        return medicalRecordsServiceImpl.getCaseClosedList(request,page);
    }


    @PostMapping(value = "/set/closed",  consumes = "application/json", produces = "application/json")
    @ResponseBody
    public Object setClosed(@RequestBody List<ClosedRequest> request) {

        return medicalRecordsServiceImpl.setClosed(request);
    }

    @GetMapping("/get/qualitylist")
    public Object getQualitylist(String recordsId) {
        LambdaQueryWrapper<QualityControlLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QualityControlLog::getRecordId, recordsId);
        return RespDTO.onSuc(qualityControlLogMapper.selectList(queryWrapper));
    }

    @PostMapping(value = "/set/closedStatus",  consumes = "application/json", produces = "application/json")
    @ResponseBody
    public Object setClosedStatus(@RequestBody List<ClosedStatusRequest> request) {

        return RespDTO.onSuc(medicalRecordsServiceImpl.setClosedStatus(request));
    }
}
