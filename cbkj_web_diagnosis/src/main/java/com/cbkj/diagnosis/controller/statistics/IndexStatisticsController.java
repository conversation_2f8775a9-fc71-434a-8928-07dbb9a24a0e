package com.cbkj.diagnosis.controller.statistics;

import com.cbkj.diagnosis.beans.home.SysHomeShowUrl;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayFollow;
import com.cbkj.diagnosis.service.home.SysHomeShowUrlService;
import com.cbkj.diagnosis.service.statistics.IndexStatisticsService;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayFollowService;
import com.cbkj.diagnosis.service.webapi.business.vo.XiaoShanPhoneSuiFangStatisticsTopRight;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.cbkj.diagnosis.service.webapi.business.vo.XiaoShanPhoneSuiFangStatisticsInfo;

/**
 * <AUTHOR>
 */
@Controller
//@Api(value = "（萧山）首页统计数据", tags = "（萧山）首页统计数据")
@Tag(name = "萧山）首页统计数据", description = "萧山）首页统计数据")
@RequestMapping("suifang/statistics")
public class IndexStatisticsController {

    private final IndexStatisticsService indexStatisticsService;
    private final SysHomeShowUrlService sysHomeShowUrlService;

    private final StatisticsErverDayFollowService statisticsErverDayFollowService;

    public IndexStatisticsController(IndexStatisticsService indexStatisticsService, SysHomeShowUrlService sysHomeShowUrlService, StatisticsErverDayFollowService statisticsErverDayFollowService) {
        this.indexStatisticsService = indexStatisticsService;
        this.sysHomeShowUrlService = sysHomeShowUrlService;
        this.statisticsErverDayFollowService = statisticsErverDayFollowService;
    }

    @GetMapping(value = "index/top/left")
    //@ApiOperation(value = "萧山-获取首页左上角的随访数量数据", notes = "萧山-获取首页左上角的随访数量数据", response = XiaoShanPhoneSuiFangStatisticsInfo.class)
    @ResponseBody
    public Object getXiaoShanPhoneSuiFangStatisticsInfo() {
        return ResEntity.success(indexStatisticsService.getXiaoShanPhoneSuiFangStatisticsInfo());
    }

    @GetMapping(value = "index/top/right")
    //@ApiOperation(value = "萧山-获取首页右上角累计随访患者和完成随访份数", notes = "萧山-获取首页右上角累计随访患者和完成随访份数", response = XiaoShanPhoneSuiFangStatisticsTopRight.class)
    @ResponseBody
    public Object getXiaoShanPhoneSuiFangStatisticsTopRight() {
        return ResEntity.success(indexStatisticsService.getXiaoShanPhoneSuiFangStatisticsTopRight());
    }

    @GetMapping(value = "index/top/middle")
    //@ApiOperation(value = "萧山-最近七天随访人数", notes = "萧山-最近七天随访人数", response = StatisticsErverDayFollow.class)
    @ResponseBody
    public Object getPastSevenDays() {
        return ResEntity.success(statisticsErverDayFollowService.getPastSevenDays());
    }

    @GetMapping(value = "index/middle/urlinfo")
    //@ApiOperation(value = "萧山-获取跳转路径", notes = "萧山-获取跳转路径", response = SysHomeShowUrl.class)
    @ResponseBody
    public Object getHomeURLList() {
        return ResEntity.success(sysHomeShowUrlService.getHomeIndexUrlList());
    }



}
