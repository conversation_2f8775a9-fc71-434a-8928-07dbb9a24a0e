package com.cbkj.diagnosis.controller.his.vo;

import com.cbkj.diagnosis.beans.health.MedicalRecordsExam;
import com.cbkj.diagnosis.beans.health.MedicalRecordsLab;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class VisitRecords {
    @Schema(description =  "应用id",required = true)
    private String appId;
    @Schema(description =  "机构代码",required = true)
    private String insCode;
    @Schema(description =  "机构名称",required = true)
    private String insName;
    @Schema(description =  "时间戳")
    private Long timestamp;
    @Schema(description =  "医生信息",required = true)
    private DoctorInfo doctorInfo;
    @Schema(description =  "患者信息")
    private PatientInfo patientInfo;
    @Schema(description =  "病历信息")
    private RecordInfo recordInfo;
    @Schema(description =  "中医处方列表")
    private List<ChinesePrescriptions> chinesePrescriptions;
    @Schema(description =  "西医处方列表")
    private List<WestPrescriptions> westPrescriptions;
    /**
     * 一般处置
     */
    @Schema(description =  "一般处置列表")
    private List<GeneralTreatments> generalTreatments;
    @Schema(description =  "治疗费用信息")
    private RecordCost recordCost;

    /**
     * 20250304 新增
     */
    @Schema(description =  "检验记录")
    private List<MedicalRecordsLab> medicalRecordsLab;
    @Schema(description =  "检查记录")
    private List<MedicalRecordsExam> medicalRecordsExam;

}
