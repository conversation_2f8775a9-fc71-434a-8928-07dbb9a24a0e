package com.cbkj.diagnosis.controller.sysController;

import com.cbkj.diagnosis.beans.business.SysDept;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.SysDeptMapper;
import com.cbkj.diagnosis.service.sysService.SysDeptService;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

//@Api(value = "机构科室信息", tags = "机构科室信息")
@Tag(name = "机构科室信息", description = "机构科室信息")
@Slf4j
@RestController
public class SysInsController {
    private SysDeptService sysDeptService;

    SysInsController(SysDeptService sysDeptService) {
        this.sysDeptService = sysDeptService;
    }

    @RequestMapping(value = "dept/list", method = RequestMethod.GET)
    //@ApiOperation(value = "获取科室", notes = "获取科室")
    @ResponseBody
    public Object getDeptList(@ParameterObject SysDept sysDept, @ParameterObject Page page) {
        return sysDeptService.getDeptList(sysDept, page);
    }
}
