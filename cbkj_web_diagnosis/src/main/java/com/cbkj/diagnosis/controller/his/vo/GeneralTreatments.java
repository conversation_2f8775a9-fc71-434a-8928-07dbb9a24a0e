package com.cbkj.diagnosis.controller.his.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class GeneralTreatments {
    @Schema(description =  "操作编码")
    private String operationCode;

    @Schema(description =  "操作名称")
    private String operationName;

    @Schema(description =  "操作目标部位名称")
    private String operationTarget;

    @Schema(description =  "操作方法描述")
    private String operationMethod;

    @Schema(description =  "操作次数")
    private String operationNumber;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "操作日期时间")
    private Date operationTime;

    @Schema(description =  "处理及指导意见")
    private String treatmentInstruction;

    @Schema(description =  "有创诊疗操作标志")
    private String invasiveDiagnosis;

    @Schema(description =  "治疗过程描述")
    private String processDescription;

    @Schema(description =  "今后治疗方案")
    private String futureTreatmentPlan;

    @Schema(description =  "随访方式")
    private String followUpMethodCode;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "随访日期")
    private Date followUpDate;

    @Schema(description =  "随访周期建议")
    private String followUpPeriod;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "签名日期时间")
    private Date signTime;

    @Schema(description =  "治疗处置开立医师签名")
    private String prescribingDoctorSign;

    @Schema(description =  "治疗处置执行医师签名")
    private String executiveDoctorSign;
}
