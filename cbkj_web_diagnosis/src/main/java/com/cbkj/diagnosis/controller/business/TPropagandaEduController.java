package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.TPropagandaEdu;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.common.annotation.RequiresMenus;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.TPropagandaEduService;
import com.cbkj.diagnosis.service.common.vo.SuiFangPreListReVo;
import com.cbkj.diagnosis.service.statistics.StatisticsHealth;
import com.cbkj.diagnosis.service.vo.EduSendMessage;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/propaganda/edu")
//@Api(value = "健康宣教", tags = "健康宣教")
@Tag(name = "健康宣教", description = "健康宣教")
public class TPropagandaEduController {

    private final TPropagandaEduService tPropagandaEduService;

    private final StatisticsHealth statisticsHealth;

    TPropagandaEduController(TPropagandaEduService tPropagandaEduService, StatisticsHealth statisticsHealth) {
        this.tPropagandaEduService = tPropagandaEduService;
        this.statisticsHealth = statisticsHealth;
    }
    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "getEduPageList")
    //@ApiOperation(value = "健康宣教列表", notes = "健康宣教列表")
    @ResponseBody
    public Object getEduPageList(@ParameterObject TPropagandaEdu tPropagandaEdu, @ParameterObject Page page) {
        return tPropagandaEduService.getEduPageList(tPropagandaEdu, page);

    }

    @GetMapping(value = "/show/eduPageList")
    //@ApiOperation(value = "健康宣教showStatus是true的列表", notes = "健康宣教showStatus是true的列表")
    @ResponseBody
    public Object getEduPageShowStatusIsTrueList(@ParameterObject TPropagandaEdu tPropagandaEdu, @ParameterObject Page page) {
        tPropagandaEdu.setShowStatus(true);
        return tPropagandaEduService.getEduPageList(tPropagandaEdu, page);
    }
    @RequiresMenus(value = {"100503"})
    @PostMapping(value = "updateEduPageList")
    //@ApiOperation(value = "健康宣教编辑/插入", notes = "健康宣教编辑、插入")
    @ResponseBody
    public Object updateEduPageList(@RequestBody TPropagandaEdu tPropagandaEdu) {
        return tPropagandaEduService.updateEduPageList(tPropagandaEdu);

    }
    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "deleteEduPageList")
    //@ApiOperation(value = "健康宣教删除", notes = "健康宣教删除")
    @ResponseBody
    public Object deleteEduPageList(Integer tPropagandaEduId) {
        return tPropagandaEduService.deleteEduPageList(tPropagandaEduId);

    }

    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "changeShowStatus")
    //@ApiOperation(value = "变更健康宣教展示按钮状态", notes = "变更健康宣教展示按钮状态")
    @ResponseBody
    public Object changeShowStatus(Integer tPropagandaEduId,boolean showStatus) {
        return tPropagandaEduService.changeShowStatus(tPropagandaEduId,showStatus);
    }
    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "details")
    //@ApiOperation(value = "健康宣教详情", notes = "健康宣教详情")
    @ResponseBody
    public Object details(Integer tPropagandaEduId) {
        return ResEntity.success(tPropagandaEduService.details(tPropagandaEduId));

    }


    @PostMapping(value = "sendMessage")
    //@ApiOperation(value = "健康宣教主动发送", notes = "健康宣教主动发送")
    @ResponseBody
    public Object sendMessage(@RequestBody EduSendMessage eduSendMessage) {
        ResEntity sendMessage = tPropagandaEduService.sendMessage(eduSendMessage);
        if (sendMessage.getStatus()){
            AdminInfo currentHr = AdminWebUtils.getCurrentHr();
            statisticsHealth.writeReadFromRedis(eduSendMessage.getTPropagandaEduId(),currentHr.getAppId(),currentHr.getInsCode(),currentHr.getInsId(),currentHr.getInsName() );
        }
        return sendMessage;


    }

}
