package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.TPropagandaEdu;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.common.annotation.RequiresMenus;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.TPropagandaEduService;
import com.cbkj.diagnosis.service.common.vo.SuiFangPreListReVo;
import com.cbkj.diagnosis.service.statistics.StatisticsHealth;
import com.cbkj.diagnosis.service.sysService.SysAdminService;
import com.cbkj.diagnosis.beans.business.EduSendMessage;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/propaganda/edu")
//@Api(value = "健康宣教", tags = "健康宣教")
@Tag(name = "健康宣教", description = "健康宣教")
public class TPropagandaEduController {

    private final TPropagandaEduService tPropagandaEduService;

    private final StatisticsHealth statisticsHealth;

    private final SysAdminService sysAdminService;

    TPropagandaEduController(TPropagandaEduService tPropagandaEduService, StatisticsHealth statisticsHealth, SysAdminService sysAdminService) {
        this.tPropagandaEduService = tPropagandaEduService;
        this.statisticsHealth = statisticsHealth;
        this.sysAdminService = sysAdminService;
    }
    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "getEduPageList")
    @Operation(description = "健康宣教列表", summary = "健康宣教列表",responses = {
            @ApiResponse(description = "健康宣教列表",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TPropagandaEdu.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getEduPageList(@ParameterObject TPropagandaEdu tPropagandaEdu, @ParameterObject Page page) {
        return tPropagandaEduService.getEduPageList(tPropagandaEdu, page);

    }

    @GetMapping(value = "/show/eduPageList")
    @Operation(description = "健康宣教showStatus是true的列表", summary = "健康宣教showStatus是true的列表",responses = {
            @ApiResponse(description = "健康宣教showStatus是true的列表",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TPropagandaEdu.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getEduPageShowStatusIsTrueList(@ParameterObject TPropagandaEdu tPropagandaEdu, @ParameterObject Page page) {
        tPropagandaEdu.setShowStatus(true);
        return tPropagandaEduService.getEduPageList(tPropagandaEdu, page);
    }
    @RequiresMenus(value = {"100503"})
    @PostMapping(value = "updateEduPageList")
    @Operation(description = "健康宣教编辑/插入", summary = "健康宣教编辑/插入",responses = {
            @ApiResponse(description = "健康宣教编辑/插入",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TPropagandaEdu.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object updateEduPageList(@RequestBody TPropagandaEdu tPropagandaEdu) {
        return tPropagandaEduService.updateEduPageList(tPropagandaEdu);

    }
    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "deleteEduPageList")
    @Operation(description = "健康宣教删除", summary = "健康宣教删除",responses = {
            @ApiResponse(description = "健康宣教删除",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object deleteEduPageList(Integer tPropagandaEduId) {
        return tPropagandaEduService.deleteEduPageList(tPropagandaEduId);

    }

    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "changeShowStatus")
    @Operation(description = "变更健康宣教展示按钮状态", summary = "变更健康宣教展示按钮状态",responses = {
            @ApiResponse(description = "变更健康宣教展示按钮状态",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TPropagandaEdu.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object changeShowStatus(Integer tPropagandaEduId,boolean showStatus) {
        return tPropagandaEduService.changeShowStatus(tPropagandaEduId,showStatus);
    }

    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "changeShowPatient")
    @ResponseBody
    public Object changeShowPatient(Integer tPropagandaEduId,String isShow) {
        return tPropagandaEduService.changeShowPatient(tPropagandaEduId,isShow);
    }

    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "changeSharing")
    @ResponseBody
    public Object changeSharing(Integer tPropagandaEduId,String isSharing) {
        return tPropagandaEduService.changeSharing(tPropagandaEduId,isSharing);
    }

    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "getHomeList")
    @ResponseBody
    public Object getHomeList() {
        return tPropagandaEduService.getHomeList();
    }

    @RequiresMenus(value = {"100503"})
    @GetMapping(value = "details")
    @Operation(description = "健康宣教详情", summary = "健康宣教详情",responses = {
            @ApiResponse(description = "健康宣教详情",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TPropagandaEdu.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object details(Integer tPropagandaEduId) {
        return ResEntity.success(tPropagandaEduService.details(tPropagandaEduId));

    }


    @PostMapping(value = "sendMessage")
    @Operation(description = "健康宣教主动发送", summary = "健康宣教主动发送",responses = {
            @ApiResponse(description = "健康宣教主动发送",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object sendMessage(@RequestBody EduSendMessage eduSendMessage) {
        eduSendMessage.setRoadExecuteEventType(Constant.BASIC_STRING_ONE);
        ResEntity sendMessage = tPropagandaEduService.sendMessage(eduSendMessage);
        if (sendMessage.getStatus()){
            AdminInfo currentHr = AdminWebUtils.getCurrentHr();
            AdminInfo adminInfoByUserId = sysAdminService.getAdminInfoByUserId(eduSendMessage.getCreateUserId());
            if (null == currentHr && null == adminInfoByUserId){
                return sendMessage;
            }
            statisticsHealth.writeReadFromRedis(eduSendMessage.getTPropagandaEduId(),
                    null != currentHr ?currentHr.getAppId() : adminInfoByUserId.getAppId(),
                    null != currentHr ? currentHr.getInsCode() : adminInfoByUserId.getInsCode(),
                    null != currentHr ? currentHr.getInsId() : adminInfoByUserId.getInsId(),
                    null != currentHr ? currentHr.getInsName() : adminInfoByUserId.getInsName() );
        }
        return sendMessage;

    }

}
