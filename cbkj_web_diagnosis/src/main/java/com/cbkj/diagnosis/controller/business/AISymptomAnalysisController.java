package com.cbkj.diagnosis.controller.business;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import com.cbkj.diagnosis.beans.request.AISymptomAnalysisVo;
import com.cbkj.diagnosis.common.openfeign.LargeModelRes;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import com.cbkj.diagnosis.service.AISymptomAnalysisService;

import java.time.Duration;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/29 10:29
 * @Version 1.0
 */
@Tag(name = "AI症状分析", description = "AI症状分析")
@Controller
@RequestMapping("diagnosis/aiSymptomAnalysis")
@Slf4j
public class AISymptomAnalysisController {

    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;

    private final AISymptomAnalysisService aiSymptomAnalysisService;

    public AISymptomAnalysisController(AISymptomAnalysisService aiSymptomAnalysisService) {
        this.aiSymptomAnalysisService = aiSymptomAnalysisService;
    }

    /**
     * 接口是流式返回字符串信息
     *
     * @return
     */
    //produces因该是TEXT_EVENT_STREAM_VALUE 和json格式
    @PostMapping(value = "getAISymptomAnalysis",
            // consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(
            summary = "流式分析症状变化",
            description = "实时返回AI对患者近10次症状数据的渐进式分析结果",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "成功返回分析流",
                            content = @Content(
                                    mediaType = "text/event-stream",
                                    schema = @Schema(
                                            type = "string",
                                            example = "你好，这是模型返回数据"
                                    )
                            )
                    )
            }
    )
//    @CrossOrigin
    public Flux<ServerSentEvent<Object>> getAISymptomAnalysis(@RequestBody AISymptomAnalysisVo analysisVo) {
        // 设置必要的默认值
//        gptAskRequest.setCbdata("VndZ7sJ9bcYK9XpV+1doA8K5mrn80aNZyqzcfGoOjd6feq5wRx+2C9uwAjrI7WEWEGSSq3/2idnuugisdaBKaBYiITth26azQ4t3ZucdJsddiUEN7WOeXNnfL2PWza585g0r5t02Ipznc1ZWohhqOlw8lAwBa13M4nuKC5+G1Z+WX04qSYOeq1KCe3Sl7ddGfNokpvjzPY5VJJ5py8FgBhqL24RWntMoSgVgc76RuWM=");
//        gptAskRequest.setAgent_id("a1e5a48bf35411efb9620242ac1e0004");
//        gptAskRequest.setStream(true);

        return aiSymptomAnalysisService.getAiSymptomAnalysisRecordsInfoText(analysisVo)
                .doOnNext(answer -> log.info("Sending: {}", answer))
                .map(answer -> ServerSentEvent.builder()
                        .data(answer)
                        .event("message")
                        .id(IdUtil.getSnowflake(snowflake).nextIdStr())
                        .build())
                .timeout(Duration.ofSeconds(30));
    }

    @Operation(
            summary = "分析症状变化-JSON格式返回",
            description = "实时返回AI对患者近10次症状数据的渐进式分析结果",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "成功返回-JSON格式返回",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(
                                            type = "string",
                                            example = "你好，这是模型返回数据"
                                    )
                            )
                    )
            }
    )
    @PostMapping(value = "getAISymptomAnalysisText",
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResEntity<LargeModelRes> getAISymptomAnalysisText(@RequestBody AISymptomAnalysisVo analysisVo) {
        return aiSymptomAnalysisService.getAiSymptomAnalysisJSON(analysisVo);
    }
}

