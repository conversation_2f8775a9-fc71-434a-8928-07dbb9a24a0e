package com.cbkj.diagnosis.controller.sysController;


import com.cbkj.diagnosis.beans.business.TBusinessAnnex;
import com.cbkj.diagnosis.beans.sysBeans.AdminMenu;
import com.cbkj.diagnosis.common.annotation.RequiresMenus;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.DownloadUtil;
import com.cbkj.diagnosis.common.utils.UploadComponent;
import com.cbkj.diagnosis.service.business.AdviceFileUploadService;
import com.cbkj.diagnosis.service.business.TBusinessAnnexService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiImplicitParam;
//import io.swagger.annotations.ApiImplicitParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
@Tag(name = "上传文件接口", description = "上传文件接口")
//@Api(value = "上传文件接口", tags = "上传文件接口")
@Controller
public class UploadController {

    @Value("${file.address}")
    private String location;

    @Value("${root.preview}")
    private String preview;

    @Value("${root.upload.relative}")
    private String relative;


    private final UploadComponent uploadComponent;
    private final AdviceFileUploadService adviceFileUploadService;

    private final TBusinessAnnexService tBusinessAnnexService;


    @Autowired
    public UploadController(UploadComponent uploadComponent, AdviceFileUploadService adviceFileUploadService, TBusinessAnnexService tBusinessAnnexService) {
        this.uploadComponent = uploadComponent;
        this.adviceFileUploadService = adviceFileUploadService;
        this.tBusinessAnnexService = tBusinessAnnexService;
    }


    /**
     * 用一个tomcat 作为图片服务器 上传
     *
     * @param file file
     */
    @RequestMapping(value = "upload/post", method = RequestMethod.POST,consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    //@ApiOperation(value = "上传普通业务图片视频文件", notes = "上传普通业务图片视频文件")
    @Operation(description = "上传普通业务图片视频文件", summary = "上传普通业务图片视频文件",responses = {
            @ApiResponse(description = "上传普通业务图片视频文件",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    public Object handleFileUpload(@Parameter(
            description = "文件",
            required = true,
            content = @Content(mediaType = "application/octet-stream", schema = @Schema(type = "string", format = "binary"))  // 标准文件声明
    ) @RequestPart("file")MultipartFile file) {
        System.out.println("----------------------------");
        return uploadComponent.uploadMultipartFile(file);
    }
    @RequestMapping(value = "upload/image", method = RequestMethod.POST)
    @ResponseBody
    //@ApiOperation(value = "上传意见建议图片文件", notes = "上传意见建议图片文件")
  //  @ApiImplicitParams( {
     //       @ApiImplicitParam(name = "annexType",value = "附件所属业务类型：1系统建议反馈,2系统帮助操作手册3.系统帮助操作视频",dataType = "String",required = true)} )
    public Object uploadImage(MultipartFile file) {
        Object o = adviceFileUploadService.uploadImage(file, "1",AdminWebUtils.getCurrentHr().getUserId());
        return o;
    }



    @Value("${template.address}")
    private String templateAddress;

    public String getJarRootPath(HttpServletRequest request, String fileName) throws UnsupportedEncodingException {
        String agent = request.getHeader("USER-AGENT");
        if (null != agent && agent.contains("MSIE") || null != agent && agent.contains("Trident")) {
            // IE浏览器
            fileName = java.net.URLEncoder.encode(fileName, "UTF8");

        } else if (null != agent && agent.contains("Mozilla")) {
            // 火狐,chrome等浏览器
            fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        }
        String path = templateAddress + fileName;
        File file = new File(path);
        if (file.exists()) {
            return path;
        }
        return "templates" + File.separator + fileName;
    }

    @RequiresMenus(value = {"100500"})
    @RequestMapping(value = "srcFile/get", method = RequestMethod.POST)
    @ResponseBody
    @Operation(
            description = "通过文件名获取服务器文件，返回二进制流",
            summary = "文件下载",responses = {
            @ApiResponse(
                    responseCode = "200",
                    description = "文件下载成功",
                    content = @Content(
                            mediaType = "application/octet-stream",
                            schema = @Schema(type = "string", format = "binary")  // OpenAPI 3.0规范:ml-citation{ref="1,3" data="citationList"}
                    )
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "程序错误",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)  // 错误响应体:ml-citation{ref="3" data="citationList"}
                    )
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "文件不存在",
                    content = @Content(schema = @Schema(hidden = true))  // 隐藏无响应体的结构:ml-citation{ref="3" data="citationList"}
            )

    }
    )
    public Object getFiles(String fileName) throws UnsupportedEncodingException {
        if (!fileName.contains(Constant.PONIT)) {
            return new ResEntity(false, "请输入文件名", null);
        }
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null;
        HttpServletResponse response = null;
        if (attr != null) {
            request = attr.getRequest();
            response = attr.getResponse();
        } else {
            return null;
        }


        String path = getJarRootPath(request, fileName);
        int i = fileName.lastIndexOf(".");
        if (i > 0) {
            String substring = fileName.substring(0, i);
            DownloadUtil.downloadFile(path, substring, response, request);
        }
        return null;
    }


}