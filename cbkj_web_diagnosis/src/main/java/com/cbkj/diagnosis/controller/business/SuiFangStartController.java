package com.cbkj.diagnosis.controller.business;

import com.cbkj.diagnosis.beans.business.TRecord;
import com.cbkj.diagnosis.beans.business.requestvo.GetTodayPatientList;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsPhoneMapper;
import com.cbkj.diagnosis.service.RecordExcuteService;
import com.cbkj.diagnosis.service.SuiFangStartService;
import com.cbkj.diagnosis.service.common.vo.*;
import com.cbkj.diagnosis.service.statistics.StatisticsAdverse;
import com.cbkj.diagnosis.service.statistics.StatisticsDicEvent;
import com.cbkj.diagnosis.service.statistics.StatisticsDis;
import com.cbkj.diagnosis.service.statistics.StatisticsEffect;
import com.cbkj.diagnosis.service.webapi.business.vo.PhoneTaskPageListReturnVo;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
@Slf4j
@Controller
//@Api(value = "随访其它接口", tags = "随访其它接口")
@Tag(name = "随访其它接口", description = "随访其它接口")
@RequestMapping("suifang/other")
public class SuiFangStartController {
    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;
    private final SuiFangStartService suiFangStartService;
    private final RecordExcuteService recordExcuteService;
    private final StatisticsEffect statisticsEffect;
    private final StatisticsAdverse statisticsAdverse;
    private final StatisticsDis statisticsDis;
    private final StatisticsDicEvent statisticsDicEvent;

    public SuiFangStartController(SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper, SuiFangStartService suiFangStartService, RecordExcuteService recordExcuteService, StatisticsEffect statisticsEffect, StatisticsAdverse statisticsAdverse, StatisticsDis statisticsDis, StatisticsDicEvent statisticsDicEvent) {
        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
        this.suiFangStartService = suiFangStartService;
        this.recordExcuteService = recordExcuteService;
        this.statisticsEffect = statisticsEffect;
        this.statisticsAdverse = statisticsAdverse;
        this.statisticsDis = statisticsDis;
        this.statisticsDicEvent = statisticsDicEvent;
    }
    // 处理当前Controller中发生的ClientAbortException
    @ExceptionHandler(ClientAbortException.class)
    public ResEntity<Object> handleClientAbort() {
        log.debug("客户端中断连接");
        return new ResEntity<>(false, "客户端浏览器主动中断连接导致异常");
    }
    /**
     * @param getTodayPatientList
     * @param page
     * @return
     */
    @GetMapping(value = "today/patient/list")
    @Operation(description = "获取随访患者任务列表-含有管理员字样的角色，自动获取全部患者", summary = "获取随访患者任务列表-含有管理员字样的角色，自动获取全部患者", responses = {
            @ApiResponse(description = "获取随访患者任务列表-含有管理员字样的角色，自动获取全部患者", responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TodayPatientList.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getTodayPatientList(@ParameterObject GetTodayPatientList getTodayPatientList) {
        return (suiFangStartService.getTodayPatientList(getTodayPatientList));
    }

    @GetMapping(value = "diagnosis/suifang/pati/group")
    @Operation(description = "随访患者管理-返回有任务的患者", summary = "随访患者管理-返回有任务的患者", responses = {
            @ApiResponse(description = "随访患者管理-返回有任务的患者", responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getStartSuiFangInfoPatiGroup(@ParameterObject GetTodayPatientList getTodayPatientList) {
        getTodayPatientList.setUserId(AdminWebUtils.getCurrentHr().getUserId());
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        List<AdminRule> roles = currentHr.getRoles();
        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            getTodayPatientList.setUserId(null);
        }
        if (null != getTodayPatientList.getToDayTaskStatus() && 1 == getTodayPatientList.getToDayTaskStatus()) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            getTodayPatientList.setStartDate(simpleDateFormat.format(DateUtil.getToDayZeroDate()));
            getTodayPatientList.setEndDate(simpleDateFormat.format(DateUtil.getNextDate()));
        }
//        List<String> patiIdList = new ArrayList<>();
        if (getTodayPatientList.getPage() == null) {
            getTodayPatientList.setPage(1);
            getTodayPatientList.setLimit(100);
        }
        LinkedHashSet<String> set = new LinkedHashSet<>();
        Integer page = getTodayPatientList.getPage();
        Integer limit = getTodayPatientList.getLimit();
        getTodayPatientList.setPage((page - 1) * limit);
        List<TodayPatientList> todayPatientLists = sRoadTaskPatientsPhoneMapper.getTodayPatientList(getTodayPatientList);
        if (todayPatientLists.size() > 0) {
//获取todayPatientLists中所有的patientId 并且是按照顺序，并且不能重复。如1，2，2，3，5，6 结果是12356
            for (int i = 0; i < todayPatientLists.size(); i++) {
                set.add(todayPatientLists.get(i).getPatientId());
            }
        }
        return ResEntity.success(set);

    }


    @GetMapping(value = "diagnosis/suifang/info")
    @Operation(description = "进行随访-获取患者随访信息", summary = "进行随访-获取患者随访信息", responses = {
            @ApiResponse(description = "进行随访-获取患者随访信息", responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SuiFangInfoPatientList.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getStartSuiFangInfo(@ParameterObject GetStartSuiFangInfo getStartSuiFangInfo) {

        return ResEntity.success(suiFangStartService.getStartSuiFangInfo(getStartSuiFangInfo));
    }

    @GetMapping(value = "diagnosis/suifang/info/bypati")
    @Operation(description = "进行随访-获取患者随访信息通过患者id", summary = "进行随访-获取患者随访信息通过患者id", responses = {
            @ApiResponse(description = "进行随访-获取患者随访信息通过患者id", responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SuiFangInfoPatientList.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getStartSuiFangInfoByPati(GetStartSuiFangInfo getStartSuiFangInfo) {
        //获取到患者最新一条任务数据
        if (StringUtils.isBlank(getStartSuiFangInfo.getPatientId())) {
            return ResEntity.error("缺少patientId");
        }
        SuiFangPatientDetail lastestSRoadTaskPatientsId = suiFangStartService.getLastestSRoadTaskPatientsId(getStartSuiFangInfo.getPatientId());
        if (lastestSRoadTaskPatientsId == null) {
            return ResEntity.error("该患者没有随访任务");
        }

        //BeanUtils.copyProperties(lastestSRoadTaskPatientsId, getStartSuiFangInfo);
        getStartSuiFangInfo.setPatientId(lastestSRoadTaskPatientsId.getTaskPatientsId());
        getStartSuiFangInfo.setTaskPatientsId(Long.valueOf(lastestSRoadTaskPatientsId.getTaskPatientsId()));
        getStartSuiFangInfo.setSroadTaskPatientsPhoneId(lastestSRoadTaskPatientsId.getSRoadTaskPatientsPhoneId());
        return ResEntity.success(suiFangStartService.getStartSuiFangInfo(getStartSuiFangInfo));
    }


    @GetMapping(value = "/phone/record/list")
    @Operation(description = "进行随访-获取患者所有电话随访记录", summary = "进行随访-获取患者所有电话随访记录", responses = {
            @ApiResponse(description = "进行随访-获取患者所有电话随访记录", responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SuiFangPhoneRecordListRes.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getSuiFangPhoneRecordList(String patientId, Page page) {
        return suiFangStartService.getSuiFangPhoneRecordList(patientId, page);
    }

    @GetMapping(value = "phone/suifang/details")
    //@ApiOperation(value = "进行随访-获取电话随访详情", notes = "进行随访-获取电话随访详情", response = StartSuiFangInfoDetailsRes.class)
    @Operation(description = "进行随访-获取电话随访详情", summary = "进行随访-获取电话随访详情", responses = {
            @ApiResponse(description = "进行随访-获取电话随访详情", responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = StartSuiFangInfoDetailsRes.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getStartSuiFangInfoDetails(Long sRoadTaskPatientsPhoneId) {
        return ResEntity.success(suiFangStartService.getStartSuiFangInfoDetails(sRoadTaskPatientsPhoneId));
    }


    //    @PostMapping(value = "pone/suifang/save")
//    //@ApiOperation(value = "进行随访-保存电话随访", notes = "进行随访-保存电话随访")
//    @ResponseBody
//    public Object saveQuestions(@RequestBody QuestionListVo questionListVo) {
//        if (questionListVo.getSRoadTaskPatientsPhoneId() == null) {
//            return ResEntity.error("SRoadTaskPatientsPhoneId不能为空");
//        }
//        if (questionListVo.getPhoneStatus() == null) {
//            return ResEntity.error("PhoneStatus不能为空");
//        }
//        questionListVo.setFormType("2");
//        if (questionListVo.getPhoneStatus() == 8) {
//            //存问卷，其它状态问卷不需要存
//            return recordExcuteService.saveQuestions(questionListVo);
//
//        } else {
//            //不存问卷,
//            return recordExcuteService.phoneSuiFang(null, questionListVo);
//        }
//    }
    @PostMapping(value = "/save")
    //@ApiOperation(value = "随访患者管理-保存随访", notes = "随访患者管理-保存随访")
    @Operation(description = "随访患者管理-保存随访", summary = "随访患者管理-保存随访", responses = {
            @ApiResponse(description = "随访患者管理-保存随访", responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TRecord.class)
                    )
            ), @ApiResponse(description = "随访患者管理-保存随访", responseCode = "500",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ResEntity.class)
            )
    )
    }
    )
    @ResponseBody
    public Object saveQuestions(@RequestBody QuestionListVo questionListVo) {

        if (questionListVo.getTaskExcuteStatus() == null) {
            return ResEntity.error("taskExcuteStatus不能为空");
        }
        if (StringUtils.isBlank(questionListVo.getRoadExecuteEventWay())) {
            return ResEntity.error("roadExecuteEventWay不能为空");
        }
        if (StringUtils.isBlank(questionListVo.getTaskPatientsId())) {
            return ResEntity.error("taskPatientsId不能为空");
        }
//        questionListVo.setFormType("2");
        if (questionListVo.getTaskExcuteStatus() == 8) {
            //存问卷，其它状态问卷不需要存

            ResEntity resEntity = recordExcuteService.saveQuestions(questionListVo);
            if (!resEntity.getStatus()) {
                return resEntity;
            }
            AdminInfo currentHr = AdminWebUtils.getCurrentHr();
            TRecord tRecord = (TRecord) resEntity.getData();
            //疗效评价
            statisticsEffect.writeReadFromRedis(tRecord.getRecId(), tRecord.getDiaId(), currentHr.getAppId(), currentHr.getInsCode(), currentHr.getInsId(), currentHr.getInsName(), questionListVo.getPatientId());
            //不良反应
            statisticsAdverse.writeReadFromRedis(tRecord.getRecId(), tRecord.getDiaId(), currentHr.getAppId(), currentHr.getInsCode(), currentHr.getInsId(), currentHr.getInsName(), questionListVo.getPatientId());
            //统计预诊、随访关联病种统计
            statisticsDis.statisticsDis(tRecord.getRecId(), tRecord.getDiaId(), currentHr.getAppId(), currentHr.getInsCode(), currentHr.getInsId(), currentHr.getInsName(), questionListVo.getFormType());

            //统计选项的事件数据
            statisticsDicEvent.writeReadFromRedis(tRecord.getRecId(), tRecord.getDiaId(), currentHr.getAppId(), currentHr.getInsCode(), currentHr.getInsId(), currentHr.getInsName(), questionListVo.getPatientId());


            return resEntity;


        } else {
            //不存问卷,
            recordExcuteService.phoneSuiFang(null, questionListVo);
            return ResEntity.success();
        }
    }

    @GetMapping(value = "/delete/patientTask")
    @Operation(description = "进行随访-删除患者随访任务", summary = "进行随访-删除患者随访任务", responses = {
            @ApiResponse(description = "进行随访-删除患者随访任务", responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public ResEntity deleteStartSuiFangInfo(@Schema(description = "患者电话随访任务id（如果列表返回这个字段）", requiredMode = Schema.RequiredMode.NOT_REQUIRED) Long sRoadTaskPatientsPhoneId,
                                            @Schema(description = "患者随访任务id",requiredMode = Schema.RequiredMode.REQUIRED) String taskPatientsId) {
        return ResEntity.success(suiFangStartService.deleteStartSuiFangInfo(sRoadTaskPatientsPhoneId,taskPatientsId));
    }


}
