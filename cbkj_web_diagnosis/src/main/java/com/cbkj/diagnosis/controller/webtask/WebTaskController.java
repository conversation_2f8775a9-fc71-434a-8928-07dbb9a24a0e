package com.cbkj.diagnosis.controller.webtask;

import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.task.RoadTaskConditonsSQLResult;
import com.cbkj.diagnosis.beans.task.SRoad;
import com.cbkj.diagnosis.beans.task.SRoadTask;
import com.cbkj.diagnosis.beans.task.SRoadTaskResponse;
import com.cbkj.diagnosis.beans.task.webvo.InsertOrUpdateTask;
import com.cbkj.diagnosis.beans.task.webvo.RoadTaskConditons;
import com.cbkj.diagnosis.beans.task.webvo.UpdateOrInsertRoad;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.DateValidator;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.controller.webtask.vo.AddPrediagnosis;
import com.cbkj.diagnosis.controller.webtask.vo.ResetPatientTasksStatus;
import com.cbkj.diagnosis.mapper.task.SRoadMapper;
import com.cbkj.diagnosis.service.webtask.WebTaskService;
import com.cbkj.diagnosis.service.webapi.business.vo.*;
import com.cbkj.diagnosis.service.webtask.WebRoadTaskPatient;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
//@Api(value = "随访任务-相关接口", tags = "随访任务相关接口")
@Tag(name = "随访任务-相关接口", description = "随访任务-相关接口")
@RestController
@RequestMapping("webapi/task")
public class WebTaskController {
    private final WebRoadTaskPatient webRoadTaskPatient;
    private final WebTaskService webTaskService;

    private final SRoadMapper sRoadMapper;

    WebTaskController(WebTaskService webTaskService, WebRoadTaskPatient webRoadTaskPatient, SRoadMapper sRoadMapper) {
        this.webTaskService = webTaskService;
        this.webRoadTaskPatient = webRoadTaskPatient;
        this.sRoadMapper = sRoadMapper;
    }

    @PostMapping(value = "insertOrUpdateTask")
    //@ApiOperation(value = "随访任务添加、修改", notes = "随访任务添加、修改", response = SRoadTaskResponse.class)
    @Operation(description = "随访任务添加、修改", summary = "随访任务添加、修改",responses = {
            @ApiResponse(description = "随访任务添加、修改",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SRoadTaskResponse.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object insertOrUpdateTask(@RequestBody InsertOrUpdateTask insertOrUpdateTask) {
        String sRoadTaskId = insertOrUpdateTask.getSRoadTaskId();
//        if (StringUtils.isBlank(insertOrUpdateTask.getSRoadTaskId())){
//            return ResEntity.error("缺少参数sRoadTaskId");
//        }
        String sRoadId = insertOrUpdateTask.getSRoadId();
        if (StringUtils.isBlank(sRoadId)) {
            return ResEntity.error("缺少参数sRoadId");
        }
        SRoadTaskResponse sRoadTaskResponse = new SRoadTaskResponse();
        SRoad sRoad = sRoadMapper.getObjectById(sRoadId);
        if (sRoad == null) {
            return ResEntity.error("该路径不存在");
        }
        if (Constant.BASIC_STRING_ONE.equals(sRoad.getStatus())) {
            return ResEntity.error("该路径已经被删除不允许被修改");
        }
        if (!Constant.BASIC_STRING_TWO.equals(sRoad.getSRoadGroupWay())) {
            insertOrUpdateTask.setRoadTaskConditons(null);
        }
        RoadTaskConditons roadTaskConditons = insertOrUpdateTask.getRoadTaskConditons();
        if (roadTaskConditons != null) {
            if (StringUtils.isNotBlank(roadTaskConditons.getRecordStartTime())) {
                if (!DateValidator.isValidDate(roadTaskConditons.getRecordStartTime())) {
                    return ResEntity.error("开始时间格式不正确：" + roadTaskConditons.getRecordStartTime());
                }
            } else {
                return ResEntity.error("缺少就诊时间");
            }
            if (StringUtils.isNotBlank(roadTaskConditons.getRecordEndTime())) {
                if (!DateValidator.isValidDate(roadTaskConditons.getRecordEndTime())) {
                    return ResEntity.error("结束时间格式不正确：" + roadTaskConditons.getRecordEndTime());
                }
            } else {
                return ResEntity.error("缺少就诊时间");
            }
            List<RoadTaskConditonsSQLResult> deptList = roadTaskConditons.getDeptList();
            if (deptList == null || deptList.size() == 0) {
                return ResEntity.error("科室不能为空");
            }
        }
        SRoadTask task = null;
        if (sRoad != null) {
            task = webTaskService.insertOrUpdateTask(insertOrUpdateTask, AdminWebUtils.getCurrentHr().getNameZh(), AdminWebUtils.getCurrentHr().getUserId(), sRoad);
            BeanUtils.copyProperties(task, sRoadTaskResponse);
        }
        /**
         * 随访任务自动入组+不是修改的自动入组。
         */
        if (task.getSRoadGroupWay().equals(Constant.BASIC_STRING_TWO) ) {
            webRoadTaskPatient.setTaskPatient(task);
        }
        return ResEntity.success(sRoadTaskResponse);
    }


    @GetMapping(value = "getPageTaskList")
    //@ApiOperation(value = "随访任务列表", notes = "随访任务列表")
    @Operation(description = "随访任务列表", summary = "随访任务列表",responses = {
            @ApiResponse(description = "随访任务列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SRoadTaskResponse.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPageTaskList(@ParameterObject GetPageTaskListRe getPageTaskListRe, @ParameterObject Page page) {

        return webTaskService.getPageTaskList(getPageTaskListRe, page);

    }

    @GetMapping(value = "getRoadTaskConditon")
    //@ApiOperation(value = "获取随访任务自动入组条件", notes = "获取随访任务自动入组条件", response = RoadTaskConditons.class)
    @Operation(description = "获取随访任务自动入组条件", summary = "获取随访任务自动入组条件",responses = {
            @ApiResponse(description = "获取随访任务自动入组条件",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = RoadTaskConditons.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getRoadTaskConditon(String sRoadTaskId) {

        return ResEntity.success(webTaskService.getRoadTaskConditon(sRoadTaskId));

    }

    @GetMapping(value = "changeTaskStatus")
    //@ApiOperation(value = "更改随访任务的状态", notes = "更改随访任务的状态")
    @Operation(description = "更改随访任务的状态", summary = "更改随访任务的状态",responses = {
            @ApiResponse(description = "更改随访任务的状态",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object updateTaskStatus(UpdateTaskStatus updateTaskStatus) {

        return webTaskService.updateTaskStatus(updateTaskStatus);

    }

    /**
     * 患者管理-
     *
     * @param getPatientListVo
     * @param page
     * @return
     */
    @GetMapping(value = "patient/list")
    //@ApiOperation(value = "患者管理-列表", notes = "患者管理-列表")
    @Operation(description = "患者管理-列表", summary = "患者管理-列表",responses = {
            @ApiResponse(description = "患者管理-列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TAdminInfo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPatientList(@ParameterObject GetPatientListVo getPatientListVo, @ParameterObject Page page) {

        return webTaskService.getPatientList(getPatientListVo, page);

    }


    @GetMapping(value = "patient/filter/list")
    //@ApiOperation(value = "患者管理-筛选导入-列表查询", notes = "患者管理-筛选导入-列表查询")
    @Operation(description = "患者管理-筛选导入-列表查询", summary = "患者管理-筛选导入-列表查询",responses = {
            @ApiResponse(description = "患者管理-筛选导入-列表查询",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TAdminInfo.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object getPatientFilterList(GetPatientFilterListVo getPatientFilterListVo, Page page) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if ("admin".equals(currentHr.getUsername())) {
            return ResEntity.error("请使用admin账号之外的账号进行操作！");
        }
        return webTaskService.getPatientFilterList(getPatientFilterListVo, page);
    }

    @PostMapping(value = "patient/filter/add")
    //@ApiOperation(value = "患者管理-筛选导入-导入患者到任务", notes = "患者管理-筛选导入-导入患者到任务")
    @Operation(description = "患者管理-筛选导入-导入患者到任务", summary = "患者管理-筛选导入-导入患者到任务",responses = {
            @ApiResponse(description = "患者管理-筛选导入-导入患者到任务",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object insertPatientFilterAdd(@RequestBody InsertPatientFilterAddVo insertPatientFilterAddVo) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if ("admin".equals(currentHr.getUsername())) {
            return ResEntity.error("请使用admin账号之外的账号进行操作！");
        }
        return webTaskService.insertPatientFilterAdd(insertPatientFilterAddVo);
    }

    @GetMapping(value = "patient/scheduling/list")
    //@ApiOperation(value = "患者管理-排期", notes = "患者管理-排期")
    @Operation(description = "患者管理-排期", summary = "患者管理-排期",responses = {
            @ApiResponse(description = "患者管理-排期",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object searchScheduling(@ParameterObject SearchSchedulingVo searchSchedulingVo, @ParameterObject Page page) {
        return webTaskService.searchScheduling(searchSchedulingVo, page);
    }

    @GetMapping(value = "patient/mark")
    //@ApiOperation(value = "患者管理-患者标记", notes = "患者管理-患者标记")
    @Operation(description = "患者管理-患者标记", summary = "患者管理-患者标记",responses = {
            @ApiResponse(description = "患者管理-患者标记",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object setPatientMark(@ParameterObject SetPatientMarkVo setPatientMarkVo) {
        return webTaskService.setPatientMark(setPatientMarkVo);
    }

    @GetMapping(value = "patient/task/delete")
    //@ApiOperation(value = "患者管理-取消、恢复待执行状态患者任务", notes = "患者管理-取消、恢复待执行状态患者任务")
    @Operation(description = "患者管理-取消取消/恢复待执行状态患者任务", summary = "患者管理-取消/恢复待执行状态患者任务",responses = {
            @ApiResponse(description = "患者管理-取消恢/复待执行状态患者任务",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object setPatientTaskDelete(PatientTaskDelete patientTaskDelete) {
        return webTaskService.setPatientTaskDelete(patientTaskDelete);
    }


    /**
     * EXCEL导入用户数据
     *
     * @param file file
     */
    //@ApiOperation(value = "EXCEL导入用户数据", notes = "EXCEL导入用户数据")
    @Operation(description = "EXCEL导入用户数据", summary = "EXCEL导入用户数据",responses = {
            @ApiResponse(description = "EXCEL导入用户数据",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @RequestMapping(value = "excel/import", method = RequestMethod.POST,consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    public Object importPatientsTaskByExcel(
            @Parameter(
                    description = "Excel文件",
                    required = true,
                    content = @Content(mediaType = "application/octet-stream", schema = @Schema(type = "string", format = "binary"))  // 标准文件声明
            ) @RequestPart("file")MultipartFile file, @Parameter(
            name = "sroadTaskId",
            description = "关联的任务ID",
            required = true,
            example = "task_123456",
            in = ParameterIn.QUERY  // 明确参数位置（默认会从请求体或路径推断）
    )String sroadTaskId) {
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if ("admin".equals(currentHr.getUsername())) {
            return ResEntity.error("请使用admin账号之外的账号进行操作！");
        }
        if (file == null || file.isEmpty()) {
            return new ResEntity(false, "未选择文件", null);
        }
        if (StringUtils.isBlank(sroadTaskId)) {
            return ResEntity.error("缺少字段");
        }
        return webTaskService.importPatientsTaskByExcel(file, sroadTaskId);
    }


    @PostMapping(value = "patient/prediagnosis/add")
    //@ApiOperation(value = "患者管理-患者加入某个随访", notes = "患者管理-患者加入某个随访")
    @ResponseBody
    public Object addPrediagnosis(@RequestBody AddPrediagnosis addPrediagnosis) {

        return webTaskService.addPatientToTask(addPrediagnosis);
    }


//    @GetMapping(value = "deleteTask")
//    //@ApiOperation(value = "删除随访任务", notes = "删除随访任务")
//    @ResponseBody
//    public Object deleteTask(String sRoadTaskId) {
//        webTaskService.deleteTask(sRoadTaskId);
//        return ResEntity.success();
//
//    }

    @PostMapping(value = "patient/task/reset")
    //@ApiOperation(value = "今日随访患者-任务重发", notes = "今日随访患者-任务重发")
    @Operation(description = "今日随访患者-任务重发", summary = "今日随访患者-任务重发",responses = {
            @ApiResponse(description = "今日随访患者-任务重发",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public ResEntity resetPatientTasksStatus(@RequestBody ResetPatientTasksStatus resetPatientTasksStatus) {
        webTaskService.resetPatientTasksStatus(resetPatientTasksStatus);
        return ResEntity.success();
    }


}
