package com.cbkj.diagnosis.controller.sysController;


import com.cbkj.diagnosis.beans.sysBeans.AdminMenu;
import com.cbkj.diagnosis.common.annotation.RequiresMenus;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.sysService.AdminMenuService;
import com.cbkj.diagnosis.service.sysService.AdminRuleService;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 */
//@Api(value = "菜单接口", tags = "菜单接口", position = 4)
@Tag(name = "菜单接口", description = "菜单接口")
@Controller
@RequestMapping("sys")
public class SysMenuController {


    private final AdminMenuService adminMenuService;
    private final AdminRuleService adminRuleService;

    @Autowired
    SysMenuController(AdminMenuService adminMenuService,
                      AdminRuleService adminRuleService){
        this.adminMenuService = adminMenuService;
        this.adminRuleService = adminRuleService;

    }

    @RequiresMenus(value = {"199900"})
    @Operation(description = "获取菜单列表(非按钮)", summary = "获取菜单列表(非按钮)", tags = {"菜单接口"},responses = {
            @ApiResponse(description = "获取菜单列表",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AdminMenu.class)
                    )
            )
    }
    )
    @RequestMapping(value = "menu/getPages", method = RequestMethod.GET)
    @ResponseBody
    public Object getApps(String mname,  String parentMid, Page page) {
        AdminMenu adminMenu = new AdminMenu();
        adminMenu.setParentMenuId(parentMid);
        adminMenu.setMenuName(mname);
        return adminMenuService.getPageDatas(adminMenu, page);
    }
    @RequiresMenus(value = {"199900"})
    @Operation(description = "获取菜单列表(非按钮)", summary = "获取菜单列表(非按钮)", tags = {"菜单接口"},responses = {
            @ApiResponse(description = "获取菜单列表(非按钮)",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AdminMenu.class)
                    )
            )
    }
    )
    @RequestMapping(value = "menu/getList", method = RequestMethod.GET)
    @ResponseBody
    public Object getPatList() {
        return adminMenuService.getAllMenuListM();
    }
    @RequiresMenus(value = {"199900"})
    @RequestMapping(value = "menu/findObj", method = RequestMethod.GET)
    @Operation(description = "获取菜单详情", summary = "获取菜单详情", tags = {"菜单接口"},responses = {
            @ApiResponse(description = "获取菜单详情",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AdminMenu.class)
                    )
            )
    }
    )

    @ResponseBody
    public Object getObj(String id) {
        return adminMenuService.findObj(id);
    }
    @RequiresMenus(value = {"199900"})
    @RequestMapping(value = "menu/insert", method = RequestMethod.POST)
    @Operation(description = "新增菜单", summary = "新增菜单", tags = {"菜单接口"},responses = {
            @ApiResponse(description = "新增菜单",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AdminMenu.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object insert(@RequestBody AdminMenu adminMenu) {
        return adminMenuService.insert(adminMenu);
    }
    @RequiresMenus(value = {"199900"})
    @RequestMapping(value = "menu/update", method = RequestMethod.POST)
    @Operation(description = "修改菜单", summary = "修改菜单", tags = {"菜单接口"},responses = {
            @ApiResponse(description = "修改菜单",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AdminMenu.class)
                    )
            )
    }
//    ,requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "修改菜单",content = @Content(schema = @Schema(implementation = AdminMenu.class)))
    )
    @ResponseBody
    public Object update(@RequestBody AdminMenu adminMenu) {
        return adminMenuService.update(adminMenu);
    }


    @RequiresMenus(value = {"199900"})
    @RequestMapping(value = "menu/deleteLis", method = RequestMethod.GET)
    @Operation(description = "删除菜单", summary = "删除菜单", tags = {"菜单接口"},responses = {
            @ApiResponse(description = "删除菜单",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AdminMenu.class)
                    )
            )
    }
    )

    @ResponseBody
    public Object deleteLis(AdminMenu adminMenu) {
        return adminMenuService.deleteLisJL(adminMenu);
    }


    @RequiresMenus(value = {"199900"})
    @RequestMapping(value = "menu/changeEnabled", method = RequestMethod.GET)
    @Operation(description = "禁用启用菜单", summary = "禁用启用菜单", tags = {"菜单接口"},responses = {
            @ApiResponse(description = "禁用启用菜单",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AdminMenu.class)
                    )
            )
    }
    )

    @ResponseBody
    public Object changeEnabled(AdminMenu adminMenu) {
        return adminMenuService.updateEnableds(adminMenu);
    }

    @RequiresMenus(value = {"199900"})
    @RequestMapping(value = "menu/tree", method = RequestMethod.GET)
    @Operation(description = "获取菜单树", summary = "获取菜单树", tags = {"菜单接口"},responses = {
            @ApiResponse(description = "获取菜单树",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AdminMenu.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object findMenu(String id) {
        ResEntity result = adminRuleService.findMenuByMID(id, "1", false);
        if (result.getStatus()) {
            return result.getData();
        }
        return result;
    }

    @RequiresMenus(value = {"199900"})
    @RequestMapping(value = "menu/tree/all", method = RequestMethod.GET)
    @Operation(description = "获取所有菜单树", summary = "获取所有菜单树", tags = {"菜单接口"},responses = {
            @ApiResponse(description = "获取所有菜单树",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AdminMenu.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object findMenuList() {
        return adminMenuService.getAllMenuList();
    }

    @RequiresMenus(value = {"199900"})
    @RequestMapping(value = "menu/updateSort", method = RequestMethod.POST)
    @Operation(description = "修改菜单树序号", summary = "修改菜单树序号", tags = {"菜单接口"},responses = {
            @ApiResponse(description = "修改菜单树序号",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AdminMenu.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object findMenuList(@RequestBody  List<AdminMenu> menuList) {
        return adminMenuService.updateSortNumberByList(menuList);
    }
}