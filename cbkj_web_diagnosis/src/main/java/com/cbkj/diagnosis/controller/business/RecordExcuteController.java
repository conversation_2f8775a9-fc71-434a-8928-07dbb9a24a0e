package com.cbkj.diagnosis.controller.business;


import com.cbkj.diagnosis.beans.business.TRecord;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.service.common.vo.QuestionListVo;
import com.cbkj.diagnosis.service.statistics.StatisticsAdverse;
import com.cbkj.diagnosis.service.statistics.StatisticsDicEvent;
import com.cbkj.diagnosis.service.statistics.StatisticsDis;
import com.cbkj.diagnosis.service.statistics.StatisticsEffect;
import com.cbkj.diagnosis.sysBeans.ResEntity;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import com.cbkj.diagnosis.service.RecordExcuteService;

//@Api(value = "随访、预诊面访", tags = "随访、预诊面访")
@Tag(name = "随访、预诊面访", description = "随访、预诊面访")
@Slf4j
@RestController
@RequestMapping("diagnosis/questions")
public class RecordExcuteController {
    private final StatisticsEffect statisticsEffect;
    private final StatisticsAdverse statisticsAdverse;
    private final StatisticsDis statisticsDis;
    private final StatisticsDicEvent statisticsDicEvent;
    private RecordExcuteService recordExcuteService;

    RecordExcuteController(StatisticsEffect statisticsEffect, StatisticsAdverse statisticsAdverse, StatisticsDis statisticsDis, StatisticsDicEvent statisticsDicEvent, RecordExcuteService recordExcuteService){
        this.statisticsEffect = statisticsEffect;
        this.statisticsAdverse = statisticsAdverse;
        this.statisticsDis = statisticsDis;
        this.statisticsDicEvent = statisticsDicEvent;
        this.recordExcuteService = recordExcuteService;
    }

    @PostMapping(value = "saveQuestions")
    //@ApiOperation(value = "保存随访、预诊面诊答题", notes = "保存随访、预诊面诊答题")
    @Operation(description = "保存随访、预诊面诊答题", summary = "保存随访、预诊面诊答题",responses = {
            @ApiResponse(description = "保存随访、预诊面诊答题",responseCode = "200",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ResEntity.class)
                    )
            )
    }
    )
    @ResponseBody
    public Object saveQuestions(@RequestBody QuestionListVo questionListVo) {
        ResEntity resEntity = recordExcuteService.saveQuestions(questionListVo);
        if (resEntity.getStatus() == false) {
            return resEntity;
        }
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        TRecord tRecord = (TRecord) resEntity.getData();
        //疗效评价
        statisticsEffect.writeReadFromRedis(tRecord.getRecId(),tRecord.getDiaId(),currentHr.getAppId(),currentHr.getInsCode(),currentHr.getInsId(),currentHr.getInsName(),questionListVo.getPatientId());
        //不良反应
        statisticsAdverse.writeReadFromRedis(tRecord.getRecId(),tRecord.getDiaId(),currentHr.getAppId(),currentHr.getInsCode(),currentHr.getInsId(),currentHr.getInsName(),questionListVo.getPatientId());
        //统计预诊、随访关联病种统计
        statisticsDis.statisticsDis(tRecord.getRecId(), tRecord.getDiaId(),currentHr.getAppId(),currentHr.getInsCode(),currentHr.getInsId(),currentHr.getInsName(),questionListVo.getFormType());

        //统计选项的事件数据
        statisticsDicEvent.writeReadFromRedis(tRecord.getRecId(), tRecord.getDiaId(),currentHr.getAppId(),currentHr.getInsCode(),currentHr.getInsId(),currentHr.getInsName(),questionListVo.getPatientId());
        return resEntity;

    }
}
