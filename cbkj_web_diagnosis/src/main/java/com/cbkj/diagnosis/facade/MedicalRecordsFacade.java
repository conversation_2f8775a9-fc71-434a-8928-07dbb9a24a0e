package com.cbkj.diagnosis.facade;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.monitor.vo.GetPatientInfo;
import com.cbkj.diagnosis.beans.monitor.dto.PatientInfoDTO;
import com.cbkj.diagnosis.beans.monitor.dto.RecordInfoDTO;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.response.TDiseaseResponse;
import com.cbkj.diagnosis.common.enums.*;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.service.monitor.impl.MedicalRecordsServiceImpl;
import com.cbkj.diagnosis.utils.BeanUtilSelf;
import com.cbkj.diagnosis.utils.DesensitizeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by zbh on 2024/6/4 15:00
 *
 * @description：病历业务实现层
 */
@Component
public class MedicalRecordsFacade {

    private final MedicalRecordsServiceImpl medicalRecordsService;
    private final TAdminInfoMapper tAdminInfoMapper;

    public MedicalRecordsFacade(MedicalRecordsServiceImpl medicalRecordsService, TAdminInfoMapper tAdminInfoMapper) {
        this.medicalRecordsService = medicalRecordsService;
        this.tAdminInfoMapper = tAdminInfoMapper;
    }

    /**
     * 获取患者信息列表根据患者ID
     *
     * @param patientId 患者ID
     * @return 患者信息列表
     */
    public PatientInfoDTO getPatientByPatientId(String patientId) {

//        QueryWrapper<MedicalRecords> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("patient_id",patientId);
//        MedicalRecords patientInfo = this.getOne(queryWrapper, false);
        TAdminInfo tAdminInfoById = tAdminInfoMapper.getTAdminInfoById(patientId);
        PatientInfoDTO patientInfoDTO = new PatientInfoDTO();
        if (null != tAdminInfoById) {
            BeanUtil.copyProperties(tAdminInfoById, patientInfoDTO);
            patientInfoDTO.setPatientId(tAdminInfoById.getUserId());
            patientInfoDTO.setPatientHealthCardNum(tAdminInfoById.getHealthCardNum());
            patientInfoDTO.setPatientAge(tAdminInfoById.getAge());
            patientInfoDTO.setInsuranceTypeCode("");
            patientInfoDTO.setNationality(tAdminInfoById.getNationality());
            patientInfoDTO.setNation(tAdminInfoById.getNation());
            patientInfoDTO.setPatientCardType(IdentificationType.getValueName(tAdminInfoById.getPatientCardType()));
            patientInfoDTO.setEducationCode(EducationLevel.getNameByCode(tAdminInfoById.getEducationCode()));
            patientInfoDTO.setMaritalStatus(MaritalStatus.getNameByCode(tAdminInfoById.getMaritalStatus()));
            patientInfoDTO.setPatientSex(StringUtils.isNotEmpty(tAdminInfoById.getSex()) ? Enum.valueOf(SexEnum.class, tAdminInfoById.getSex()).getMessage() : null);

            //脱敏处理
            String phone = DesensitizeUtils.mobileDesensitize(tAdminInfoById.getMobile());
            patientInfoDTO.setPatientPhone(phone);
            String cardNo = DesensitizeUtils.idCardDesensitize(tAdminInfoById.getCardNumber());
            patientInfoDTO.setPatientCardNumber(cardNo);
            String name = DesensitizeUtils.nameDesensitize(tAdminInfoById.getUserName());
            patientInfoDTO.setPatientName(name);

        }

        return patientInfoDTO;
    }


    /**
     * 获取中医疾病列表根据患者Id
     *
     * @param patientId
     * @return
     */
    public List<TDiseaseResponse> getChineseDiseasesListByPatientId(String patientId) {
        QueryWrapper<MedicalRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("patient_id", patientId).groupBy("chinese_dis_name");
        List<MedicalRecords> list = medicalRecordsService.list(queryWrapper);
        List<TDiseaseResponse> responseList = null;
        if (CollectionUtil.isNotEmpty(list)) {
            responseList = BeanUtilSelf.listCopyTo(list, TDiseaseResponse.class);
        }
        return responseList;
    }


    /**
     * 获取患者信息列表根据患者ID
     *
     * @param getPatientInfo getPatientInfo
     * @return 患者信息列表
     */
    public PatientInfoDTO getPatientInfo(GetPatientInfo getPatientInfo) {

        PatientInfoDTO dto = this.getPatientByPatientId(getPatientInfo.getPatientId());
        //获取患者所有中医疾病
        List<TDiseaseResponse> disList = this.getChineseDiseasesListByPatientId(getPatientInfo.getPatientId());
        dto.setChineseDiseases(disList);

        //获取患者所有记录信息
        List<RecordInfoDTO> recordList = medicalRecordsService.getRecordListByCreateDate(getPatientInfo);
        dto.setRecordList(recordList);

        //将四个类型全部返还
        RecordTypeEnum[] values = RecordTypeEnum.values();
        dto.setRecordTypeEnums(values);

        return dto;
    }


}
