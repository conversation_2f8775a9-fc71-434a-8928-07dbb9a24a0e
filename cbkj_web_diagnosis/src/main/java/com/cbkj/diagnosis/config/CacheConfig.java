//package com.cbkj.diagnosis.config;
//
//import org.springframework.cache.CacheManager;
//import org.springframework.cache.annotation.EnableCaching;
//import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 缓存配置类 - 性能优化版本
// * 为问卷问题数据提供缓存支持，显著提升查询性能
// *
// * <AUTHOR>
// * @date 2024/12/23
// * @version 2.0 - 性能优化版本
// */
//@Configuration
//@EnableCaching
//public class CacheConfig {
//
//    /**
//     * 配置缓存管理器
//     * 使用ConcurrentMapCacheManager作为高性能内存缓存
//     * 生产环境建议使用Redis等分布式缓存
//     */
//    @Bean
//    public CacheManager cacheManager() {
//        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
//
//        // 设置缓存名称
//        cacheManager.setCacheNames(java.util.Arrays.asList(
//            "questionnaireQuestions",  // 问卷问题缓存
//            "questionnaireCount"       // 问卷统计缓存
//        ));
//
//        // 不允许空值缓存，提高缓存效率
//        cacheManager.setAllowNullValues(false);
//
//        return cacheManager;
//    }
//}
