//package com.cbkj.diagnosis.config;
//
//import org.springframework.cache.CacheManager;
//import org.springframework.cache.annotation.EnableCaching;
//import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 缓存配置类
// * 为问卷问题数据提供缓存支持
// */
//@Configuration
//@EnableCaching
//public class CacheConfig {
//
//    /**
//     * 配置缓存管理器
//     * 使用ConcurrentMapCacheManager作为简单的内存缓存
//     * 生产环境建议使用Redis等分布式缓存
//     */
//    @Bean
//    public CacheManager cacheManager() {
//        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
//        // 设置缓存名称
//        cacheManager.setCacheNames(java.util.Arrays.asList("questionnaireQuestions"));
//        // 允许空值缓存
//        cacheManager.setAllowNullValues(false);
//        return cacheManager;
//    }
//}
