server.port=78

#spring.datasource.primary.username=DgUfz0nYLCR7UJ66sgG1ag==
#spring.datasource.primary.password=OPXCNabmBpi/FKayjb2kubBySH5LiQln
#spring.datasource.primary.url=**********************************************************************************************************************************************************
spring.datasource.primary.username=DN8P0fnEZZLBobVIRULXJg==
spring.datasource.primary.password=TvvEYfAlbtiZqahGRCFUwuThx8Xy7Qb8
spring.datasource.primary.url=*********************************************************************************************************************************************************



#spring.datasource.primary.username=b+ReRdk5H8BV1MZXnuv3Pg==
#spring.datasource.primary.password=qk0UBu3JMuh2VxsiRPcJ2cavr9LawxbE
#spring.datasource.primary.url=jdbc:mysql://************:3506/cbkj_pre_diagnosis?allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai&autoReconnect=true&failOverReadOnly=false
## redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.password=cbkj123!@#
spring.redis.database = 7
#\u4E2D\u836F\u714E\u914D\u67E5\u8BE2\u79FB\u52A8\u7AEF
address.mobile.decoct=http://**************:91/
######\u4EE5\u4E0B\u4E3A\u56FA\u5B9A\u914D\u7F6E\uFF0C\u4E0D\u8981\u8F7B\u6613\u6539\u52A8
spring.datasource.primary.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.primary.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.common=yes
spring.datasource.common.initialSize=1
spring.datasource.common.minIdle=3
spring.datasource.common.maxActive=20
spring.datasource.common.maxWait=60000
spring.datasource.common.timeBetweenEvictionRunsMillis=60000
spring.datasource.common.minEvictableIdleTimeMillis=300000
spring.datasource.common.validationQuery=SELECT 1 FROM DUAL
spring.datasource.common.testWhileIdle=true
spring.datasource.common.testOnBorrow=false
spring.datasource.common.testOnReturn=false
spring.datasource.common.poolPreparedStatements=true
spring.datasource.common.filters=stat, wall
spring.datasource.common.wall.enabled=false
spring.datasource.common.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.common.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
spring.datasource.common.useGlobalDataSourceStat=true
spring.datasource.common.removeAbandoned=true
spring.datasource.common.removeAbandonedTimeout=300
spring.datasource.common.logAbandoned=false
spring.datasource.common.socketTimeout = 60000
spring.datasource.common.connectTimeout = 30000
server.tomcat.uri-encoding=UTF-8
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true
spring.messages.encoding=UTF-8
management.endpoints.web.exposure.include=*
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB
server.servlet.context-path=/diagnosis_web_api
#spring.mvc.favicon.enabled=false
logging.config=classpath:logback-spring.xml
logging.level.root=WARN
logging.level.com=DEBUG
mybatis.mapper-locations=classpath*:mapper/**/*.xml
mybatis.type-aliases-package=com.cbkj.diagnosis.beans
mybatis.config-location=classpath:/mybatis-config.xml
spring.web.resources.chain.cache=true
spring.web.resources.chain.compressed=true
spring.web.resources.chain.strategy.fixed.enabled=true
spring.web.resources.chain.strategy.fixed.paths=/sys/**
spring.web.resources.chain.strategy.fixed.version=2.0.0
spring.jmx.enabled=false
## redis
spring.redis.timeout=5000ms
spring.redis.jedis.pool.max-active=100
spring.redis.jedis.pool.max-idle=20
spring.redis.jedis.pool.min-idle=1
spring.redis.jedis.pool.max-wait=100ms
## http \u8FDE\u63A5\u6C60\u8BBE\u7F6E
http-pool.max-total=200
http-pool.default-max-per-Route=100
http-pool.connect-timeout=3000
http-pool.connection-request-timeout=1000
http-pool.socket-timeout=60000
http-pool.validate-after-inactivity=2000
## \u6839\u5BC6\u7801
root.encryptor.password=::!qb#9467@
## \u76F8\u5BF9\u8DEF\u5F84
root.upload.relative=cbkjFile/
## \u8BBF\u95EE\u8DEF\u5F84
root.preview=preview/
##
jwt.secret=secret
#jwt.expiration = 10800000
jwt.expiration=86400000
jwt.expirationExt=3600000
jwt.token=Authorization
file.address=/app/uploadFile/
template.address=/app/templates/
login.one=false
rsa.publicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDaKmCXLWqBIVOVSa/dTY08bgKhWcvi3j6vPksjrV7X3oFZ6dbF3XhaTXuV65x9qqmlQqlNepkqv03miY72QkjWMgbaEt0v5MFnQ+B2AWPIGunvSbaDIBwer/6V9f+ra7imLdgt9jwlkjDYvRjvw7mDHFkXkMvKWl1FFihV0EN8JwIDAQAB
rsa.privateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANoqYJctaoEhU5VJr91NjTxuAqFZy+LePq8+SyOtXtfegVnp1sXdeFpNe5XrnH2qqaVCqU16mSq/TeaJjvZCSNYyBtoS3S/kwWdD4HYBY8ga6e9JtoMgHB6v/pX1/6truKYt2C32PCWSMNi9GO/DuYMcWReQy8paXUUWKFXQQ3wnAgMBAAECgYBnRa4pgg8rh0oYniQIv0A/Pdgy5t3zy76il/tbrSL7TtGubVoEmfzxykHZSwnuFs5tc2vPSFye9qX6nl01R1VQZ7JM2FVpldRw0Rrqv3paQjyHdKOcIYBJG2mxI7fpg/du8kwlkNCHRl39aJHQXHbY+lLEwE4HBJ7niMvdGoIpGQJBAPb+47h3++8vFFx99GlsrEVS5NAYNr8VyuGRYfxJHD/qNFcOZmosi8NnziFFnyQ/0C0rou5EDJu+jlBJdl1KNHsCQQDiHm2J9zSLDAr70ZyQ4l+vaPbw+26CFByBG4dsh7xeNa95mSbj72JuCZCyuQZaacThf3iWQuzy2N9TUFib+9VFAkEAlamnFJzndGwDm3PayJLH5A2xhgJWEf1DfODaDcPDMVtZsbKRDh7F5Xad6X1FS/K60tQRGuzy8uBJXY7WAPs4xwJAZ1zNadHM/PrGUpJg5YH1h3ON3l6xB1k2JnZ1E1GA8/fKfOVbd7pH3lEVCf22P8I1s3bXoqh5NBGbFLSXrMYTmQJBAJBxs9VEz2+qFfJmWSYiJBugoFD/cBWfL1VHwNeuZtJxFXXpkB3G1CLaII1SaGOWXHFMOycbemfTQzxSEiRFjOE=
rsa.publicKey2=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjzm8Gij4bzGp5oVP/6IjUwKGtuAt/MX2di7/e09teyL0JD3E2TFY8hfJE+RN4tp8SwZi+44mKwZUNhjhtDayEBx+LnKnhtkxtruGbyteHe0GUczDy2FFh4eT6cN/KXR/Bcc19TSfleZMRq56l1atKicPgXfysEzWq+O8UgRiZ/UyY/pf8UNec3PePtlj47hzs+7Bh+q1tUYOHqrogeSD92xvG6xushjQcuoTcdDJCVCLVxXVmff/YBbpZAwCQKB28W0gH4B7n9IpaONL6/jghbNUahKO6NL6iofxE6TOgc2VfzR5wlqaqD3wi0NRxx+S9JiSA+k3O5uqFk7V7URiwQIDAQAB
rsa.privateKey2=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCPObwaKPhvManmhU//oiNTAoa24C38xfZ2Lv97T217IvQkPcTZMVjyF8kT5E3i2nxLBmL7jiYrBlQ2GOG0NrIQHH4ucqeG2TG2u4ZvK14d7QZRzMPLYUWHh5Ppw38pdH8FxzX1NJ+V5kxGrnqXVq0qJw+Bd/KwTNar47xSBGJn9TJj+l/xQ15zc94+2WPjuHOz7sGH6rW1Rg4equiB5IP3bG8brG6yGNBy6hNx0MkJUItXFdWZ9/9gFulkDAJAoHbxbSAfgHuf0ilo40vr+OCFs1RqEo7o0vqKh/ETpM6BzZV/NHnCWpqoPfCLQ1HHH5L0mJID6Tc7m6oWTtXtRGLBAgMBAAECggEBAI6+pveXn8sKv19tvIZ17SjdWrBHC+5iyrmNK7mVNvNDqfm4ykkBQrm99PzrTKjswnXvvpUiXm4BF8r2z3RSEvO1whi+0rK2IzMRAPIpUdsXkXXTU8yOfMKP4F5aJpGFdtembOOe9/JpHdpQKQROMbXSSkTp7CnD2yxjhBxmHEGog8PbpHTElP0SsYPPf3PkfkoEiwkTPS18j8sK5kEdqgZ3/y5GUKOb1iBsoGxM1TGDFD+iux3Jc1WbuYeRKdsQjvT2KMV5V+9+OFj4yqd70/v2lq2RW80B9H15dKWvPYHfEA69KV6nJneD6gVJ2G+/RLr/pfqHXZt1hsMgXexY/1UCgYEAycUxbtdi5Tjg1xV6gfhjNwoqkGdC0V7EMrJqv+OwuQLBciMWYo1AskBO9EwbUHwes2fTFDTAj//sSY/mLlp9k9UpofqisUCw3u2Vz4rRTThRDlNNMkZ4B3JtSEshzGoT/6TIYG7SZwfY99hjabbVHHf8NNxguIcVEhzQaE988lcCgYEAtbhea6lQJAACPrdkXViHtKfnxIId/h3VotA3/FRMpxTYfj/okNJsIhWrnWjLmv5QV0N40OP0Sc/5NmuuxJ/XVnVrpcMgD6eQGW8m88oHz6k6RTngW1vl63spdFF9TQS1v6Reo7QYScSrn5p0JifQ4gBRqESdByJpdtV9f8WYlKcCgYEApNgVjfAsnQe+MhsbLqpnLKWCpt2manXZJ2465rE4Rb8pmn1uIXAz5i3CE7dGLZhqYLq3ae+7TyqQunz1WvOVWz0xOWQSnWwz1L4Ywiow27ziBCksjaGjGLp07DZt05rq4daX2qlP/tsbeUnx1oGIVSz2AZ3262t5XIXsAvzGIRsCgYAdkxX626FpWn8DCgHRl1jus+zqdGrBMsXUK6MQ8/9NqfzZ/ziwaS6kFXbKtYV9gio9KYP+KztUz/41Ny1IXuNC9PVCExGiupifKxHh112SFG6hWZwEAl7XBkJm5eRIpp2VxQCKEANr7hAod5CzrQM35OllQ1VzFcyoRHKaWuRMPQKBgHaMzmTgk+32ds7VVpAdds+xaOByPF6ZFaq47x0vDXUjPgQOse07h5mA32CA0nbQUbI2yVzYAa+Gdrlnn0U8thnwn0lSp7dzh/c/UWEkHDaqp9YyaVBUw3G7K0dpb5UdfAhD1vhqecAmERirJLTxXqW8UuoiuaqqqAXDR+nRXLXg
#???????
interceptor.tianan=true
interceptor.secret.type=sm4Stategy
#interceptor.secret.type=adminInfoStrategy
#swagger.production= false
swagger.basic.enable=true
swagger.basic.username=jack
swagger.basic.password=Admin2021@999
idutils.Snowflake=1
spring.devtools.restart.enabled=true
file.encoding = UTF-8

xia.men.mobile.wx.url = http://************:233/pdfu/mobile/login
#移动端【前端开发】提供的可通过cbdata登录的内网地址:对接云系统ipd的数字诊间使用
xia.men.mobile.wx.neiwang_url = http://************:233/pdfu/mobile/login

#his提供的互联网地址：用于中转跳到聪宝的移动端指定界面
xia.men.his.jump.login.url = https://*.dev.zoenet.cn/10082

diagnosis.interface.url = http://************:8073/diagnosis_interface_api

sys.aes.key = OUT3041JT51Y7HF2
#是否开启用户锁定
on.user.lock=true
#登录失败次数
login.attempts=5
#锁定时间 单位分钟
login.limit.time=15
#修改密码90天 单位天
change.password.reminder=90
#10分钟不操作 退出登录 单位分钟
idle.time=60

# 启用 Flyway
spring.flyway.enabled=false
## 指定迁移脚本的位置
#spring.flyway.locations=classpath:dbUp
## 在新数据库上设置基线
#spring.flyway.baseline-on-migrate=true
## 允许迁移脚本顺序不严格
#spring.flyway.out-of-order=true
#
## 数据库连接 URL
#spring.flyway.url=******************************************************************************
## 数据库用户名
#spring.flyway.user=root
## 数据库密码
#spring.flyway.password=root
#spring.security.user.name=jack
#spring.security.user.password=Admin2021@999
logging.level.org.springframework.cloud.openfeign= DEBUG
feign.compression.response.enabled=true
feign.httpclient.ok-http.read-timeout=60000
feign.httpclient.ok-http.connection-timeout=50000
feign.httpclient.enabled=true
feign.okhttp.enabled=true
feign.client.config.default.errorDecoder= com.cbkj.diagnosis.common.openfeign.CustomErrorDecoder
spring.task.execution.bounded.max-size = 200
spring.task.execution.bounded.queue-capacity = 50
oath.self.address: http://***********/platform/pre_parameter
gpt.api.base.url = https://healthcare.tcmbrain.com:10115/agent/api/tcm
gpt.api.ageng.id=a1e5a48bf35411efb9620242ac1e0004
gpt.api.cbdata.key=99748ee4718e43cd
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=5000 # 超时时间，默认1000ms
hystrix.command.default.execution.timeout.enabled=true
hystrix.command.default.circuitBreaker.requestVolumeThreshold = 10 # 统计窗口内请求数量阈值，默认20
hystrix.command.default.circuitBreaker.errorThresholdPercentage = 50  # 错误率百分比，默认50%
hystrix.command.default.circuitBreaker.sleepWindowInMilliseconds = 5000 # 熔断后休眠时间，默认5000ms