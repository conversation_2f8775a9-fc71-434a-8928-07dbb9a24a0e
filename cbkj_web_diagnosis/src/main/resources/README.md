# pre_paramater

综合业务

```text
tcm-platform.release.*.zip 为前端压缩包
pre_paramater_1.0.jar 为后端jar包
pre_paramater.*.sql 为数据库升级脚本
config/application.properties 为后端配置文件
application.properties中的数据库用户名密码加密方式：请求"http://:67/pre_ai/noAuth/encrypt?val=你的用户名"，把返回值填入spring.datasource.primary.username，密码同理
```
### 1.1.4
`date:2022.09.27` `Git版本：`` 版本MD5值：` ``
- 字典映射的优化:知识库映射优化：1、双击药品自动搜索知识库名称；2、知识库名称支持简拼搜索.知识库映射、国标目录映射、国标字典映射：1、映射字典数据后后定位至当前页，不重新返回首页；2、搜索键绑定“回车”键值；
- 登录优化:登录token有效期延调整为1天
- 医疗机构新增处方一件事字段维护
- 业务管理增加个性化参数抽屉展示:存在个性化的参数，参数编号前出现图标提醒（参数配置页、参数搜索页）

### 1.1.3-rc3
`date:2022.09.19` `Git版本：`bda4168e` 版本MD5值：` `9cc8cbc06795955099f51df29ba559f4`
- 修复状态同步日志列表显示

### 1.1.3、1.1.3-rc1、1.1.3-rc2
`date:2022.09.14` `Git版本：`58cb35a9` 版本MD5值：` `cab6fe77d2210af4b3ce099ae9713b59`
- 药房服务配置增加通用配置
- 药房服务配置-增加配置复制保存接口
- 药房服务配置-增加获取通用数据接口和保存通用数据接口
- 新增同步任务类型：状态同步。--rc1
- 系统字典-字典维护-药房字典维护优化。--rc2


### 1.1.2
`date:2022.07.28` `Git版本：`84faa8d` 版本MD5值：` `d5d4671e3863dd4cd96030594dcc7b4f`
- 新增药房服务配置

### 1.1.1
`date:2022.06.22` `Git版本：`3c16648` 版本MD5值：` `6e395fd8c110929a07a319e1c5e5dba1`

- 参数全局搜索