<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.sysmapper.AdminInfoMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.sysBeans.AdminInfo">
        <id column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="name_zh" jdbcType="VARCHAR" property="nameZh"/>
        <result column="user_heand" jdbcType="VARCHAR" property="userHeand"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="EXPIRE_DATE" jdbcType="TIMESTAMP" property="expireDate"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="sort" jdbcType="VARCHAR" property="sort"/>
        <result column="ins_id" jdbcType="VARCHAR" property="insId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="ins_name" jdbcType="VARCHAR" property="insName"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="employee_id" jdbcType="VARCHAR" property="employeeId"/>
        <result column="certificate" jdbcType="VARCHAR" property="certificate"/>
        <result column="last_update_pwd" jdbcType="TIMESTAMP" property="lastUpdatePwd"/>
        <collection property="roles" ofType="com.cbkj.diagnosis.beans.sysBeans.AdminRule">
            <result column="role_id" property="roleId"/>
            <result column="role_name" property="roleName"/>
        </collection>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.beans.sysBeans.AdminInfo">
        <id column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="name_zh" jdbcType="VARCHAR" property="nameZh"/>
        <result column="user_heand" jdbcType="VARCHAR" property="userHeand"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="EXPIRE_DATE" jdbcType="TIMESTAMP" property="expireDate"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="sort" jdbcType="VARCHAR" property="sort"/>
        <result column="ins_id" jdbcType="VARCHAR" property="insId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="ins_name" jdbcType="VARCHAR" property="insName"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="employee_id" jdbcType="VARCHAR" property="employeeId"/>
        <result column="certificate" jdbcType="VARCHAR" property="certificate"/>
        <collection property="roles" ofType="com.cbkj.diagnosis.beans.sysBeans.AdminRule">
            <result column="role_id" property="roleId"/>
            <result column="role_name" property="roleName"/>
        </collection>
        <collection property="userDisMappingList" ofType="com.cbkj.diagnosis.beans.business.AdminDisList">
            <result column="dis_id" property="disId"/>
            <result column="dis_name" property="disName"/>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        user_id,user_name,password,sex,status,phone,name_zh,email,EXPIRE_DATE,sort,ins_id,ins_code,dept_id,dept_name,app_id,ins_name,last_update_pwd,create_date
    </sql>

    <insert id="updateUserInfo" parameterType="com.cbkj.diagnosis.beans.sysBeans.AdminInfo">

        update sys_admin_info
        <set>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="password != null and password != ''">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="employeeId != null">
            employee_id = #{employeeId,jdbcType=VARCHAR},
            </if>
            <if test="insName != null">
                ins_name = #{insName,jdbcType=VARCHAR},
            </if>



            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="nameZh != null">
                name_zh = #{nameZh,jdbcType=VARCHAR},
            </if>
            <if test="userHeand != null">
                user_heand = #{userHeand,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="insCode != null">
                INS_CODE = #{insCode},
            </if>
            <if test="insId != null">
                ins_id = #{insId},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="deptCode != null">
                dept_code = #{deptCode},
            </if>
            <if test="deptName != null ">
                dept_name = #{deptName},
            </if>
            <if test="expireDate != null ">
                EXPIRE_DATE = #{expireDate},
            </if>

            <if test="certificate != null">
                certificate = #{certificate},
            </if>

        </set>
        where user_id = #{userId,jdbcType=VARCHAR}

    </insert>
    <insert id="insertUserInfo" parameterType="com.cbkj.diagnosis.beans.sysBeans.AdminInfo">
        insert into sys_admin_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createDate != null">
                create_date,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createUserName != null and createUserName !=''">
                create_user_name,
            </if>

            <if test="lastIp != null">
                last_ip,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="nameZh != null">
                name_zh,
            </if>
            <if test="userHeand != null">
                user_heand,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="certificate != null">
                certificate,
            </if>
            <if test="employeeId != null">
                employee_id,
            </if>
            <if test="insId != null">
                ins_id,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="deptName != null">
                dept_name,
            </if>
            <if test="insName != null">
                ins_name,
            </if>
            <if test="appId != null">
                APP_ID,
            </if>
            <if test="insCode != null">
                ins_code,
            </if>
            <if test="deptCode != null">
                dept_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="createUserName != null and createUserName !=''">
                #{createUserName,jdbcType=VARCHAR},
            </if>

            <if test="lastIp != null">
                #{lastIp,jdbcType=VARCHAR},
            </if>

            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="nameZh != null">
                #{nameZh,jdbcType=VARCHAR},
            </if>
            <if test="userHeand != null">
                #{userHeand,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="certificate != null">
                #{certificate,jdbcType=VARCHAR},
            </if>
            <if test="employeeId != null">
                #{employeeId,jdbcType=VARCHAR},
            </if>
            <if test="insId != null">
                #{insId,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="deptName != null">
                #{deptName,jdbcType=VARCHAR},
            </if>
            <if test="insName != null">
                #{insName,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="insCode != null">
                #{insCode,jdbcType=VARCHAR},
            </if>
            <if test="deptCode != null">
                #{deptCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertRule">
        insert into sys_admin_rule(
            role_id,
        create_date
        <if test="roleName != null and roleName != ''">
            ,role_name
        </if>
        <if test="roleDesc != null and roleDesc != ''">
            ,role_desc
        </if>
        <if test="createUser != null and createUser !='' ">
            ,create_user
        </if>
        <if test="indexUrl != null and indexUrl !=''">
            ,index_url
        </if>
        ) values (

            #{roleId},
                #{createDate}
                <if test="roleName != null and roleName != ''">
                    ,#{roleName}
                </if>
                <if test="roleDesc != null and roleDesc != ''">
                    ,#{roleDesc}
                </if>
                <if test="createUser != null and createUser !='' ">
                    ,#{createUser}
                </if>
                <if test="indexUrl != null and indexUrl !=''">
                    ,#{indexUrl}
                </if>
        )
    </insert>
    <update id="updateRoleInfo" parameterType="com.cbkj.diagnosis.beans.sysBeans.AdminRule" >
        update sys_admin_rule
        set

            role_name = #{roleName}

        <if test="roleDesc != null and roleDesc != ''">
            ,role_desc = #{roleDesc}
        </if>
        <if test="createUser != null and createUser !='' ">
            ,create_user = #{createUser}
        </if>
        <if test="indexUrl != null and indexUrl !=''">
            ,index_url = #{indexUrl}
        </if>
        where role_id = #{roleId}

    </update>
    <update id="updatePwd" parameterType="Map">
        update sys_admin_info set password = #{newPwd} ,last_update_pwd = #{lastUpdatePwd} where user_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <delete id="deleteAdminInfoRulebyRuleId" parameterType="com.cbkj.diagnosis.beans.sysBeans.AdminRule">
        DELETE a ,b
        FROM sys_admin_rule  b LEFT JOIN sys_admin_info_rule a ON(a.role_id = b.role_id)
        <where>
            b.role_id != '1'

            AND b.`role_id` = #{roleId}

        </where>


    </delete>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,(select ins_name from sys_institution ins where ins.status = '0' and ins.ins_code = ai.ins_code) as ins_name
        from sys_admin_info ai
        where user_id = #{id,jdbcType=VARCHAR} and status = '0'
    </select>

    <select id="loadUserByUsername" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_admin_info ai
        where (user_name = #{username} ) and status = '0'
    </select>

    <select id="countAdminList" resultType="Integer" parameterType="com.cbkj.diagnosis.service.vo.QueryContent">
        select count(*) from sys_admin_info as a
        <where>
            status = '0'
            <if test="queryContent != null and queryContent != ''">
                and (
                a.user_name like CONCAT('%',trim(#{queryContent}),'%')
                or
                a.name_zh like CONCAT('%',trim(#{queryContent}),'%')
                or
                a.phone like CONCAT('%',trim(#{queryContent}),'%')
                or
                a.ins_name like CONCAT('%',trim(#{queryContent}),'%')
                )
            </if>
        </where>
    </select>
    <select id="getAdminList" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.service.vo.QueryContent">
        select
        a.user_id,
        a.user_name,
        a.password,
        a.sex,
        a.status,
        a.phone,
        a.name_zh,
        a.email,
        a.EXPIRE_DATE,
        a.sort,
        a.ins_id,
        a.ins_code,
        a.ins_name ,
        a.employee_id ,
        a.certificate,
        a.dept_id,
        a.dept_name,
        c.role_name,
        c.role_id
            from sys_admin_info a
                left join sys_admin_info_rule b on (a.user_id = b.user_id)
                left join sys_admin_rule c on(c.role_id=b.role_id)

<where>
    status = '0'
    <if test="queryContent != null and queryContent != ''">
        and (
        a.user_name like CONCAT('%',trim(#{queryContent}),'%')
            or
        a.name_zh like CONCAT('%',trim(#{queryContent}),'%')
            or
        a.phone like CONCAT('%',trim(#{queryContent}),'%')
            or
        a.ins_name like CONCAT('%',trim(#{queryContent}),'%')
        )
    </if>
</where>
order by a.create_date desc limit #{limit} offset #{page}
    </select>



    <select id="getAdminDisList" resultType="com.cbkj.diagnosis.beans.business.AdminDisList" parameterType="String">
        select         saidm.dis_id ,
                       saidm.dis_name from sys_admin_info_dis_mapping as saidm where saidm.user_id = #{id}
    </select>


    <resultMap id="RoleMap" type="com.cbkj.diagnosis.beans.sysBeans.AdminRule">
        <id column="role_id" jdbcType="VARCHAR" property="roleId"/>
        <id column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <id column="create_date" jdbcType="VARCHAR" property="createDate"/>
        <id column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <id column="role_desc" jdbcType="VARCHAR" property="roleDesc"/>

    </resultMap>
    <select id="getRoleListPage" parameterType="com.cbkj.diagnosis.beans.sysBeans.AdminRule" resultMap="RoleMap">
        select
        role_id,role_name,create_date,create_user,role_desc
        from sys_admin_rule
        <where>
            <if test="roleName != null and roleName != ''">
                and role_name like CONCAT('%',trim(#{roleName}),'%')
            </if>

        </where>
    </select>
    <select id="getUserById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List"/>
            from
        sys_admin_info <where>
<if test="userId != null">
    and user_id = #{userId}
</if>

    </where>
    </select>
    <select id="validateParam" parameterType="Map" resultType="Map">
        SELECT
        a.`user_name` userName,
        a.name_zh nameZh,
        a.user_id userId
        FROM
        sys_admin_info AS a
        <where>
            a.status = '0'
            <if test="name != null and name !=''">
                and a.user_name = #{name}
            </if>
            <if test="insCode != null and insCode !=''">
                AND a.INS_CODE = #{insCode}
            </if>
            <if test="appId != null and appId !=''">
                AND a.APP_ID = #{appId}
            </if>
            <if test="employeeId != null and employeeId !=''">
                AND a.employee_id = #{employeeId}
            </if>
            <if test="id != null and id != ''">
                AND a.user_id != #{id}
            </if>

        </where>

    </select>
    <select id="getUserByEmployeeId" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.beans.sysBeans.AdminInfo">

        select <include refid="Base_Column_List"/>
        from
        sys_admin_info
        <where>
            status = '0'
            <if test="insCode != null and insCode !=''">
                AND INS_CODE = #{insCode}
            </if>
            <if test="appId != null and appId !=''">
                AND APP_ID = #{appId}
            </if>
            <if test="employeeId != null and employeeId !=''">
                AND employee_id = #{employeeId}
            </if>
            <if test="certificate != null and certificate !=''">
                AND certificate = #{certificate}
            </if>

    </where>
    </select>
    <select id="getDiagnosisUserList" resultType="com.cbkj.diagnosis.beans.sysBeans.AdminInfo" parameterType="List">
        select a.name_zh nameZh ,a.user_id userId from sys_admin_info as a join sys_dept as b on( a.dept_id = b.dept_id)
<where>
        b.dept_id in
    <foreach collection="strings" item="item" open="(" separator="," close=")">
        #{item}
    </foreach>
</where>

    </select>
    <select id="getSuiFangUserList" resultType="com.cbkj.diagnosis.beans.sysBeans.AdminInfo" parameterType="String">
        select a.name_zh nameZh ,a.user_id userId from sys_admin_info as a
<where>
    <if test="doctorName != null and doctorName !=''">
        a.name_zh like CONCAT('%',trim(#{doctorName}),'%')
    </if>
</where>
    </select>

</mapper>
