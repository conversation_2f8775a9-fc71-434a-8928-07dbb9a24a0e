<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.sysmapper.AdminMenuMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.sysBeans.AdminMenu">
        <id column="menu_id" jdbcType="VARCHAR" property="menuId"/>
        <result column="menu_name" jdbcType="VARCHAR" property="menuName"/>
        <result column="menu_path" jdbcType="VARCHAR" property="menuPath"/>
        <result column="menu_class" jdbcType="VARCHAR" property="menuClass"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="parent_menu_id" jdbcType="VARCHAR" property="parentMenuId"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="cteate_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="menu_type" jdbcType="VARCHAR" property="menuType"/>
        <result column="btn_class" jdbcType="VARCHAR" property="btnClass"/>
        <result column="btn_type" jdbcType="VARCHAR" property="btnType"/>
        <result column="btn_weight" jdbcType="INTEGER" property="btnWeight"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="open_type" jdbcType="INTEGER" property="openType"/>
        <result column="modual_code" jdbcType="INTEGER" property="modualCode"/>
        <result column="menu_level" jdbcType="INTEGER" property="menuLevel"/>
        <collection property="rules" ofType="com.cbkj.diagnosis.beans.sysBeans.AdminRule">
            <result column="role_id" property="roleId"/>
            <result column="role_name" property="roleName"/>
        </collection>
    </resultMap>

    <resultMap id="ADMINMENUMAP" type="com.cbkj.diagnosis.beans.sysBeans.AdminMenu">
        <id column="menu_id" jdbcType="VARCHAR" property="menuId"/>
        <result column="menu_name" jdbcType="VARCHAR" property="menuName"/>
        <result column="menu_path" jdbcType="VARCHAR" property="menuPath"/>
        <result column="menu_class" jdbcType="VARCHAR" property="menuClass"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="parent_menu_id" jdbcType="VARCHAR" property="parentMenuId"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="cteate_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="menu_type" jdbcType="VARCHAR" property="menuType"/>
        <result column="btn_class" jdbcType="VARCHAR" property="btnClass"/>
        <result column="btn_type" jdbcType="VARCHAR" property="btnType"/>
        <result column="btn_weight" jdbcType="INTEGER" property="btnWeight"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="open_type" jdbcType="INTEGER" property="openType"/>
        <result column="modual_code" jdbcType="INTEGER" property="modualCode"/>
        <result column="menu_level" jdbcType="INTEGER" property="menuLevel"/>
    </resultMap>

    <resultMap id="BaseResultMap3" type="com.cbkj.diagnosis.beans.sysBeans.AdminMenu">
        <id column="menu_id" jdbcType="VARCHAR" property="menuId"/>
        <result column="menu_name" jdbcType="VARCHAR" property="menuName"/>
        <result column="menu_path" jdbcType="VARCHAR" property="menuPath"/>
        <result column="menu_class" jdbcType="VARCHAR" property="menuClass"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="parent_menu_id" jdbcType="VARCHAR" property="parentMenuId"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="cteate_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="menu_type" jdbcType="VARCHAR" property="menuType"/>
        <result column="btn_class" jdbcType="VARCHAR" property="btnClass"/>
        <result column="btn_type" jdbcType="VARCHAR" property="btnType"/>
        <result column="btn_weight" jdbcType="INTEGER" property="btnWeight"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="open_type" jdbcType="INTEGER" property="openType"/>
        <result column="modual_code" jdbcType="INTEGER" property="modualCode"/>
        <result column="menu_level" jdbcType="INTEGER" property="menuLevel"/>
    </resultMap>
    <sql id="Base_Column_List">
        menu_id
        , menu_name, menu_path, menu_class, status, parent_menu_id, create_date, cteate_user,menu_type,btn_class,btn_type,btn_weight,sort,open_type,modual_code,menu_level
    </sql>
    <insert id="insert" parameterType="com.cbkj.diagnosis.beans.sysBeans.AdminMenu" keyProperty="menuId"
            useGeneratedKeys="true">
        insert into sys_admin_menu (menu_name, menu_path, menu_class, status,
                                    parent_menu_id, create_date, cteate_user, menu_type, btn_class, btn_type,
                                    btn_weight, sort, open_type, modual_code, menu_level)
        values (#{menuName,jdbcType=VARCHAR},
                #{menuPath,jdbcType=VARCHAR}, #{menuClass,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
                #{parentMenuId,jdbcType=VARCHAR}, now(), #{createUser,jdbcType=VARCHAR}, #{menuType}, #{btnClass},
                #{btnType}, #{btnWeight}, #{sort},
                #{openType}, #{modualCode}, #{menuLevel})
    </insert>
    <insert id="insertListM" parameterType="List">
        insert into sys_admin_rule_menu (role_id,menu_id) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.roleId},#{item.menuId})
        </foreach>

    </insert>
    <update id="updateSortNumberByList" parameterType="ArrayList">
        <foreach collection="list" separator=";" item="item">
            update sys_admin_menu set sort =#{item.sort},parent_menu_id =#{item.parentMenuId} where
            menu_id=#{item.menuId}
        </foreach>
    </update>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.sysBeans.AdminMenu">
        update sys_admin_menu
        <set>
            <if test="menuName != null">
                menu_name = #{menuName,jdbcType=VARCHAR},
            </if>

            <if test="menuClass != null">
                menu_class = #{menuClass,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="parentMenuId != null">
                parent_menu_id = #{parentMenuId,jdbcType=INTEGER},
            </if>
            <if test="menuType != null">
                menu_type =#{menuType},
            </if>
            <if test="btnType != null">
                btn_type = #{btnType},
            </if>
            <if test="btnClass != null">
                btn_class =#{btnClass},
            </if>
            <if test="btnWeight != null">
                btn_weight =#{btnWeight},
            </if>
            <if test="sort != null">
                sort =#{sort},
            </if>
            <if test="openType != null">
                open_type =#{openType},
            </if>
            <if test="modualCode != null">
                modual_code =#{modualCode},
            </if>
            <if test="menuLevel != null">
                menu_level =#{menuLevel},
            </if>
        </set>
        where menu_id = #{menuId,jdbcType=VARCHAR}
    </update>
    <update id="updateEnabled" parameterType="Map">
        update sys_admin_menu
        set status = #{status}
        where menu_id = #{menuId}

    </update>

    <delete id="deleteRMbyMid" parameterType="ArrayList">
        delete from sys_admin_rule_menu where menu_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteBylis" parameterType="ArrayList">
        delete from sys_admin_menu where menu_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </delete>
    <delete id="deleteRmByRid" parameterType="String">
        delete
        from sys_admin_rule_menu
        where role_id = #{rid}
    </delete>

    <select id="getAllMenu" resultMap="BaseResultMap">
        select m.*, r.role_id, r.role_name
        from sys_admin_menu m
                 left join sys_admin_rule_menu mr on m.`menu_id` = mr.`menu_id`
                 left join sys_admin_rule r on mr.`role_id` = r.`role_id`
        WHERE m.`status` = '0'
        order by m.menu_level, m.sort
    </select>

    <select id="getMenuByPID" parameterType="Map" resultMap="BaseResultMap">
        select m.menu_id,m.menu_type ,m.menu_name,m.sort, m.menu_path, m.parent_menu_id, m.open_type,m.menu_level
        from sys_admin_menu m
        ,sys_admin_rule_menu rm
        ,sys_admin_info_rule ur
        where m.menu_id = rm.menu_id
        and rm.role_id = ur.role_id
        and ur.user_id = #{uid}
        and m.status ='0' and m.menu_type in
        <foreach collection="menuTypes.split(',')" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>

        <if test="parentMenuId != null">
            and m.parent_menu_id = #{parentMenuId}
        </if>


        GROUP BY m.menu_id order by m.menu_level, m.sort
    </select>
    <select id="getRolesNamesByUserId" resultType="java.lang.String" parameterType="String">
        SELECT
            GROUP_CONCAT(a.`role_name`)
        FROM `sys_admin_rule` a JOIN `sys_admin_info_rule` b ON(a.`role_id` = b.`role_id`)
        WHERE b.`user_id` = #{userId};
    </select>
    <select id="getPageDatas" parameterType="com.cbkj.diagnosis.beans.sysBeans.AdminMenu" resultType="Map">
        SELECT m.menu_id, m.menu_name, m.menu_path, m.menu_class, m.status, m.parent_menu_id,
        DATE_FORMAT(m.create_date,'%Y-%m-%d %H:%i:%s') create_date, m.cteate_user, m.menu_type,
        m.btn_class,m.btn_type,m.btn_weight,
        a.create_user_name AS createName,b.menu_name parentName,m.sort sort,m.open_type openType
        FROM sys_admin_menu m
        LEFT JOIN sys_admin_info a ON m.cteate_user = a.user_id
        LEFT JOIN sys_admin_menu b ON b.menu_id = m.parent_menu_id
        where 1=1
        <if test="menuName != null and menuName.length > 0">
            and m.menu_name like CONCAT('%',trim(#{menuName}),'%')
        </if>
        <if test="parentMenuId != -1">
            and m.parent_menu_id = #{parentMenuId}
        </if>
        order by m.sort ASC

    </select>
    <select id="selectByPrimaryKey" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_admin_menu
        where menu_id = #{menuId}
    </select>
    <select id="getMenuObjByMID" resultMap="ADMINMENUMAP" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from sys_admin_menu
        WHERE status = '0' and menu_id = #{id}
        ORDER by sort ASC
    </select>
    <select id="getMenuObjByRID" resultMap="ADMINMENUMAP" parameterType="String">
        SELECT a.menu_id,
               a.menu_name,
               a.menu_path,
               a.menu_class,
               a.status,
               a.parent_menu_id,
               a.create_date,
               a.cteate_user,
               a.menu_type,
               a.btn_class,
               a.btn_type,
               a.btn_weight,
               a.sort,
               a.open_type
        FROM sys_admin_menu a
                 left join sys_admin_rule_menu c on c.menu_id = a.menu_id
        WHERE a.status = '0'
          AND c.role_id = #{id}
        GROUP BY a.menu_id
        ORDER by a.sort ASC
    </select>
    <select id="getAllMenuList" resultMap="BaseResultMap">
        select m.*
        from sys_admin_menu m
        ORDER by m.sort ASC
    </select>

</mapper>