START TRANSACTION;
ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    CHANGE `question_type` `question_type` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '题目类型（1文本  2单选  3多选  4时间 5.血压 6日期 7值域滑动样式 8图片 9。AI舌诊 10.OCR药盒识别 11.OCR检查检验单）',
    ADD COLUMN `question_img` TEXT NULL COMMENT 'V2.1.0-图示示例' ,
    ADD COLUMN `question_img_max_num` INT(2) NULL COMMENT 'V2.1.0-图片最大上传数量' ;

ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    CHANGE `question_type` `question_type` VARCHAR(2) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '题目类型（1文本  2单选  3多选  4时间 5.血压 6日期 7值域滑动样式 8图片 9。AI舌诊 10.OCR药盒识别 11.OCR检查检验单）';

ALTER TABLE `cbkj_pre_diagnosis`.`t_record_dia`
    CHANGE `question_type` `question_type` VARCHAR(2) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目类型（1文本  2单选  3多选  4时间 5.血压 6日期 7值域滑动样式 8图片 9。AI舌诊 10.OCR药盒识别 11.OCR检查检验单）';


INSERT INTO `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `parent_id`) VALUES ('question_dimension', '问卷题目维度', 'question_dimension', '-1', '-1');

INSERT INTO `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `parent_id`) VALUES ('question_dimension_pain', '疼痛', 'question_dimension', '1', 'question_dimension');
INSERT INTO `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `parent_id`) VALUES ('question_dimension_function', '功能', 'question_dimension', '2', 'question_dimension');
INSERT INTO `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `parent_id`) VALUES ('question_dimension_myselfFace', '自我形象', 'question_dimension', '3', 'question_dimension');
INSERT INTO `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `parent_id`) VALUES ('question_dimension_psychology', '心理', 'question_dimension', '4', 'question_dimension');
INSERT INTO `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `parent_id`) VALUES ('question_dimension_satisfaction', '满意度', 'question_dimension', '5', 'question_dimension');


CREATE TABLE if not exists `t_record_dia_image` (
                                                  `id` bigint(11) NOT NULL AUTO_INCREMENT,
                                                  `question_id` int(11) NOT NULL COMMENT '题目id',
                                                  `image_url` text NOT NULL COMMENT '上传图片地址',
                                                  `sort` int(11) DEFAULT NULL COMMENT '排序',
                                                  `dia_rec_id` int(11) DEFAULT NULL ,
                                                  `question_type` varchar(2) DEFAULT NULL COMMENT '1文本  2单选  3多选  4时间 5.血压 6日期 7值域滑动样式 8图片 9。AI舌诊 10.OCR药盒识别 11.OCR检查检验单',
                                                  `ai_content` text COMMENT 'ai识别结果',
                                                  PRIMARY KEY (`id`)
) ;


ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    ADD COLUMN `question_dimension_code` VARCHAR(32) NULL COMMENT '维度代码' ,
    ADD COLUMN `question_dimension_name` VARCHAR(64) NULL COMMENT '维度名称' ;
ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option`
    ADD COLUMN `option_dimension_score` DOUBLE(10,2) NULL COMMENT '选项分数' ,
    ADD COLUMN `option_dimension_score_switch` INT(1) NULL COMMENT '选项得分开关 1开0关';
COMMIT;