START TRANSACTION;
CREATE TABLE `t_pre_diagnosis_question_range` (
                                                  `question_range_id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `question_id` int(11) DEFAULT NULL,
                                                  `question_range_title` varchar(64) NOT NULL COMMENT '非必填，如填写则移动端展示',
                                                  `question_range_min` decimal(10,3) NOT NULL COMMENT '最小值必填，可输入，可上下翻',
                                                  `question_range_max` decimal(10,3) NOT NULL COMMENT '最大值必填，可输入，可上下翻',
                                                  `question_range_decimal` int(1) NOT NULL COMMENT '小数点必填，默认0，最多3位，可上下翻',
                                                  `question_range_default` decimal(10,3) NOT NULL COMMENT '默认值必填，填写时的默认项，与前三项联动',
                                                  `question_range_unit` varchar(16) DEFAULT NULL COMMENT '单位：必填',
                                                  PRIMARY KEY (`question_range_id`),
                                                  KEY `question_id` (`question_id`)
) ;


COMMIT;