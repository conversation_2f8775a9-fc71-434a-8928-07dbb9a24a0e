ALTER TABLE `cbkj_pre_diagnosis`.`t_record`
    ADD COLUMN `reg_plan_id` VARCHAR(32) NULL COMMENT 'HIS传入的挂号记录id' AFTER `form_type`;


ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_form`
    ADD COLUMN `show_status` BOOLEAN DEFAULT TRUE NULL AFTER `form_code`;



ALTER TABLE `cbkj_pre_diagnosis`.`t_record_dia`
    ADD COLUMN `week` VARCHAR(10) NULL COMMENT '周（6.日期类型）' AFTER `hour`;


ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    CHANGE `date_unit` `date_unit` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '时间单位（1年  2月  3日   4小时 5周 可多选,逗号拼接）';




insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `parent_id`) values ('suifang-type', '随访题目类别', 'sftmlb', '-1', '-1');

insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('zztzzb', '症状/体征指标', 'zztzzb', '1', 'suifang-type');

insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('zyzzzhzb', '中医症状/证候指标', 'zyzzzhzb', '2', 'suifang-type');

insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('fzjc', '辅助检查', 'fzjc', '3', 'suifang-type');

insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('shzl', '生活质量', 'shzl', '4', 'suifang-type');

insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('aqxpg', '安全性评估', 'aqxpg', '5', 'suifang-type');

insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('zdsj', '重大事件', 'zdsj', '6', 'suifang-type');

insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('suifang-type-qt', '其他', 'suifang-type-qt', '7', 'suifang-type');
insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values ('follow-up-event_unusual', '安全性评估-不良反应事件', 'follow-up-event', NULL, '7', '1', 'follow-up-event');
ALTER TABLE `cbkj_pre_diagnosis`.`t_propaganda_edu`
    ADD COLUMN `update_user_id` VARCHAR(32) NULL AFTER `edu_abstract`,
    ADD COLUMN `update_user_name` VARCHAR(256) NULL AFTER `update_user_id`,
    ADD COLUMN `show_status` TINYINT NULL AFTER `update_user_name`;



ALTER TABLE `cbkj_pre_diagnosis`.`t_propaganda_edu`
    ADD COLUMN `update_date` DATETIME NULL AFTER `show_status`;





ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    ADD COLUMN `follow_up_class_type_code` VARCHAR(32) NULL COMMENT '随访类别code' AFTER `question_code`;


insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `parent_id`) values ('follow-up-event', '随访答案单选/多选随访事件', 'follow-up-event', '-1', '-1');

insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('follow-up-event-focus', '异常关注事件', 'follow-up-event-focus', '1', 'follow-up-event');
insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('follow-up-event-archive-closure', '全流程档案结案事件', 'follow-up-event-archive-closure', 2, 'follow-up-event');
insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('follow-up-event-recovery', '疗效评价-痊愈事件', 'follow-up-event-recovery', 3, 'follow-up-event');
insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('follow-up-event-significant', '疗效评价-显效事件', 'follow-up-event-significant', 4, 'follow-up-event');
insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('follow-up-event-effective', '疗效评价-有效事件', 'follow-up-event-effective', 5, 'follow-up-event');
insert into `cbkj_pre_diagnosis`.`sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_sort`, `parent_id`) values ('follow-up-event-ineffective', '疗效评价-无效事件', 'follow-up-event-ineffective', 6, 'follow-up-event');

CREATE TABLE `t_propaganda_edu_dis_mapping` (
                                                `t_propaganda_edu_id` INT(10) NOT NULL COMMENT '健康宣教id',
                                                `dis_id` VARCHAR(32) CHARACTER SET utf8mb4 NOT NULL COMMENT '疾病id',
                                                `dis_name` VARCHAR(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '疾病名称',
                                                `dis_type` VARCHAR(1) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '疾病类型（1中医疾病   2西医疾病）',
                                                `dis_code` VARCHAR(32) DEFAULT NULL,
                                                PRIMARY KEY (`t_propaganda_edu_id`,`dis_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='宣教-疾病映射'
;


ALTER TABLE `cbkj_pre_diagnosis`.`t_propaganda_edu`
    ADD COLUMN `chinese_name` VARBINARY(250) NULL AFTER `update_date`;


insert into `cbkj_pre_diagnosis`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) values ('100202', '预诊报表', '/preDiagnosis', NULL, '0', '100200', NULL, NULL, '1', NULL, NULL, '1', '2', '2', '1', NULL);

ALTER TABLE `cbkj_pre_diagnosis`.`sys_admin_info` ADD last_update_pwd DATE COMMENT '上次修改密码时间';

insert into `cbkj_pre_diagnosis`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) values ('100600', '工作台', '/home', NULL, '0', '0', NULL, NULL, '1', NULL, NULL, '1', '0', '1', '1', NULL);

UPDATE `t_admin_info` AS a , `sys_admin_info` AS b SET a.last_doctor_name = b.`name_zh` WHERE a.`last_doctor_id` = b.`user_id`;

ALTER TABLE `cbkj_pre_diagnosis`.`sys_dic`
    CHANGE `dic_name` `dic_name` VARCHAR(64) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称',
    CHANGE `dic_code` `dic_code` VARCHAR(64) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字典代码',
    CHANGE `dic_value` `dic_value` VARCHAR(64) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字典的值';

CREATE TABLE `cbkj_pre_diagnosis`.`t_record_event`(
                                                      `rec_id` VARCHAR(32) NOT NULL,
                                                      `dic_id` VARCHAR(32),
                                                      `dic_code` VARCHAR(64),
                                                      `dic_name` VARCHAR(64),
                                                      INDEX (`rec_id`, `dic_code`)
);

CREATE TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option_event`(
                                                                    `option_id` INT(11) NOT NULL,
                                                                    `dic_id` VARCHAR(32) NOT NULL,
                                                                    `dic_code` VARCHAR(64) NOT NULL,
                                                                    `dic_name` VARCHAR(64) NOT NULL,
                                                                    INDEX (`option_id`)
);

update `cbkj_pre_diagnosis`.`sys_admin_menu` set `menu_name` = '已采集预诊' where `menu_id` = '100202';
update `cbkj_pre_diagnosis`.`sys_admin_menu` set `menu_name` = '已采集随访' where `menu_id` = '100305';



ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option_event`
    ADD COLUMN `dia_id` VARCHAR(32) NULL AFTER `dic_name`;
ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option_event`
    ADD INDEX (`dia_id`);


ALTER TABLE `cbkj_pre_diagnosis`.`statistics_erver_day_expenses`
    CHANGE `type` `type` INT(5) NULL COMMENT '1.中药处方2.中成药3.现代医学检查检查4.中医适宜技术 5西药处方';


UPDATE t_pre_diagnosis_dis_mapping AS a,`t_disease` AS b SET a.`dis_code` = b.`dis_code` WHERE a.`dis_id` = b.`dis_id`;

UPDATE t_pre_diagnosis_dis_mapping AS a SET a.`dis_code` = '-1' WHERE a.`dis_name` = '全科';


CREATE TABLE `statistics_diagnosis_dic` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计随访/量表事件',
                                            `create_time` date DEFAULT NULL COMMENT '统计日期',
                                            `app_id` varchar(32) DEFAULT NULL,
                                            `ins_id` varchar(32) DEFAULT NULL,
                                            `ins_code` varchar(32) DEFAULT NULL,
                                            `ins_name` varchar(64) DEFAULT NULL COMMENT '机构名称',
                                            `dis_id` varchar(32) DEFAULT NULL COMMENT '疾病id',
                                            `dis_code` varchar(32) DEFAULT NULL COMMENT '疾病代码',
                                            `dis_name` varchar(64) DEFAULT NULL COMMENT '疾病名称',
                                            `num` int(11) DEFAULT NULL COMMENT '今日统计次数',
                                            `dic_id` varchar(32) DEFAULT NULL,
                                            `dic_name` varchar(32) DEFAULT NULL,
                                            `dic_code` varchar(32) DEFAULT NULL,
                                            `patient_id` varchar(32) DEFAULT NULL,
                                            `insert_time` datetime DEFAULT NULL,
                                            PRIMARY KEY (`id`),
                                            UNIQUE KEY `create_time_2` (`create_time`,`app_id`,`ins_code`,`dis_code`,`dic_id`,`patient_id`),
                                            KEY `dic_code` (`dic_code`),
                                            KEY `dic_id` (`dic_id`)
) ENGINE=InnoDB AUTO_INCREMENT=85 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `statistics_erver_day_adverse` (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '不良反应人次统计',
                                                `create_time` date DEFAULT NULL COMMENT '统计日期',
                                                `app_id` varchar(32) DEFAULT NULL,
                                                `ins_id` varchar(32) DEFAULT NULL,
                                                `ins_code` varchar(32) DEFAULT NULL,
                                                `ins_name` varchar(64) DEFAULT NULL COMMENT '机构名称',
                                                `dis_id` varchar(32) DEFAULT NULL COMMENT '疾病id',
                                                `dis_name` varchar(64) DEFAULT NULL COMMENT '疾病名称',
                                                `dis_code` varchar(32) DEFAULT NULL COMMENT '疾病代码',
                                                `num` int(11) DEFAULT NULL COMMENT '不良反应人次',
                                                PRIMARY KEY (`id`),
                                                KEY `create_time` (`create_time`),
                                                KEY `dis_code` (`dis_code`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `statistics_erver_day_dis` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '监测病种对应数量',
                                            `dis_id` varchar(32) DEFAULT NULL COMMENT '疾病id',
                                            `dis_code` varchar(64) DEFAULT NULL COMMENT '疾病代码',
                                            `dis_name` varchar(256) DEFAULT NULL COMMENT '疾病名称',
                                            `follow_num` int(11) DEFAULT NULL COMMENT '随访数量',
                                            `diagnosis_num` int(11) DEFAULT NULL COMMENT '预诊数量',
                                            `records_num` int(11) DEFAULT NULL COMMENT '有效病历',
                                            `total_num` int(11) DEFAULT NULL COMMENT '总计（随访数量+预诊数量）',
                                            `app_id` varchar(32) DEFAULT NULL,
                                            `ins_id` varchar(32) DEFAULT NULL,
                                            `ins_code` varchar(32) DEFAULT NULL,
                                            `ins_name` varchar(64) DEFAULT NULL,
                                            `create_time` date NOT NULL COMMENT '统计日期',
                                            PRIMARY KEY (`id`),
                                            KEY `create_time` (`create_time`),
                                            KEY `ins_name` (`ins_name`),
                                            KEY `dis_code` (`dis_code`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `statistics_erver_day_effect` (
                                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '疗效评价',
                                               `create_time` date NOT NULL COMMENT '统计时间',
                                               `app_id` varchar(32) DEFAULT NULL,
                                               `ins_id` varchar(32) DEFAULT NULL,
                                               `ins_code` varchar(32) DEFAULT NULL,
                                               `ins_name` varchar(64) DEFAULT NULL,
                                               `dis_id` varchar(32) DEFAULT NULL,
                                               `dis_name` varchar(64) DEFAULT NULL,
                                               `dis_code` varchar(32) DEFAULT NULL,
                                               `num` int(11) DEFAULT NULL COMMENT '统计数',
                                               `type` int(11) DEFAULT NULL COMMENT '疗效评价1.有效2.显效3.痊愈4无效',
                                               `dic_code` varchar(32) DEFAULT NULL,
                                               `dic_name` varchar(64) DEFAULT NULL,
                                               `dic_id` varchar(32) DEFAULT NULL,
                                               `patient_id` varchar(32) DEFAULT NULL,
                                               PRIMARY KEY (`id`),
                                               KEY `dis_code` (`dis_code`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `statistics_erver_day_expenses` (
                                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '诊疗费用统计',
                                                 `create_time` date NOT NULL,
                                                 `app_id` varchar(32) DEFAULT NULL,
                                                 `ins_id` varchar(32) DEFAULT NULL,
                                                 `ins_code` varchar(32) DEFAULT NULL,
                                                 `ins_name` varchar(64) DEFAULT NULL,
                                                 `dis_id` varchar(32) DEFAULT NULL,
                                                 `dis_name` varchar(64) DEFAULT NULL,
                                                 `dis_code` varchar(32) DEFAULT NULL,
                                                 `num` int(11) DEFAULT NULL,
                                                 `type` int(1) DEFAULT NULL COMMENT '1.中药处方2.中成药3.现代医学检查检查4.中医适宜技术',
                                                 PRIMARY KEY (`id`),
                                                 KEY `dis_code` (`dis_code`),
                                                 KEY `create_time` (`create_time`),
                                                 KEY `ins_name` (`ins_name`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `statistics_erver_day_health` (
                                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '健康宣教',
                                               `num` int(11) DEFAULT NULL,
                                               `create_time` date DEFAULT NULL COMMENT '统计记录日期',
                                               `app_id` varchar(32) DEFAULT NULL,
                                               `ins_code` varchar(32) DEFAULT NULL,
                                               `ins_id` varchar(32) DEFAULT NULL,
                                               `ins_name` varchar(64) DEFAULT NULL,
                                               `dis_id` varchar(32) DEFAULT NULL,
                                               `dis_name` varchar(64) DEFAULT NULL,
                                               `dis_code` varchar(32) DEFAULT NULL,
                                               PRIMARY KEY (`id`),
                                               KEY `create_time` (`create_time`),
                                               KEY `dis_code` (`dis_code`),
                                               KEY `ins_name` (`ins_name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;


UPDATE `t_admin_info` AS a,`medical_records` AS b SET a.last_doctor_name = b.doctor_name WHERE a.last_records_id = b.records_id;



TRUNCATE TABLE t_business_edition;
insert into `t_business_edition` (`id`, `create_time`, `create_user_id`, `create_user_name`, `update_time`, `update_user_id`, `delete_time`, `delete_user_id`, `edition_num`, `edition_content`, `edition_time`, `is_del`) values('114','2024-09-03 16:22:26','1','admin','2024-09-03 16:22:31','1',NULL,NULL,'1.4','1、用户管理：重置密码修改；&#10;2、患者端、医生端单点登录接口密钥配置；&#10;3、患者端、医生端新增“系统建议、反馈”；&#10;4、医生端新增“系统帮助”、“版本更新”；&#10;5、随访患者管理-已过期任务增加消息重发；&#10;6、随访患者管理-增加“随访任务”跳转；','2024-09-03 16:22:45','0');
insert into `t_business_edition` (`id`, `create_time`, `create_user_id`, `create_user_name`, `update_time`, `update_user_id`, `delete_time`, `delete_user_id`, `edition_num`, `edition_content`, `edition_time`, `is_del`) values('115','2024-12-10 16:22:26','1','admin','2024-12-10 16:22:31','1',NULL,NULL,'1.5','1、预诊单增加电子病历模板配置功能；&#10;2、电子病历回传接口升级；','2024-12-10 16:22:45','0');
insert into `t_business_edition` (`id`, `create_time`, `create_user_id`, `create_user_name`, `update_time`, `update_user_id`, `delete_time`, `delete_user_id`, `edition_num`, `edition_content`, `edition_time`, `is_del`) values('116','2025-01-20 16:22:26','1','admin','2025-01-20 16:22:31','1',NULL,NULL,'1.6','1、医生端主题风格升级；&#10;2、新增系统首页工作台；&#10;3、系统权限控制升级；&#10;4、专病语料管理升级；&#10;5、报表管理升级及新增预诊单原始数据导出；&#10;6、用户访问升级：增加图形验证码，增加用户安全访问限制；','2025-01-20 16:22:45','0');


UPDATE `t_propaganda_edu` SET show_status = 1;




ALTER TABLE `cbkj_pre_diagnosis`.`t_record`
    ADD COLUMN `insert_date` DATE DEFAULT NULL ;

UPDATE `t_record` SET insert_date = create_date;


ALTER TABLE `cbkj_pre_diagnosis`.`s_road_task_patients`
    ADD COLUMN `insert_date` DATE DEFAULT NULL ;

UPDATE `s_road_task_patients` SET insert_date = task_excute_time;

