
TRUNCATE sys_admin_menu;
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100100','患者管理','/patient',NULL,'0','0',NULL,NULL,'1',NULL,NULL,'1','1','1','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100200','诊前管理',NULL,NULL,'0','0',NULL,NULL,'1',NULL,NULL,'1','2','1','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100201','预诊信息管理','/diagnosis-before',NULL,'0','100200',NULL,NULL,'1',NULL,NULL,'1','1','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100202','已采集预诊','/preDiagnosis',NULL,'0','100200',NULL,NULL,'1',NULL,NULL,'1','2','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100300','诊后管理',NULL,NULL,'0','0',NULL,NULL,'1',NULL,NULL,'1','3','1','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100301','随访路径管理','/diagnosis-after/follow-up/route',NULL,'0','100300',NULL,NULL,'1',NULL,NULL,'1','1','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100302','随访任务管理','/diagnosis-after/follow-up/task',NULL,'0','100300',NULL,NULL,'1',NULL,NULL,'1','2','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100303','电话随访患者','/patient/tel',NULL,'0','100300',NULL,NULL,'1',NULL,NULL,'1','3','1','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100304','随访患者管理','/patient/tel/today',NULL,'0','100300',NULL,NULL,'1',NULL,NULL,'1','4','1','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100305','已采集随访','/question/complete',NULL,'0','100300',NULL,NULL,'1',NULL,NULL,'1','5','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100400','执行明细查询',NULL,NULL,'0','0',NULL,NULL,'1',NULL,NULL,'1','4','1','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100401','智能随访明细','/diagnosis-after/follow-up/execute/ai',NULL,'0','100400',NULL,NULL,'1',NULL,NULL,'1','1','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100402','医生面访明细','/diagnosis-after/follow-up/execute/face',NULL,'0','100400',NULL,NULL,'1',NULL,NULL,'1','3','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100403','电话随访明细','/diagnosis-after/follow-up/execute/tel',NULL,'0','100400',NULL,NULL,'1',NULL,NULL,'1','2','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100500','资源中心',NULL,NULL,'0','0',NULL,NULL,'1',NULL,NULL,'1','5','1','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100501','预诊单管理','/question/pre-diagnosis',NULL,'0','100500',NULL,NULL,'1',NULL,NULL,'1','1','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100502','问卷/量表管理','/question/follow-up',NULL,'0','100500',NULL,NULL,'1',NULL,NULL,'1','2','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100503','健康宣教管理','/education',NULL,'0','100500',NULL,NULL,'1',NULL,NULL,'1','3','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100504','症状管理','/illness/symptom',NULL,'0','100500',NULL,NULL,'1',NULL,NULL,'1','4','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100505','题库管理','/question/default',NULL,'0','100500',NULL,NULL,'1',NULL,NULL,'1','5','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100600','工作台','/home',NULL,'0','0',NULL,NULL,'1',NULL,NULL,'1','0','1','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100700','数据质控','/home',NULL,'0','0',NULL,NULL,'1',NULL,NULL,'1','6','1','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100701','病案筛选','/data/case-filter',NULL,'0','100700',NULL,NULL,'1',NULL,NULL,'1','1','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100702','病案查询','/data/case-search',NULL,'0','100700',NULL,NULL,'1',NULL,NULL,'1','2','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('100703','病案归档','/data/case-archive',NULL,'0','100700',NULL,NULL,'1',NULL,NULL,'1','3','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('199900','系统管理',NULL,NULL,'0','0',NULL,NULL,'1',NULL,NULL,'1','999','1','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('199901','角色管理','/system/role',NULL,'0','199900',NULL,NULL,'1',NULL,NULL,'1','1','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('199902','用户管理','/system/user',NULL,'0','199900',NULL,NULL,'1',NULL,NULL,'1','2','2','1',NULL);
INSERT INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES('199903','意见处理','/system/advice',NULL,'0','199900',NULL,NULL,'1',NULL,NULL,'1','2','2','1',NULL);
