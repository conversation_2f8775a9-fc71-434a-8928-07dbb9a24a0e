INSERT INTO `cbkj_pre_diagnosis`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES ('100700', '数据质控', '/home', NULL, '0', '0', NULL, NULL, '1', NULL, NULL, '1', '6', '1', '1', NULL);

INSERT INTO `cbkj_pre_diagnosis`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES ('100701', '科室质控', '/data/department-quality-control', NULL, '0', '100700', NULL, NULL, '1', NULL, NULL, '1', '1', '2', '1', NULL);

INSERT INTO `cbkj_pre_diagnosis`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) VALUES ('100702', '病案审核归档', '/data/medical-record', NULL, '0', '100700', NULL, NULL, '1', NULL, NULL, '1', '2', '2', '1', NULL);
ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_form`
    ADD COLUMN `cue_word_status` INT(1) NULL COMMENT '大模型：0开1关' ,
    ADD COLUMN `cue_word_text` TEXT NULL COMMENT '提示词' ,
    ADD COLUMN `cue_word_trans` TEXT NULL COMMENT '转换词' ;


UPDATE `t_pre_diagnosis_form` SET cue_word_status='1',cue_word_text='请根据以上预问诊问答的内容生成专病电子病历，电子病历中的内容需全部来源于预问诊问答的内容，不能进行扩充。病历分类覆盖如下：主诉、现病史、既往史、个人史、过敏史、传染病史、手术史、输血史、家族史、月经史、体格检查，缺失的内容无需额外补充',
                                  cue_word_trans='，并以json格式返回，注意json格式内容不要有空格，不要有换行符号，questionClassName字段不能为空字符串，content字段如果为空字符串则填入无，中文全部使用简体中文字字体。示例如下:[{"content":"这里写主诉","questionClassName":"主诉"},{"content":"这里写现病史","questionClassName":"现病史"},{"content":".......","questionClassName":"既往史"}]';





CREATE TABLE `t_record_large_model` (
                                        `record_model_id` varchar(32) NOT NULL,
                                        `rec_id` varchar(32) DEFAULT NULL COMMENT '表单记录id',
                                        `dia_id` varchar(32) DEFAULT NULL COMMENT '表单id',
                                        `request_text` text COMMENT '传入大模型入上下文',
                                        `response_text` text COMMENT '大模型出参',
                                        `cue_word_text` text COMMENT '提示词',
                                        `cuw_word_trans` text COMMENT '转换词',
                                        `record_model_status` int(1) DEFAULT NULL COMMENT '0成功1失败',
                                        `record_model_fail_msg` text COMMENT '失败返回的内容',
                                        `insert_time` datetime DEFAULT NULL COMMENT '数据插入时间',
                                        `re_call_times` int(11) DEFAULT NULL,
                                        `last_re_call_time` datetime DEFAULT NULL,
                                        PRIMARY KEY (`record_model_id`),
                                        KEY `rec_id` (`rec_id`),
                                        KEY `dia_id` (`dia_id`)
);
DROP TABLE IF EXISTS `t_sys_param`;

CREATE TABLE `t_sys_param` (
                               `PAR_ID` varchar(32) NOT NULL COMMENT '参数ID',
                               `PAR_CODE` varchar(64) NOT NULL COMMENT '参数代码',
                               `PAR_NAME` varchar(64) DEFAULT NULL COMMENT '参数名称',
                               `PAR_VALUES` varchar(500) DEFAULT NULL COMMENT '参数值',
                               `CREATE_DATE` datetime NOT NULL COMMENT '创建时间',
                               `CREATE_USER` varchar(32) NOT NULL COMMENT '创建人',
                               `create_user_name` varchar(32) NOT NULL COMMENT '创建人姓名',
                               `status` varchar(1) NOT NULL DEFAULT '0' COMMENT '是否有效（1删除 0有效 2禁用） 默认0',
                               `param_desc` varchar(500) DEFAULT NULL COMMENT '参数说明 ，备注 项目名称等',
                               `sort` int(3) DEFAULT NULL COMMENT '排序',
                               PRIMARY KEY (`PAR_ID`),
                               KEY `PAR_CODE` (`PAR_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



insert  into `t_sys_param`(`PAR_ID`,`PAR_CODE`,`PAR_NAME`,`PAR_VALUES`,`CREATE_DATE`,`CREATE_USER`,`create_user_name`,`status`,`param_desc`,`sort`) values
    ('1','ai_symptom_analysis','健康趋势分析','1','2025-04-29 10:20:20','zjh','zjh','0','健康趋势分析:0开1关',1);


ALTER TABLE `cbkj_pre_diagnosis`.`s_road_task_patients`
    ADD COLUMN `delete_time` DATETIME NULL ,
    ADD COLUMN `delete_user_id` VARCHAR(32) NULL ,
    ADD COLUMN `delete_user_name` VARCHAR(64) NULL;



ALTER TABLE `cbkj_pre_diagnosis`.`sys_dic`
    ADD COLUMN `dic_shot_name` VARCHAR(32) NULL COMMENT '字典简写名称' AFTER `dic_name`;

UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_shot_name` = '随访异常' WHERE `dic_id` = 'follow-up-event-focus';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_shot_name` = '不良反应' WHERE `dic_id` = 'follow-up-event_unusual';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_shot_name` = '结案' WHERE `dic_id` = 'follow-up-event-archive-closure';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_shot_name` = '疗效:有效' WHERE `dic_id` = 'follow-up-event-effective';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_shot_name` = '疗效:无效' WHERE `dic_id` = 'follow-up-event-ineffective';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_shot_name` = '疗效:痊愈' WHERE `dic_id` = 'follow-up-event-recovery';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_shot_name` = '疗效:显效' WHERE `dic_id` = 'follow-up-event-significant';



UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_value` = '#ec808d' WHERE `dic_id` = 'follow-up-event-focus';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_value` = '#ec808d' WHERE `dic_id` = 'follow-up-event_unusual';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_value` = '#c280ff' WHERE `dic_id` = 'follow-up-event-archive-closure';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_value` = '#00aaaa' WHERE `dic_id` = 'follow-up-event-effective';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_value` = '#00aaaa' WHERE `dic_id` = 'follow-up-event-ineffective';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_value` = '#00aaaa' WHERE `dic_id` = 'follow-up-event-recovery';
UPDATE `cbkj_pre_diagnosis`.`sys_dic` SET `dic_value` = '#00aaaa' WHERE `dic_id` = 'follow-up-event-significant';



UPDATE s_road_task_patients_phone AS a , s_road_task_patients AS b SET a.`sui_fang_time` = b.task_excute_time WHERE a.task_patients_id = b.task_patients_id AND a.`sui_fang_time` IS NULL;

UPDATE `t_pre_diagnosis_option_event` SET dic_code = dic_id;

DELETE  FROM `t_pre_diagnosis_option_event` WHERE dic_id = 'follow-up-event';

DELETE FROM t_record_event WHERE dic_code = 'follow-up-event';


update `cbkj_pre_diagnosis`.`sys_dic` set `dic_code` = 'follow-up-event_unusual' where `dic_id` = 'follow-up-event_unusual';

ALTER TABLE `cbkj_pre_diagnosis`.`t_propaganda_edu`
    CHANGE `edu_cover_image` `edu_cover_image` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '封面图片url';

