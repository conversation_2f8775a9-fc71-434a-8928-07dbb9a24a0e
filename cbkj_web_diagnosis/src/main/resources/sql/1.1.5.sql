ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option`
    ADD COLUMN `option_structure_save_blank` INT(1) NULL COMMENT '单选、多选又选项填空时候：1.保留选项填空信息 2保留结构化转化术语' AFTER `option_structure_value`;


ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option`
    CHANGE `option_structure_save_blank` `option_structure_save_blank` INT(1) NULL COMMENT '0勾选了病历转化结构化术语 1未勾病历转化结构化术语',
    ADD COLUMN `option_structure_save_patient` INT(1) NULL COMMENT '0勾选了保留患者输入 1未勾保留患者输入' AFTER `option_structure_save_blank`;

ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option`
    CHANGE `option_structure_save_blank` `option_structure_save_blank` INT(1) NULL COMMENT '0未勾病历转化结构化术语 1勾了 病历转化结构化术语',
    CHANGE `option_structure_save_patient` `option_structure_save_patient` INT(1) NULL COMMENT '0未勾了保留患者输入 1勾了 保留患者输入';

ALTER TABLE `cbkj_pre_diagnosis`.`t_admin_info`
    ADD COLUMN `medical_card` VARCHAR(32) NULL COMMENT '患者就诊卡号' AFTER `health_files_num`;



CREATE TABLE `t_pre_diagnosis_structure` (
                                             `diagnosis_structure_id` varchar(32) NOT NULL COMMENT '主键',
                                             `dia_id` varchar(32) NOT NULL COMMENT '预诊表单id',
                                             `emr_type` varchar(32) NOT NULL COMMENT '主诉、现病史等',
                                             `type_code` varchar(32) NOT NULL COMMENT '对应字典值',
                                             `sort` int(2) DEFAULT NULL COMMENT '排序',
                                             PRIMARY KEY (`diagnosis_structure_id`),
                                             KEY `dia_id` (`dia_id`,`emr_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `t_pre_diagnosis_structure_child` (
                                                   `structure_child` int(11) NOT NULL AUTO_INCREMENT,
                                                   `content` text,
                                                   `content_type` varchar(32) DEFAULT NULL,
                                                   `structure_content_id` int(11) DEFAULT NULL,
                                                   `paragraph_sort` int(11) DEFAULT NULL,
                                                   `question_is_del` int(1) DEFAULT NULL COMMENT '表示这个问题的原题是否删除了 0 否 1 是',
                                                   PRIMARY KEY (`structure_child`),
                                                   KEY `structure_content_id` (`structure_content_id`)
) ENGINE=InnoDB AUTO_INCREMENT=20689 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `t_pre_diagnosis_structure_content` (
                                                     `structure_content_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'EmrContent 表：存储 EMR 内容，与 EmrItem 表相关联。',
                                                     `diagnosis_structure_id` varchar(32) NOT NULL,
                                                     PRIMARY KEY (`structure_content_id`),
                                                     KEY `diagnosis_structure_id` (`diagnosis_structure_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8916 DEFAULT CHARSET=utf8mb4;
