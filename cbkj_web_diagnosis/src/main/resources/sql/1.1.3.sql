CREATE TABLE `s_road_task_ex` (
                                  `s_road_task_id` varchar(32) NOT NULL,
                                  `dept_id` varchar(32) DEFAULT NULL COMMENT '任务关联哪些科室',
                                  `app_id` varchar(32) DEFAULT NULL,
                                  `dept_code` varchar(32) DEFAULT NULL,
                                  `ins_code` varchar(32) DEFAULT NULL,
                                  `ins_id` varchar(32) DEFAULT NULL,
                                  `record_doctor_id` varchar(32) DEFAULT NULL COMMENT '就诊医生id',
                                  `diagnosis_doctor_id` varchar(32) DEFAULT NULL COMMENT '随访成员医生id',
                                  `ex_type` tinyint(4) DEFAULT NULL COMMENT '0科室1就诊医生2随访成员',
                                  `dept_name` varchar(64) DEFAULT NULL,
                                  `record_doctor_name` varchar(64) DEFAULT NULL,
                                  `diagnosis_doctor_name` varchar(64) DEFAULT NULL,
                                  KEY `s_road_task_id` (`s_road_task_id`),
                                  KEY `dept_id` (`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


ALTER TABLE `cbkj_pre_diagnosis`.`s_road_task`
    ADD COLUMN `record_start_time` DATETIME NULL COMMENT '自动入组条件：就诊开始时间' AFTER `s_road_group_way`,
    ADD COLUMN `record_end_time` DATETIME NULL COMMENT '自动入组条件：就诊结束时间' AFTER `record_start_time`,
    ADD COLUMN `limit_diagnosis_days_info` INT NULL COMMENT '自动入组条件：就诊当日几天内有预诊信息的患者' AFTER `record_end_time`,
    ADD COLUMN `limit_repeat_record` INT NULL COMMENT '自动入组条件：几日内重复就诊的患者不入组' AFTER `limit_diagnosis_days_info`,
    ADD COLUMN `join_road_task` INT NULL COMMENT '是否过滤不愿意随访的患者0.是 1.否' AFTER `limit_repeat_record`;



ALTER TABLE `cbkj_pre_diagnosis`.`medical_records`
    ADD INDEX (`record_time`);

ALTER TABLE `cbkj_pre_diagnosis`.`t_record`
    ADD INDEX (`create_date`);

ALTER TABLE `cbkj_pre_diagnosis`.`t_record`
    ADD INDEX (`patient_id`);



ALTER TABLE `cbkj_pre_diagnosis`.`s_road_task`
    ADD COLUMN `limit_diagnosis_days_info_check` INT(1) NULL COMMENT '0不勾1勾' AFTER `join_road_task`,
    ADD COLUMN `limit_repeat_record_check` INT(1) NULL COMMENT '0不勾1勾' AFTER `limit_diagnosis_days_info_check`;







ALTER TABLE `cbkj_pre_diagnosis`.`sys_dic`
    ADD COLUMN `parent_id` VARCHAR(32) NULL COMMENT '父id' AFTER `status`;
ALTER TABLE `cbkj_pre_diagnosis`.`sys_dic`
    CHANGE `dic_code` `dic_code` VARCHAR(256) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字典代码',
    CHANGE `dic_value` `dic_value` VARCHAR(246) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字典的值',
    CHANGE `status` `status` INT(1) DEFAULT 1 NOT NULL COMMENT '1有效0无效',
    CHANGE `parent_id` `parent_id` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父id';


CREATE TABLE `cbkj_pre_diagnosis`.`sys_admin_info_dis_mapping`(
                                                                  `user_id` VARCHAR(32) NOT NULL,
                                                                  `dis_id` VARCHAR(32) NOT NULL,
                                                                  `dis_name` VARCHAR(256) NOT NULL
);
ALTER TABLE `cbkj_pre_diagnosis`.`sys_admin_info_dis_mapping`
    ADD INDEX (`user_id`, `dis_id`);

ALTER TABLE `cbkj_pre_diagnosis`.`sys_admin_info_dis_mapping`
    ADD COLUMN `sort` INT(10) NOT NULL AUTO_INCREMENT AFTER `dis_name`,
    ADD KEY(`sort`);


INSERT INTO `t_pre_diagnosis_dis_mapping`
(
    dia_id,dis_id,dis_name,dis_type,dis_code
)

SELECT dia_id ,'-1','全科','1','-1' FROM `t_pre_diagnosis_form` WHERE dia_type = '2';


UPDATE`t_record` SET patient_sex='M' WHERE patient_sex='男';
UPDATE`t_record` SET patient_sex='F' WHERE patient_sex='女';















ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    CHANGE `question_class_type` `question_class_type` VARCHAR(2) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '1主诉2现病史3.既往史4.体格检查5.辅助检查5.其它.具体根据字段数据来';

insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('1','预诊单管理-题干配置-类别','dzblfl','-1','0','1','-1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('10','家族史','12',NULL,'9','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('11','体格检查','4',NULL,'10','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('12','中医四诊','13',NULL,'11','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('13','辅助检查','5',NULL,'12','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('14','其他','6',NULL,'13','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('2','主诉','1',NULL,'1','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('3','现病史','2',NULL,'2','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('4','既往史','3',NULL,'3','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('5','个人史','7',NULL,'4','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('6','过敏史','8',NULL,'5','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('7','传染病史','9',NULL,'6','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('8','手术史','10',NULL,'7','1','1');
insert into `sys_dic` (`dic_id`, `dic_name`, `dic_code`, `dic_value`, `dic_sort`, `status`, `parent_id`) values('9','输血史','11',NULL,'8','1','1');



ALTER TABLE `cbkj_pre_diagnosis`.`t_admin_info`
    ADD COLUMN `nationality` VARCHAR(8) NULL COMMENT '国籍代码' AFTER `contacts_relationship`;




