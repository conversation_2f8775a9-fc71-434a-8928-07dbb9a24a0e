ALTER TABLE `cbkj_pre_diagnosis`.`statistics_erver_day_follow`
    ADD COLUMN `app_id` VARCHAR(32) NULL AFTER `doctor_id`,
    ADD COLUMN `ins_name` VARCHAR(64) NULL AFTER `app_id`;


ALTER TABLE medical_records DROP COLUMN chinese_base_tongue_info;
ALTER TABLE medical_records DROP COLUMN chinese_base_pulse;
ALTER TABLE medical_records DROP COLUMN yi_shi_sign;


ALTER TABLE `medical_records`
    CHANGE `contacts_and_patient_relationship` `contacts_relationship` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '联系人与患者的关系代码';

ALTER TABLE `medical_west_prescriptions`
    CHANGE `prescriptions_effective_days` `prescriptions_effective_days` VARCHAR(32) NULL COMMENT '处方有效天数';


ALTER TABLE `cbkj_pre_diagnosis`.`medical_records_prescriptions_item`
    CHANGE `item_id` `item_id` BIGINT(20) NOT NULL AUTO_INCREMENT;

