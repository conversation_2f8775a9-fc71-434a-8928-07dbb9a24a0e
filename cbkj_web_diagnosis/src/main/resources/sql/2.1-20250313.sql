CREATE TABLE `quality_control_log` (
                                       `id` varchar(32) NOT NULL,
                                       `app_id` varchar(32) DEFAULT NULL,
                                       `ins_code` varchar(32) DEFAULT NULL,
                                       `record_id` varchar(32) DEFAULT NULL,
                                       `id_card` varchar(32) DEFAULT NULL COMMENT '证件号',
                                       `dataset_code` varchar(32) DEFAULT NULL COMMENT '数据集代码',
                                       `dataset_name` varchar(32) DEFAULT NULL COMMENT '数据集名称',
                                       `rules_type` varchar(2) DEFAULT NULL COMMENT '01:完整性质控-非空检验;11:准确性质控-字典值域检验',
                                       `rules_desc` varchar(200) DEFAULT NULL COMMENT '质控说明',
                                       `key_field` varchar(100) DEFAULT NULL COMMENT '主键字段',
                                       `key_value` varchar(200) DEFAULT NULL COMMENT '主键值',
                                       `problem_field` varchar(200) DEFAULT NULL COMMENT '问题字段',
                                       `insert_time` datetime DEFAULT NULL,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `medical_meta` (
                                `TYPE_CODE` VARCHAR(32) NOT NULL COMMENT '类型编码',
                                `META_CODE` VARCHAR(32) NOT NULL COMMENT '元数据编码',
                                `TYPE_NAME` VARCHAR(32) DEFAULT NULL COMMENT '类别名称',
                                `META_NAME` VARCHAR(32) DEFAULT NULL COMMENT '元数据名称',
                                `TAG_TYPE` VARCHAR(32) DEFAULT NULL COMMENT '标签类型',
                                `SORT` INT(11) DEFAULT '0',
                                PRIMARY KEY (`TYPE_CODE`,`META_CODE`) USING BTREE
) ENGINE=INNODB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

insert  into `medical_meta`(`TYPE_CODE`,`META_CODE`,`TYPE_NAME`,`META_NAME`,`TAG_TYPE`,`SORT`) values ('administrationRoute','1','用药途径','口服',NULL,0),('administrationRoute','2','用药途径','直肠用药',NULL,0),('administrationRoute','3','用药途径','舌下用药',NULL,0),('administrationRoute','4','用药途径','注射用药',NULL,0),('administrationRoute','401','用药途径','皮下注射',NULL,0),('administrationRoute','402','用药途径','皮内注射',NULL,0),('administrationRoute','403','用药途径','肌肉注射',NULL,0),('administrationRoute','404','用药途径','静脉注射或静脉滴注',NULL,0),('administrationRoute','405','用药途径','鞘内注射',NULL,0),('administrationRoute','5','用药途径','吸入用药',NULL,0),('administrationRoute','6','用药途径','局部用药',NULL,0),('administrationRoute','601','用药途径','椎管内用药',NULL,0),('administrationRoute','602','用药途径','关节腔内用药',NULL,0),('administrationRoute','603','用药途径','胸膜腔用药',NULL,0),('administrationRoute','604','用药途径','腹腔用药',NULL,0),('administrationRoute','605','用药途径','阴道用药',NULL,0),('administrationRoute','606','用药途径','气管内用药',NULL,0),('administrationRoute','607','用药途径','滴眼',NULL,0),('administrationRoute','608','用药途径','滴鼻',NULL,0),('administrationRoute','609','用药途径','喷喉',NULL,0),('administrationRoute','610','用药途径','含化',NULL,0),('administrationRoute','611','用药途径','敷伤口',NULL,0),('administrationRoute','612','用药途径','擦皮肤',NULL,0),('administrationRoute','699','用药途径','其他局部用药途径',NULL,0),('administrationRoute','7','用药途径','经皮用药',NULL,0),('administrationRoute','9','用药途径','其他用药途径',NULL,0),('cardType','01','证件类别','居民身份证',NULL,0),('cardType','02','证件类别','居民户口簿',NULL,0),('cardType','03','证件类别','护照',NULL,0),('cardType','04','证件类别','军官证',NULL,0),('cardType','05','证件类别','驾驶证',NULL,0),('cardType','06','证件类别','港澳居民来往内地通行证',NULL,0),('cardType','07','证件类别','台湾居民来往内地通行证',NULL,0),('cardType','08','证件类别','港澳台居民居住证',NULL,0),('cardType','99','证件类别','其他法定有效证件',NULL,0),('contactsRelationship','1','家庭关系','配偶',NULL,0),('contactsRelationship','2','家庭关系','子',NULL,0),('contactsRelationship','3','家庭关系','女',NULL,0),('contactsRelationship','4','家庭关系','孙子、孙女或外孙子、外孙女',NULL,0),('contactsRelationship','5','家庭关系','父母',NULL,0),('contactsRelationship','6','家庭关系','祖父母或外祖父母',NULL,0),('contactsRelationship','7','家庭关系','兄、弟、姐、妹',NULL,0),('contactsRelationship','8','家庭关系','其他',NULL,0),('drugDecoctingMethod','1','煎煮方法','先煎',NULL,0),('drugDecoctingMethod','2','煎煮方法','后下',NULL,0),('drugDecoctingMethod','3','煎煮方法','包煎',NULL,0),('drugDecoctingMethod','4','煎煮方法','另煎',NULL,0),('drugDecoctingMethod','5','煎煮方法','烊化',NULL,0),('drugDecoctingMethod','6','煎煮方法','水煎',NULL,0),('drugDecoctingMethod','7','煎煮方法','酒煎',NULL,0),('drugDecoctingMethod','8','煎煮方法','煎汤代水',NULL,0),('drugDecoctingMethod','9','煎煮方法','其他煎煮方式',NULL,0),('drugFrequency','01','药物使用频次','bid',NULL,0),('drugFrequency','02','药物使用频次','biw',NULL,0),('drugFrequency','03','药物使用频次','Hs',NULL,0),('drugFrequency','04','药物使用频次','q12h',NULL,0),('drugFrequency','05','药物使用频次','q1h',NULL,0),('drugFrequency','06','药物使用频次','q3h',NULL,0),('drugFrequency','07','药物使用频次','q6h',NULL,0),('drugFrequency','08','药物使用频次','q8h',NULL,0),('drugFrequency','09','药物使用频次','qd',NULL,0),('drugFrequency','10','药物使用频次','qid',NULL,0),('drugFrequency','11','药物使用频次','qod',NULL,0),('drugFrequency','12','药物使用频次','qw',NULL,0),('drugFrequency','13','药物使用频次','st',NULL,0),('drugFrequency','99','药物使用频次','其他',NULL,0),('educationCode','10','学历','研究生教育',NULL,0),('educationCode','11','学历','博士研究生毕业',NULL,0),('educationCode','12','学历','博士研究生结业',NULL,0),('educationCode','13','学历','博士研究生肄业',NULL,0),('educationCode','14','学历','硕士研究生毕业',NULL,0),('educationCode','15','学历','硕士研究生结业',NULL,0),('educationCode','16','学历','硕士研究生肄业',NULL,0),('educationCode','17','学历','研究生班毕业',NULL,0),('educationCode','18','学历','研究生班结业',NULL,0),('educationCode','19','学历','研究生班肄业',NULL,0),('educationCode','20','学历','大学本科教育',NULL,0),('educationCode','21','学历','大学本科毕业',NULL,0),('educationCode','22','学历','大学本科结业',NULL,0),('educationCode','23','学历','大学本科肄业',NULL,0),('educationCode','28','学历','大学普通班毕业',NULL,0),('educationCode','30','学历','大学专科教育',NULL,0),('educationCode','31','学历','大学专科毕业',NULL,0),('educationCode','32','学历','大学专科结业',NULL,0),('educationCode','33','学历','大学专科肄业',NULL,0),('educationCode','40','学历','中等职业教育',NULL,0),('educationCode','41','学历','中等专科毕业',NULL,0),('educationCode','42','学历','中等专科结业',NULL,0),('educationCode','43','学历','中等专科肄业',NULL,0),('educationCode','44','学历','职业高中毕业',NULL,0),('educationCode','45','学历','职业高中结业',NULL,0),('educationCode','46','学历','职业高中肄业',NULL,0),('educationCode','47','学历','技工学校毕业',NULL,0),('educationCode','48','学历','技工学校结业',NULL,0),('educationCode','49','学历','技工学校肄业',NULL,0),('educationCode','60','学历','普通高级中学教育',NULL,0),('educationCode','61','学历','普通高中毕业',NULL,0),('educationCode','62','学历','普通高中结业',NULL,0),('educationCode','63','学历','普通高中肄业',NULL,0),('educationCode','70','学历','初级中学教育',NULL,0),('educationCode','71','学历','初中毕业',NULL,0),('educationCode','73','学历','初中肄业',NULL,0),('educationCode','80','学历','小学教育',NULL,0),('educationCode','81','学历','小学毕业',NULL,0),('educationCode','83','学历','小学肄业',NULL,0),('educationCode','90','学历','其他',NULL,0),('etiologyClassification','1','中医病因分类','内因',NULL,0),('etiologyClassification','11','中医病因分类','七情所伤',NULL,0),('etiologyClassification','111','中医病因分类','惊恐伤肾',NULL,0),('etiologyClassification','112','中医病因分类','悲忧伤肺',NULL,0),('etiologyClassification','113','中医病因分类','思虑伤脾',NULL,0),('etiologyClassification','114','中医病因分类','暴喜伤心',NULL,0),('etiologyClassification','115','中医病因分类','大怒伤肝',NULL,0),('etiologyClassification','116','中医病因分类','喜怒伤气',NULL,0),('etiologyClassification','12','中医病因分类','饮食所伤',NULL,0),('etiologyClassification','121','中医病因分类','过饥',NULL,0),('etiologyClassification','122','中医病因分类','过饱',NULL,0),('etiologyClassification','123','中医病因分类','饮食不洁',NULL,0),('etiologyClassification','124','中医病因分类','寒热偏嗜',NULL,0),('etiologyClassification','125','中医病因分类','五味偏嗜',NULL,0),('etiologyClassification','126','中医病因分类','食类偏嗜',NULL,0),('etiologyClassification','13','中医病因分类','劳倦',NULL,0),('etiologyClassification','131','中医病因分类','过用',NULL,0),('etiologyClassification','132','中医病因分类','房劳',NULL,0),('etiologyClassification','2','中医病因分类','外因',NULL,0),('etiologyClassification','21','中医病因分类','六淫',NULL,0),('etiologyClassification','211','中医病因分类','风邪',NULL,0),('etiologyClassification','212','中医病因分类','寒邪',NULL,0),('etiologyClassification','213','中医病因分类','湿邪',NULL,0),('etiologyClassification','214','中医病因分类','燥邪',NULL,0),('etiologyClassification','215','中医病因分类','火邪',NULL,0),('etiologyClassification','216','中医病因分类','暑邪',NULL,0),('etiologyClassification','22','中医病因分类','疠气',NULL,0),('etiologyClassification','221','中医病因分类','瘟毒',NULL,0),('etiologyClassification','222','中医病因分类','疟邪',NULL,0),('etiologyClassification','223','中医病因分类','麻毒',NULL,0),('etiologyClassification','224','中医病因分类','瘴气',NULL,0),('etiologyClassification','23','中医病因分类','病理产物',NULL,0),('etiologyClassification','231','中医病因分类','痰饮',NULL,0),('etiologyClassification','232','中医病因分类','瘀血',NULL,0),('etiologyClassification','233','中医病因分类','结石',NULL,0),('etiologyClassification','24','中医病因分类','外伤',NULL,0),('etiologyClassification','241','中医病因分类','外力损伤',NULL,0),('etiologyClassification','242','中医病因分类','烧烫伤',NULL,0),('etiologyClassification','243','中医病因分类','冻伤',NULL,0),('etiologyClassification','244','中医病因分类','虫兽伤',NULL,0),('etiologyClassification','25','中医病因分类','药邪',NULL,0),('etiologyClassification','26','中医病因分类','医过',NULL,0),('etiologyClassification','27','中医病因分类','先天因素',NULL,0),('etiologyClassification','271','中医病因分类','胎弱',NULL,0),('etiologyClassification','272','中医病因分类','胎毒',NULL,0),('etiologyClassification','9','中医病因分类','其他中医病因',NULL,0),('insuranceCategoryCode','01','医疗保险类别','城镇职工基本医疗保险',NULL,0),('insuranceCategoryCode','02','医疗保险类别','城镇居民基本医疗保险',NULL,0),('insuranceCategoryCode','03','医疗保险类别','新型农村合作医疗',NULL,0),('insuranceCategoryCode','04','医疗保险类别','公务员医疗补助',NULL,0),('insuranceCategoryCode','05','医疗保险类别','企业补充医疗保险',NULL,0),('insuranceCategoryCode','06','医疗保险类别','大额补充医疗保险',NULL,0),('insuranceCategoryCode','07','医疗保险类别','商业医疗保险',NULL,0),('insuranceCategoryCode','99','医疗保险类别','其他',NULL,0),('maritalStatus','10','婚姻状况','未婚',NULL,0),('maritalStatus','20','婚姻状况','已婚',NULL,0),('maritalStatus','21','婚姻状况','初婚',NULL,0),('maritalStatus','22','婚姻状况','再婚',NULL,0),('maritalStatus','23','婚姻状况','复婚',NULL,0),('maritalStatus','30','婚姻状况','丧偶',NULL,0),('maritalStatus','40','婚姻状况','离婚',NULL,0),('maritalStatus','90','婚姻状况','未说明的婚姻状况',NULL,0),('medicalExpensesSettledCode','01','医疗费用结算','现金',NULL,0),('medicalExpensesSettledCode','02','医疗费用结算','支票',NULL,0),('medicalExpensesSettledCode','03','医疗费用结算','汇款存款',NULL,0),('medicalExpensesSettledCode','04','医疗费用结算','内部转账',NULL,0),('medicalExpensesSettledCode','05','医疗费用结算','单位记账',NULL,0),('medicalExpensesSettledCode','06','医疗费用结算','账户金',NULL,0),('medicalExpensesSettledCode','07','医疗费用结算','统筹金',NULL,0),('medicalExpensesSettledCode','08','医疗费用结算','银行卡',NULL,0),('medicalExpensesSettledCode','09','医疗费用结算','移动支付',NULL,0),('medicalExpensesSettledCode','10','医疗费用结算','数字化人民币',NULL,0),('medicalExpensesSettledCode','11','医疗费用结算','绿色通道',NULL,0),('medicalExpensesSettledCode','99','医疗费用结算','其他',NULL,0),('nation','01','民族名称','汉族',NULL,0),('nation','02','民族名称','蒙古族',NULL,0),('nation','03','民族名称','回族',NULL,0),('nation','04','民族名称','藏族',NULL,0),('nation','05','民族名称','维吾尔族',NULL,0),('nation','06','民族名称','苗族',NULL,0),('nation','07','民族名称','彝族',NULL,0),('nation','08','民族名称','壮族',NULL,0),('nation','09','民族名称','布依族',NULL,0),('nation','10','民族名称','朝鲜族',NULL,0),('nation','11','民族名称','满族',NULL,0),('nation','12','民族名称','侗族',NULL,0),('nation','13','民族名称','瑶族',NULL,0),('nation','14','民族名称','白族',NULL,0),('nation','15','民族名称','土家族',NULL,0),('nation','16','民族名称','哈尼族',NULL,0),('nation','17','民族名称','哈萨克族',NULL,0),('nation','18','民族名称','傣族',NULL,0),('nation','19','民族名称','黎族',NULL,0),('nation','20','民族名称','傈僳族',NULL,0),('nation','21','民族名称','佤族',NULL,0),('nation','22','民族名称','畲族',NULL,0),('nation','23','民族名称','高山族',NULL,0),('nation','24','民族名称','拉祜族',NULL,0),('nation','25','民族名称','水族',NULL,0),('nation','26','民族名称','东乡族',NULL,0),('nation','27','民族名称','纳西族',NULL,0),('nation','28','民族名称','景颇族',NULL,0),('nation','29','民族名称','柯尔克孜族',NULL,0),('nation','30','民族名称','土族',NULL,0),('nation','31','民族名称','达斡尔族',NULL,0),('nation','32','民族名称','仫佬族',NULL,0),('nation','33','民族名称','羌族',NULL,0),('nation','34','民族名称','布朗族',NULL,0),('nation','35','民族名称','撒拉族',NULL,0),('nation','36','民族名称','毛难族',NULL,0),('nation','37','民族名称','仡佬族',NULL,0),('nation','38','民族名称','锡伯族',NULL,0),('nation','39','民族名称','阿昌族',NULL,0),('nation','40','民族名称','普米族',NULL,0),('nation','41','民族名称','塔吉克族',NULL,0),('nation','42','民族名称','怒族',NULL,0),('nation','43','民族名称','乌孜别克族',NULL,0),('nation','44','民族名称','俄罗斯族',NULL,0),('nation','45','民族名称','鄂温克族',NULL,0),('nation','46','民族名称','德昂族',NULL,0),('nation','47','民族名称','保安族',NULL,0),('nation','48','民族名称','裕固族',NULL,0),('nation','49','民族名称','京族',NULL,0),('nation','50','民族名称','塔塔尔族',NULL,0),('nation','51','民族名称','独龙族',NULL,0),('nation','52','民族名称','鄂伦春族',NULL,0),('nation','53','民族名称','赫哲族',NULL,0),('nation','54','民族名称','门巴族',NULL,0),('nation','55','民族名称','珞巴族',NULL,0),('nation','56','民族名称','基诺族',NULL,0),('nationality','004','世界各国和地区名称','阿富汗',NULL,0),('nationality','008','世界各国和地区名称','阿尔巴尼亚',NULL,0),('nationality','010','世界各国和地区名称','南极洲',NULL,0),('nationality','012','世界各国和地区名称','阿尔及利亚',NULL,0),('nationality','016','世界各国和地区名称','美属萨摩亚',NULL,0),('nationality','020','世界各国和地区名称','安道尔',NULL,0),('nationality','024','世界各国和地区名称','安哥拉',NULL,0),('nationality','028','世界各国和地区名称','安提瓜和巴布达',NULL,0),('nationality','031','世界各国和地区名称','阿塞拜疆',NULL,0),('nationality','032','世界各国和地区名称','阿根廷',NULL,0),('nationality','036','世界各国和地区名称','澳大利亚',NULL,0),('nationality','040','世界各国和地区名称','奥地利',NULL,0),('nationality','044','世界各国和地区名称','巴哈马',NULL,0),('nationality','048','世界各国和地区名称','巴林',NULL,0),('nationality','050','世界各国和地区名称','孟加拉国',NULL,0),('nationality','051','世界各国和地区名称','亚美尼亚',NULL,0),('nationality','052','世界各国和地区名称','巴巴多斯',NULL,0),('nationality','056','世界各国和地区名称','比利时',NULL,0),('nationality','060','世界各国和地区名称','百慕大',NULL,0),('nationality','064','世界各国和地区名称','不丹',NULL,0),('nationality','068','世界各国和地区名称','玻利维亚',NULL,0),('nationality','070','世界各国和地区名称','波黑',NULL,0),('nationality','072','世界各国和地区名称','博茨瓦纳',NULL,0),('nationality','074','世界各国和地区名称','布维岛',NULL,0),('nationality','076','世界各国和地区名称','巴西',NULL,0),('nationality','084','世界各国和地区名称','伯利兹',NULL,0),('nationality','086','世界各国和地区名称','英属印度洋领地',NULL,0),('nationality','090','世界各国和地区名称','所罗门群岛',NULL,0),('nationality','092','世界各国和地区名称','英属维尔京群岛',NULL,0),('nationality','096','世界各国和地区名称','文莱',NULL,0),('nationality','100','世界各国和地区名称','保加利亚',NULL,0),('nationality','104','世界各国和地区名称','缅甸',NULL,0),('nationality','108','世界各国和地区名称','布隆迪',NULL,0),('nationality','112','世界各国和地区名称','白俄罗斯',NULL,0),('nationality','116','世界各国和地区名称','柬埔寨',NULL,0),('nationality','120','世界各国和地区名称','喀麦隆',NULL,0),('nationality','124','世界各国和地区名称','加拿大',NULL,0),('nationality','132','世界各国和地区名称','佛得角',NULL,0),('nationality','136','世界各国和地区名称','开曼群岛',NULL,0),('nationality','140','世界各国和地区名称','中非',NULL,0),('nationality','144','世界各国和地区名称','斯里兰卡',NULL,0),('nationality','148','世界各国和地区名称','乍得',NULL,0),('nationality','152','世界各国和地区名称','智利',NULL,0),('nationality','156','世界各国和地区名称','中国',NULL,0),('nationality','158','世界各国和地区名称','台湾',NULL,0),('nationality','162','世界各国和地区名称','圣诞岛',NULL,0),('nationality','166','世界各国和地区名称','科科斯（基林）群岛',NULL,0),('nationality','170','世界各国和地区名称','哥伦比亚',NULL,0),('nationality','174','世界各国和地区名称','科摩罗',NULL,0),('nationality','175','世界各国和地区名称','马约特',NULL,0),('nationality','178','世界各国和地区名称','刚果（布）',NULL,0),('nationality','180','世界各国和地区名称','刚果（金）',NULL,0),('nationality','184','世界各国和地区名称','库克群岛',NULL,0),('nationality','188','世界各国和地区名称','哥斯达黎加',NULL,0),('nationality','191','世界各国和地区名称','克罗地亚',NULL,0),('nationality','192','世界各国和地区名称','古巴',NULL,0),('nationality','196','世界各国和地区名称','塞浦路斯',NULL,0),('nationality','203','世界各国和地区名称','捷克',NULL,0),('nationality','204','世界各国和地区名称','贝宁',NULL,0),('nationality','208','世界各国和地区名称','丹麦',NULL,0),('nationality','212','世界各国和地区名称','多米尼克',NULL,0),('nationality','214','世界各国和地区名称','多米尼加',NULL,0),('nationality','218','世界各国和地区名称','厄瓜多尔',NULL,0),('nationality','222','世界各国和地区名称','萨尔瓦多',NULL,0),('nationality','226','世界各国和地区名称','赤道几内亚',NULL,0),('nationality','231','世界各国和地区名称','埃塞俄比亚',NULL,0),('nationality','232','世界各国和地区名称','厄立特里亚',NULL,0),('nationality','233','世界各国和地区名称','爱沙尼亚',NULL,0),('nationality','234','世界各国和地区名称','法罗群岛',NULL,0),('nationality','238','世界各国和地区名称','福克兰群岛（马尔维纳斯）',NULL,0),('nationality','239','世界各国和地区名称','南乔治亚岛和南桑德韦奇岛',NULL,0),('nationality','242','世界各国和地区名称','斐济',NULL,0),('nationality','246','世界各国和地区名称','芬兰',NULL,0),('nationality','250','世界各国和地区名称','法国',NULL,0),('nationality','254','世界各国和地区名称','法属圭亚那',NULL,0),('nationality','258','世界各国和地区名称','法属波利尼西亚',NULL,0),('nationality','260','世界各国和地区名称','法属南部领地',NULL,0),('nationality','262','世界各国和地区名称','吉布提',NULL,0),('nationality','266','世界各国和地区名称','加蓬',NULL,0),('nationality','268','世界各国和地区名称','格鲁吉亚',NULL,0),('nationality','270','世界各国和地区名称','冈比亚',NULL,0),('nationality','275','世界各国和地区名称','巴勒斯坦',NULL,0),('nationality','276','世界各国和地区名称','德国',NULL,0),('nationality','288','世界各国和地区名称','加纳',NULL,0),('nationality','292','世界各国和地区名称','直布罗陀',NULL,0),('nationality','296','世界各国和地区名称','基里巴斯',NULL,0),('nationality','300','世界各国和地区名称','希腊',NULL,0),('nationality','304','世界各国和地区名称','格陵兰',NULL,0),('nationality','308','世界各国和地区名称','格林纳达',NULL,0),('nationality','312','世界各国和地区名称','瓜德罗普',NULL,0),('nationality','316','世界各国和地区名称','关岛',NULL,0),('nationality','320','世界各国和地区名称','危地马拉',NULL,0),('nationality','324','世界各国和地区名称','几内亚',NULL,0),('nationality','328','世界各国和地区名称','圭亚那',NULL,0),('nationality','332','世界各国和地区名称','海地',NULL,0),('nationality','334','世界各国和地区名称','赫德岛和麦克唐纳岛',NULL,0),('nationality','336','世界各国和地区名称','梵蒂冈',NULL,0),('nationality','340','世界各国和地区名称','洪都拉斯',NULL,0),('nationality','344','世界各国和地区名称','香港',NULL,0),('nationality','348','世界各国和地区名称','匈牙利',NULL,0),('nationality','352','世界各国和地区名称','冰岛',NULL,0),('nationality','356','世界各国和地区名称','印度',NULL,0),('nationality','360','世界各国和地区名称','印度尼西亚',NULL,0),('nationality','364','世界各国和地区名称','伊朗',NULL,0),('nationality','368','世界各国和地区名称','伊拉克',NULL,0),('nationality','372','世界各国和地区名称','爱尔兰',NULL,0),('nationality','376','世界各国和地区名称','以色列',NULL,0),('nationality','380','世界各国和地区名称','意大利',NULL,0),('nationality','384','世界各国和地区名称','科特迪瓦',NULL,0),('nationality','388','世界各国和地区名称','牙买加',NULL,0),('nationality','392','世界各国和地区名称','日本',NULL,0),('nationality','398','世界各国和地区名称','哈萨克斯坦',NULL,0),('nationality','400','世界各国和地区名称','约旦',NULL,0),('nationality','404','世界各国和地区名称','肯尼亚',NULL,0),('nationality','408','世界各国和地区名称','朝鲜',NULL,0),('nationality','410','世界各国和地区名称','韩国',NULL,0),('nationality','414','世界各国和地区名称','科威特',NULL,0),('nationality','417','世界各国和地区名称','吉尔吉斯斯坦',NULL,0),('nationality','418','世界各国和地区名称','老挝',NULL,0),('nationality','422','世界各国和地区名称','黎巴嫩',NULL,0),('nationality','426','世界各国和地区名称','莱索托',NULL,0),('nationality','428','世界各国和地区名称','拉脱维亚',NULL,0),('nationality','430','世界各国和地区名称','利比里亚',NULL,0),('nationality','434','世界各国和地区名称','利比亚',NULL,0),('nationality','438','世界各国和地区名称','列支敦士登',NULL,0),('nationality','440','世界各国和地区名称','立陶宛',NULL,0),('nationality','442','世界各国和地区名称','卢森堡',NULL,0),('nationality','446','世界各国和地区名称','澳门',NULL,0),('nationality','450','世界各国和地区名称','马达加斯加',NULL,0),('nationality','454','世界各国和地区名称','马拉维',NULL,0),('nationality','458','世界各国和地区名称','马来西亚',NULL,0),('nationality','462','世界各国和地区名称','马尔代夫',NULL,0),('nationality','466','世界各国和地区名称','马里',NULL,0),('nationality','470','世界各国和地区名称','马耳他',NULL,0),('nationality','474','世界各国和地区名称','马提尼克',NULL,0),('nationality','478','世界各国和地区名称','毛里塔尼亚',NULL,0),('nationality','480','世界各国和地区名称','毛里求斯',NULL,0),('nationality','484','世界各国和地区名称','墨西哥',NULL,0),('nationality','492','世界各国和地区名称','摩纳哥',NULL,0),('nationality','496','世界各国和地区名称','蒙古',NULL,0),('nationality','498','世界各国和地区名称','摩尔多瓦',NULL,0),('nationality','500','世界各国和地区名称','蒙特塞拉特',NULL,0),('nationality','504','世界各国和地区名称','摩洛哥',NULL,0),('nationality','508','世界各国和地区名称','莫桑比克',NULL,0),('nationality','512','世界各国和地区名称','阿曼',NULL,0),('nationality','516','世界各国和地区名称','纳米比亚',NULL,0),('nationality','520','世界各国和地区名称','瑙鲁',NULL,0),('nationality','524','世界各国和地区名称','尼泊尔',NULL,0),('nationality','528','世界各国和地区名称','荷兰',NULL,0),('nationality','530','世界各国和地区名称','荷属安的列斯',NULL,0),('nationality','533','世界各国和地区名称','阿鲁巴',NULL,0),('nationality','540','世界各国和地区名称','新喀里多尼亚',NULL,0),('nationality','548','世界各国和地区名称','瓦努阿图',NULL,0),('nationality','554','世界各国和地区名称','新西兰',NULL,0),('nationality','558','世界各国和地区名称','尼加拉瓜',NULL,0),('nationality','562','世界各国和地区名称','尼日尔',NULL,0),('nationality','566','世界各国和地区名称','尼日利亚',NULL,0),('nationality','570','世界各国和地区名称','纽埃',NULL,0),('nationality','574','世界各国和地区名称','诺福克岛',NULL,0),('nationality','578','世界各国和地区名称','挪威',NULL,0),('nationality','580','世界各国和地区名称','北马里亚纳',NULL,0),('nationality','581','世界各国和地区名称','美国本土外小岛屿',NULL,0),('nationality','583','世界各国和地区名称','密克罗尼西亚联邦',NULL,0),('nationality','584','世界各国和地区名称','马绍尔群岛',NULL,0),('nationality','585','世界各国和地区名称','帕劳',NULL,0),('nationality','586','世界各国和地区名称','巴基斯坦',NULL,0),('nationality','591','世界各国和地区名称','巴拿马',NULL,0),('nationality','598','世界各国和地区名称','巴布亚新几内亚',NULL,0),('nationality','600','世界各国和地区名称','巴拉圭',NULL,0),('nationality','604','世界各国和地区名称','秘鲁',NULL,0),('nationality','608','世界各国和地区名称','菲律宾',NULL,0),('nationality','612','世界各国和地区名称','皮特凯恩',NULL,0),('nationality','616','世界各国和地区名称','波兰',NULL,0),('nationality','620','世界各国和地区名称','葡萄牙',NULL,0),('nationality','624','世界各国和地区名称','几内亚比绍',NULL,0),('nationality','626','世界各国和地区名称','东帝汶',NULL,0),('nationality','630','世界各国和地区名称','波多黎各',NULL,0),('nationality','634','世界各国和地区名称','卡塔尔',NULL,0),('nationality','638','世界各国和地区名称','留尼汪',NULL,0),('nationality','642','世界各国和地区名称','罗马尼亚',NULL,0),('nationality','643','世界各国和地区名称','俄罗斯联邦',NULL,0),('nationality','646','世界各国和地区名称','卢旺达',NULL,0),('nationality','654','世界各国和地区名称','圣赫勒拿',NULL,0),('nationality','659','世界各国和地区名称','圣基茨和尼维斯',NULL,0),('nationality','660','世界各国和地区名称','安圭拉',NULL,0),('nationality','662','世界各国和地区名称','圣卢西亚',NULL,0),('nationality','666','世界各国和地区名称','圣皮埃尔和密克隆',NULL,0),('nationality','670','世界各国和地区名称','圣文森特和格林纳丁斯',NULL,0),('nationality','674','世界各国和地区名称','圣马力诺',NULL,0),('nationality','678','世界各国和地区名称','圣多美和普林西比',NULL,0),('nationality','682','世界各国和地区名称','沙特阿拉伯',NULL,0),('nationality','686','世界各国和地区名称','塞内加尔',NULL,0),('nationality','690','世界各国和地区名称','塞舌尔',NULL,0),('nationality','694','世界各国和地区名称','塞拉利昂',NULL,0),('nationality','702','世界各国和地区名称','新加坡',NULL,0),('nationality','703','世界各国和地区名称','斯洛伐克',NULL,0),('nationality','704','世界各国和地区名称','越南',NULL,0),('nationality','705','世界各国和地区名称','斯洛文尼亚',NULL,0),('nationality','706','世界各国和地区名称','索马里',NULL,0),('nationality','710','世界各国和地区名称','南非',NULL,0),('nationality','716','世界各国和地区名称','津巴布韦',NULL,0),('nationality','724','世界各国和地区名称','西班牙',NULL,0),('nationality','732','世界各国和地区名称','西撒哈拉',NULL,0),('nationality','736','世界各国和地区名称','苏丹',NULL,0),('nationality','740','世界各国和地区名称','苏里南',NULL,0),('nationality','744','世界各国和地区名称','斯瓦尔巴岛和扬马延岛',NULL,0),('nationality','748','世界各国和地区名称','斯威士兰',NULL,0),('nationality','752','世界各国和地区名称','瑞典',NULL,0),('nationality','756','世界各国和地区名称','瑞士',NULL,0),('nationality','760','世界各国和地区名称','叙利亚',NULL,0),('nationality','762','世界各国和地区名称','塔吉克斯坦',NULL,0),('nationality','764','世界各国和地区名称','泰国',NULL,0),('nationality','768','世界各国和地区名称','多哥',NULL,0),('nationality','772','世界各国和地区名称','托克劳',NULL,0),('nationality','776','世界各国和地区名称','汤加',NULL,0),('nationality','780','世界各国和地区名称','特立尼达和多巴哥',NULL,0),('nationality','784','世界各国和地区名称','阿联酋',NULL,0),('nationality','788','世界各国和地区名称','突尼斯',NULL,0),('nationality','792','世界各国和地区名称','土耳其',NULL,0),('nationality','795','世界各国和地区名称','土库曼斯坦',NULL,0),('nationality','796','世界各国和地区名称','特克斯和凯科斯群岛',NULL,0),('nationality','798','世界各国和地区名称','图瓦卢',NULL,0),('nationality','800','世界各国和地区名称','乌干达',NULL,0),('nationality','804','世界各国和地区名称','乌克兰',NULL,0),('nationality','807','世界各国和地区名称','前南马其顿',NULL,0),('nationality','818','世界各国和地区名称','埃及',NULL,0),('nationality','826','世界各国和地区名称','英国',NULL,0),('nationality','834','世界各国和地区名称','坦桑尼亚',NULL,0),('nationality','840','世界各国和地区名称','美国',NULL,0),('nationality','850','世界各国和地区名称','美属维尔京群岛',NULL,0),('nationality','854','世界各国和地区名称','布基纳法索',NULL,0),('nationality','858','世界各国和地区名称','乌拉圭',NULL,0),('nationality','860','世界各国和地区名称','乌兹别克斯坦',NULL,0),('nationality','862','世界各国和地区名称','委内瑞拉',NULL,0),('nationality','876','世界各国和地区名称','瓦利斯和富图纳',NULL,0),('nationality','882','世界各国和地区名称','萨摩亚',NULL,0),('nationality','887','世界各国和地区名称','也门',NULL,0),('nationality','891','世界各国和地区名称','南斯拉夫',NULL,0),('nationality','894','世界各国和地区名称','赞比亚',NULL,0),('onsetSolarTermCode','01','发病节气','立春',NULL,0),('onsetSolarTermCode','02','发病节气','雨水',NULL,0),('onsetSolarTermCode','03','发病节气','惊蛰',NULL,0),('onsetSolarTermCode','04','发病节气','春分',NULL,0),('onsetSolarTermCode','05','发病节气','清明',NULL,0),('onsetSolarTermCode','06','发病节气','谷雨',NULL,0),('onsetSolarTermCode','07','发病节气','立夏',NULL,0),('onsetSolarTermCode','08','发病节气','小满',NULL,0),('onsetSolarTermCode','09','发病节气','芒种',NULL,0),('onsetSolarTermCode','10','发病节气','夏至',NULL,0),('onsetSolarTermCode','11','发病节气','小暑',NULL,0),('onsetSolarTermCode','12','发病节气','大暑',NULL,0),('onsetSolarTermCode','13','发病节气','立秋',NULL,0),('onsetSolarTermCode','14','发病节气','处暑',NULL,0),('onsetSolarTermCode','15','发病节气','白露',NULL,0),('onsetSolarTermCode','16','发病节气','秋分',NULL,0),('onsetSolarTermCode','17','发病节气','寒露',NULL,0),('onsetSolarTermCode','18','发病节气','霜降',NULL,0),('onsetSolarTermCode','19','发病节气','立冬',NULL,0),('onsetSolarTermCode','20','发病节气','小雪',NULL,0),('onsetSolarTermCode','21','发病节气','大雪',NULL,0),('onsetSolarTermCode','22','发病节气','冬至',NULL,0),('onsetSolarTermCode','23','发病节气','小寒',NULL,0),('onsetSolarTermCode','24','发病节气','大寒',NULL,0),('physicalAffectingFactors','1','体质影响因素','先天禀赋',NULL,0),('physicalAffectingFactors','2','体质影响因素','年龄',NULL,0),('physicalAffectingFactors','3','体质影响因素','性别',NULL,0),('physicalAffectingFactors','4','体质影响因素','饮食',NULL,0),('physicalAffectingFactors','5','体质影响因素','劳逸所伤',NULL,0),('physicalAffectingFactors','6','体质影响因素','情志',NULL,0),('physicalAffectingFactors','7','体质影响因素','地理',NULL,0),('physicalAffectingFactors','8','体质影响因素','疾病针药',NULL,0),('physicalAffectingFactors','9','体质影响因素','其他影响因素',NULL,0),('sex','0','性别','未知的性别',NULL,0),('sex','1','性别','男性',NULL,0),('sex','2','性别','女性',NULL,0),('sex','9','性别','未说明的性别',NULL,0),('treatmentCategoryCode','1','治疗类别','中医',NULL,0),('treatmentCategoryCode','11','治疗类别','中医',NULL,0),('treatmentCategoryCode','12','治疗类别','民族医',NULL,0),('treatmentCategoryCode','2','治疗类别','中西医',NULL,0),('treatmentCategoryCode','3','治疗类别','西医',NULL,0);

INSERT INTO `t_disease`(dis_id,dis_name,dis_code,dis_type,STATUS)
VALUES(
          '-1','全科','-1','1','0'
      );