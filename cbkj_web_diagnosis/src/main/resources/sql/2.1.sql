ALTER TABLE `cbkj_pre_diagnosis`.`medical_records`
    ADD COLUMN `medical_expenses_settled_code` VARCHAR(32) NULL COMMENT '医疗费用结算方式代码';

ALTER TABLE `cbkj_pre_diagnosis`.`medical_records`
    ADD COLUMN `etiology_classification_code` VARCHAR(32) NULL COMMENT '中医病因分类代码' AFTER etiology_classification;

ALTER TABLE `cbkj_pre_diagnosis`.`medical_records`
    ADD COLUMN `physical_affecting_factors_code` VARCHAR(32) NULL COMMENT '体质影响因素代码' AFTER physical_affecting_factors;


ALTER TABLE `cbkj_pre_diagnosis`.`medical_records_prescriptions_item`
    ADD COLUMN `drug_frequency_code` VARCHAR(32) NULL COMMENT '药物使用频次代码' AFTER drug_frequency;

ALTER TABLE `cbkj_pre_diagnosis`.`medical_records_prescriptions_item`
    ADD COLUMN `administration_route_code` VARCHAR(32) NULL COMMENT '用药途径代码' AFTER administration_route;


ALTER TABLE `cbkj_pre_diagnosis`.`medical_west_prescriptions_item`
    ADD COLUMN `drug_frequency_code` VARCHAR(32) NULL COMMENT '药物使用频次代码' AFTER drug_frequency;

ALTER TABLE `cbkj_pre_diagnosis`.`medical_west_prescriptions_item`
    ADD COLUMN `administration_route_code` VARCHAR(32) NULL COMMENT '用药途径代码' AFTER administration_route;


CREATE TABLE `medical_records_exam` (
                                        `exam_id` VARCHAR(32) NOT NULL COMMENT '检查',
                                        `record_id` VARCHAR(32) DEFAULT NULL COMMENT '病历编号',
                                        `apply_no` VARCHAR(50) DEFAULT NULL COMMENT '电子申请单编号',
                                        `apply_dept_name` VARCHAR(50) DEFAULT NULL COMMENT '检查申请科室',
                                        `apply_doctor_name` VARCHAR(50) DEFAULT NULL COMMENT '检查申请医生签名',
                                        `exam_class` VARCHAR(100) DEFAULT NULL COMMENT '检查类别',
                                        `exam_part` VARCHAR(100) DEFAULT NULL COMMENT '检查部位',
                                        `exam_objective` VARCHAR(200) DEFAULT NULL COMMENT '检查目的',
                                        `exam_item_code` VARCHAR(20) DEFAULT NULL COMMENT '检查项目代码',
                                        `doc_name` VARCHAR(50) DEFAULT NULL COMMENT '检查医师签名',
                                        `exam_time` DATETIME DEFAULT NULL COMMENT '检查日期时间',
                                        `report_no` VARCHAR(20) DEFAULT NULL COMMENT '检查报告单编号',
                                        `report_dept_name` VARCHAR(50) DEFAULT NULL COMMENT '检查报告科室',
                                        `report_ins_name` VARCHAR(50) DEFAULT NULL COMMENT '检查报告机构名称',
                                        `report_obj_feature` VARCHAR(200) DEFAULT NULL COMMENT '检查报告结果-客观所见',
                                        `report_sub_feature` VARCHAR(200) DEFAULT NULL COMMENT '检查报告结果-主观提示',
                                        `report_ime` DATETIME DEFAULT NULL COMMENT '检查报告日期时间',
                                        `check_doc_name` VARCHAR(50) DEFAULT NULL COMMENT '审核医师签名',
                                        PRIMARY KEY (`exam_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `medical_records_lab` (
                                       `lab_id` VARCHAR(32) NOT NULL,
                                       `records_id` VARCHAR(32) DEFAULT NULL COMMENT '病历编号',
                                       `apply_no` VARCHAR(50) DEFAULT NULL COMMENT '电子申请单编号',
                                       `apply_dept_name` VARCHAR(50) DEFAULT NULL COMMENT '申请检验的科室名称',
                                       `apply_doctor_name` VARCHAR(50) DEFAULT NULL COMMENT '检验申请医生签名',
                                       `sample_time` DATETIME DEFAULT NULL COMMENT '标本采样日期时间',
                                       `lab_type_code` VARCHAR(20) DEFAULT NULL COMMENT '标本类别',
                                       `lab_aim` VARCHAR(200) DEFAULT NULL COMMENT '检验目的',
                                       `lab_item_code` VARCHAR(20) DEFAULT NULL COMMENT '检验项目代码',
                                       `lab_doctor_name` VARCHAR(50) DEFAULT NULL COMMENT '检验医师签名',
                                       `lab_time` DATETIME DEFAULT NULL COMMENT '检验日期时间',
                                       `lab_no` VARCHAR(20) DEFAULT NULL COMMENT '检验报告单编号',
                                       `report_dept_name` VARCHAR(50) DEFAULT NULL COMMENT '检验报告科室',
                                       `report_ins_name` VARCHAR(50) DEFAULT NULL COMMENT '验报告机构名称',
                                       `report_result` VARCHAR(200) DEFAULT NULL COMMENT '检验报告结果',
                                       `report_remark` VARCHAR(100) DEFAULT NULL COMMENT '检验报告备注',
                                       `report_time` DATETIME DEFAULT NULL COMMENT '检验报告日期时间',
                                       `report_doc_name` VARCHAR(50) DEFAULT NULL COMMENT '报告医师签名',
                                       `check_doc_name` VARCHAR(50) DEFAULT NULL COMMENT '审核医师签名',
                                       PRIMARY KEY (`lab_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

