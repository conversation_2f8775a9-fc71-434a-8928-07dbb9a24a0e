ALTER TABLE `cbkj_pre_diagnosis`.`sys_dic`
    ADD COLUMN `parent_id` VARCHAR(32) NULL COMMENT '父id' AFTER `status`;
ALTER TABLE `cbkj_pre_diagnosis`.`sys_dic`
    CHANGE `dic_code` `dic_code` VARCHAR(256) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字典代码',
    CHANGE `dic_value` `dic_value` VARCHAR(246) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字典的值',
    CHANGE `status` `status` INT(1) DEFAULT 1 NOT NULL COMMENT '1有效0无效',
    CHANGE `parent_id` `parent_id` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父id';


CREATE TABLE `cbkj_pre_diagnosis`.`sys_admin_info_dis_mapping`(
                                                                  `user_id` VARCHAR(32) NOT NULL,
                                                                  `dis_id` VARCHAR(32) NOT NULL,
                                                                  `dis_name` VARCHAR(256) NOT NULL
);
ALTER TABLE `cbkj_pre_diagnosis`.`sys_admin_info_dis_mapping`
    ADD INDEX (`user_id`, `dis_id`);

ALTER TABLE `cbkj_pre_diagnosis`.`sys_admin_info_dis_mapping`
    ADD COLUMN `sort` INT(10) NOT NULL AUTO_INCREMENT AFTER `dis_name`,
    ADD KEY(`sort`);


INSERT INTO `t_pre_diagnosis_dis_mapping`
(
    dia_id,dis_id,dis_name,dis_type,dis_code
)

SELECT dia_id ,'-1','全科','1','-1' FROM `t_pre_diagnosis_form` WHERE dia_type = '2';



