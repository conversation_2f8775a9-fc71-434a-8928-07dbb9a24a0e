ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_form`
    ADD COLUMN `form_code` VARCHAR(16) NULL COMMENT '表单编码' AFTER `ins_code`;

ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    ADD COLUMN `question_code` VARCHAR(16) NULL AFTER `question_option_groups`;

ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option`
    ADD COLUMN `option_code` VARCHAR(16) NULL AFTER `option_fill_check`;


ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    CHANGE `question_type` `question_type` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '题目类型（1文本  2单选  3多选  4时间 5.血压 6.日期）';

ALTER TABLE `cbkj_pre_diagnosis`.`t_record_dia`
    CHANGE `question_type` `question_type` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目类型（1文本  2单选  3多选  4时间 5.血压  6日期）';


ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option`
    ADD COLUMN `option_structure_value` VARCHAR(128) NULL COMMENT '病历转化结构化术语' AFTER `option_code`;






ALTER TABLE `medical_patient_record_cost`
    CHANGE `tcm_bian_zheng_cost` `tcm_bian_zheng_cost` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '综合医疗服务类-一般医疗服务费-中医辨证论治费',
    CHANGE `tcm_cost` `tcm_cost` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '中药类-中草药费',
    CHANGE `cpd_cost` `cpd_cost` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '中药类-中成药费',
    CHANGE `ip_cost` `ip_cost` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '中药类-中成药费-医疗机构中药制剂费',
    CHANGE `wesDrugCost` `wesDrugCost` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '西药类-西药费',
    CHANGE `others_cost` `others_cost` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '其他类-其他费',
    ADD COLUMN `decocting_cost` VARCHAR(10) NULL COMMENT '煎药费用金额' AFTER `others_cost`,
    ADD COLUMN `personal_cost` VARCHAR(10) NULL COMMENT '个人承担费用金额' AFTER `decocting_cost`,
    ADD COLUMN `insurance_reimbursement_cost` VARCHAR(10) NULL COMMENT '门诊医保报销金额' AFTER `personal_cost`,
    ADD COLUMN `cn_medicine_cost` VARCHAR(10) NULL COMMENT '中医类-中医治疗费' AFTER `insurance_reimbursement_cost`,
    ADD COLUMN `imaging_cost` VARCHAR(10) NULL COMMENT '诊断类-影像学诊断费' AFTER `cn_medicine_cost`,
    ADD COLUMN `laboratory_cost` VARCHAR(10) NULL COMMENT '诊断类-实验室诊断费' AFTER `imaging_cost`,
    ADD COLUMN `surgical_cost` VARCHAR(10) NULL COMMENT '治疗类-手术治疗费' AFTER `laboratory_cost`,
    CHANGE `total_cost` `total_cost` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '本次患者就诊所支付的各项费用，单位为元'  AFTER `surgical_cost`;



ALTER TABLE `medical_patient_record_cost`
    CHANGE `examinationCost` `examination_cost` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '辅助检查费',
    CHANGE `assayCost` `assay_cost` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '化验费',
    CHANGE `wesDrugCost` `west_drug_cost` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '西药类-西药费';