
ALTER TABLE s_road_task_patients
    ADD COLUMN app_id VARCHAR(32) NULL AFTER doctor_name,
    ADD COLUMN ins_code VARCHAR(32) NULL AFTER app_id,
    ADD COLUMN ins_id VARCHAR(32) NULL AFTER ins_code,
    ADD COLUMN dept_id VARCHAR(32) NULL AFTER ins_id,
    ADD COLUMN dept_code VARCHAR(32) NULL AFTER dept_id,
    ADD COLUMN ins_name VARCHAR(64) NULL AFTER dept_code;
    ;


ALTER TABLE t_propaganda_edu
    CHANGE ins_name ins_name VARCHAR(128) NULL,
    ADD COLUMN app_id VARCHAR(32) NULL AFTER ins_name,
    ADD COLUMN ins_id VARCHAR(32) NULL AFTER app_id;





ALTER TABLE statistics_erver_day_follow
    ADD COLUMN `type` INT(2) DEFAULT 1 NULL COMMENT '1.电话随访2.问卷',
    ADD COLUMN dis_id VARCHAR(32),
    ADD COLUMN dis_name VA<PERSON>HAR(64),
    ADD COLUMN dis_code VARCHAR(32)

;
ALTER TABLE `t_pre_diagnosis_dis_mapping`
    ADD COLUMN `dis_code` VARCHAR(32) NULL AFTER `dis_type`;


ALTER TABLE `t_record`
    ADD INDEX (`dia_id`);















