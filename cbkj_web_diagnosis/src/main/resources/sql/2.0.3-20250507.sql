ALTER TABLE `medical_records` ADD COLUMN `closed_status` VARCHAR(2) DEFAULT '0' NOT NULL COMMENT '状态，0:创建(产集中)，1:结案，2:归档，3:上传';
ALTER TABLE `medical_records` ADD COLUMN `closed_time` DATETIME COMMENT '结案时间';
ALTER TABLE `medical_records` ADD COLUMN `closed_no` VARCHAR(32) COMMENT '结案号';

ALTER TABLE `s_road_task_patients` ADD COLUMN `closed_status` VARCHAR(2) DEFAULT '0' NOT NULL COMMENT '状态，0:创建(产集中)，1:结案，2:归档，3:上传';
ALTER TABLE `s_road_task_patients` ADD COLUMN `closed_time` DATETIME COMMENT '结案时间';
ALTER TABLE `s_road_task_patients` ADD COLUMN `closed_no` VARCHAR(32) COMMENT '结案号';

ALTER TABLE `t_record` ADD COLUMN `closed_status` VARCHAR(2) DEFAULT '0' NOT NULL COMMENT '状态，0:创建(产集中)，1:结案，2:归档，3:上传';
ALTER TABLE `t_record` ADD COLUMN `closed_time` DATETIME COMMENT '结案时间';
ALTER TABLE `t_record` ADD COLUMN `closed_no` VARCHAR(32) COMMENT '结案号';

ALTER TABLE `medical_basic_handle_record` MODIFY COLUMN `operation_number` VARCHAR(100) COMMENT '操作次数';

CREATE TABLE `upload_log` (
                              `id` VARCHAR(32) NOT NULL,
                              `app_id` VARCHAR(32) DEFAULT NULL,
                              `ins_code` VARCHAR(32) DEFAULT NULL,
                              `upload_type` VARCHAR(2) DEFAULT NULL COMMENT '1：诊前，2，诊后，3诊中，4宣教',
                              `upload_type_id` VARCHAR(32) DEFAULT NULL COMMENT '相应的id',
                              `closed_no` VARCHAR(32) DEFAULT NULL COMMENT '结案号',
                              `upload_result_code` VARCHAR(2) DEFAULT NULL COMMENT '上传结果 0成功，1失败',
                              `upload_result` TEXT DEFAULT NULL COMMENT '返回内容',
                              `insert_time` DATETIME DEFAULT NULL,
                              PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `t_disease_no` (
                                `dis_id` VARCHAR(32) NOT NULL,
                                `dis_name` VARCHAR(32) NOT NULL,
                                `ins_name` VARCHAR(32) NULL,
                                `dis_no` INT(4) NOT NULL,
                                PRIMARY KEY (`dis_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

INSERT INTO t_disease_no SELECT dis_id,dis_name,'',1 FROM`t_disease` WHERE dis_type ='1' AND STATUS = '0';

