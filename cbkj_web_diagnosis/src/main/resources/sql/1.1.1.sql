ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    ADD COLUMN `question_unit` VARCHAR(32) NULL COMMENT '问题单位（只有文本样式擦你有这个值）' AFTER `question_class_type`;
ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    CHANGE `question_type` `question_type` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '题目类型（1文本  2单选  3多选  4时间 5.血压）';
ALTER TABLE `cbkj_pre_diagnosis`.`t_record_dia`
    CHANGE `question_type` `question_type` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目类型（1文本  2单选  3多选  4时间 5.血压）';

ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option`
    ADD COLUMN `option_fill_blank` VARCHAR(128) NULL COMMENT '选项填空提示语' AFTER `option_image`,
    ADD COLUMN `option_fill_check` INT(1) NULL COMMENT '选项填空填空是否必填 1.是 0否' AFTER `option_fill_blank`;



CREATE TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option_mutex`(
                                                                    `mutex_id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '互斥选项表',
                                                                    `option_id` INT(11) COMMENT '选项id',
                                                                    `option_name` VARCHAR(64) COMMENT '选项名称',
                                                                    `mutex_option_id` INT(11) COMMENT '互斥选项id',
                                                                    PRIMARY KEY (`mutex_id`),
                                                                    INDEX (`option_id`),
                                                                    INDEX (`mutex_option_id`)
);





insert into `cbkj_pre_diagnosis`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `parent_menu_id`, `sort`, `menu_level`) values ('100505', '题库管理', '/question/default', '100500', '5', '2')

ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_question`
    ADD COLUMN `question_option_groups` TEXT NULL COMMENT '保存前端需要的信息' AFTER `question_unit`;

ALTER TABLE `cbkj_pre_diagnosis`.`t_propaganda_edu`
    ADD COLUMN `edu_abstract` TEXT NULL COMMENT '摘要' AFTER `ins_id`;


ALTER TABLE `cbkj_pre_diagnosis`.`s_road_task_patients`
    ADD COLUMN `dept_name` VARCHAR(128) NULL AFTER `ins_name`;



CREATE TABLE `sys_log_interface` (
                                     `ID` VARCHAR(32) NOT NULL,
                                     `APP_ID` VARCHAR(32) DEFAULT NULL,
                                     `INS_CODE` VARCHAR(32) DEFAULT NULL,
                                     `DOCTOR_ID` VARCHAR(32) DEFAULT NULL,
                                     `DOCTOR_NAME` VARCHAR(32) DEFAULT NULL,
                                     `PATIENT_ID` VARCHAR(32) DEFAULT NULL,
                                     `PATIENT_NAME` VARCHAR(32) DEFAULT NULL,
                                     `CREATE_TIME` DATETIME DEFAULT NULL,
                                     `INTERFACE_NAME` VARCHAR(64) DEFAULT NULL,
                                     `INTERFACE_DESC` VARCHAR(64) DEFAULT NULL,
                                     `INTERFACE_TOKEN` VARCHAR(64) DEFAULT NULL,
                                     `INTERFACE_PARAMS` TEXT,
                                     `RESULT_STATUS` VARCHAR(1) DEFAULT NULL,
                                     `RESULT_MESEAGE` TEXT,
                                     `RESULT_DATA` TEXT,
                                     PRIMARY KEY (`ID`),
                                     KEY `SLI_IDX_CREATE_TIME` (`CREATE_TIME`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='接口日志'


