update `cbkj_pre_diagnosis`.`sys_admin_menu` set `menu_name` = '随访患者管理' where `menu_id` = '100304';




ALTER TABLE `cbkj_pre_diagnosis`.`t_pre_diagnosis_option`
    CHANGE `option_code` `option_code` VARCHAR(64) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '选项代码';


insert into `cbkj_pre_diagnosis`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `parent_menu_id`, `sort`, `menu_level`) values ('199903', '意见处理', '/system/advice', '199900', '2', '2');



ALTER TABLE `medical_records`
    CHANGE `infectious_history` `infectious_history` text  COLLATE utf8mb4_general_ci NULL COMMENT '传染病史' after `infectious_history_flag` ,
    CHANGE `surgical_history` `surgical_history` text  COLLATE utf8mb4_general_ci NULL COMMENT '手术史' after `vaccination_history` ,
    CHANGE `blood_transfusion_history` `blood_transfusion_history` text  COLLATE utf8mb4_general_ci NULL COMMENT '输血史' after `surgical_history` ,
    CHANGE `obstetric_history` `obstetric_history` varchar(500)  COLLATE utf8mb4_general_ci NULL COMMENT '婚育史' after `suckling_period_flag` ,
    CHANGE `menstrual_history` `menstrual_history` text  COLLATE utf8mb4_general_ci NULL COMMENT '月经史' after `obstetric_history` ;



DROP TABLE IF EXISTS `t_business_annex`;

CREATE TABLE `t_business_annex` (
                                    `id` varchar(32) NOT NULL COMMENT 'ID',
                                    `insert_user_id` varchar(32) NOT NULL COMMENT '上传者id（医生或者是患者id）',
                                    `annex_foreign_id` varchar(32) DEFAULT NULL COMMENT '第三方ID',
                                    `annex_name` varchar(150) DEFAULT NULL COMMENT '附件名称',
                                    `annex_Original_name` varchar(100) DEFAULT NULL COMMENT '附件原名称',
                                    `annex_suffix_name` varchar(100) DEFAULT NULL COMMENT '附件后缀名',
                                    `annex_size` double(10,2) DEFAULT NULL COMMENT '附件大小',
                                    `annex_path` varchar(100) DEFAULT NULL COMMENT '附件路径',
                                    `annex_type` tinyint(2) DEFAULT NULL COMMENT '附件所属业务类型：1系统建议反馈,2系统帮助操作手册3.系统帮助操作视频',
                                    `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
                                    `create_time` datetime DEFAULT NULL,
                                    PRIMARY KEY (`id`),
                                    KEY `insert_user_id` (`insert_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务管理附件表';



DROP TABLE IF EXISTS `t_business_edition`;

CREATE TABLE `t_business_edition` (
                                      `id` varchar(32) NOT NULL COMMENT 'ID',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建者ID',
                                      `create_user_name` varchar(32) DEFAULT NULL COMMENT '创建者姓名',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `update_user_id` varchar(32) DEFAULT NULL COMMENT '修改者ID',
                                      `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
                                      `delete_user_id` varchar(32) DEFAULT NULL COMMENT '删除者ID',
                                      `edition_num` varchar(100) NOT NULL COMMENT '版本号',
                                      `edition_content` varchar(1000) DEFAULT NULL COMMENT '发布内容',
                                      `edition_time` datetime DEFAULT NULL COMMENT '发布时间',
                                      `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务管理版本维护';



DROP TABLE IF EXISTS `t_business_proposal`;

CREATE TABLE `t_business_proposal` (
                                       `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                       `app_id` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT 'APPID',
                                       `app_name` varchar(32) DEFAULT NULL COMMENT '医联体名称',
                                       `ins_code` varchar(32) DEFAULT NULL COMMENT '医疗机构编码',
                                       `ins_name` varchar(128) DEFAULT NULL COMMENT '医疗机构名称',
                                       `proposal_title` varchar(100) DEFAULT NULL COMMENT '标题限制20字',
                                       `proposal_content` varchar(500) DEFAULT NULL COMMENT '建议内容限制500字',
                                       `proposal_liaison` varchar(50) DEFAULT NULL COMMENT '联系方式',
                                       `proposal_type` int(2) DEFAULT NULL COMMENT '反馈类型：1建议，2问题 3.咨询',
                                       `proposal_receive_state` int(2) DEFAULT NULL COMMENT '受理状态：0受理，1未受理',
                                       `proposal_receive_opinion` varchar(200) DEFAULT NULL COMMENT '受理意见',
                                       `proposal_receive_id` varchar(32) DEFAULT NULL COMMENT '受理人ID',
                                       `proposal_receive_name` varchar(20) DEFAULT NULL COMMENT '受理人姓名',
                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                       `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建者ID',
                                       `create_user_name` varchar(32) DEFAULT NULL COMMENT '创建者姓名',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `update_user_id` varchar(32) DEFAULT NULL COMMENT '修改者ID',
                                       `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
                                       `delete_user_id` varchar(32) DEFAULT NULL COMMENT '删除者ID',
                                       `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
                                       `source_from` int(1) DEFAULT NULL COMMENT '来源（1.医生端2.患者端）',
                                       PRIMARY KEY (`id`),
                                       KEY `create_user_id` (`create_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COMMENT='业务管理建议问题咨询表';




UPDATE `t_admin_info` SET patient_card_type = '01' WHERE patient_card_type IS NULL ;



-- 请手动上传文件到 /home/<USER>/uploadFile/cbkjFile/20240903 路径下！！！！！！！！！！！！！！！！！！！并修改 annex_path中文件名和后缀 名且文件名称不能为中文。annex_suffix_name 字段是文件类型
insert into `t_business_annex` (`id`, `insert_user_id`, `annex_foreign_id`, `annex_name`, `annex_Original_name`, `annex_suffix_name`, `annex_size`, `annex_path`, `annex_type`, `is_del`, `create_time`) values('1','537caeea002846c6a3fabf1b06f53456','','中医预诊随访系统特色病种预料维护操作讲解.mp4','中医预诊随访系统特色病种预料维护操作讲解','video/mp4','45000.00','preview/cbkjFile/20240903/2.mp4','3','0',NULL);
insert into `t_business_annex` (`id`, `insert_user_id`, `annex_foreign_id`, `annex_name`, `annex_Original_name`, `annex_suffix_name`, `annex_size`, `annex_path`, `annex_type`, `is_del`, `create_time`) values('2','537caeea002846c6a3fabf1b06f53456',NULL,'中医预诊随访系统操作手册.docx','中医预诊随访系统操作手册','application/vnd.openxmlformats-officedocument.wordprocessingml.document','1000.00','preview/cbkjFile/20240903/1.docx','2','0','2024-09-03 11:01:59');



