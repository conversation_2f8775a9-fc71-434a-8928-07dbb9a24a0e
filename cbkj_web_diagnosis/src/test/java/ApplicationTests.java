import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCoreRe;
import com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCount;
import com.cbkj.diagnosis.beans.business.CompletedQuestionnaireListExcel;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.mapper.business.TDiseaseMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2017-08-01 21:32:05
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class)
public class ApplicationTests {

	@Autowired
	ApplicationContext ctx;

	@Test
	public void testContextLoads() throws Exception {
		Assert.assertNotNull(this.ctx);
		Assert.assertTrue(this.ctx.containsBean("diagnosisApiApplication"));
	}


	@Autowired
	private TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;

	@Autowired
	private TDiseaseMapper tDiseaseMapper;
	
	@Resource
	private TAdminInfoMapper tAdminInfoMapper;

	@Resource
	private SRoadTaskPatientsMapper sRoadTaskPatientsMapper;
	/**
	 * 测试，是不是自动填充到对象中 把数据库自动生成的id。
	 */
	@Test
	public void test(){
		ArrayList<TPreDiagnosisQuestion> tPreDiagnosisQuestions = new ArrayList<>();
		for (int i = 0; i < 5 ; i++) {
			TPreDiagnosisQuestion question = new TPreDiagnosisQuestion();
			question.setQuestionName(i+"name");
			question.setQuestionType("1");
			tPreDiagnosisQuestions.add(question);
		}
		tPreDiagnosisQuestionMapper.insertList(tPreDiagnosisQuestions);
		for (int i = 0; i < tPreDiagnosisQuestions.size(); i++) {
			System.out.println(tPreDiagnosisQuestions.get(i).getQuestionId());
		}
	}

	@Test
	public void test2(){
		ArrayList<TAdminInfo> tAdminInfos = new ArrayList<>();
		TAdminInfo tAdminInfo = new TAdminInfo();
		tAdminInfo.setUserId("1");

		TAdminInfo tAdminInfo2 = new TAdminInfo();
		tAdminInfo2.setUserId("3a80b5a407e84fafa713018979ed775e");

		tAdminInfos.add(tAdminInfo2);
		tAdminInfos.add(tAdminInfo);
		List<TAdminInfo> list = tAdminInfoMapper.getListByIds(tAdminInfos);

		for (int i = 0; i < list.size(); i++) {
			System.out.println(list.get(i).getCardNumber());
		}
	}
	@Test
	public void test3(){
		CompletedQuestionnaireCoreRe re = new CompletedQuestionnaireCoreRe();
		List<CompletedQuestionnaireListExcel> excelCompletedQuestionnaireList = sRoadTaskPatientsMapper.getExcelCompletedQuestionnaireList(re);

		List<CompletedQuestionnaireCount> countCompletedQuestionnaireList = sRoadTaskPatientsMapper.getCountCompletedQuestionnaireList(re);
		System.out.println("1");
	}
}