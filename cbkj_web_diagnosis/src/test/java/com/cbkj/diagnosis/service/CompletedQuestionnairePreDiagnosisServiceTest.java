package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreRe;
import com.cbkj.diagnosis.common.utils.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/23 11:31
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class CompletedQuestionnairePreDiagnosisServiceTest {

    @Autowired
    private CompletedQuestionnairePreDiagnosisService completedQuestionnairePreDiagnosisService;

    @Test
    void getList() {
        CompletedQuestionnairePreRe completedQuestionnairePreRe = new CompletedQuestionnairePreRe();
        String[] strings = new String[]  {"1"};
        completedQuestionnairePreRe.setDisIdsList(strings);
        completedQuestionnairePreDiagnosisService.getList(completedQuestionnairePreRe, new Page(1, 10));
    }

    @Test
    void downloadQuestionnaire() {
    }

    @Test
    void testDownloadQuestionnaire() {
        CompletedQuestionnairePreRe completedQuestionnairePreRe = new CompletedQuestionnairePreRe();
        completedQuestionnairePreDiagnosisService.downloadQuestionnaire(completedQuestionnairePreRe,null);
    }
}