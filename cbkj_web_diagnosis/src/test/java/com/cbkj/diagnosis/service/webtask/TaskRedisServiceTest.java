package com.cbkj.diagnosis.service.webtask;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisForm;
import com.cbkj.diagnosis.common.http.XiaMenResEntity;
import com.cbkj.diagnosis.common.scheduler.RoadEventSendTaskSchedulerBatch;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import com.cbkj.diagnosis.service.common.vo.PreListReVo;
import com.cbkj.diagnosis.service.his.XiaMenSendWXMessage;
import com.cbkj.diagnosis.service.sysService.RedisService;
import com.cbkj.diagnosis.service.webapi.business.WebTRecordService;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TaskRedisServiceTest extends TestCase {

    @Autowired
    private WebTRecordService webTRecordService;

    @Resource
    private  TaskRedisService taskRedisService;
    @Resource
    private  RedisService redisService;

    @Resource
    private XiaMenSendWXMessage xiaMenSendWXMessage;

    @Autowired
    private RoadEventSendTaskSchedulerBatch roadEventSendTaskSchedulerBatch;

    @Resource
    private TPreDiagnosisFormMapper tPreDiagnosisFormMapper;
    @Test
public void test1(){
        TPreDiagnosisForm tPreDiagnosisForm = new TPreDiagnosisForm();
        tPreDiagnosisForm.setDiaId("test");
        tPreDiagnosisForm.setCreateDate(new Date());
        tPreDiagnosisForm.setFormCode("testCode");
        tPreDiagnosisForm.setStatus("1");
        int insert = tPreDiagnosisFormMapper.insert(tPreDiagnosisForm);
        int insert2 = tPreDiagnosisFormMapper.insert(tPreDiagnosisForm);
        System.out.println(insert);
        System.out.println(insert2);
    }

    @Test
    public void test() {
        redisService.putTaskWait1("1", "2222222" + "", new Date());
        redisService.set("task-wait-1" + "::" + "123", DateUtil.getNextDate(-2));
        redisService.set("task-wait-1::2", DateUtil.getNextDate(-3));


        ArrayList<String> arrivedKey = taskRedisService.getArrivedKey();
        for (String s : arrivedKey) {
            System.out.println(s);
        }
    }
    @Test
    public void roadEventSendTaskSchedulerBatchTest(){
        roadEventSendTaskSchedulerBatch.batchSchedule();
    }

    @Test
    public void test2(){
        System.out.println(redisService.get("task-wait-1::1"));
    }
//    @Test
//    public void test3(){
//        XiaMenResEntity xiaMenResEntity = xiaMenSendWXMessage.sendMsgbyOut();
//        System.out.println(JSON.toJSONString(xiaMenResEntity));
//
//    }
    @Test
    public void testwebTRecordService() throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        PreListReVo preListReVo = new PreListReVo();
//        preListReVo.setStartDate(simpleDateFormat.parse("2024-07-09 00:00:00"));
//        preListReVo.setEndDate(simpleDateFormat.parse("2024-08-09 23:59:59"));
        Object a = webTRecordService.getPageDatas(preListReVo, new Page(), "a31119f5e9384399ab0fe3f2c5b67ca0");
        System.out.println(JSON.toJSONString(a));
    }
}