package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.DiagnosisApiApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/18 14:51
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class TFormServiceTest {

    @Autowired
    private TFormService tFormService;
    @Test
    void changeShowStatus() {
        tFormService.changeShowStatus("007536a6da904a0a80e49aeceedd09c5",false);

        tFormService.getDoctorMappingDisList(null);
    }
}