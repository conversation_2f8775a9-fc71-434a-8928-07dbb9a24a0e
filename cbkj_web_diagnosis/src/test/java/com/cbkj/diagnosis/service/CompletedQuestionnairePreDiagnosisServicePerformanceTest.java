package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreCount;
import com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreRe;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion;
import com.cbkj.diagnosis.common.utils.PerformanceMonitor;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 预诊问卷下载服务性能测试
 * 
 * <AUTHOR>
 * @date 2024/12/23
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class CompletedQuestionnairePreDiagnosisServicePerformanceTest {

    @Autowired
    private CompletedQuestionnairePreDiagnosisService service;
    
    @Autowired
    private TPreDiagnosisQuestionMapper questionMapper;

    @Autowired
    private PreDiagnosisListCacheService preDiagnosisListCacheService;

    @BeforeEach
    void setUp() {
        PerformanceMonitor.reset();
    }

    /**
     * 测试缓存优化效果
     */
    @Test
    void testCacheOptimization() {
        log.info("=== 测试缓存优化效果 ===");
        
        String diaId = "test_dia_id";
        int queryCount = 10;
        
        // 测试未优化版本（重复查询数据库）
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < queryCount; i++) {
            // 模拟重复查询
            questionMapper.getAllQuestionListByDiaId(diaId);
        }
        long unoptimizedTime = System.currentTimeMillis() - startTime;
        
        // 测试优化版本（使用缓存）
        startTime = System.currentTimeMillis();
        List<TPreDiagnosisQuestion> cachedResult = null;
        for (int i = 0; i < queryCount; i++) {
            if (cachedResult == null) {
                cachedResult = preDiagnosisListCacheService.getQuestionsByDiaIdCached(diaId);
            }
            // 后续查询直接使用缓存结果
        }
        long optimizedTime = System.currentTimeMillis() - startTime;
        
        log.info("未优化版本耗时: {}ms", unoptimizedTime);
        log.info("优化版本耗时: {}ms", optimizedTime);
        log.info("性能提升: {}倍", unoptimizedTime > 0 ? (double)unoptimizedTime / optimizedTime : "无法计算");
        
        // 优化版本应该显著快于未优化版本
        assertTrue(optimizedTime <= unoptimizedTime, "缓存优化应该提升性能");
    }

    /**
     * 测试并行处理性能
     */
    @Test
    void testParallelProcessing() {
        log.info("=== 测试并行处理性能 ===");
        
        // 创建测试数据
        List<CompletedQuestionnairePreCount> countList = createTestCountList(5);
        
        // 测试串行处理
        long startTime = System.currentTimeMillis();
        for (CompletedQuestionnairePreCount count : countList) {
            simulateExcelGeneration(count);
        }
        long serialTime = System.currentTimeMillis() - startTime;
        
        // 测试并行处理
        ExecutorService executor = Executors.newFixedThreadPool(4);
        startTime = System.currentTimeMillis();
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (CompletedQuestionnairePreCount count : countList) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> 
                simulateExcelGeneration(count), executor);
            futures.add(future);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        long parallelTime = System.currentTimeMillis() - startTime;
        
        executor.shutdown();
        
        log.info("串行处理耗时: {}ms", serialTime);
        log.info("并行处理耗时: {}ms", parallelTime);
        log.info("性能提升: {}倍", serialTime > 0 ? (double)serialTime / parallelTime : "无法计算");
        
        // 并行处理应该快于串行处理
        assertTrue(parallelTime <= serialTime, "并行处理应该提升性能");
    }

    /**
     * 测试内存使用优化
     */
    @Test
    void testMemoryOptimization() {
        log.info("=== 测试内存使用优化 ===");
        
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // 强制垃圾回收
        
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 模拟未优化版本：在内存中存储大量字节数组
        List<byte[]> largeDataList = new ArrayList<>();
        for (int i = 0; i < 50; i++) {
            largeDataList.add(new byte[1024 * 1024]); // 1MB per array
        }
        
        long memoryAfterUnoptimized = runtime.totalMemory() - runtime.freeMemory();
        long unoptimizedMemoryUsage = memoryAfterUnoptimized - initialMemory;
        
        // 清理数据
        largeDataList.clear();
        runtime.gc();
        
        // 模拟优化版本：流式处理，及时释放内存
        for (int i = 0; i < 50; i++) {
            byte[] data = new byte[1024 * 1024];
            // 模拟处理后立即释放
            data = null;
            if (i % 10 == 0) {
                runtime.gc(); // 定期垃圾回收
            }
        }
        
        long memoryAfterOptimized = runtime.totalMemory() - runtime.freeMemory();
        long optimizedMemoryUsage = memoryAfterOptimized - initialMemory;
        
        log.info("未优化版本内存使用: {} MB", unoptimizedMemoryUsage / 1024 / 1024);
        log.info("优化版本内存使用: {} MB", optimizedMemoryUsage / 1024 / 1024);
        
        // 优化版本应该使用更少的内存
        assertTrue(optimizedMemoryUsage < unoptimizedMemoryUsage, "内存优化应该减少内存使用");
    }

    /**
     * 测试整体性能提升
     */
    @Test
    void testOverallPerformanceImprovement() {
        log.info("=== 整体性能优化测试 ===");
        
        // 创建测试参数
        CompletedQuestionnairePreRe request = new CompletedQuestionnairePreRe();
        request.setStartDate("2024-01-01");
        request.setEndDate("2024-12-31");
        
        // 测试优化后的方法
        long startTime = System.currentTimeMillis();
        
        try {
            // 这里可以调用实际的方法进行测试
            // service.downloadQuestionnaire(request, mockResponse);
            
            // 模拟处理时间
            Thread.sleep(100);
            
        } catch (Exception e) {
            log.error("测试过程中出现异常", e);
        }
        
        long totalTime = System.currentTimeMillis() - startTime;
        
        log.info("整体处理耗时: {}ms", totalTime);
        
        // 验证性能监控数据
        PerformanceMonitor.printPerformanceReport();
        
        assertTrue(totalTime >= 0, "处理时间应该为正数");
    }

    /**
     * 创建测试用的计数列表
     */
    private List<CompletedQuestionnairePreCount> createTestCountList(int size) {
        List<CompletedQuestionnairePreCount> list = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            CompletedQuestionnairePreCount count = new CompletedQuestionnairePreCount();
            count.setDiaId("test_dia_" + i);
            count.setNum(100 + i * 10);
            count.setChineseDisName("测试问卷" + i);
            list.add(count);
        }
        return list;
    }

    /**
     * 模拟Excel生成过程
     */
    private void simulateExcelGeneration(CompletedQuestionnairePreCount count) {
        try {
            // 模拟耗时操作
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
