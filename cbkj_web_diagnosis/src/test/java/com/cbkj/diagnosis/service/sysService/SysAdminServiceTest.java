package com.cbkj.diagnosis.service.sysService;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.common.utils.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/8 9:05
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SysAdminServiceTest {

    @Autowired
    private SysAdminService sysAdminService;
    @Test
    void getAdminList() {
        Object admin = sysAdminService.getAdminList("admin", new Page());
        System.out.println(JSON.toJSONString(admin));

    }
}