package com.cbkj.diagnosis.service.webtask;

import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.controller.webtask.vo.ResetPatientTasksStatus;
import com.cbkj.diagnosis.service.webapi.business.WebTRecordService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/21 13:34
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class WebTaskServiceTest {

    @Autowired
    private WebTaskService webTaskService;

    @Autowired
    private WebTRecordService webTRecordService;
    @Autowired
    private  WebRoadTaskPatient webRoadTaskPatient;
    @Test
    void resetPatientTasksStatus() {
        String[] a = new String[]{"1", "2"};
        ResetPatientTasksStatus resetPatientTasksStatus = new ResetPatientTasksStatus();
        resetPatientTasksStatus.setTaskPatientsId(a);
        webTaskService.resetPatientTasksStatus(resetPatientTasksStatus);
    }

    @Test
    public void testGetPreResultDifferent() {
//        webTRecordService.getPreResultDifferent(null, null, "q9ekNWRW2K_Km_pkppB4_");
        webRoadTaskPatient.newRecordsCreateTask("1909421522646732800");
    }
}