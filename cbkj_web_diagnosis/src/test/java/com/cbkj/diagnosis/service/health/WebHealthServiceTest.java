//package com.cbkj.diagnosis.service.health;
//
//import com.alibaba.fastjson.JSON;
//import com.cbkj.diagnosis.DiagnosisApiApplication;
//import com.cbkj.diagnosis.beans.TAdminInfo;
//import com.cbkj.diagnosis.beans.health.MedicalRecords;
//import com.cbkj.diagnosis.beans.health.vo.GetMedicalRecordsDetails;
//import com.cbkj.diagnosis.common.scheduler.RoadEventTaskScheduler;
//import com.cbkj.diagnosis.common.utils.IDUtil;
//import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
//import junit.framework.TestCase;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class WebHealthServiceTest extends TestCase {
//
//    @Resource
//    private WebHealthService webHealthService;
//
//    @Resource
//    private TAdminInfoMapper tAdminInfoMapper;
//
//    @Resource
//    private RoadEventTaskScheduler roadEventTaskScheduler;
//
//    @Test
//    public void testGetMedicalRecordsDetails() {
//        GetMedicalRecordsDetails details = new GetMedicalRecordsDetails();
//        List<MedicalRecords> medicalRecordsDetails = webHealthService.getMedicalRecordsDetails(details);
//
//        System.out.println(JSON.toJSONString(medicalRecordsDetails));
//    }
//
//    @Test
//    public void testInsertEn(){
//        TAdminInfo tAdminInfo = new TAdminInfo();
//        tAdminInfo.setUserId(IDUtil.getID());
//        tAdminInfo.setCardNumber("130124199402240335");
//
//        TAdminInfo tAdminInfo2 = new TAdminInfo();
//        tAdminInfo2.setUserId(IDUtil.getID());
//        tAdminInfo2.setCardNumber("230124199402240338");
//
//        ArrayList<TAdminInfo> list = new ArrayList<>();
//        list.add(tAdminInfo2);
//        list.add(tAdminInfo);
//
//        tAdminInfoMapper.insertList(list);
//
//
//        TAdminInfo tAdminInfo3 = new TAdminInfo();
//        tAdminInfo3.setUserId(IDUtil.getID());
//        tAdminInfo3.setCardNumber("330124199402240330");
//        tAdminInfoMapper.insert(tAdminInfo3);
//    }
//
//    @Test
//    public void test2(){
//        roadEventTaskScheduler.task();
//    }
//}