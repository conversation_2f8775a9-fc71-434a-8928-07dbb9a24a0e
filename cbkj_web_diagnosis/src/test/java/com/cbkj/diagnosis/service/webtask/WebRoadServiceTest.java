package com.cbkj.diagnosis.service.webtask;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.task.SRoad;
import com.cbkj.diagnosis.beans.task.SRoadTask;
import com.cbkj.diagnosis.beans.task.SRoadTaskResponse;
import com.cbkj.diagnosis.beans.task.webvo.InsertOrUpdateTask;
import com.cbkj.diagnosis.beans.task.webvo.UpdateOrInsertRoad;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.task.SRoadMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.webapi.business.vo.GetPatientListVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/17 9:22
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class WebRoadServiceTest {

    @Autowired
    private WebRoadService webRoadService;

    @Autowired
    private WebRoadTaskPatient webRoadTaskPatient;

    @Autowired
    private WebTaskService webTaskService;

    @Autowired
    private SRoadMapper sRoadMapper;
    @Autowired
    private SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    @Test
    void getRoadDetailById() {
//        UpdateOrInsertRoad roadDetailById = webRoadService.getRoadDetailById("1813383176631357440");
//        log.info("{}", roadDetailById.getRoadConditionsList());

        SRoadTask sRoadTask = new SRoadTask();
        sRoadTask.setSRoadGroupWay("2");
        sRoadTask.setSRoadTaskId("1818212716843110400");
        sRoadTask.setSRoadId("1818106901280854016");
        webRoadTaskPatient.setTaskPatient(sRoadTask);
    }

    @Test
    public void testgetPatientList() {
        InsertOrUpdateTask insertOrUpdateTask = new InsertOrUpdateTask();
        insertOrUpdateTask.setSRoadId("1815940995675066368");
        insertOrUpdateTask.setTaskName("lina");
        insertOrUpdateTask.setTaskContent("lina");

        SRoad sRoad = sRoadMapper.getObjectById(insertOrUpdateTask.getSRoadId());
        SRoadTask task = webTaskService.insertOrUpdateTask(insertOrUpdateTask, "lima", "8f000ada10a24d49aca1ae4bbf24bf22", sRoad);
        SRoadTaskResponse sRoadTaskResponse = new SRoadTaskResponse();

        BeanUtils.copyProperties(task, sRoadTaskResponse);
        webRoadTaskPatient.setTaskPatient(task);
    }

    @Test
    public void test2() {
        GetPatientListVo getPatientListVo = new GetPatientListVo();
        getPatientListVo.setSRoadTaskId("1815941087614210048");
        List<TAdminInfo> patientListTask = sRoadTaskPatientsMapper.getPatientListTask(getPatientListVo);
        System.out.println(JSON.toJSONString(patientListTask));
    }


}