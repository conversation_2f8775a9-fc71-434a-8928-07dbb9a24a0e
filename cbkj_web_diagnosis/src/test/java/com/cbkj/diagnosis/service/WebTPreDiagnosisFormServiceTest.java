package com.cbkj.diagnosis.service;

import com.alibaba.fastjson2.JSON;
import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.webapi.business.vo.PaperListReVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/24 10:24
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class WebTPreDiagnosisFormServiceTest {
    @Autowired
    private WebTPreDiagnosisFormService webTPreDiagnosisFormService;

    @Test
    void getPaperList() {
        PaperListReVo paperListReVo = new PaperListReVo();
        paperListReVo.setFormType("2");
        String[] a = {"1","2"};
        paperListReVo.setSroadIds(a);
        webTPreDiagnosisFormService.getPaperList(paperListReVo,new Page());
    }
@Test
    public void  testGetPreDiagnosisForm(){
        System.out.println(JSON.toJSONString(
                webTPreDiagnosisFormService.getLookPaper("007536a6da904a0a80e49aeceedd09c5")
        ));
    }
}