package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.service.vo.CompletedQuestionnaireRe;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/4 09:35
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class CompletedQuestionnaireServiceTest {

    @Autowired
    private CompletedQuestionnaireService completedQuestionnaireService;
    @Test
    void downloadQuestionnaire() {
        CompletedQuestionnaireRe completedQuestionnaireRe = new CompletedQuestionnaireRe();
        completedQuestionnaireRe.setSroadIdsstr("1920687449040228352");
        completedQuestionnaireRe.setStartDate("2025-08-03 00:00:00");
        completedQuestionnaireRe.setEndDate("2025-09-03 23:59:59");
        completedQuestionnaireService.downloadQuestionnaire(completedQuestionnaireRe, null);
    }
}