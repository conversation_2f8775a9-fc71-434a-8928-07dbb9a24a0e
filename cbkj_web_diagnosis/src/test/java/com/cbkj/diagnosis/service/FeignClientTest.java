package com.cbkj.diagnosis.service;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.beans.business.GPTAskRequest;
import com.cbkj.diagnosis.common.openfeign.*;
import com.cbkj.diagnosis.common.openfeign.reqAndres.LargeModelRes;
import com.cbkj.diagnosis.common.openfeign.reqAndres.OCRModelRes;
import com.cbkj.diagnosis.common.openfeign.reqAndres.OcrResponseDTO;
import com.cbkj.diagnosis.common.utils.LocalMultipartFile;
import com.cbkj.diagnosis.service.webapi.business.vo.GetQuestionClassTypeInfo;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AESPKCS7Util;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24 11:06
 * @Version 1.0
 */
@AutoConfigureMockMvc
@EnableFeignClients
@EnableCircuitBreaker
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class FeignClientTest {

    @Autowired
    private TestFeignClient testFeignClient;

    @Autowired
    private LargeModelClient largeModelClient;

    @Autowired
    private OCRModelClient ocrModelClient;

    /**
     * 测试ocr上传图片
     * @throws IOException
     */
    @Test
    public void testOCRUpLoad() throws IOException {
        //从本地电脑上传图片
        File file = new File("C:\\Users\\<USER>\\Pictures\\DINGTALK_IM_1290060248.JPG"); // 本地文件路径
//        FileInputStream input = new FileInputStream(file);
//        MultipartFile multipartFile = new MockMultipartFile(
//                "file",                  // 表单字段名
//                file.getName(),          // 原始文件名
//                "image/png",             // Content-Type
//                input                    // 文件输入流
//        );
        LocalMultipartFile multipartFile = new LocalMultipartFile("file", file);
        OCRModelRes ocrModelRes = ocrModelClient.ocrImageUpload(multipartFile, "zjh-test", "Bearer app-eACKGVSxQf3nQX0SkAVFhhdz");
        System.out.println(ocrModelRes.toString());
    }

    /**
     * 测试把上传的图片作为入参调用ocr获取图片内容
     */
    @Test
    public void testOCRResult() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("response_mode", "blocking");
        map.put("user", "zjh-test");
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("document_url", "/files/ee6ff6d6-0a63-4a4a-ac5e-7fea1b2c176c/file-preview?timestamp=1757487609&nonce=bb176d6dc7a4c56fcbb923f3b72363e6&sign=rA3Y56jYtfYQeYJLwECZ-O6DjLR0RGWv8fKBmZruFJU=");
        map.put("inputs", objectObjectHashMap);
        OcrResponseDTO ocrResponseDTO = ocrModelClient.ocrResult(map, "Bearer app-eACKGVSxQf3nQX0SkAVFhhdz");
        System.out.println(JSON.toJSONString(ocrResponseDTO));
    }
    @Test
    public void test() {
        Object test = testFeignClient.test("123");
        System.out.println(JSON.toJSONString(test));
    }

    @Test
    /**
     * 测试大模型-电子病历
     */
    public void testLargeModel() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("appId", "123");
        map.put("insCode", "123");
        map.put("deptId", "123");
        map.put("userName", "医生1");
        map.put("origin", "tcm");
        map.put("userId", "123");
        String json = JSON.toJSONString(map);
        String s = null;
        try {
            s = AESPKCS7Util.encrypt(json, "99748ee4718e43cd", "base64");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String question = "1.您的身高是？165cm2.您的体重是？62.5kg3.请问您大便的情况？排便费力大便干燥排便次数减少排便不尽4.您每次排便时间？10-20分钟5.您大便的性状是？硬结状大肠样6.您的排便次数？（次/周）1-27.您的便秘情况持续多久了？5年8.您感觉便秘是哪些原因引起的呢？（可多选）饮食不当其他情况9.您的运动情况？（每次运动>10分钟）4-5次/周10.您有其他不适症状吗？（可多选）腹胀11.您之前进行过什么检查及治疗吗？（可多选）有检查12.您曾经做过什么检查吗？（可多选）结肠镜检查13.请问您是否有慢性病？（可多选）糖尿病高血压病14.您做过手术吗？没有15.请问您的饮食情况？胃口正常16.请问您的饮食口味偏好（可多选）喜食甜食口味清淡17.请问您的情绪状态是？（可多选）情绪稳定18.请问您的睡眠情况如何？（可多选）正常19.请问您有以下情况吗？（可多选）头晕胃口好手脚发冷20.请问您的小便情况？（可多选）尿急.请根据以上预问诊问答的内容生成专病电子病历，电子病历中的内容需全部来源于预问诊问答的内容，不能进行扩充。病历分类覆盖如下：主诉、现病史、既往史、个人史、过敏史、传染病史、手术史、输血史、家族史、月经史、体格检查，缺失的内容无需额外补充，并以json格式返回，注意json格式内容不要有空格，不要有换行符号。示例如下:[{\\\"content\\\":\\\"这里写主诉\\\",\\\"questionClassName\\\":\\\"主诉\\\"},{\\\"content\\\":\\\"这里写现病史\\\",\\\"questionClassName\\\":\\\"现病史\\\"},{\\\"content\\\":\\\".......\\\",\\\"questionClassName\\\":\\\"既往史\\\"}]";
        GPTAskRequest gptAskRequest = new GPTAskRequest();
        gptAskRequest.setAgent_id("a1e5a48bf35411efb9620242ac1e0004");
        gptAskRequest.setQuestion(question);
        gptAskRequest.setCbdata(s);
        ResEntity<LargeModelRes> largeModelResResEntity = largeModelClient.gptAsk(gptAskRequest);
        if (largeModelResResEntity.getCode() == 0){
            if (largeModelResResEntity.getData() != null){
                LargeModelRes data = largeModelResResEntity.getData();
                String s1 = LargeModelJsonExtractor.extractJson(data.getAnswer());
                //s1转成 List<GetQuestionClassTypeInfo>
                System.out.println("=============="+s1);
                try {
                    List<GetQuestionClassTypeInfo> getQuestionClassTypeInfos = JSON.parseArray(s1, GetQuestionClassTypeInfo.class);
                    System.out.println("=============="+JSON.toJSONString(getQuestionClassTypeInfos));
                } catch (Exception e) {
                    System.out.println("=============="+e.getMessage());
                }

            }
        }

    }
}
