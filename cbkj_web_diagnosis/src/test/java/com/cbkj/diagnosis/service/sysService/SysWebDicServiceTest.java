package com.cbkj.diagnosis.service.sysService;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/30 13:33
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SysWebDicServiceTest {

    @Autowired
    private  SysWebDicService sysWebDicService;



    @Test
    public void getEleDisDicList() {
        ResEntity eleDisDicList = sysWebDicService.getEleDisDicList();

        System.out.println(JSON.toJSONString(eleDisDicList));
    }
}