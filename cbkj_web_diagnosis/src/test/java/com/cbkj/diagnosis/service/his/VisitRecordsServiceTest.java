package com.cbkj.diagnosis.service.his;

import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.common.http.XiaMenTemplate;
import com.cbkj.diagnosis.service.TPropagandaEduService;
import com.cbkj.diagnosis.beans.business.EduSendMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/18 15:53
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class VisitRecordsServiceTest {

    @Resource
    private VisitRecordsService visitRecordsService;

    @Resource
    private XiaMenTemplate xiaMenTemplate;

    @Resource
    private XiaMenSendWXMessage xiaMenSendWXMessage;

    @Resource
    private TPropagandaEduService tPropagandaEduService;

//    @Test
//    public void testSendWx(){
//        xiaMenSendWXMessage.sendMessageByWxTemplate("***********","f16601b3b50211ee85090050569411b9",
//                null,"https://www.xmtcm.com/pdfu/mobile/login?cbdata=bdNf0KlySD8yz0+J/YuxqJ5M+tfkFscNzNxOmVxMZceAqZ7VuoTWs2jYCiC1sLk0EJR0qNIPivlwXwS/AGm+ut8akp8dz+PRcUAXuGxV6Y1UYIDgfHps7hRp/QwdO84K1UOT3euN98OBPhrTEE63zcwvU2BWVCwy16K+FGzcm/pLjcVlLwmL2OyrOzNo/dFq70k/f67Pf+VQK9pbZfmdGPs7WxtWx8bbAxW5RRj0Xrdq0pnokRID/oEYTMQFv1Xe&jumpUrl=doctorfollowup%3Fyuzhen%3D1%26diaId%3Da49e679576c84a8282089ae70880c39d",
//                "患者李辉1|医生李辉2|测试科室1|测试名称");
//    }
//    @Test
//    void getPreDiagnosis() {
//        String recId = "_bMSapGaPHmuTC_nNLXUi";
//        GetPreDiagnosis getPreDiagnosis = new GetPreDiagnosis();
//        getPreDiagnosis.setRecId(recId);
//        Object preDiagnosis = visitRecordsService.getPreDiagnosis(getPreDiagnosis);
//
//        System.out.println(preDiagnosis.toString());
//    }
    @Test
    public void test22(){
        EduSendMessage eduSendMessage = new EduSendMessage();
        eduSendMessage.setEduTitle("123123");
        eduSendMessage.setTPropagandaEduId(31);
        eduSendMessage.setRoadExecuteEventWay("1");
        List<TAdminInfo> tAdminInfoList = new ArrayList<TAdminInfo>();
        TAdminInfo tAdminInfo = new TAdminInfo();
        tAdminInfo.setUserId("74fe4e3bc31a47a6a477e6819d6f8033");
        tAdminInfoList.add(tAdminInfo);
        eduSendMessage.setPatientList(tAdminInfoList);
        tPropagandaEduService.sendMessage(eduSendMessage);
    }

//    @Test
//    void testxiaMenTemplate() {
//
//
//
//        XiaMenResEntity xiaMenResEntity = xiaMenTemplate.callWsdlService("<root>\n" +
//                "<RECID>记录id </RECID>\n" +
//                "<DIAID>预诊单id </DIAID>\n" +
//                "<CREATEDATE>创建日期</CREATEDATE>\n" +
//                "<DIACONTENT>预诊单信息，患者答题结构化文本内容</DIACONTENT>\n" +
//                "<PATIENTIDCARD>证件号码</PATIENTIDCARD>\n" +
//                "<PATIENTCARDTYPE>证件类型</PATIENTCARDTYPE>\n" +
//                "<HEALTHCARDNUM>居民健康卡号</HEALTHCARDNUM>\n" +
//                "<PATIENTNAME>姓名</PATIENTNAME>\n" +
//                "<QUESTIONID>题目id </QUESTIONID>\n" +
//                "<QUESTIONNAME>题目名</QUESTIONNAME>\n" +
//                "<QUESTIONTYPE>题目类型（1文本 2单选 3多选 4时间）</QUESTIONTYPE>\n" +
//                "<ANSWERCONTENT>患者选择的题目选项的内容</ANSWERCONTENT>\n" +
//                "<ANSWERCONTENTID>患者选择的题目选项的id </ANSWERCONTENTID>\n" +
//                "<DATEUNIT>时间类型题目</DATEUNIT>\n" +
//                "<YEAR>年</YEAR>\n" +
//                "<MONTH>月</MONTH>\n" +
//                "<DAY>日</DAY>\n" +
//                "<HOUR>小时</HOUR>\n" +
//                "<OPTIONID>选项id </OPTIONID>\n" +
//                "<OPTIONNAME>选项名</OPTIONNAME>\n" +
//                "<OPTIONTYPE>选项类型（1文本 2症状）</OPTIONTYPE> \n" +
//                "</root>","");
//        System.out.println(xiaMenResEntity.getMessage());
//    }

//    @Test
//    public void test2(){
//        xiaMenTemplate.callWsdlSendWxTemplate("假装这是json字符串","11111111111111");
//    }
}