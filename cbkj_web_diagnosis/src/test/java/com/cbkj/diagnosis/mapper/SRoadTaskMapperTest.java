package com.cbkj.diagnosis.mapper;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.task.webvo.RoadTaskConditons;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.sysmapper.AdminInfoMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskMapper;
import com.cbkj.diagnosis.service.webapi.business.vo.GetPatientListVo;
import com.cbkj.diagnosis.service.webtask.WebTaskService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/22 14:33
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SRoadTaskMapperTest {

    @Autowired
    private WebTaskService webTaskService;

    @Autowired
    private AdminInfoMapper adminInfoMapper;

    @Test
    public void test() {
        RoadTaskConditons roadTaskConditon = webTaskService.getRoadTaskConditon("1814224023493021696");
        System.out.println(JSON.toJSONString(roadTaskConditon));
    }


    @Test
    public void testgetPatientList() {
        GetPatientListVo getPatientListVo = new GetPatientListVo();
        getPatientListVo.setSRoadTaskId("1815941087614210048");
        webTaskService.getPatientList(getPatientListVo, new Page());
    }

    @Test
    public void testgetPatientFilterList() {
        List<AdminInfo> adminList = adminInfoMapper.getAdminList(null);
        System.out.println(JSON.toJSONString(adminList));
    }
}

