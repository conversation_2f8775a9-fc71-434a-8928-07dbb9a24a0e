package com.cbkj.diagnosis.mapper;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.DiagnosisApiApplication;
import com.cbkj.diagnosis.beans.diagnosisstructure.EmrItem;
import com.cbkj.diagnosis.beans.diagnosisstructure.EmrList;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisStructureMapper;
import com.cbkj.diagnosis.service.business.DiagnosisStructureUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/15 11:30
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagnosisApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TPreDiagnosisStructureMapperTest {
    @Autowired
    private TPreDiagnosisStructureMapper tPreDiagnosisStructureMapper;
    @Autowired
    private DiagnosisStructureUtils diagnosisStructureUtils;

    @Test
    public void test1() {
        EmrList diagnosisStructure = diagnosisStructureUtils.getDiagnosisStructure("1");
        log.info("emrList:{}", JSON.toJSONString(diagnosisStructure));
    }
}
