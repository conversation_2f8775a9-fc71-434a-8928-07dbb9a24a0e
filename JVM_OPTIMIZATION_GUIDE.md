# JVM性能优化指南

## 针对大数据量Excel生成的JVM优化配置

### 1. 堆内存优化

#### 基础配置
```bash
# 设置初始堆大小和最大堆大小
-Xms4g -Xmx8g

# 设置新生代大小（建议为堆大小的1/3到1/2）
-Xmn2g

# 或者使用比例设置
-XX:NewRatio=2  # 新生代:老年代 = 1:2
```

#### 针对3600条数据优化
```bash
# 推荐配置（根据服务器内存调整）
-Xms2g -Xmx4g -Xmn1g
```

### 2. 垃圾回收器优化

#### G1GC配置（推荐）
```bash
# 使用G1垃圾回收器
-XX:+UseG1GC

# 设置最大GC暂停时间目标（毫秒）
-XX:MaxGCPauseMillis=200

# 设置并行GC线程数
-XX:ParallelGCThreads=8

# 设置并发GC线程数
-XX:ConcGCThreads=2

# G1堆区域大小
-XX:G1HeapRegionSize=16m

# 启动并发GC的堆占用百分比
-XX:G1MixedGCCountTarget=8
-XX:G1MixedGCLiveThresholdPercent=85
```

#### ParallelGC配置（备选）
```bash
# 使用并行垃圾回收器
-XX:+UseParallelGC
-XX:+UseParallelOldGC

# 设置并行GC线程数
-XX:ParallelGCThreads=8

# 自适应大小策略
-XX:+UseAdaptiveSizePolicy
```

### 3. 内存管理优化

#### 直接内存配置
```bash
# 设置直接内存大小（用于NIO操作）
-XX:MaxDirectMemorySize=1g
```

#### 元空间配置
```bash
# 设置元空间大小
-XX:MetaspaceSize=256m
-XX:MaxMetaspaceSize=512m
```

#### 压缩指针
```bash
# 启用压缩普通对象指针（64位JVM默认启用）
-XX:+UseCompressedOops
-XX:+UseCompressedClassPointers
```

### 4. 编译优化

#### JIT编译器优化
```bash
# 设置编译阈值
-XX:CompileThreshold=10000

# 启用分层编译
-XX:+TieredCompilation

# 设置编译线程数
-XX:CICompilerCount=4
```

### 5. 完整的生产环境JVM参数

#### 高性能配置
```bash
java -server \
  -Xms4g -Xmx8g -Xmn2g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:ParallelGCThreads=8 \
  -XX:ConcGCThreads=2 \
  -XX:G1HeapRegionSize=16m \
  -XX:MaxDirectMemorySize=1g \
  -XX:MetaspaceSize=256m \
  -XX:MaxMetaspaceSize=512m \
  -XX:+UseCompressedOops \
  -XX:+TieredCompilation \
  -XX:CICompilerCount=4 \
  -XX:+UnlockExperimentalVMOptions \
  -XX:+UseStringDeduplication \
  -jar your-application.jar
```

#### 中等配置（适合3600条数据）
```bash
java -server \
  -Xms2g -Xmx4g -Xmn1g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=100 \
  -XX:ParallelGCThreads=4 \
  -XX:ConcGCThreads=1 \
  -XX:MaxDirectMemorySize=512m \
  -XX:MetaspaceSize=128m \
  -XX:MaxMetaspaceSize=256m \
  -XX:+UseCompressedOops \
  -jar your-application.jar
```

### 6. GC日志配置

#### 详细GC日志
```bash
# JDK 8
-XX:+PrintGC \
-XX:+PrintGCDetails \
-XX:+PrintGCTimeStamps \
-XX:+PrintGCDateStamps \
-Xloggc:gc.log \
-XX:+UseGCLogFileRotation \
-XX:NumberOfGCLogFiles=5 \
-XX:GCLogFileSize=100M

# JDK 11+
-Xlog:gc*:gc.log:time,tags \
-XX:+UseGCLogFileRotation \
-XX:NumberOfGCLogFiles=5 \
-XX:GCLogFileSize=100M
```

### 7. 监控和调试参数

#### 性能监控
```bash
# 启用JFR（Java Flight Recorder）
-XX:+FlightRecorder \
-XX:StartFlightRecording=duration=60s,filename=app-profile.jfr

# 启用JMX
-Dcom.sun.management.jmxremote \
-Dcom.sun.management.jmxremote.port=9999 \
-Dcom.sun.management.jmxremote.authenticate=false \
-Dcom.sun.management.jmxremote.ssl=false
```

### 8. 应用级别优化

#### Spring Boot配置
```properties
# application.properties
# 设置Tomcat线程池
server.tomcat.threads.max=200
server.tomcat.threads.min-spare=10
server.tomcat.max-connections=8192
server.tomcat.accept-count=100

# 设置连接超时
server.tomcat.connection-timeout=20000

# 设置最大HTTP头大小
server.max-http-header-size=8KB
```

### 9. 性能测试和调优

#### 压力测试命令
```bash
# 使用JMeter或AB进行压力测试
ab -n 100 -c 10 http://localhost:8080/download-questionnaire

# 监控GC性能
jstat -gc -t <pid> 5s

# 监控内存使用
jmap -histo <pid>

# 生成堆转储
jmap -dump:format=b,file=heap.hprof <pid>
```

### 10. 预期性能提升

| 优化项目 | 预期提升 | 说明 |
|---------|---------|------|
| G1GC优化 | 20-30% | 减少GC暂停时间 |
| 堆内存调优 | 15-25% | 减少GC频率 |
| 直接内存优化 | 10-15% | 提升NIO性能 |
| JIT编译优化 | 5-10% | 提升代码执行效率 |

**整体预期**：通过JVM优化，可以额外获得 **30-50%** 的性能提升

### 11. 监控指标

#### 关键指标
- **GC频率**：目标 < 1次/分钟
- **GC暂停时间**：目标 < 100ms
- **内存使用率**：目标 < 80%
- **CPU使用率**：目标 < 70%

#### 监控工具
- **JVisualVM**：可视化监控
- **JProfiler**：专业性能分析
- **Arthas**：在线诊断工具
- **Prometheus + Grafana**：生产环境监控

### 12. 故障排查

#### 常见问题
1. **OutOfMemoryError**: 增加堆内存或优化内存使用
2. **GC频繁**: 调整新生代大小或GC参数
3. **响应慢**: 检查GC日志和线程状态

#### 排查命令
```bash
# 查看JVM参数
jinfo -flags <pid>

# 查看线程状态
jstack <pid>

# 查看内存使用
jmap -histo <pid> | head -20
```
