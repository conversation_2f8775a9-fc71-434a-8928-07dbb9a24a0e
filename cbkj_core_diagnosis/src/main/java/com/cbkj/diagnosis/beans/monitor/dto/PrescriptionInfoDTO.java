package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zbh on 2024/6/5 15:03
 *
 * @description：处方信息
 */
@Getter
@Setter
@Schema(description =  "处方信息")
public class PrescriptionInfoDTO {


    @Schema(description =  "中医处方")
    private TCMPrescriptionParentDTO tcmPrescriptionParentDTO;

    @Schema(description =  "西医处方")
    private MedicalWestPrescriptionsParentDTO medicalWestPrescriptionsParentDTO;
}
