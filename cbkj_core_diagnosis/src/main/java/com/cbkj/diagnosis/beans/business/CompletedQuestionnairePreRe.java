package com.cbkj.diagnosis.beans.business;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompletedQuestionnairePreRe implements Serializable {

//    @Schema(description =  "时间类型：1.填写时间、2发送时间")
//    private Integer dateType;
    @Schema(description =  "开始时间 时分秒传 00:00:00")
    private String startDate;

    @Schema(description =  "结束时间 时分秒传 23:59:59")
    private String endDate;

//    private String formName;

//    @Schema(description =  "随访任务id")
//    private String sRoadTaskId;
//
//    @Schema(description =  "随访任务名称。")
//    private String taskName;
//
//
//    private String roadExecuteEventWay;
//    private String patientName;
    @CBKJEncryptField
    @Schema(description =  "患者证件号/患者姓名")
    private String textSearch;

    @Schema(description =  "疾病id")
    private String[] disIdsList;

    private String disIds;

    @Schema(hidden = true)
    private String userId;
    @Schema(hidden = true)
    private String diaId;

}
