package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Getter
@Setter
@TableName("t_sys_param")
@Schema(description = "TSysParam对象")
public class TSysParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "参数ID")
    @TableId("PAR_ID")
    private String parId;

    @Schema(description ="参数代码")
    @TableField("PAR_CODE")
    private String parCode;

    @Schema(description ="参数名称")
    @TableField("PAR_NAME")
    private String parName;

    @Schema(description ="参数值")
    @TableField("PAR_VALUES")
    private String parValues;

    @Schema(description ="创建时间")
    @TableField("CREATE_DATE")
    private Date createDate;

    @Schema(description ="创建人")
    @TableField("CREATE_USER")
    private String createUser;

    @Schema(description ="创建人姓名")
    @TableField("create_user_name")
    private String createUserName;

    @Schema(description ="是否有效（1删除 0有效 2禁用） 默认0")
    @TableField("status")
    private String status;

    @Schema(description ="参数说明 ，备注 项目名称等")
    @TableField("param_desc")
    private String paramDesc;

    @Schema(description ="排序")
    @TableField("sort")
    private Integer sort;


}
