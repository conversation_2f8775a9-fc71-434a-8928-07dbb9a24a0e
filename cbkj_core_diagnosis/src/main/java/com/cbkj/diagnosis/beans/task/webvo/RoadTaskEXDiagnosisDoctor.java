package com.cbkj.diagnosis.beans.task.webvo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/19 16:30
 * @Version 1.0
 */
@Schema
@Data
public class RoadTaskEXDiagnosisDoctor {

    @Schema(description="随访成员医生id")
    private String diagnosisDoctorId;
    private String diagnosisDoctorName;


}
