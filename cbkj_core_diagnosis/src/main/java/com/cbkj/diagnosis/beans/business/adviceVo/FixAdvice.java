package com.cbkj.diagnosis.beans.business.adviceVo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/2 10:10
 * @Version 1.0
 */
@Data
@Schema(description = "修复建议")
public class FixAdvice {
    @Schema(description="意见建议主键id，自增长")
    private Long id;
    @Schema(description="受理意见")
    private String proposalReceiveOpinion;
}
