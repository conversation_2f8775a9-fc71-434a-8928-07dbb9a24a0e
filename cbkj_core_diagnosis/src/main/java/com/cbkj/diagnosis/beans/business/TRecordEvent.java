package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Getter
@Setter
@TableName("t_record_event")
@Schema(description =  "TRecordEvent对象")
public class TRecordEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("rec_id")
    private String recId;

    @TableField("dic_id")
    private String dicId;

    @TableField("dic_code")
    private String dicCode;

    @TableField("dic_name")
    private String dicName;


}
