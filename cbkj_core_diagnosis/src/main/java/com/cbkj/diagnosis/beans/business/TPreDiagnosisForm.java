package com.cbkj.diagnosis.beans.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class TPreDiagnosisForm implements Serializable {

    @Schema(description =  "预诊单ID")
    private String diaId;

    @Schema(description =  "中医疾病名称（多个/拼接）")
    private String chineseDisName;

    @Schema(description =  "西医疾病名称（多个/拼接）")
    private String westDisName;

    @Schema(description =  "总题数")
    private Integer totalQuestionNum;

    @Schema(description =  "最长路径答题数")
    private Integer longestQuestionNum;

    @Schema(description =  "最短路径答题数")
    private Integer shortestQuestionNum;

    @Schema(description =  "问诊单类型（1 专病预诊单   2、全科）如果是随访 3.随访问卷4.自测量表")
    private String diaType;

    @Schema(description =  "创建时间")
    private Date createDate;

    @Schema(description =  "创建人")
    private String createUser;

    @Schema(description =  "创建人姓名")
    private String createUsername;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "修改时间")
    private Date updateDate;

    @Schema(description =  "修改人")
    private String updateUser;

    @Schema(description =  "修改人姓名")
    private String updateUsername;

    @Schema(description =  "删除时间")
    private Date delDate;

    @Schema(description =  "删除人")
    private String delUser;

    @Schema(description =  "删除人姓名")
    private String delUsername;

    @Schema(description =  "状态（0正常   1删除  2禁用）")
    private String status;

    @Schema(description =  "1.预诊2 随访")
    private String formType;

    @Schema(description =  "随访问卷类型（1.随访问卷2.自测量表）,随访必传这个字段")
    private String formSuiFangType;

    @Schema(description =  "随访名称")
    private String formName;
    private String appId;
    private String insId;
    private String insCode;

    @Schema(description =  "表单编码")
    private String formCode;

    @Schema(description =  "预诊随访问卷生成二维码地址")
    private String hisDiagnosisFormUrl;

    @Schema(description =  "是否展示")
    private boolean showStatus;

    /**
     * 这里是为了预诊界面 手动做随访单子，的接口需要这个字段值 不然前端默认传3
     */
    @Schema(description =  "3.电话（自动+手动）、4.面访（手动）、5.其他（手动）")
    private String  roadExecuteEventWay;
    /**
     * 这个字段也是
     */
    @Schema(description =  "电话随访排期状态：2待随访4已访未完成5空号6.错号8.完成排期随访")
    private Integer taskExcuteStatus;

    @Schema(description =  "是否展示大模型：0开1关")
    private Integer cueWordStatus;
    @Schema(description =  "提示词")
    private String cueWordText;
    @Schema(description =  "大模型转换词")
    private String cueWordTrans;

    @Schema(description =  "多个科室,分隔")
    private String deptIds;

    @Schema(description =  "科室预诊单名称")
    private String deptDiaName;


}
