package com.cbkj.diagnosis.beans.task.webvo;

import com.cbkj.diagnosis.beans.task.RoadTaskConditonsSQLResult;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/19 16:24
 * @Version 1.0
 */
@Data
@Schema
public class RoadTaskConditons {
    @Schema(description =  "随访任务")
    private String sRoadTaskId;

//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description="自动入组条件：就诊开始时间")
    private String recordStartTime;

//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description="自动入组条件：就诊结束时间")
    private String recordEndTime;

    @Schema(description="自动入组条件：就诊当日几天内有预诊信息的患者")
    private Integer limitDiagnosisDaysInfo;

    @Schema(description="自动入组条件：就诊当日几天内有预诊信息的患者-0不勾1勾")
    private Integer limitDiagnosisDaysInfoCheck;

    @Schema(description="自动入组条件：几日内重复就诊的患者不入组")
    private Integer limitRepeatRecord;
    @Schema(description="自动入组条件：几日内重复就诊的患者不入组-0不勾 1勾")
    private Integer limitRepeatRecordCheck;

    @Schema(description="是否过滤不愿意随访的患者0.是 1.否")
    private String joinRoadTask;
    @Schema(description="自动入组条件：科室和科室名称")
    private List<RoadTaskConditonsSQLResult> deptList;
    @Schema(description="自动入组条件：随访成员医生id和名字")
    private List<RoadTaskConditonsSQLResult> diagnosisDoctorList;

    @Schema(description="自动入组条件：就诊医生id和名字")
    private List<RoadTaskConditonsSQLResult> recordDoctorList;



}
