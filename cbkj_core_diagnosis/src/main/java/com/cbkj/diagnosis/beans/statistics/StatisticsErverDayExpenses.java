package com.cbkj.diagnosis.beans.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Getter
@Setter
@TableName("statistics_erver_day_expenses")
@Schema(description =  "StatisticsErverDayExpenses对象")
public class StatisticsErverDayExpenses implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="诊疗费用统计")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("create_time")
    private Date createTime;

    @TableField("app_id")
    private String appId;

    @TableField("ins_id")
    private String insId;

    @TableField("ins_code")
    private String insCode;

    @TableField("ins_name")
    private String insName;

    @TableField("dis_id")
    private String disId;

    @TableField("dis_name")
    private String disName;

    @TableField("dis_code")
    private String disCode;

    @TableField("num")
    private Integer num;

    @Schema(description="1.中药处方2.中成药3.现代医学检查检查4.中医适宜技术")
    @TableField("type")
    private Integer type;


}
