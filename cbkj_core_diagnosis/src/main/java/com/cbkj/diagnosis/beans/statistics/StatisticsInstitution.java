package com.cbkj.diagnosis.beans.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Getter
@Setter
@TableName("statistics_institution")
@Schema(description =  "StatisticsInstitution对象")
public class StatisticsInstitution implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description="机构名称")
    @TableField("ins_name")
    private String insName;

    @Schema(description="机构代码")
    @TableField("ins_code")
    private String insCode;

    @TableField("app_id")
    private String appId;

    @Schema(description="地区")
    @TableField("ins_area")
    private String insArea;


}
