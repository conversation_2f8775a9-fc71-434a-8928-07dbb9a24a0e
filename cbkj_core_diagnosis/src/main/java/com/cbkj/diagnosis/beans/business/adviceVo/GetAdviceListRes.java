package com.cbkj.diagnosis.beans.business.adviceVo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/2 10:13
 * @Version 1.0
 */
@Data
public class GetAdviceListRes {
    private static final long serialVersionUID = 1L;

    @Schema(description="意见建议主键id，自增长")
    private Long id;

    @Schema(description="标题限制20字")
    private String proposalTitle;

    @Schema(description="建议内容限制500字")
    private String proposalContent;


    @Schema(description="反馈类型：1建议，2问题 3.咨询")
    @TableField("proposal_type")
    private Integer proposalType;

    @Schema(description="联系方式")
    private String proposalLiaison;

    @Schema(description="受理状态：0受理，1未受理")
    private Integer proposalReceiveState;


    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description="创建者ID")
    private String createUserId;

    @Schema(description="创建者姓名")
    private String createUserName;
    @Schema(description="受理意见")
    @TableField("proposal_receive_opinion")
    private String proposalReceiveOpinion;

    @Schema(description="来源（1.医生端2.患者端）")
    private String sourceFrom;
    @Schema(description="建议图片")
    private List<ImageUploadRes> adviceImages;


}
