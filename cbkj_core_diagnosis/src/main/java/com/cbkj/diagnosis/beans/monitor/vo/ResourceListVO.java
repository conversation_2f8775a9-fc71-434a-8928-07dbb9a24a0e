package com.cbkj.diagnosis.beans.monitor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

/**
 * Created by zbh on 2024/6/27 11:16
 *
 * @description：资源列表请求体-数据库交互层
 */
@Getter
@Setter
public class ResourceListVO {

    /**
     * 中医疾病id
     */
    private List<String> chineseDisIds;


    /**
     * 患者姓别
     */
    private String patientSex;

    /**
     * 患者最小年龄
     */
    private String patientMinAge;

    /**
     * 患者最大年龄
     */
    private String patientMaxAge;

    /**
     * 医疗机构id
     */
    private List<String> insCodes;

    private String patientName;

    /**
     * 监测开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String monitorStartDateStr;

    /**
     * 监测结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String monitorEndDateStr;

    /**
     * 预诊,诊前
     */
    private String recordTypeCode1;

    /**
     * 就诊，诊中
     */
    private String recordTypeCode2;

    /**
     * 宣教
     */
    private String recordTypeCode3;

    /**
     * 随访，诊后
     */
    private String recordTypeCode4;

    /**
     * 病案状态
     * 病案状态 0:创建(产集中)，1:结案，2:归档，3:上传成功，4：上传失败
     */
    private String closedStatus;

}
