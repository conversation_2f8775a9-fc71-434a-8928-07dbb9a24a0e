package com.cbkj.diagnosis.beans.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Getter
@Setter
@TableName("statistics_erver_day_health")
@Schema(description =  "StatisticsErverDayHealth对象")
public class StatisticsErverDayHealth implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="健康宣教")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("num")
    private Integer num;

    @Schema(description="统计记录日期")
    @TableField("create_time")
    private Date createTime;

    @TableField("app_id")
    private String appId;

    @TableField("ins_code")
    private String insCode;

    @TableField("ins_id")
    private String insId;

    @TableField("ins_name")
    private String insName;

    @TableField("dis_id")
    private String disId;

    @TableField("dis_name")
    private String disName;

    @TableField("dis_code")
    private String disCode;


}
