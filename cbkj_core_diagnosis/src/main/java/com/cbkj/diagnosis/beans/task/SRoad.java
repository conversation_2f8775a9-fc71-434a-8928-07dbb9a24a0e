package com.cbkj.diagnosis.beans.task;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SRoad implements Serializable{

    @Schema(description =  "随访-路径id")
    private String sRoadId;

    @Schema(description =  "随访-路径名")
    private String sRoadName;

    @Schema(description =  "0正常1删除2.隐藏（任务中创建选择不保存）3.停用")
    private String status;

    @Schema(description =  "插入日期")
    private Date createTime;

    @Schema(description =  "创建者id")
    private String createUserId;

    @Schema(description =  "创建者名字")
    private String createUserName;

    @Schema(description =  "删除日期")
    private Date delTime;

    @Schema(description =  "删除用户id")
    private String delUserId;

    @Schema(description =  "删除用户名字")
    private String delUserName;

    @Schema(description =  "1.手动2.自动")
    private String sRoadGroupWay;


}
