package com.cbkj.diagnosis.beans.monitor.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Created by zbh on 2024/6/4 17:32
 *
 * @description：就诊记录信息
 */
@Setter
@Getter
public class MedicalRecordsDTO  {

    @Schema(description =  "就诊机构")
    private String insName;

    @Schema(description =  "就诊科室")
    private String deptName;

    @Schema(description =  "医生")
    private String doctorName;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "就诊日期")
    private Date recordTime;

    @Schema(description =  "患者年龄")
    private String patientAge;

    @Schema(description =  "患者性别")
    private String patientSex;

    @Schema(description =  "是否初诊")
    private String isFirstVisit;

    @Schema(description =  "门（急）诊号")
    private String diagnosticNo;

    @Schema(description =  "怀孕")
    private String isPregnant;

    @Schema(description =  "哺乳期")
    private String isLactationPeriod;

    @Schema(description =  "发病节气")
    private String solarTerm;

    @Schema(description =  "治疗类别")
    private String treatmentCategory;

    @Schema(description =  "传染病历")
    private String isInfectiousHistory;

    @Schema(description =  "科研病历")
    private String scientificRecord;

    @Schema(description =  "病史信息")
    private MedicalHistoryDTO medicalHistoryDTO;

    @Schema(description =  "中医四诊")
    private TCMDiagnosticMethodsDTO tcmDiagnosticMethodsDTO;

    @Schema(description =  "辨证分析")
    private DialecticalAnalysisDTO dialecticalAnalysisDTO;

    @Schema(description =  "辅助检查")
    private AuxiliaryInspectionDTO auxiliaryInspectionDTO;

    @Schema(description =  "治疗建议")
    private TreatmentSuggestionDTO treatmentSuggestionDTO;

    @Schema(description =  "医师签名")
    private String doctorSign;

}
