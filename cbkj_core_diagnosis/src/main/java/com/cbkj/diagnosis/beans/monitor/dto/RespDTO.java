package com.cbkj.diagnosis.beans.monitor.dto;

import java.io.Serializable;

/**
 * @Description: 通用返回格式
 * @author: wang<PERSON>
 * @time: 2024/03/29 14:55
 */
public class RespDTO<T> implements Serializable {


    public String code = "200";
    public String message = "SUCCESS";
    public T data;
    public boolean status = true;


    public static String SUCCESS = "200";

    public static String ERROR = "500";


    public static RespDTO onSuc(Object data) {
        RespDTO resp = new RespDTO();
        resp.data = data;
        return resp;
    }

    public static RespDTO onSucBoth(String code, String msg, Boolean status, Object data) {
        RespDTO resp = new RespDTO();
        resp.code = code;
        resp.message = msg;
        resp.status = status;
        resp.data = data;
        return resp;
    }

    public static RespDTO onError(String errMsg) {
        RespDTO resp = new RespDTO();
        resp.code = "-1";
        resp.message = errMsg;
        resp.status = false;
        return resp;
    }

    @Override
    public String toString() {
        return "RespDTO{" +
                "code='" + code + '\'' +
                ", msg='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
