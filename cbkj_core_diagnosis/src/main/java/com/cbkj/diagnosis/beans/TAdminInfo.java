package com.cbkj.diagnosis.beans;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class TAdminInfo implements Serializable {
    @CBKJEncryptField
    @CBKJDecryptField
    private String cardNumber;
    @Schema(description="患者用户id")
    private String userId;
    @Schema(description="患者性别")
    private String sex;
    private String nation;

    @Schema(description =  "国籍代码")
    private String nationality;

    @CBKJEncryptField
    @CBKJDecryptField
    private String wxNiceName;


    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description="患者名字")
    private String userName;

    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description="患者手机号")
    private String mobile;


    @Schema(description="患者手年龄")
    private String age;

    private String openId;


    private String wxHeadImg;

    private String identity;

    private Integer shareNum;

    private Integer shareClickNum;

    private String shareUserId;

    private String expertise;

    private String expect;

    private Date createTime;

    private String smsCode;

    private String expectxh;

    //private String recordId;

    private String circleId;

    private String tokenId;

    private Long expiresin;

    private String oauthType;

    private String circlePwd;

    private String password;


    private String idcard;

    private String aesStr;

    private String name;

    private String MAK;

    private String weight;

    private String height;

    private String bqdata;
    private String lastRecordsId;
    @Schema(description="末次就诊中医疾病")
    private String lastChineseDisName;
    @Schema(description="末次就诊西医疾病名")
    private String lastWestDisName;

    @CBKJDecryptField
    @CBKJEncryptField
    @Schema(description="末次就诊医生名字")
    private String lastDoctorName;
    @Schema(description="末次就诊证型")
    private String lastSymName;
    @Schema(description="备注")
    private String remark;

    @Schema(description="是否能自动加入计划0.可以 1.不行")
    private String joinRoadTask;
    @Schema(description="患者任务的状态 1正常 3取消")
    private String taskPatientStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description="就诊时间（末次）")
    private Date lastRecordsTime;


    @Schema(description="主账号的id")
    private String masterUserId;
    private String recordsId;


    @Schema(description =  "身份证件类别代码")
    private String patientCardType;

    @Schema(description =  "居民健康卡号")
    private String healthCardNum;

    @Schema(description =  "城乡居民健康档案编号")
    private String healthFilesNum;

    @Schema(description =  "婚姻状况代码")
    private String maritalStatus;

    @Schema(description =  "学历代码")
    private String educationCode;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "联系人姓名")
    private String contactsName;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "联系人电话号码")
    private String contactsPhone;

    @Schema(description =  "联系人与患者的关系代码")
    private String contactsRelationship;
    private String lastDeptName;
    private String lastDoctorId;

    @Schema(description =  "是否有预诊：0否 1是")
    private Integer hasDiagnosis;

    @Schema(description =  "医生id", hidden = true)
    private String doctorId;

    @Schema(description =  "医生姓名", hidden = true)
    private String doctorName;

    @Schema(description =  "患者就诊卡号")
    private String medicalCard;

    @Schema(description =  "挂号记录，此处是暂存接口入参")
    private String regPlanId;
    private String appId;
    private String insCode;
    private String insId;
    private String insName;


    public String getLastDeptName() {
        return lastDeptName;
    }

//    public boolean equals(Object o) {
//        if(o==null) {
//            return true;
//        }
//        TAdminInfo obj = (TAdminInfo)o;
//        if(this.hashCode()==obj.hashCode()) {
//            return true;
//        }
//        return false;
//    }


}