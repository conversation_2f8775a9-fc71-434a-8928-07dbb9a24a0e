package com.cbkj.diagnosis.beans.business.requestvo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Schema
public class ListByTaskPatientsIdSearch {

    private String patientId;
    private String sRoadTaskId;

//    private String startDate;
//    private String endDate;


    @CBKJEncryptField
    @CBKJDecryptField
    private String patientName;

//    @Schema(description="患者id")
//    private String patientId;

    @Schema(description="身份证号码")
    @CBKJEncryptField
    @CBKJDecryptField
    private String patientCardNumber;
    @CBKJEncryptField
    private String patientIdcard;

    @Schema(description="中西医疾病名")
    private String diseaseName;
    @Schema(description =  "时间开始")
    private String startDate;

    @Schema(description =  "时间结束")
    private String endDate;
    private String userId;
    private String recId;
    private Integer phoneStatus;


    @Schema(description="-1过期 0 完成 今日任务也会传1 2 计划中")
    private Integer toDayTaskStatus; ;
}
