package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SRoadTaskPresMapping implements Serializable{

    @Schema(description =  "处方id")
    private String prescriptionsId;

    @Schema(description =  "任务id")
    private String sRoadTaskId;

    @Schema(description =  "患者id")
    private String patientId;


}
