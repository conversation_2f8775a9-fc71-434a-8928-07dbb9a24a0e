package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SRoadTaskPatientsMapping implements Serializable{

    @Schema
    private String sRoadTaskId;

    @Schema
    private String patientId;
    private String recordsId;

    @Schema(description =  "患者对应整个任务状态1正常3取消")
    private String status;
    private Date createTime;


}
