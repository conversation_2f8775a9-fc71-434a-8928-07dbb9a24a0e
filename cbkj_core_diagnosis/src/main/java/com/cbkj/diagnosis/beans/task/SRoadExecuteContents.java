package com.cbkj.diagnosis.beans.task;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SRoadExecuteContents implements Serializable {

    @Schema
    private Integer roadExecuteContentsId;

    @Schema(description =  "随访路径执行方案")
    private Integer roadExecuteId;

    @Schema(description =  "执行的具体内容类型1.健康宣教2.随访问卷（随访的一种类型）3.复诊提醒4.自测量表（随访的一种类型）")
    private String roadExecuteEventType;

    @Schema(description =  "执行内容所在主键id")
    private String roadExecuteEventContentId;

    @Schema(description =  "执行内容所名称-复诊不需要这个字段")
    private String roadExecuteEventContentName;


}
