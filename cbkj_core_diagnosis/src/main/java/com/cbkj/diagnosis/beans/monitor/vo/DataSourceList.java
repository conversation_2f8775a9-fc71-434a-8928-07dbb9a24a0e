package com.cbkj.diagnosis.beans.monitor.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/5 9:26
 * @Version 1.0
 */
@Data
public class DataSourceList {


    @Schema(description =  "中医疾病名")
    private String chineseDisName;

    @Schema(description =  "中医疾病id")
    private String chineseDisId;

    @Schema(description =  "患者id")
    private String patientId;

    @Schema(description =  "患者编码")
    private String patientCode;

    @Schema(description =  "患者证件号")
    private String patientCardNumber;

    @Schema(description =  "患者姓名")
    @CBKJDecryptField
    private String patientName;

    @Schema(description =  "患者姓别")
    private String patientSex;

    @Schema(description =  "患者年龄")
    private String patientAge;

    @Schema(description =  "医疗机构")
    private String insName;

    @Schema(description =  "医生姓名")
    @CBKJDecryptField
    private String doctorName;

    @Schema(description =  "档案编号")
    private String closedNo;

    @Schema(description =  "病案状态 0:创建(产集中)，1:结案，2:归档，3:上传成功，4：上传失败 ")
    private String closedStatus;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "监测开始日期")
    private Date monitorStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "监测结束日期")
    private Date monitorEndDate;


    @Schema(description =  "总就诊次数")
    private String  totalMedicineVisit;


    @Schema(description =  "总预诊次数")
    private String  totalYuZhen;

    @Schema(description =  "总随访次数")
    private String  totalSuiFang;

    @Schema(description =  "总宣教数")
    private String  totalXuanJiao;

}
