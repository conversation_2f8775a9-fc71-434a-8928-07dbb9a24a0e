package com.cbkj.diagnosis.beans.statistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema
public class StatisticsVo {

    @Schema(description =  "block-index-top 首页顶部数据" +
//            "block-index-ins 首页介入机构数据" +
//            "block-index-dis  首页监测病种" +
            "block-index-1    首页方块1" +
            "block-index-dis   首页方块2" +
            "block-index-lxpj  首页方块3-疗效评价" +
            "block-index-blfy  首页方块4-不良反应" +
            "block-index-zlfy 首页方块5-诊疗费用",required = true)
    private String blockId;

    @Schema(description="开始日期格式：2020-01-01")
    private String startDate;
    @Schema(description="结束日期格式：2020-01-02")
    private String endDate;


    @Schema(hidden = true)
    private String appId;
    @Schema(hidden = true)
    private String insCode;
    @Schema(hidden = true)
    private String insName;

    @Schema(description="疾病代码code")
    private String disCode;
    private String disName;

    @Schema(hidden = true)
    private Integer searchType;

    private String userId;
    private String disCodes;



}
