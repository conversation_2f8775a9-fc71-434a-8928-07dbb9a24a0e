package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zbh on 2024/6/5 16:20
 *
 * @description：中医处置
 */
@Getter
@Setter
@Schema(description =  "中医适宜技术处置")
public class TCMTreatmentDTO {

    @Schema(description =  "中医适宜技术处置明细")
    private List<TCMItemTreatmentDTO> westernMedicineTreatmentItems;

    @Schema(description =  "适宜技术治疗描述")
    private String memo;

    @Schema(description =  "开立医师签名")
    private String prescribingDoctorSign;

    @Schema(description =  "执行医护签名")
    private String executiveDoctorSign;
}
