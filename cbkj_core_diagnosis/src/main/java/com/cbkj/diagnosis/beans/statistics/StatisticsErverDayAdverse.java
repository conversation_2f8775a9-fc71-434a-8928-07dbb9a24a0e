package com.cbkj.diagnosis.beans.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Getter
@Setter
@TableName("statistics_erver_day_adverse")
@Schema(description =  "StatisticsErverDayAdverse对象")
public class StatisticsErverDayAdverse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="不良反应人次统计")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description="统计日期")
    @TableField("create_time")
    private Date createTime;

    @TableField("app_id")
    private String appId;

    @TableField("ins_id")
    private String insId;

    @TableField("ins_code")
    private String insCode;

    @Schema(description="机构名称")
    @TableField("ins_name")
    private String insName;

    @Schema(description="疾病id")
    @TableField("dis_id")
    private String disId;

    @Schema(description="疾病名称")
    @TableField("dis_name")
    private String disName;

    @Schema(description="疾病代码")
    @TableField("dis_code")
    private String disCode;

    @Schema(description="不良反应人次")
    @TableField("num")
    private Integer num;


}
