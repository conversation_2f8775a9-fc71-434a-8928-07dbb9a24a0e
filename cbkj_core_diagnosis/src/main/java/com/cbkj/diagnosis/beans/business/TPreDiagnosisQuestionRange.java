package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Getter
@Setter
@TableName("t_pre_diagnosis_question_range")
public class TPreDiagnosisQuestionRange implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "question_range_id", type = IdType.AUTO)
    private Integer questionRangeId;

    @TableField("question_id")
    private Integer questionId;

    /**
     * 非必填，如填写则移动端展示
     */
    @TableField("question_range_title")
    private String questionRangeTitle;

    /**
     * 最小值必填，可输入，可上下翻
     */
    @TableField("question_range_min")
    private BigDecimal questionRangeMin;

    /**
     * 最大值必填，可输入，可上下翻
     */
    @TableField("question_range_max")
    private BigDecimal questionRangeMax;

    /**
     * 小数点必填，默认0，最多3位，可上下翻
     */
    @TableField("question_range_decimal")
    private Integer questionRangeDecimal;

    /**
     * 默认值必填，填写时的默认项，与前三项联动
     */
    @TableField("question_range_default")
    private BigDecimal questionRangeDefault;

    /**
     * 单位：必填
     */
    @TableField("question_range_unit")
    private String questionRangeUnit;


}
