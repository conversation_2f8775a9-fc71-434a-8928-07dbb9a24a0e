package com.cbkj.diagnosis.beans.business;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Schema
public class TPropagandaEdu implements Serializable{

    @Schema(description =  "健康宣教id")
    private Integer tPropagandaEduId;
    @Schema(description =  "宣教类别")
    private String eduType;

    @Schema(description =  "健康宣教标题")
    private String eduTitle;

    @Schema(description =  "封面图片url")
    private String eduCoverImage;

    @Schema(description =  "视频地址")
    private String eduCoverVideo;

    @Schema(description =  "富文本")
    private String eduContent;

    @Schema(description =  "0正常1删除")
    private String status;

    @Schema(description =  "创建人")
    private String createUserId;

    @Schema(description =  "创建者名字")
    private String createUserName;
    private String insName;
    private String insId;
    private String appId;
    private String insCode;
    private String eduAbstract;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;
    private String updateUserId;
    private String updateUserName;
    private String chineseName;
    @Schema(hidden = true)
    private String userId;
    private Boolean showStatus;

    private String isShow;
    private String isSharing;

    private List<String> chineseDis;

}
