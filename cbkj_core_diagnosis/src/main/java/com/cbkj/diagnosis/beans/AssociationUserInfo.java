package com.cbkj.diagnosis.beans;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AssociationUserInfo {



    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description="名字")
    private String userName;


    @Schema(description="用户id")
    private String userId;


    @Schema(description="性别")
    private String userSex;

//    @CBKJEncryptField
//    @CBKJDecryptField
//    private String mobile;

}
