package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/5 15:56
 *
 * @description：西医药品明细
 */
@Getter
@Setter
@Schema(description =  "西医药品明细")
public class MedicalWestPrescriptionsItemDTO {

    @Schema(description =  "药物名称")
    private String drugName;
    @Schema(description =  "药物规格")
    private String drugSpecifications;
    @Schema(description =  "用法")
    private String usage;
    @Schema(description =  "使用频次")
    private String drugFrequency;
    @Schema(description =  "用药途径")
    private String administrationRoute;
}
