package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class TDisease implements Serializable{

    @Schema
    private String disId;

    @Schema
    private String disName;

    @Schema(description =  "疾病编码")
    private String disCode;

    @Schema(description =  "疾病类型（1中医2西医）")
    private String disType;

    @Schema(description =  "创建时间")
    private Date createDate;

    @Schema(description =  "创建人")
    private String createUser;

    @Schema(description =  "创建人姓名")
    private String createUsername;

    @Schema(description =  "修改时间")
    private Date updateDate;

    @Schema(description =  "修改人")
    private String updateUser;

    @Schema(description =  "修改人姓名")
    private String updateUsername;

    @Schema(description =  "删除时间")
    private Date delDate;

    @Schema(description =  "删除人")
    private String delUser;

    @Schema(description =  "删除人姓名")
    private String delUsername;

    @Schema(description =  "顺序号")
    private String sort;

    @Schema(description =  "状态（0正常   1删除）")
    private String status;

    @Schema(description =  "疾病概述")
    private String disSummary;

}
