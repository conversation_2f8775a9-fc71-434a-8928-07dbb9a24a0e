package com.cbkj.diagnosis.beans.task;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SRoadTaskPatients implements Serializable {

    @Schema(description =  "患者任务")
    private Long taskPatientsId;

    @Schema(description =  "1.缴费后（删除） 2.就诊后 3.服药 4.服药结束前 5针灸治疗后")
    private String roadExecuteEvent;

    @Schema(description =  "事件产生后多久执行")
    private String roadExecuteEventTime;

    @Schema(description =  "执行时间单位 天（D）小时（H）分钟（m）")
    private String roadExecuteEventUnit;

    @Schema(description =  "1.微信2短信3.电话")
    private String roadExecuteEventWay;

    @Schema(description =  "执行的具体内容类型1.健康宣教2.随访问卷（随访的一种类型）3.复诊提醒4.自测量表(随访的一种类型)5. 用药指导")
    private String roadExecuteEventType;

    @Schema(description =  "执行内容所在主键id")
    private String roadExecuteEventContentId;
    @Schema(description =  "执行内容名称")
    private String roadExecuteEventContentName;

    @Schema(description =  "所属那个随访路径主表id")
    private String sRoadId;

    @Schema(description =  "随访任务主表id")
    private String sRoadTaskId;
    private String taskName;

    @Schema(description =  "患者id")
    private String patientId;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者名字")
    private String patientName;

    @Schema(description =  "患者年龄")
    private String patientAge;

    @Schema(description =  "患者性别")
    private String patientSex;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者身份证")
    private String patientCardNumber;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "任务-触发时间")
    private Date taskExcuteTime;

    @Schema(description =  "任务-创建时间")
    private Date createTime;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "任务-基线时间")
    private Date taskBasicTime;

    @Schema(description =  "用于没有病历，但是有excel导入末次就诊时间，用这个时间去确定就诊后的任务执行时间。",hidden = true)
    private Date taskExcuteTimeTmp;

    @Schema(description =  "1.待执行（未发送）2.已执行（已发送,未读（微信短信））3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通  7.已读（微信短信）8.已填写")
    private Integer taskExcuteStatus;

//    @Schema(description =  "随访小记录（电话随访填写的小记）")
//    private String taskPatientsNode;
    @Schema(description="就诊记录id")
    private String recordsId;
    @Schema(description="这条数据的状态 0正常 1删除")
    private String status;
    /**
     * 0.正常任务1.手动点击发送
     */
    private String handSend;
    @Schema(description="患者问卷作答的结果id")
    private String recId;

    @Schema(description="随访路径执行方案")
    private Integer roadExecuteId;

    @Schema(description="电话随访分配")
    private Date allotTime;

    @Schema(description="电话随访：0分配 1 未分配")
    private String allotTaskStatus;

    @Schema(description="电话随访：随访人id")
    private String doctorId;

    @Schema(description="电话随访：随访人名字")
    private String doctorName;
    @CBKJDecryptField
    private String patientMobile;

    private String appId;
    private String insId;
    private String insCode;
    private String insName;
    private String deptId;
    private String deptCode;
    private String deptName;

    @Schema(description="随访小记")
    private String taskPatientsNode;
    private Date deleteTime;
    private String deleteUserName;
    private String deleteUserId;

}
