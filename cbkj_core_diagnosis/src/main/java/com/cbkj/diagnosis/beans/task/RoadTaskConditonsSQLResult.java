package com.cbkj.diagnosis.beans.task;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/22 15:51
 * @Version 1.0
 */
@Data
@Schema
public class RoadTaskConditonsSQLResult {

    @Schema(description="科室")
    private String deptId;

    @Schema(description="科室名称")
    private String deptName;

    @Schema(description="随访医生id")
    private String diagnosisDoctorId;
    @Schema(description="随访医生名称")
    private String diagnosisDoctorName;
    @Schema(description="就诊医生id")
    private String recordDoctorId;

    @Schema(description="就诊医生名字")
    private String recordDoctorName;

}
