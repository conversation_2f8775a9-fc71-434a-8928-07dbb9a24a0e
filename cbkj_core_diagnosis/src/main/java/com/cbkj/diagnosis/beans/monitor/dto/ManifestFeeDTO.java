package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zbh on 2024/6/5 16:42
 *
 * @description：费用清单
 */
@Getter
@Setter
@Schema(description =  "费用清单")
public class ManifestFeeDTO {

    @Schema(description =  "诊疗总费用")
    private String totalHealthcareCosts;

    @Schema(description =  "中医辨证论治费")
    private String bianZhengCost;

    @Schema(description =  "煎药费")
    private String decoctingCost;

    @Schema(description =   "医保报销金额")
    private String insuranceReimbursementCost;

    @Schema(description =  "个人承担金额")
    private String personalCost;

    @Schema(description =  "辅助检查费 key=项目名称 value=金额")
    private List<ProjectFeeDTO> auxiliaryExaminationFee;
//    @Schema(description =  "化验费  key=项目名称 value=金额")
//    private List<ProjectFeeDTO> tcmMedicalServiceProjectFee;
//    @Schema(description =  "治疗处置金额  key=项目名称 value=金额")
//    private List<ProjectFeeDTO> treatmentAndDisposalAmount;


    @Schema(description =  "中医医疗服务项目费 key=项目名称 value=金额")
    private List<ProjectFeeDTO> tcmMedicalServiceProjectFee;

    @Schema(description =  "中医医疗服务项目费-中医辩证论治 key=项目名称 value=金额")
    private List<ProjectFeeDTO> tcmSyndromeDifferentiation;

    @Schema(description =  "影像学诊断费 key=项目名称 value=金额")
    private List<ProjectFeeDTO> imagingFees;

    @Schema(description =  "实验室诊断费 key=项目名称 value=金额")
    private List<ProjectFeeDTO> feesForLaboratoryTests;

    @Schema(description =  "中草药费 key=项目名称 value=金额")
    private List<ProjectFeeDTO> chineseHerbalMedicineFee;

    @Schema(description =  "西药费 key=项目名称 value=金额")
    private List<ProjectFeeDTO> westernMedicineFee;

    @Schema(description =  "中成药费 key=项目名称 value=金额")
    private List<ProjectFeeDTO> chinesePatentMedicineFee;

    @Schema(description =  "机构中药制剂费 key=项目名称 value=金额")
    private List<ProjectFeeDTO> institutionChineseMedicinePreparationFee;

    @Schema(description =  "手术治疗费 key=项目名称 value=金额")
    private List<ProjectFeeDTO> surgicalTreatmentFee;

    @Schema(description =  "其它费用 key=项目名称 value=金额")
    private List<ProjectFeeDTO>  otherFees;

}
