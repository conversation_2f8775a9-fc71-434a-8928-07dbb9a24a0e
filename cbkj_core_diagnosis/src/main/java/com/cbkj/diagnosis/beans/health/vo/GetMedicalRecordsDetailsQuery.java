package com.cbkj.diagnosis.beans.health.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class GetMedicalRecordsDetailsQuery {
    private String patientId;

    @Schema(description="查询年份“比如：2023")
    private String queryYearStart;
    private String queryYearEnd;
}
