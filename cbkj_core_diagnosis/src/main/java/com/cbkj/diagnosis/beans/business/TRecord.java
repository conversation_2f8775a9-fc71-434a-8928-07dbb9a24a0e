package com.cbkj.diagnosis.beans.business;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.cbkj.diagnosis.service.webapi.business.vo.GetQuestionClassTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Schema
public class TRecord implements Serializable {

    @Schema(description = "记录id")
    private String recId;

    @Schema(description = "患者id")
    private String patientId;

    @CBKJDecryptField
    @CBKJEncryptField
    @Schema(description = "患者名称")
    private String patientName;
    @CBKJDecryptField
    @CBKJEncryptField
    @Schema(description = "患者身份证号")
    private String patientIdcard;

    @Schema(description = "患者性别（M男   F女）")
    private String patientSex;

    @Schema(description = "患者年龄")
    private String patientAge;

    @Schema(description = "预诊单信息")
    private String diaContent;

    @Schema(description = "预诊单id")
    private String diaId;

    @Schema(description = "创建时间")
    private Date createDate;

    @Schema(description = "创建人")
    private String createUser;

    @Schema(description = "创建人姓名")
    private String createUsername;

    @Schema(description = "修改时间")
    private Date updateDate;

    @Schema(description = "修改人")
    private String updateUser;

    @Schema(description = "修改人姓名")
    private String updateUsername;

    @Schema(description = "删除时间")
    private Date delDate;

    @Schema(description = "删除人")
    private String delUser;

    @Schema(description = "删除人姓名")
    private String delUsername;

    @Schema(description = "状态（0正常   1删除）")
    private String status;

    @Schema(description = "1.手机2.电脑面访 3.电话随访")
    private String recordResource;
    @Schema(description = "1.预诊2随访")
    private String formType;

    @Schema(description = "挂号记录")
    private String regPlanId;

    @Schema(description = "是否开启大模型病历：0开1关")
    private Integer cueWordStatus;

    /**
     * 解密内容
     */
    @CBKJDecryptField
    private String decryptContent;

    List<GetQuestionClassTypeInfo> questionClassTypeInfoList;
    List<GetQuestionClassTypeInfo> questionClassTypeInfoAIList;


}
