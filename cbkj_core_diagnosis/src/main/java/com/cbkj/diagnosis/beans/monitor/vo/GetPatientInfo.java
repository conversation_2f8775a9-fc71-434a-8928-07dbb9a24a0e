package com.cbkj.diagnosis.beans.monitor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/6 16:38
 * @Version 1.0
 */
@Data
@Schema
public class GetPatientInfo {
    @Schema(description =  "患者id")
    private String patientId;
    @Schema(description =  "中医疾病id")
    private String disId;
    @Schema(description =  "可以传空。 中医疾病  code=1 , message=预诊 ,code=2 , message=就诊 ,code=3 , message=宣教 ,code=4 , message=随访")
    private String recordTypeCode;
    @Schema(description =  "档案编号")
    private String closedNo;
}
