package com.cbkj.diagnosis.beans.business.adviceVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/3 10:26
 * @Version 1.0
 */
@Data
public class SystemHelpList {
    @Schema(description="附件主键编号ID")
    private String id;
    @Schema(description="附件所属业务类型：1系统建议反馈,2系统帮助操作手册3.系统帮助操作视频")
    private Integer annexType;
    @Schema(description="附件名称")
    private String annexName;
    @Schema(description="附件路径")
    private String annexPath;

    @Schema(description="创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

}
