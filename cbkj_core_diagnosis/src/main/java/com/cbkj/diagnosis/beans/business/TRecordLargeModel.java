package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@TableName("t_record_large_model")
@Schema(description = "TRecordLargeModel对象")
public class TRecordLargeModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "record_model_id")
    private String recordModelId;

    @Schema(description =  "表单记录id")
    @TableField("rec_id")
    private String recId;

    @Schema(description = "表单id")
    @TableField("dia_id")
    private String diaId;

    @Schema(description = "传入大模型入上下文")
    @TableField("request_text")
    private String requestText;

    @Schema(description = "大模型出参")
    @TableField("response_text")
    private String responseText;

    @Schema(description = "提示词")
    @TableField("cue_word_text")
    private String cueWordText;

    @Schema(description = "转换词")
    @TableField("cuw_word_trans")
    private String cuwWordTrans;

    @Schema(description = "0成功1失败")
    @TableField("record_model_status")
    private Integer recordModelStatus;

    @Schema(description = "失败返回的内容")
    @TableField("record_model_fail_msg")
    private String recordModelFailMsg;

    @Schema(description = "数据插入时间")
    @TableField("insert_time")
    private Date insertTime;

    @TableField("re_call_times")
    private Integer reCallTimes;

    @TableField("last_re_call_time")
    private Date lastReCallTime;

    @TableField("type")
    private Integer type;


}
