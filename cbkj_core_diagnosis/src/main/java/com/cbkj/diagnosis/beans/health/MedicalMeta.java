package com.cbkj.diagnosis.beans.health;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MedicalMeta.java
 * @Description TODO
 * @createTime 2025年03月17日 15:01:00
 */
@Data
public class MedicalMeta {
    /**
     * 类型编码
     */
    private String typeCode;

    /**
     * 类别名称
     */
    private String typeName;

    /**
     * 元数据编码
     */
    private String metaCode;

    /**
     * 元数据名称
     */
    private String metaName;

    /**
     * 标签类型
     */
    private String tagType;

    private Integer sort;


    public MedicalMeta(String metaCode, String metaName, String tagType) {
        this.metaCode = metaCode;
        this.metaName = metaName;
        this.tagType = tagType;
    }
}
