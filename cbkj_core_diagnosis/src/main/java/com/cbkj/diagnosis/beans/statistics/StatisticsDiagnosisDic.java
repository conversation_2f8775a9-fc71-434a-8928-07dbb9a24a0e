package com.cbkj.diagnosis.beans.statistics;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Getter
@Setter
@TableName("statistics_diagnosis_dic")
@Schema(description =  "StatisticsDiagnosisDic对象")
public class StatisticsDiagnosisDic implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="统计随访/量表事件")
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @Schema(description="统计日期")
    @TableField("create_time")
    private Date createTime;

    @TableField("app_id")
    private String appId;

    @TableField("ins_id")
    private String insId;

    @TableField("ins_code")
    private String insCode;

    @Schema(description="机构名称")
    @TableField("ins_name")
    private String insName;

    @Schema(description="疾病id")
    @TableField("dis_id")
    private String disId;

    @Schema(description="疾病代码")
    @TableField("dis_code")
    private String disCode;

    @Schema(description="疾病名称")
    @TableField("dis_name")
    private String disName;

//    @Version
    @Schema(description="今日统计次数")
    @TableField("num")
    private Integer num;

    @TableField("dic_id")
    private String dicId;

    @TableField("dic_name")
    private String dicName;

    @TableField("dic_code")
    private String dicCode;

    @TableField("patient_id")
    private String patientId;
    @TableField("insert_time")
    private Date insertTime;


}
