package com.cbkj.diagnosis.beans.his;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName QualityControlLog.java
 * @Description TODO
 * @createTime 2025年03月11日 14:43:00
 */
@Data
public class QualityControlLog implements Serializable {
    @TableId
    private String id;

    private String appId;

    private String insCode;

    private String recordId;

    private String idCard;

    /**
     * 数据集代码
     */
    private String datasetCode;

    /**
     * 数据集名称
     */
    private String datasetName;

    /**
     * 01:完整性质控-非空检验;
     * 11:准确性质控-字典值域检验
     */
    private String rulesType;

    /**
     * 质控说明
     */
    private String rulesDesc;

    /**
     * 主键字段
     */
    private String keyField;

    /**
     * 主键值
     */
    private String keyValue;

    /**
     * 问题字段
     */
    private String problemField;

    private Date insertTime;

    public QualityControlLog(String id, String appId, String insCode, String idCard, String datasetCode, String datasetName, String rulesType, String rulesDesc, String keyField, String keyValue, String problemField, Date insertTime) {
        this.id = id;
        this.appId = appId;
        this.insCode = insCode;
        this.idCard = idCard;
        this.datasetCode = datasetCode;
        this.datasetName = datasetName;
        this.rulesType = rulesType;
        this.rulesDesc = rulesDesc;
        this.keyField = keyField;
        this.keyValue = keyValue;
        this.problemField = problemField;
        this.insertTime = insertTime;
    }
    public QualityControlLog(){

    }
}
