package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;
import java.util.List;


@Data
@NoArgsConstructor
@Schema
public class TRecordDia implements Serializable {

    @Schema(description =  "预诊记录明细id")
    private Integer diaRecId;

    @Schema(description =  "预诊单id")
    private String diaId;
    private String questionCode;


    @Schema(description =  "记录id")
    private String recId;

    @Schema(description =  "题目id")
    private Integer questionId;

    @Schema(description =  "题目")
    private String questionName;

    @Schema(description =  "题号")
    private Integer questionNumber;

    @Schema(description =  "题目类型（1文本  2单选  3多选  4时间 5.血压 6日期 7值域滑动样式 8图片 9。AI舌诊 10.OCR药盒识别 11.OCR检查检验单）")
    private String questionType;

    @Schema(description =  "选项id，多个用逗号拼接")
    private String optionIds;

    @Schema(description =  "选项值")
    private String optionNames;

    @Schema(description =  "患者回答得值 数组形式保存比如[\"小腿\"]")
    private String content;

    @Schema(description =  "年")
    private String year;

    @Schema(description =  "月")
    private String month;

    @Schema(description =  "日")
    private String day;

    @Schema(description =  "小时")
    private String hour;

    @Schema(description =  "小时")
    private String week;
    @Schema(description =  "时间单位（1年 2月 3日 4小时，可多选,逗号拼接）")
    private String dateUnit;

    @Schema(description =  "问题单位（只有文本样式有这个值）")
    private String questionUnit;
    private String questionGroups;
    private String questionOptionGroups;

//    @Schema(description =  "V2.1.0-图示示例")
//    private String questionImg;
//
//    @Schema(description =  "V2.1.0-图片最大上传数量")
//    private Integer questionImgMaxNum;

    @Schema(description =  "V2.1.0-用户上传的图片列表")
    private List<TRecordDiaImage> questionImages;
}
