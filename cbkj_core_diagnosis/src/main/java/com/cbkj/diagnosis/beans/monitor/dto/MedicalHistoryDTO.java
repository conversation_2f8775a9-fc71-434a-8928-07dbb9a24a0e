package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/5 13:30
 *
 * @description：病史信息
 */
@Getter
@Setter
@Schema(description =  "病史信息")
public class MedicalHistoryDTO {

    @Schema(description =  "西医诊断")
    private String westDisName;

    @Schema(description =  "中医诊断")
    private String chineseDisName;

    @Schema(description =  "中医症候")
    private String symName;

    @Schema(description =  "诊断标识")
    private String diagnosticFlag;

    @Schema(description =  "主诉")
    private String chiefComplaint;

    @Schema(description =  "现病史")
    private String presentIllness;

    @Schema(description =  "既往史")
    private String pastHistory;

    @Schema(description =  "个人史")
    private String personalHistory;

    @Schema(description =  "过敏史标志")
    private String allergyHistoryFlag;

    @Schema(description =  "过敏史")
    private String allergyHistory;

    @Schema(description =  "传染病史")
    private String infectiousHistory;

    @Schema(description =  "预防接种史")
    private String vaccinationHistory;

    @Schema(description =  "手术史")
    private String surgicalHistory;

    @Schema(description =  "输血史")
    private String bloodTransfusionHistory;

    @Schema(description =  "婚育史")
    private String obstetricHistory;

    @Schema(description =  "家族史")
    private String familyHistory;

    @Schema(description =  "月经史")
    private String menstrualHistory;

    @Schema(description =  "体格检查")
    private String physicalExamination;

    @Schema(description =  "职业")
    private String occupation;


}
