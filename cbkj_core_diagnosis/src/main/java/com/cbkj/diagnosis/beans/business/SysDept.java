package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SysDept implements Serializable{

    @Schema
    private String deptId;

    @Schema
    private String deptCode;

    @Schema(description =  "科室名称")
    private String deptName;

    @Schema
    private String appId;

    @Schema
    private String insId;

    @Schema
    private String insCode;

    @Schema
    private String insName;

    @Schema(description =  "0启用1删除")
    private String status;


}
