package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zbh on 2024/6/5 16:19
 *
 * @description：西医处置
 */
@Getter
@Setter
@Schema(description =  "西医处置")
public class WesternMedicineTreatmentDTO {
    @Schema(description =  "西医处置明细")
    private List<WesternMedicineTreatmentItemDTO> westernMedicineTreatmentItems;

    @Schema(description =  "开立医师签名")
    private String prescribingDoctorSign;

    @Schema(description =  "执行医护签名")
    private String executiveDoctorSign;
}
