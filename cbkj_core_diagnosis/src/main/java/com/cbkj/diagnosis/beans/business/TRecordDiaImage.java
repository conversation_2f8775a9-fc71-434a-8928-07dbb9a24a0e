package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Getter
@Setter
@TableName("t_pre_diagnosis_question_image")
@Schema
public class TRecordDiaImage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "id", hidden = true)
    private Long id;

    @Schema(description = "题目id", hidden = true)
    @TableField("question_id")
    private Integer questionId;

    @Schema(description = "记录明细id", hidden = true)
    @TableField("dia_rec_id")
    private Integer diaRecId;

    @Schema(description = "上传图片地址", hidden = true)
    @TableField("image_url")
    private String imageUrl;

    @Schema(description = "排序", hidden = true)
    @TableField("sort")
    private Integer sort;

    @Schema(description = "1文本  2单选  3多选  4时间 5.血压 6日期 7值域滑动样式 8图片 9。AI舌诊 10.OCR药盒识别 11.OCR检查检验单", hidden = true)
    @TableField("question_type")
    private String questionType;

    @Schema(description = "ai识别结果", hidden = true)
    @TableField("ai_content")
    private String aiContent;
    private String recId;
    private String aiUrl;


}
