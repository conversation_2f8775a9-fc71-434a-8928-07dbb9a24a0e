package com.cbkj.diagnosis.beans.health;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MedicalRecordsLab.java
 * @Description TODO
 * @createTime 2025年02月27日 16:25:00
 */
@Data
public class MedicalRecordsLab implements Serializable {
    @TableId
    private String labId;
    /**
     * 病历编号
     */
    private String recordId;
    /**
     * 电子申请单编号
     */
    private String applyNo;
    /**
     * 检验申请科室
     */
    private String applyDeptName;
    /**
     * 检验申请医生签名
     */
    private String applyDoctorName;
    /**
     * 标本采样日期时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sampleTime;
    /**
     * 标本类别
     */
    private String labTypeCode;
    /**
     * 检验目的
     */
    private String labAim;
    /**
     * 检验项目代码
     */
    private String labItemCode;
    /**
     * 检验医师签名
     */
    private String labDoctorName;
    /**
     * 检验日期时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date labTime;
    /**
     * 检验报告单编号
     */
    private String labNo;
    /**
     * 检验报告科室
     */
    private String reportDeptName;
    /**
     * 检验报告机构名称
     */
    private String reportInsName;
    /**
     * 检验报告结果
     */
    private String reportResult;
    /**
     * 检验报告备注
     */
    private String reportRemark;
    /**
     * 检验报告日期时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;
    /**
     * 报告医师签名
     */
    private String reportDocName;
    /**
     * 审核医师签名
     */
    private String checkDocName;
}
