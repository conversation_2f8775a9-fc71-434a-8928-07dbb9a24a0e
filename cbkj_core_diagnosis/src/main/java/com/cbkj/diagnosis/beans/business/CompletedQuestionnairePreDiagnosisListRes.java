package com.cbkj.diagnosis.beans.business;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CompletedQuestionnairePreDiagnosisListRes implements Serializable {
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "填写完成时间")
    private Date finishTime;

    @Schema(description =  "预诊单id")
    private String diaId;

    @Schema(description =  "患者id")
    private String patientId;

    @Schema(description =  "患者名称")
    @CBKJDecryptField
    private String patientName;

    @Schema(description =  "患者性别 M男 F女")
    private String patientSex;

    @Schema(description =  "患者年龄")
    private String patientAge;

    @CBKJDecryptField
    @Schema(description =  "患者证件号")
    private String patientIdcard;

    @Schema(description =  "预诊内容")
    private String chineseDisName;

    @Schema(description =  "患者预诊记录id")
    private String recId;
}
