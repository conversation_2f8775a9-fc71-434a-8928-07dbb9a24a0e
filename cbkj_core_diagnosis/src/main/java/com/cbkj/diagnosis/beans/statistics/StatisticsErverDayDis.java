package com.cbkj.diagnosis.beans.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Getter
@Setter
@TableName("statistics_erver_day_dis")
@Schema(description =  "StatisticsErverDayDis对象")
public class StatisticsErverDayDis implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="监测病种对应数量")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description="疾病id")
    @TableField("dis_id")
    private String disId;

    @Schema(description="疾病代码")
    @TableField("dis_code")
    private String disCode;

    @Schema(description="疾病名称")
    @TableField("dis_name")
    private String disName;

    @Schema(description="随访数量")
    @TableField("follow_num")
    private Integer followNum;

    @Schema(description="有效病历(手动完成病例标记结案的数量)")
    private Integer recordsNum;

    @Schema(description="预诊数量")
    @TableField("diagnosis_num")
    private Integer diagnosisNum;

    @TableField(exist = false)
    private Integer currentNum;

    @Schema(description="总计（随访数量+预诊数量）")
    @TableField("total_num")
    private Integer totalNum;

    @TableField("app_id")
    private String appId;

    @TableField("ins_id")
    private String insId;

    @TableField("ins_code")
    private String insCode;

    @TableField("ins_name")
    private String insName;

    @Schema(description="统计日期")
    @TableField("create_time")
    private Date createTime;


}
