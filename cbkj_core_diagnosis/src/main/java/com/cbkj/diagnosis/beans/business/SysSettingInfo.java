package com.cbkj.diagnosis.beans.business;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SysSettingInfo.java
 * @Description TODO
 * @createTime 2025年0月02日 16:26:00
 */
@Data
public class SysSettingInfo implements Serializable {
    private String id;
    /**
     * 医生端系统名称
     */
    private String doctorSysName;

    /**
     * 患者端系统名称
     */
    private String patientSysName;

    /**
     * base64 图片
     */
    private String sysLogo;

    private Date insertTime;
    /**
     * 患者问答内容限制修改时间 0:不限制 1:当天
     */
    private String updateLimit;

    /**
     * 问答智能化采集多个用,分隔 1:舌诊 2:药盒 3:检验检查单
     */
    private String sysAgent;

    /**
     * 语料共享与下载 0:开启 1:关闭
     */
    private String sysCorpus;

}
