package com.cbkj.diagnosis.beans.home;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SysHomeShowUrl implements Serializable{

    @Schema
    private Integer showUrlId;

    @Schema(description =  "显示名称")
    private String label;

    @Schema(description =  "路径")
    private String route;


}
