package com.cbkj.diagnosis.beans.business;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompletedQuestionnaireCoreRe implements Serializable {

    @Schema(description =  "时间类型：1.填写时间、2发送时间")
    private Integer dateType;
    private String startDate;
    private String endDate;

    private String formName;

    @Schema(description =  "随访任务id")
    private String sRoadTaskId;

    @Schema(description =  "随访任务名称。")
    private String taskName;

    @Schema(description =  "1.微信2短信3.电话。")
    private String roadExecuteEventWay;

    @CBKJEncryptField
    private String patientName;
    @CBKJEncryptField
    @Schema(description =  "患者证件号")
    private String patientCardNum;

    @Schema(description =  "随访表单的关联的疾病名")
    private String disName;
    @Schema(hidden = true)
    private String diaId;
    private String createUserId;

    private String[] sRoadTaskIdList;

    private int page;
    private int limit;

}
