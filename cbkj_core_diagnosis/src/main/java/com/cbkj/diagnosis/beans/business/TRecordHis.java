package com.cbkj.diagnosis.beans.business;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@Schema
@XmlRootElement(name = "root")
@XmlAccessorType(XmlAccessType.FIELD)
public class TRecordHis implements Serializable {
    @XmlElement(name = "RECID")
    @Schema(description =  "记录id")
    private String recId;
    @XmlElement(name = "PATIENTNAME")
    @CBKJDecryptField
    @CBKJEncryptField
    @Schema(description =  "患者名称")
    private String patientName;

    @XmlElement(name = "PATIENTIDCARD")
    @CBKJDecryptField
    @CBKJEncryptField
    @Schema(description =  "患者身份证号")
    private String patientIdCard;

    @XmlElement(name = "PATIENTCARDTYPE")
    private String patientCardType;

    @XmlElement(name = "HEALTHCARDNUM")
    private String healthCardNum;

    @XmlElement(name = "FORMCODE")
    private String diaId;


    @XmlElement(name = "DIAID")
    private String formCode;

    @XmlElement(name = "DIACONTENT")
    private String diaContent;

    @XmlElement(name = "CREATEDATE_NO_FORMAT")
    @Schema(description =  "创建时间")
    private Date createDate;

    @XmlElement(name = "CREATEDATE")
    private String createDateFormat;


    private String regPlanId;



    private List<QuestionMain> questionList;
}
