package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/5 13:56
 *
 * @description：治疗建议
 */
@Getter
@Setter
@Schema(description =  "治疗建议")
public class TreatmentSuggestionDTO {

    @Schema(description =  "开具检查检验")
    private String inspectionInfoString;

    @Schema(description =  "中药处方信息")
    private String cnPreInfo ;

    @Schema(description =  "西药、中成药处方")
    private String weCnPreInfo;

    @Schema(description =  "一般治疗处置")
    private String commonOpInfo;

}
