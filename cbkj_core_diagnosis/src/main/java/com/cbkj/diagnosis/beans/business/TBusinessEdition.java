package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 业务管理版本维护
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
@Getter
@Setter
@TableName("t_business_edition")
@Schema(description =  "TBusinessEdition对象")
public class TBusinessEdition implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="ID")
    @TableId("id")
    private String id;

    @Schema(description="创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description="创建者ID")
    @TableField("create_user_id")
    private String createUserId;

    @Schema(description="创建者姓名")
    @TableField("create_user_name")
    private String createUserName;

    @Schema(description="修改时间")
    @TableField("update_time")
    private Date updateTime;

    @Schema(description="修改者ID")
    @TableField("update_user_id")
    private String updateUserId;

    @Schema(description="删除时间")
    @TableField("delete_time")
    private Date deleteTime;

    @Schema(description="删除者ID")
    @TableField("delete_user_id")
    private String deleteUserId;

    @Schema(description="版本号")
    @TableField("edition_num")
    private String editionNum;

    @Schema(description="发布内容")
    @TableField("edition_content")
    private String editionContent;

    @Schema(description="发布时间")
    @TableField("edition_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date editionTime;

    @Schema(description="是否删除（0否 1是）")
    @TableField("is_del")
    private Boolean isDel;


}
