package com.cbkj.diagnosis.beans.task;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Schema
public class SRoadExecute implements Serializable{

    @Schema(description =  "随访路径执行方案")
    private Integer roadExecuteId;

    @Schema(description =  "1.缴费后(删除)2.就诊后3.服药4.服药结束前5.针灸治疗后")
    private String roadExecuteEvent;

    @Schema(description =  "事件产生后多久执行")
    private String roadExecuteTime;

    @Schema(description =  "执行时间单位")
    private String roadExecuteEventUnit;

    @Schema(description =  "1.微信2短信3.电话")
    private String roadExecuteEventWay;

    @Schema(description =  "执行的具体内容类型1.健康宣教2.随机问卷3.复诊提醒4.自测量表")
    private String roadExecuteEventType;

    @Schema(description =  "所属那个随访路径主表id")
    private String sRoadId;

    @Schema(description =  "排序")
    private Integer sRoadSort;


    @Schema(description="执行方案-执行哪些内容列表（类型后面的选项能够多选）")
    private List<SRoadExecuteContents> sRoadExecuteContentsList;


}
