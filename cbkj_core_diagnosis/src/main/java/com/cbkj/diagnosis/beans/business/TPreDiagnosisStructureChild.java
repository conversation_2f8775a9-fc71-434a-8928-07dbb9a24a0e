package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Getter
@Setter
@TableName("t_pre_diagnosis_structure_child")
@Schema(description =  "TPreDiagnosisStructureChild对象")
public class TPreDiagnosisStructureChild implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "structure_child", type = IdType.AUTO)
    private Integer structureChild;

    @TableField("content")
    private String content;

    @TableField("content_type")
    private String contentType;

    @TableField("structure_content_id")
    private Integer structureContentId;

    @TableField("paragraph_sort")
    private Integer paragraphSort;

    @TableField("question_is_del")
    @Schema(description =  "表示这个问题的原题是否删除了 0 否 1 是")
    private Integer questionIsDel;


}
