package com.cbkj.diagnosis.beans.diagnosisstructure;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/15 10:16
 * @Version 1.0
 */

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema
public class EmrList {

    @Schema(description =  "预诊表单id")
    private String diaId;

    @Schema(description =  "emr病历对象数组")
    private List<EmrItem> emrItems;

}