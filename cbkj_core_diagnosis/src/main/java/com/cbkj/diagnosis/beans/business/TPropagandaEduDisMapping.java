package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 宣教-疾病映射
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Getter
@Setter
@TableName("t_propaganda_edu_dis_mapping")
@Schema(description =  "TPropagandaEduDisMapping对象")
public class TPropagandaEduDisMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="健康宣教id")
    @TableId("t_propaganda_edu_id")
    private Integer tPropagandaEduId;

    @Schema(description="疾病id")
//    @TableId("dis_id")
    private String disId;

    @Schema(description="疾病名称")
    @TableField("dis_name")
    private String disName;

    @Schema(description="疾病类型（1中医疾病   2西医疾病）")
    @TableField("dis_type")
    private String disType;

    @TableField("dis_code")
    private String disCode;


}
