package com.cbkj.diagnosis.beans.business;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SRoadTaskPatientsPhone implements Serializable{

    @Schema(description =  "电话随访患者任务映射表id，患者排期，如果更新需要带上这个字段值")
    private Long sRoadTaskPatientsPhoneId;

    @Schema(description =  "患者任务id")
    private Long taskPatientsId;

    @Schema(description =  "随访人：医生id")
    private String doctorId;

    @Schema(description =  "就诊记录id")
    private String recordsId;

    @Schema(description =  "随访人：医生名字")
    private String doctorName;

    @Schema(description =  "0分配 1 未分配")
    private String allotTaskStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "排期的随访时间")
    private Date suiFangTime;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "分配给医生的时间")
    private Date allotTime;

    @Schema(description =  "患者id")
    private String patientId;

    @Schema(description =  "排期的状态：1.待执行（未发送，未确定电话随访时间）2.已执行（确定电话随访时间）3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 ）8.已填写(完成电话随访)")
    private Integer phoneStatus;
    private Integer roadExecuteId;
    private String sRoadTaskId;

    private String patientSex;

    @CBKJDecryptField
    @CBKJEncryptField
    private String patientName;

    @CBKJDecryptField
    @CBKJEncryptField
    private String patientCardNumber;

    @Schema(description="患者电话随访回答的记录id")
    private String recId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description="随访问卷答题完成时间-和问卷答题记录表中时间一致")
    private Date suiFangFinishTime;
//    private String recordsId;

    @Schema(description="电话随访小记")
    private String taskPatientsNode;


}
