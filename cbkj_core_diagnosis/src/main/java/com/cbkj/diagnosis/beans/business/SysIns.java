package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SysIns implements Serializable{

    @Schema
    private String insId;
    private String appId;

    @Schema(description =  "机构名称")
    private String insName;

    @Schema(description =  "机构代码")
    private String insCode;

    @Schema(description =  "0启用1删除",hidden = true)
    private String status;


}
