package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
@Getter
@Setter
@TableName("t_record_dia_dimension")
public class TRecordDiaDimension implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 题目得分汇总表
     */
    @TableField("id")
    private Integer id;

    /**
     * 记录id
     */
    @TableField("t_record")
    private String tRecord;

    /**
     * 代码
     */
    @TableField("question_dimension_code")
    private String questionDimensionCode;

    /**
     * 名称
     */
    @TableField("question_dimension_name")
    private String questionDimensionName;

    /**
     * 总分
     */
    @TableField("question_dimension_total_score")
    private Double questionDimensionTotalScore;


}
