package com.cbkj.diagnosis.beans;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Getter
@Setter
@TableName("s_road_task_ex")
@Schema(description =  "SRoadTaskEx对象")
public class SRoadTaskEx implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("s_road_task_id")
    private String sRoadTaskId;

    @Schema(description="任务关联哪些科室")
    @TableField("dept_id")
    private String deptId;

    @TableField("app_id")
    private String appId;

    @TableField("dept_code")
    private String deptCode;

    @TableField("ins_code")
    private String insCode;

    @TableField("ins_id")
    private String insId;

    @Schema(description="就诊医生id")
    @TableField("record_doctor_id")
    private String recordDoctorId;
    private String recordDoctorName;

    @Schema(description="随访成员医生id")
    @TableField("diagnosis_doctor_id")
    private String diagnosisDoctorId;
    private String diagnosisDoctorName;
    private String deptName;

    @Schema(description="0科室1就诊医生2随访成员")
    @TableField("ex_type")
    private Integer exType;


}
