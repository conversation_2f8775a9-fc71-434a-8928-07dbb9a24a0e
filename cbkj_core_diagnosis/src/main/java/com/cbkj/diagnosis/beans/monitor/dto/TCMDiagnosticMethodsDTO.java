package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/5 13:52
 *
 * @description：中医四诊
 */
@Setter
@Getter
@Schema(description =  "中医四诊信息")
public class TCMDiagnosticMethodsDTO {

    @Schema(description =  "症状描述")
    private String symptomDescription;

    @Schema(description =  "舌象诊断信息")
    private String tongueCondition;

    @Schema(description =  "脉象诊断信息")
    private String pulseCondition;

}
