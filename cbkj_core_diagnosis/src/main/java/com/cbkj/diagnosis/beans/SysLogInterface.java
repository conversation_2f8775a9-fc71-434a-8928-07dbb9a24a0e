package com.cbkj.diagnosis.beans;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@Schema
public class SysLogInterface implements Serializable{

    @Schema
    private String id;

    @Schema
    private String appId;

    @Schema
    private String insCode;

    @Schema
    private String doctorId;

    @Schema
    private String doctorName;

    @Schema
    private String patientId;

    @Schema
    private String patientName;

    @Schema
    private Date createTime;

    @Schema
    private String interfaceName;

    @Schema
    private String interfaceDesc;

    @Schema
    private String interfaceToken;

    @Schema
    private String interfaceParams;

    @Schema
    private String resultStatus;

    @Schema
    private String resultMeseage;

    @Schema
    private String resultData;
    @Schema
    private String beginTime;
    @Schema
    private String endTime;

}
