package com.cbkj.diagnosis.beans.health;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class MedicalBasicHandleRecord implements Serializable{
    @TableId(value = "handle_record_id", type = IdType.AUTO)
    @Schema
    private Long handleRecordId;

    @Schema
    private String recordsId;

    @Schema(description =  "操作编码")
    private String operationCode;

    @Schema(description =  "操作名称")
    private String operationName;

    @Schema(description =  "操作目标部位名称")
    private String operationTarget;

    @Schema(description =  "操作方法描述")
    private String operationMethod;

    @Schema(description =  "操作次数")
    private String operationNumber;

    @Schema(description =  "操作日期时间")
    private Date operationTime;

    @Schema(description =  "处理及指导意见")
    private String treatmentInstruction;

    @Schema(description =  "有创诊疗操作标志")
    private String invasiveDiagnosis;

    @Schema(description =  "治疗过程描述")
    private String processDescription;

    @Schema(description =  "今后治疗方案")
    private String futureTreatmentPlan;

    @Schema(description =  "随访方式")
    private String followUpMethodCode;

    @Schema(description =  "随访日期")
    private Date followUpDate;

    @Schema(description =  "随访周期建议")
    private String followUpPeriod;

    @Schema(description =  "签名日期时间")
    private Date signTime;

    @Schema(description =  "治疗处置开立医师签名")
    private String prescribingDoctorSign;

    @Schema(description =  "治疗处置执行医师签名")
    private String executiveDoctorSign;


}
