package com.cbkj.diagnosis.beans.diagnosisstructure;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.annotation.security.DenyAll;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/15 10:16
 * @Version 1.0
 */
@Schema
@Data
public class EmrItem {
    @JSONField(serialize = false)
    @Schema(hidden = true)
    private String diagnosisStructureId;

    @Schema(description =  "类型名称-从字典接口获取")
    private String emrType;
    @Schema(description =  "类型代码-从字典接口获取")
    private String typeCode;

    @Schema(hidden = true)
    @JSONField(serialize = false)
    private String diaId;

    @Schema(description =  "排序")
    private Integer sort;
    @Schema(description =  "输入框中元素数组对象（每个整体分组，文本+{变量}+文本”）")
    private List<EmrContent> emrContent;


}
