package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SRoadTaskWestMapping implements Serializable{

    @Schema
    private String westPrescriptionsId;

    @Schema
    private String sRoadTaskId;

    @Schema
    private String patientId;


}
