package com.cbkj.diagnosis.beans.task.webvo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Schema(description =  "更新插入随访任务")
@NoArgsConstructor
@Data
public class InsertOrUpdateTask implements Serializable {
    @Schema(description =  "随访任务id")
    private String sRoadTaskId;
    @Schema(description =  "任务名称", required = true)
    private String taskName;
    @Schema(description =  "随访路径ID", required = true)
    private String sRoadId;
    @Schema(description="任务说明")
    private String taskContent;





//    @Schema(description="自动入组条件：就诊开始时间")
//    private Data recordStartTime;
//    @Schema(description="自动入组条件：就诊结束时间")
//    private Data recordEndTime;
//
//    @Schema(description="自动入组条件：就诊结束时间")
//    private Integer limitDiagnosisDaysInfo;
//
//    @Schema(description="自动入组条件：几日内重复就诊的患者不入组")
//    private Integer limitRepeatRecord;
//
//    @Schema(description="是否能自动加入计划0.可以 1.不行")
//    private String joinRoadTask;
//    @Schema(description="科室")
//    private String deptId;
//
//    @Schema(description="随访成员医生id")
//    private String diagnosisDoctorId;
//
//    @Schema(description="就诊医生id")
//    private String recordDoctorId;


    @Schema(description =  "自动入组任务：任务条件")
    private RoadTaskConditons roadTaskConditons;
}
