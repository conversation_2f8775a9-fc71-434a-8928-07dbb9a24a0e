package com.cbkj.diagnosis.beans.health;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class MedicalWestPrescriptionsItem implements Serializable{
    @TableId(value = "west_item_id", type = IdType.AUTO)
    @Schema
    private Long westItemId;

    @Schema(description =  "药物使用次剂量")
    private String drugDose;

    @Schema(description =  "药物使用剂量单位")
    private String drugDoseUnit;

    @Schema(description =  "药物使用频次")
    private String drugFrequency;

    @Schema(description =  "药物名称")
    private String drugName;

    @Schema(description =  "药物规格")
    private String drugSpecifications;

    @Schema
    private String westPrescriptionsId;

    @Schema(description =  "药物剂型")
    private String drugDosage;

    @Schema(description =  "用药途径")
    private String administrationRoute;

    @Schema(description =  "药物使用总剂量")
    private String drugTotalDosage;

    /**
     * 20250304 新增
     */
    @Schema(description =  "药物使用频次代码")
    private String drugFrequencyCode;

    @Schema(description =  "用药途径代码")
    private String administrationRouteCode;
}
