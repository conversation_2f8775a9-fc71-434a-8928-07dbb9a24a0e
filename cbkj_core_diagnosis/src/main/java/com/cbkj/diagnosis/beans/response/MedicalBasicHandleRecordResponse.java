package com.cbkj.diagnosis.beans.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/7 10:00
 *
 * @description：处置信息
 */
@Getter
@Setter
public class MedicalBasicHandleRecordResponse {

    @Schema(description =  "门（急）诊号")
    private String diagnosticNo;

    @Schema(description =  "电子申请单号")
    private String eleAppForm;

    @Schema(description =  "科室名称")
    private String deptName;

    @Schema(description =  "年龄")
    private String patientAge;

    @Schema(description =  "体重(kg)")
    private String weight;


    @Schema(description =  "中医操作编码")
    private String operationCode;

    @Schema(description =  "操作名称")
    private String operationName;

    @Schema(description =  "目标部位")
    private String operationTarget;

    @Schema(description =  "操作方法")
    private String operationMethod;

    @Schema(description =  "操作次数")
    private String operationNumber;
    @Schema(description =  "操作时间")
    private String operationTime;

    @Schema(description =  "适宜技术治疗描述")
    private String memo;

    @Schema(description="就诊类型")
    private String treatmentType;

    @Schema(description =  "开立医师签名")
    private String prescribingDoctorSign;

    @Schema(description =  "执行医护签名")
    private String executiveDoctorSign;

    @Schema(description =  "过敏史")
    private String allergyHistory;
    @Schema(description =  "药物过敏")
    private String allergicDrug;
}
