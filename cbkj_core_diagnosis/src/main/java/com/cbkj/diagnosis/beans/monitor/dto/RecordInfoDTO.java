package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Created by zbh on 2024/6/4 17:32
 *
 * @description：记录信息
 */
@Schema
@Data
public class RecordInfoDTO {

    @Schema(description =  "对应类型的id")
    private String id;
    @Schema(description =  "中医疾病  code=1 , message=预诊 ,code=2 , message=就诊 ,code=3 , message=宣教 ,code=4 , message=随访")
    private String recordTypeCode;

    @Schema(description =  "名字")
    private String recordType;

    @Schema(description =  "名字2")
    private String recordType2;

    @Schema(description =  "名字2")
    private String closedStatus;

    @Schema(description =  "时间")
    private String timedate;

    @Schema(description =  "唯一id")
    private String id2;

}
