package com.cbkj.diagnosis.beans.business.requestvo;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DiaIdentificationVo.java
 * @Description TODO
 * @createTime 2025年09月01日 11:07:00
 */
@Data
public class DiaIdentificationVo {
    @Schema(description =  "预诊单ID")
    private String diaId;

    @Schema(description =  "多个科室,分隔")
    private String deptIds;

    @Schema(description =  "科室预诊单名称")
    private String deptDiaName;

    @Schema(description =  "专病概述明细")
    private List<TPreDiagnosisDisMapping> disMappings;
}
