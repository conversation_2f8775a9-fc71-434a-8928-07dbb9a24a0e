package com.cbkj.diagnosis.beans.health;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MedicalRecordsDiagnosis.java
 * @Description TODO
 * @createTime 2025年03月10日 10:57:00
 */
@Data
public class MedicalRecordsDiagnosis implements Serializable {
    private String id;
    /**
     * 病历id
     */
    private String recordId;

    /**
     * 诊断id
     */
    private String diagnosisId;

    /**
     * 诊断代码
     */
    private String diagnosisCode;

    /**
     * 诊断名称
     */
    private String diagnosisName;

    /**
     * 诊断类型
     * 1:西医诊断；2:中医疾病；3:中医证型
     */
    private String diagnosisType;

    /**
     * 诊断标志
     * 1:主诊断；2:次诊断
     */
    private String diagnosisFlag;


}
