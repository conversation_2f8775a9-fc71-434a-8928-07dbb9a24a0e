package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 问卷-疾病映射
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Getter
@Setter
@TableName("t_pre_diagnosis_dis_mapping")
@Schema(description =  "TPreDiagnosisDisMapping对象")
public class TPreDiagnosisDisMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="预诊单id")
    @TableId("dia_id")
    private String diaId;

    @Schema(description="疾病id")
    @TableField("dis_id")
    private String disId;

    @Schema(description="疾病名称")
    @TableField("dis_name")
    private String disName;

    @Schema(description="疾病类型（1中医疾病   2西医疾病）")
    @TableField("dis_type")
    private String disType;

    @TableField("dis_code")
    private String disCode;


}
