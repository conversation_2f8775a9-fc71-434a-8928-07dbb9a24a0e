package com.cbkj.diagnosis.beans.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/7 15:06
 *
 * @description：费用清单
 */
@Getter
@Setter
public class ManifestFeeResponse {

    @Schema(description="患者诊疗费用")
    private Long recordCostId;

    @Schema(description="就诊记录id")
    private String recordsId;

    @Schema(description=" 收费项目大类名称-对应medical_patient_record_big_type解释名字")
    private String medicalPatientRecordBigTypeName;

    @Schema(description=" 收费项目子集类型名称-对应medical_patient_record_item_type解释名字")
    private String medicalPatientRecordItemTypeName;

    @Schema(description=" 实际改类型下的收费名称")
    private String medicalPatientRecordActualName;

    @Schema(description=" 收费项目费用")
    private String medicalPatientRecordCost;

    @Schema(description=" 项目大类代码：综合医疗服务类1，中医类2，诊断类3，中药类4，西药类5，治疗类6，其他类7，煎药费用金额 10，个人承担费 11，医保报销金额 12，门 (急) 诊费用金额（元） 13，医疗费用结算方式代码 14")
    private String medicalPatientRecordBigType;

    @Schema(description=" 项目大类子类代码，。前是大类代码，点后是子类，合起来确定唯一。：1.1 一般医疗服务费-中医辨证论治费，2.1 中医治疗费，3.1 影像学诊断费 3.2 实验室诊断费，4.1 中草药费 、4.2 中成药费 4.3 中成药费-医疗机构中药制剂费，5.1 西药费，6.1 手术治疗费，7.1 其他费，煎药费用金额 10 个人承担费 11医保报销金额 12门 (急) 诊费用金额（元） 13医疗费用结算方式代码 14")
    private String medicalPatientRecordItemType;

    @Schema(description=" 收费项目剂型")
    private String medicalPatientRecordDosage;

}
