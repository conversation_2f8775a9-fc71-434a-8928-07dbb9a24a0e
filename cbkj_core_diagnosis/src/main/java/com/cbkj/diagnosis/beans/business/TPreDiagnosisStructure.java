package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Getter
@Setter
@TableName("t_pre_diagnosis_structure")
@Schema(description =  "TPreDiagnosisStructure对象")
public class TPreDiagnosisStructure implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="主键")
    @TableId("diagnosis_structure_id")
    private String diagnosisStructureId;

    @Schema(description="预诊表单id")
    @TableField("dia_id")
    private String diaId;

    @Schema(description="主诉、现病史等")
    @TableField("emr_type")
    private String emrType;

    @Schema(description="对应字典值")
    @TableField("type_code")
    private String typeCode;

    @Schema(description="排序")
    @TableField("sort")
    private Integer sort;


}
