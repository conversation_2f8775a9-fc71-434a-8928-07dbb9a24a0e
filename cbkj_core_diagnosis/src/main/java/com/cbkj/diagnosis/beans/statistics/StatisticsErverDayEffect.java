package com.cbkj.diagnosis.beans.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Getter
@Setter
@TableName("statistics_erver_day_effect")
@Schema(description =  "StatisticsErverDayEffect对象")
public class StatisticsErverDayEffect implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="疗效评价")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description="统计时间")
    @TableField("create_time")
    private Date createTime;

    @TableField("app_id")
    private String appId;

    @TableField("ins_id")
    private String insId;
//    private String dicCode;
//    private String dicName;
//    private String dicId;
//@TableField(exist = false)
//    private String patientId;

    @TableField("ins_code")
    private String insCode;

    @TableField("ins_name")
    private String insName;

    @TableField("dis_id")
    private String disId;

    @TableField("dis_name")
    private String disName;

    @TableField("dis_code")
    private String disCode;

    @Schema(description="统计数")
    @TableField("num")
    private Integer num;

    @Schema(description="疗效评价1.有效2.显效3.痊愈4无效")
    @TableField("type")
    private Integer type;


}
