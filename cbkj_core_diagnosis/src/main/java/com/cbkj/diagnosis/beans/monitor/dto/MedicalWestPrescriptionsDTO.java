package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zbh on 2024/6/5 15:51
 *
 * @description：西医处方
 */
@Getter
@Setter
@Schema(description =  "西医处方")
public class MedicalWestPrescriptionsDTO {

    @Schema(description =  "处方编号")
    private String preNo;
    @Schema(description =  "开方科室")
    private String deptName;
    @Schema(description =  "处方类别")
    private String prescriptionCategory;
    @Schema(description =  "开立日期")
    private String prescriptionsTime;
    @Schema(description =  "有效天数")
    private String prescriptionsEffectiveDays;

    @Schema(description =  "西医诊断")
    private String westDisName;

    @Schema(description =  "西医处方明细")
    private List<MedicalWestPrescriptionsItemDTO> westPrescriptionsItemDTO;

    @Schema(description =  "处方备注")
    private String prescriptionRemark;
    @Schema(description =  "开方医师")
    private String prescribingDoctorSign;
    @Schema(description =  "审方药剂师")
    private String reviewedDoctorSign;
}
