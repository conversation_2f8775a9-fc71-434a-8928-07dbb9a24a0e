package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class TPreDiagnosisSkip implements Serializable{

    @Schema(description =  "跳题id ")
    private Integer skipId;

    @Schema(description =  "选项id")
    private Integer optionId;

    @Schema
    private String optionName;

    @Schema(description =  "跳过的题目id")
    private Integer skipQuestionId;
    @Schema(description =  "暂留前端过来的string类型id")
    private String skipQuestionIdStr;


}
