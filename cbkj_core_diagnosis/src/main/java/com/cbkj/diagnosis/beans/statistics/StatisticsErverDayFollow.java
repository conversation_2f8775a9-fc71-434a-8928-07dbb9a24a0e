package com.cbkj.diagnosis.beans.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class StatisticsErverDayFollow implements Serializable {

    @Schema(description =  "主键id，自递增")
    private Long followUpId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "统计日期")
    private Date createTime;

    @Schema(description =  "今日随访人数")
    private Integer num;

    @Schema(hidden = true)
    private String insCode;

    @Schema(hidden = true)
    private String doctorId;
    @Schema
    private String appId;

    @Schema
    private String insName;

    @Schema(description =  "1.电话随访2.问卷")
    private Integer type;

    @Schema
    private String disId;

    @Schema
    private String disName;

    @Schema
    private String disCode;

}
