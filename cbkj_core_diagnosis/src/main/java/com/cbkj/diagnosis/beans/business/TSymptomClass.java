package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema
public class TSymptomClass implements Serializable {

    @Schema(description="症状分类id")
    private Integer symptomClassId;
    @Schema(description="症状分类名称")
    private String symptomClassName;
    @Schema(description="层级 1 是顶层 子级 依次递增")
    private Integer classHierarchy;

    @Schema(description="父级id 默认0")
    private Integer parentClassId;

    @Schema(description="子级的")
    private List<TSymptomClass> children;

}
