package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/6 10:05
 * @Version 1.0
 */
@Getter
@Setter
@TableName("sys_admin_info_dis_mapping")
@Schema(description = "SysAdminInfoDisMapping对象")
public class SysAdminInfoDisMapping implements Serializable {

    public SysAdminInfoDisMapping(String disId, String disName, String userId,Integer sort){
        this.disId = disId;
        this.disName = disName;
        this.userId = userId;
        this.sort = sort;
    }
    private static final long serialVersionUID = 1L;

    @TableField("user_id")
    private String userId;

    @TableField("dis_id")
    private String disId;

    @TableField("dis_name")
    private String disName;

    @TableField("sort")
    private Integer sort;
}
