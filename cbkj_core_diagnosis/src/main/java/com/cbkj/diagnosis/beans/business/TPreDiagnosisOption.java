package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class TPreDiagnosisOption implements Serializable {

    @Schema(description =  "选项id")
    private Integer optionId;
    private String optionCode;
    @Schema(description =  "选项id,暂存前端传过来的optionId", hidden = true)
    private String optionIdStr;

    @Schema(description =  "问题id")
    private Integer questionId;

    @Schema(description =  "选项类型（1文本  2症状）")
    private String optionType;

    @Schema(description =  "选项名称")
    private String optionName;

    @Schema(description =  "症状id （症状填）")
    private String symptomId;

    @Schema(description =  "排序号")
    private Integer optionSort;

    @Schema(description =  "选项文字说明")
    private String optionContent;

    @Schema(description =  "选项图片说明")
    private String optionImage;

    @Schema(description =  "选项填空提示语")
    private String optionFillBlank;

    @Schema(description =  "选项填空填空是否必填 1.是 0否")
    private Integer optionFillCheck = 0;

    @Schema(description =  "病历转换术语")
    private String optionStructureValue;

//    @Schema(description =  "随访事件代码")
//    private String optionFollowEventCode;

    @Schema(description =  "保留选项原文 1保留 0不保留")
    private Integer optionStructureSaveBlank;
    @Schema(description =  "单选、多选有选项填空时候：0未勾了保留患者输入 1勾了 保留患者输入")
    private Integer optionStructureSavePatient ;



    @Schema(description =  "V2.1.0-选项维度分数")
    private Double optionDimensionScore;

    @Schema(description =  "V2.1.0-选项维度分数开关，1开0关")
    private Integer optionDimensionScoreSwitch;

    @Schema(description =  "V2.1.0-是否发送复诊提醒1开0关")
    private Integer followUpVisitStatus;

    @Schema(description =  "V2.1.0-复诊提醒id")
    private Integer followUpVisitId;

}
