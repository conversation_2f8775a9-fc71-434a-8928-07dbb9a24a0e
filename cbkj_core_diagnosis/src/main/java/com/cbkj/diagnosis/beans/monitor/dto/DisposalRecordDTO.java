package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/5 16:07
 *
 * @description：处置记录
 */
@Getter
@Setter
@Schema(description =  "处置记录")
public class DisposalRecordDTO {

    @Schema(description =  "门（急）诊号")
    private String diagnosticNo;

    @Schema(description =  "电子申请单号")
    private String eleAppForm;

    @Schema(description =  "科室名称")
    private String deptName;

    @Schema(description =  "年龄")
    private String patientAge;

    @Schema(description =  "体重(kg)")
    private String weight;

    @Schema(description =  "西医处置")
    private WesternMedicineTreatmentDTO westernMedicineTreatment;

    @Schema(description =  "中医处置")
    private TCMTreatmentDTO TCMTreatment;

    @Schema(description =  "过敏史")
    private String allergyHistory;
    @Schema(description =  "药物过敏")
    private String allergicDrug;
}
