package com.cbkj.diagnosis.beans.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class StatisticsErverDayDiagnosis implements Serializable{

    @Schema(description =  "今天统计昨天数量")
    private Long id;

    @Schema(description =  "统计日期")
    private Date createTime;

    @Schema(description =  "今日（统计日期）患者有效填写预诊单的数量")
    private Integer num;

    @Schema
    private String appId;

    @Schema(description =  "机构代码")
    private String insCode;

    @Schema(description =  "机构id")
    private String insId;

    @Schema(description =  "机构名称")
    private String insName;

    @Schema
    private String disId;

    @Schema
    private String disName;

    @Schema
    private String disCode;


}
