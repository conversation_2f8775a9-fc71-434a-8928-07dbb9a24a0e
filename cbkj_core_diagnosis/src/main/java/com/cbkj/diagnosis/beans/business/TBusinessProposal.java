package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 业务管理建议问题咨询表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Getter
@Setter
@TableName("t_business_proposal")
@Schema(description =  "TBusinessProposal对象")
public class TBusinessProposal implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description="APPID")
    @TableField("app_id")
    private String appId;

    @Schema(description="医联体名称")
    @TableField("app_name")
    private String appName;

    @Schema(description="医疗机构编码")
    @TableField("ins_code")
    private String insCode;

    @Schema(description="医疗机构名称")
    @TableField("ins_name")
    private String insName;

    @Schema(description="标题限制20字")
    @TableField("proposal_title")
    private String proposalTitle;

    @Schema(description="建议内容限制500字")
    @TableField("proposal_content")
    private String proposalContent;

    @Schema(description="联系方式")
    @TableField("proposal_liaison")
    private String proposalLiaison;

    @Schema(description="反馈类型：1建议，2问题 3.咨询")
    @TableField("proposal_type")
    private Integer proposalType;

    @Schema(description="受理状态：0受理，1未受理")
    @TableField("proposal_receive_state")
    private Integer proposalReceiveState;

    @Schema(description="受理意见")
    @TableField("proposal_receive_opinion")
    private String proposalReceiveOpinion;

    @Schema(description="受理人ID")
    @TableField("proposal_receive_id")
    private String proposalReceiveId;

    @Schema(description="受理人姓名")
    @TableField("proposal_receive_name")
    private String proposalReceiveName;

    @Schema(description="创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description="创建者ID")
    @TableField("create_user_id")
    private String createUserId;

    @Schema(description="创建者姓名")
    @TableField("create_user_name")
    private String createUserName;

    @Schema(description="修改时间")
    @TableField("update_time")
    private Date updateTime;

    @Schema(description="修改者ID")
    @TableField("update_user_id")
    private String updateUserId;

    @Schema(description="删除时间")
    @TableField("delete_time")
    private Date deleteTime;

    @Schema(description="删除者ID")
    @TableField("delete_user_id")
    private String deleteUserId;

    @Schema(description="是否删除（0否 1是）")
    @TableField("is_del")
    private Boolean isDel;

    @Schema(description="来源（1.医生端2.患者端）")
    @TableField("source_from")
    private int sourceFrom;


}
