package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/5 13:56
 *
 * @description：辅助检查
 */
@Getter
@Setter
@Schema(description =  "辅助检查")
public class AuxiliaryInspectionDTO {

    @Schema(description =  "辅助检查项目")
    private String auxiliaryInspectionItems;

    @Schema(description =  "辅助检查结果")
    private String auxiliaryInspectionResults;

}
