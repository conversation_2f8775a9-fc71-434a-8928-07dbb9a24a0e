package com.cbkj.diagnosis.beans.task.webvo;

import com.cbkj.diagnosis.beans.task.SRoad;
import com.cbkj.diagnosis.beans.task.SRoadConditions;
import com.cbkj.diagnosis.beans.task.SRoadExecute;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description =  "更新插入路径")
@NoArgsConstructor
@Data
public class UpdateOrInsertRoad implements Serializable {

    @Schema(description="路径基础信息")
    private SRoad sRoad;


    @Schema(description="自动入组条件")
    private List<SRoadConditions> roadConditionsList;


    @Schema(description="执行方案")
    private List<SRoadExecute> sRoadExecuteList;
}
