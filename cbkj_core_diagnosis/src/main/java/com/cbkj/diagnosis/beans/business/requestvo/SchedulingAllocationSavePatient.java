package com.cbkj.diagnosis.beans.business.requestvo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema
public class SchedulingAllocationSavePatient {
//    @ApiModelProperty(required = true,value = "患者id")
//    private String patientId;
//
//    @Schema(description =  "路径id",required = true)
//    private String sRoadTaskId;
//
//    @Schema(description =  "随访路径执行方案id",required = true)
//    private Long roadExecuteId;


    @Schema(description =  "就诊记录id",required = true)
    private String recordsId;

    @Schema(description =  "患者任务id",required = true)
    private Long taskPatientsId;
}
