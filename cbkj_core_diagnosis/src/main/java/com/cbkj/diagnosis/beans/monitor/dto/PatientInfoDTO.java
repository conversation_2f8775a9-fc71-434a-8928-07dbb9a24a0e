package com.cbkj.diagnosis.beans.monitor.dto;


import com.cbkj.diagnosis.beans.response.TDiseaseResponse;
import com.cbkj.diagnosis.common.enums.RecordTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zbh on 2024/6/4 15:11
 *
 * @description：患者信息DTO
 */
@Getter
@Setter
@Schema(description =  "患者信息 ")
public class PatientInfoDTO {

    /**
     * 患者身份id（t_admin_info）
     */
    @Schema(description =  "患者身份id")
    private String patientId;

    /**
     * 患者名字
     */
    @Schema(description =  "患者名字")
    private String patientName;


    /**
     * 患者性别
     */
    @Schema(description =  "患者性别")
    private String patientSex;

    /**
     * 患者手机号
     */
    @Schema(description =  "患者手机号")
    private String patientPhone;

    /**
     * 患者年龄
     */
    @Schema(description =  "患者年龄")
    private String patientAge;

    /**
     * 患者身份证
     */
    @Schema(description =  "患者身份证")
    private String patientCardNumber;


    /**
     * 身份证件类别代码
     */
    @Schema(description =  "身份证件类别代码")
    private String patientCardType;

    /**
     * 居民健康卡号
     */
    @Schema(description =  "居民健康卡号")
    private String patientHealthCardNum;

    /**
     * 医疗保险类别代码
     */
    @Schema(description =  "医疗保险类别代码")
    private String insuranceTypeCode;

    /**
     * 婚姻状况代码
     */
    @Schema(description =  "婚姻状况代码")
    private String maritalStatus;

    /**
     * 民族
     */
    @Schema(description =  "民族")
    private String nation;


    /**
     * 国籍代码
     */
    @Schema(description =  "国籍代码")
    private String nationality;

    /**
     * 学历代码
     */
    @Schema(description =  "学历代码")
    private String educationCode;

    /**
     * 中医疾病
     */
    @Schema(description =  "中医疾病列表")
    private List<TDiseaseResponse> chineseDiseases;

    @Schema(description =  "类型 (中医疾病  code=1 , message=预诊 ,code=2 , message=就诊 ,code=3 , message=宣教 ,code=4 , message=随访")
    private RecordTypeEnum[] recordTypeEnums;

    @Schema(description =  "记录信息-档案详情-时间轴")
    private List<RecordInfoDTO> recordList;
}
