package com.cbkj.diagnosis.beans.business.requestvo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@NoArgsConstructor
@Schema
public class SRoadTaskPatientsPhoneRe {

    @Schema(description =  "电话随访患者任务映射表id，患者排期，如果更新需要带上这个字段值")
    private Long sRoadTaskPatientsPhoneId;

//    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    @Schema(description =  "随访日期",required = true)
    private String taskExcuteTime;
}
