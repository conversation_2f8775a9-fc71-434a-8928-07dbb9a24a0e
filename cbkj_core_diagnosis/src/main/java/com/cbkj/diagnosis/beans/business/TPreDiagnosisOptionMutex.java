package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class TPreDiagnosisOptionMutex implements Serializable{

    @Schema(description =  "互斥选项表")
    private Integer mutexId;

    @Schema(description =  "选项id")
    private Integer optionId;

    @Schema(description =  "选项名称")
    private String optionName;

    @Schema(description =  "互斥选项id")
    private Integer mutexOptionId;


}
