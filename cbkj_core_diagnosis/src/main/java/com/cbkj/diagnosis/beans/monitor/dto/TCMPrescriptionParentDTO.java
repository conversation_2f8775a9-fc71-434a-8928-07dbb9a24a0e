package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/20 10:48
 * @Version 1.0
 */
@Getter
@Setter
@Schema(description =  "中医处方信息父类")
public class TCMPrescriptionParentDTO {



    @Schema(description =  "中医处方")
    private List<TCMPrescriptionDTO> tcmPrescriptionDTO;
}
