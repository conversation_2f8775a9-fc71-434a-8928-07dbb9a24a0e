package com.cbkj.diagnosis.beans.statistics;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/26 18:01
 * @Version 1.0
 */
@Data
public class BlockOne {

    private List<BlockOneDetail> patientServiceNumList;
    private List<BlockOneDetail> diagnosisNumList;
    private List<BlockOneDetail> folwNumList;
    private List<BlockOneDetail> eduHealthNumList;
}
