package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/5 13:54
 *
 * @description：辨证分析
 */
@Getter
@Setter
@Schema(description =  "辨证分析")
public class DialecticalAnalysisDTO {

    @Schema(description =  "辨证依据")
    private String syndromeDifferentiationBasis;

    @Schema(description =  "治则治法")
    private String tcmApproach;

    @Schema(description =  "中医病因分类")
    private String etiologyClassification;

    @Schema(description =  "中医辨证方法")
    private String syndromeDifferentiationMethod;

    @Schema(description =  "中医基本病机")
    private String tcmPathogenesis;

    @Schema(description =  "体质影响因素")
    private String physicalAffectingFactors;

    @Schema(description =  "中医发病类型")
    private String tcmIncidentsType;

    @Schema(description =  "中医发病形式")
    private String tcmIncidentsForm;
}
