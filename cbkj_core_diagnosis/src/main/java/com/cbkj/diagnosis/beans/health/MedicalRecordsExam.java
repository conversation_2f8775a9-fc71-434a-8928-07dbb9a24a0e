package com.cbkj.diagnosis.beans.health;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MedicalRecordsExam.java
 * @Description TODO
 * @createTime 2025年02月27日 16:15:00
 */
@Data
public class MedicalRecordsExam implements Serializable {
    @TableId
    private String examId;
    /**
     * 病历编号
     */
    private String recordId;
    /**
     * 电子申请单编号
     */
    private String applyNo;
    /**
     * 检查申请科室
     */
    private String applyDeptName;
    /**
     * 检查申请医生
     */
    private String applyDoctorName;
    /**
     * 检查类别
     */
    private String examClass;
    /**
     * 检查部位
     */
    private String examPart;
    /**
     * 检查目的
     */
    private String examObjective;
    /**
     * 检查项目代码
     */
    private String examItemCode;
    /**
     * 检查医师签名
     */
    private String docName;
    /**
     * 检查时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date examTime;
    /**
     * 检查报告单编号
     */
    private String reportNo;
    /**
     * 检查报告科室
     */
    private String reportDeptName;
    /**
     * 检查报告机构名称
     */
    private String reportInsName;
    /**
     * 检查报告结果-客观所见
     */
    private String reportObjFeature;
    /**
     * 检查报告结果-主观提示
     */
    private String reportSubFeature;
    /**
     * 检查报告日期时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;
    /**
     * 审核医师签名
     */
    private String checkDocName;

}
