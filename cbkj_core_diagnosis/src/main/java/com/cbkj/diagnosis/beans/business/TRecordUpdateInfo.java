package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Data
@NoArgsConstructor
@Schema
public class TRecordUpdateInfo implements Serializable {

    private String id;

    @Schema(description =  "记录id")
    private String recId;

    @Schema(description =  "创建时间")
    private Date createDate;

    @Schema(description =  "创建人")
    private String createName;

    @Schema(description =  "修改后的记录id")
    private String newRecId;

}
