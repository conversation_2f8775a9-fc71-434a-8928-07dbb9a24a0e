package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.cbkj.diagnosis.beans.TAdminInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EduSendMessage {

    @Schema(description =  "健康宣教id")
    private Integer tPropagandaEduId;

    @Schema(description =  "健康宣教标题")
    private String eduTitle;

    @Schema(description =  "健康宣教发送方式1.微信2短信")
    private String  roadExecuteEventWay;

    @Schema(description =  "就传里面的 patientList.userId")
    private List<TAdminInfo> patientList;

    @Schema(description =  "执行的具体内容类型1.健康宣教2.随访问卷（随访的一种类型）3.复诊提醒4.自测量表(随访的一种类型) 5.用药指导")
    private String roadExecuteEventType;

    @Schema(description =  "V2.1.0-随访量表中配置复诊提醒如果要发送这个宣教需要传问卷的创建者id")
   private String createUserId;
}
