package com.cbkj.diagnosis.beans.diagnosisstructure;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/15 10:17
 * @Version 1.0
 */
@Schema
@Data
public class EmrContent {
    @Schema(hidden = true)
    @JSONField(serialize = false)
    private Integer structureContentId;

    @Schema(description =  "文本+{变量}+文本” 分组数组对象")
    private List<Paragraph> paragraph;
}
