package com.cbkj.diagnosis.beans.health;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.io.Serializable;

@Getter
@Setter
@TableName("medical_west_prescriptions")
@Schema(description =  "MedicalWestPrescriptions对象")
public class MedicalWestPrescriptions implements Serializable{

    @TableId(value = "west_prescriptions_id")
    @Schema
    private String westPrescriptionsId;

    @Schema(description =  "就诊记录id")
    private String recordsId;

    @Schema(description =  "处方开立日期(示例：2023-11-21 15:10:10)")
    private Date prescriptionsTime;

    @Schema(description =  "处方有效天数")
    private String prescriptionsEffectiveDays;

    @Schema(description =  "处方备注信息")
    private String prescriptionRemark;

    @Schema(description =  "处方开立医师签名")
    private String prescribingDoctorSign;

    @Schema(description =  "处方审核药剂师签名")
    private String reviewedDoctorSign;


}
