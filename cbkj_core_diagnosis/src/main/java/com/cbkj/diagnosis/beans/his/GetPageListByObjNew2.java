package com.cbkj.diagnosis.beans.his;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import lombok.Data;

@Data
public class GetPageListByObjNew2 {

    private String appId;
    private String insCode;

    @CBKJEncryptField
    private String mobile;
    @CBKJEncryptField
    private String cardNumber;
    private String cardType;
    private String healthCardNum;
    private String startDate;
    private String endDate;
    private Integer page;
    private Integer limit;

    private String recId;
}
