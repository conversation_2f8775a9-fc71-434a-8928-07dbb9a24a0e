package com.cbkj.diagnosis.beans.business;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CompletedQuestionnaireListRes implements Serializable {
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "完成时间")
    private Date finishTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "发送时间")
    private Date sendTime;

    @Schema(description =  "患者任务执行明细id")
    private String taskPatientsId;
    private String sRoadTaskId;

    @Schema(description =  "预诊单id")
    private String diaId;
    @Schema(description =  "患者id")
    private String patientId;

    @CBKJDecryptField
    private String patientName;
    private String patientSex;
    private String patientAge;

    @CBKJDecryptField
    private String patientCardNum;

    @Schema(description =  "问卷名称")
    private String formName;

    @Schema(description =  "任务名称")
    private String taskName;
    @Schema(description =  "1.微信2短信3.电话。")
    private String roadExecuteEventWay;
    private String recId;
}
