package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Getter
@Setter
@TableName("sys_dic")
@Schema(description = "SysDic对象")
public class SysDic implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "字典主键id")
    @TableId("dic_id")
    private String dicId;

    @Schema(description = "字典名称")
    @TableField("dic_name")
    private String dicName;

    @Schema(description = "字典简写名称")
    @TableField("dic_shot_name")
    private String dicShotName;

    @Schema(description = "字典代码")
    @TableField("dic_code")
    private String dicCode;

    @Schema(description = "字典的值")
    @TableField("dic_value")
    private String dicValue;

    @Schema(description = "排序")
    @TableField("dic_sort")
    private Integer dicSort;

    @Schema(description = "1有效0无效")
    @TableField("status")
    private Integer status;

    @Schema(description = "父id")
    @TableField("parent_id")
    private String parentId;


    public Map<String, String> getExtend() {
        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        //  objectObjectHashMap.put(this.dicName,this.dicCode);
        objectObjectHashMap.put("value", this.dicCode);
        objectObjectHashMap.put("label", this.dicName);
        objectObjectHashMap.put("style", this.dicValue);
        objectObjectHashMap.put("sampleLabel", this.dicShotName);
        return objectObjectHashMap;
    }
}
