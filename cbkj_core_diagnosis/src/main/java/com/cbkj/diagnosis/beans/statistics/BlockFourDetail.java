package com.cbkj.diagnosis.beans.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 不良反应
 * @Date 2024/1/29 11:28
 * @Version 1.0
 */
@Data
public class BlockFourDetail {

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description="日期")
    private Date x;

    @Schema(description="数量")
    private Integer y;
}
