package com.cbkj.diagnosis.beans.health;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class MedicalRecordsNoEn extends Model<MedicalRecordsNoEn> implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 就诊记录id
     */
    @TableId(value = "records_id", type = IdType.INPUT)
    private String recordsId;

    /**
     * 就诊医生名字
     */

    private String doctorName;

    /**
     * 医生ID
     */
    private String doctorId;

    /**
     * 应用ID（由预诊随访系统分配给HIS系统接入的唯一标识）
     */
    private String appId;

    /**
     * 就诊科室名称
     */
    private String deptName;

    /**
     * 机构名
     */
    private String insName;

    private String insId;

    /**
     * 机构代码
     */
    private String insCode;

    /**
     * 科室代码
     */
    private String deptCode;

    /**
     * 患者就诊科室id
     */
    private String deptId;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 现病史
     */
    private String presentIllness;

    /**
     * 既往史
     */
    private String pastHistory;

    /**
     * 个人史
     */
    private String personalHistory;

    /**
     * 家族史
     */
    private String familyHistory;

    /**
     * 体格检查
     */
    private String physicalExamination;

    /**
     * 中医疾病名
     */
    private String chineseDisName;

    /**
     * 西医疾病名
     */
    private String westDisName;

    /**
     * 中医疾病id
     */
    private String chineseDisId;

    /**
     * 西医疾病id
     */
    private String westDisId;

    /**
     * 证型名称
     */
    private String symName;

    /**
     * 证型id
     */
    private String symId;

    /**
     * 治疗意见
     */
    private String recordsAdvice;

    /**
     * 就诊时间
     */
    private Date recordTime;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 诊次
     */
    private Integer recordsTimes;

    /**
     * 患者名字
     */

    private String patientName;

    /**
     * 患者身份id（t_admin_info）
     */
    private String patientId;

    /**
     * 患者性别
     */

    private String patientSex;

    /**
     * 患者手机号
     */

    private String patientPhone;

    /**
     * 患者年龄
     */
    private String patientAge;

    /**
     * 患者身份证
     */

    private String patientCardNumber;

    /**
     * his就诊号
     */
    @TableField("VISIT_NO")
    private String visitNo;

    /**
     * 城乡居民健康档案编号
     */
    private String healthFilesNum;

    /**
     * 身份证件类别代码
     */
    private String patientCardType;

    /**
     * 居民健康卡号
     */
    private String patientHealthCardNum;

    /**
     * 医疗保险类别代码
     */
    private String insuranceTypeCode;

    /**
     * 婚姻状况代码
     */
    private String maritalStatus;

    /**
     * 民族
     */
    private String nation;

    /**
     * 联系人姓名
     */
    private String contactsName;

    /**
     * 联系人电话号码
     */
    private String contactsPhone;

    /**
     * 国籍代码
     */
    private String nationality;

    /**
     * 学历代码
     */
    private String educationCode;

    /**
     * 联系人与患者的关系代码
     */
    private String contactsRelationship;

    /**
     * 中医基本舌象诊断信息
     */
//    private String chineseBaseTongueInfo;

    /**
     * 中医基本脉象诊断信息
     */
//    private String chineseBasePulse;

    /**
     * 辅助检查结果
     */
    private String auxiliaryInspectionResults;

    /**
     * 辨证依据
     */
    private String syndromeDifferentiationBasis;

    /**
     * 治则治法
     */
    private String tcmApproach;

    /**
     * 中医病因分类
     */
    private String etiologyClassification;

    /**
     * 中医辨证方法
     */
    private String syndromeDifferentiationMethod;

    /**
     * 中医基本病机
     */
    private String tcmPathogenesis;

    /**
     * 体质影响因素
     */
    private String physicalAffectingFactors;

    /**
     * 中医发病类型
     */
    private String tcmIncidentsType;

    /**
     * 中医发病形式
     */
    private String tcmIncidentsForm;

    /**
     * 检查检验项目信息
     */
    private String inspectionInfoString;

    /**
     * 医师签名
     */
    private String yiShiSign;

    /**
     * 职业(患者当前职业范畴的完整描述)
     */
    private String occupation;

    /**
     * 发病节气
     */
    private String onsetSolarTermCode;

    /**
     * 初诊标志 Y是 N否
     */
    private String initialDiagnosisCode;

    /**
     * 治疗类别
     */
    private String treatmentCategoryCode;

    /**
     * 实施临床路径
     */
    private String clinicalPathwayCode;

    /**
     * 科研病历标志
     */
    private String scientificResearchFlag;

    /**
     * 过敏史标志
     */
    private String allergyHistoryFlag;

    /**
     * 过敏史
     */
    private String allergyHistory;

    /**
     * 药物过敏标志
     */
    private String allergicDrugFlag;

    /**
     * 过敏药物
     */
    private String allergicDrug;

    /**
     * 传染病历标志
     */
    private String infectiousHistoryFlag;

    /**
     * 传染病史
     */
    private String infectiousHistory;

    /**
     * 预防接种史
     */
    private String vaccinationHistory;

    /**
     * 手术史
     */
    private String surgicalHistory;

    /**
     * 输血史
     */
    private String bloodTransfusionHistory;

    /**
     * 怀孕标志
     */
    private String pregnancyFlag;

    /**
     * 哺乳期标志
     */
    private String sucklingPeriodFlag;

    /**
     * 婚育史
     */
    private String obstetricHistory;

    /**
     * 月经史
     */
    private String menstrualHistory;

    /**
     * 身高
     */
    private String height;

    /**
     * 体重
     */
    private String weight;

    /**
     * BMI
     */
    private String bmi;

    /**
     * 辅助检查项目
     */
    private String auxiliaryInspectionItems;

    /**
     * 中医基本症状描述
     */
    private String symptomDescription;

    /**
     * 中医基本舌象诊断信息
     */
    private String tongueCondition;

    /**
     * 中医基本脉象诊断信息
     */
    private String pulseCondition;

    /**
     * 西医诊断代码
     */
    private String westDisCode;

    /**
     * 中医病名代码
     */
    private String chineseDisCode;

    /**
     * 中医证候代码
     */
    private String symCode;

    /**
     * 诊断标识代码
     */
    private String diagnosticFlag;


    /**
     * 门（急）诊号
     */
    private String diagnosticNo;


    /**
     * 中药处方信息
     */
    private String cnPreInfo ;

    /**
     * 西药、中成药处方
     */
    private String weCnPreInfo;

    /**
     * 一般治疗处置
     */
    private String commonOpInfo;


    /**
     * 医师签名
     */
//    private String doctorSign;


    public String getRecordsId() {
        return recordsId;
    }

    public void setRecordsId(String recordsId) {
        this.recordsId = recordsId;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public String getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(String doctorId) {
        this.doctorId = doctorId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getInsName() {
        return insName;
    }

    public void setInsName(String insName) {
        this.insName = insName;
    }

    public String getInsId() {
        return insId;
    }

    public void setInsId(String insId) {
        this.insId = insId;
    }

    public String getInsCode() {
        return insCode;
    }

    public void setInsCode(String insCode) {
        this.insCode = insCode;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getChiefComplaint() {
        return chiefComplaint;
    }

    public void setChiefComplaint(String chiefComplaint) {
        this.chiefComplaint = chiefComplaint;
    }

    public String getPresentIllness() {
        return presentIllness;
    }

    public void setPresentIllness(String presentIllness) {
        this.presentIllness = presentIllness;
    }

    public String getPastHistory() {
        return pastHistory;
    }

    public void setPastHistory(String pastHistory) {
        this.pastHistory = pastHistory;
    }

    public String getPersonalHistory() {
        return personalHistory;
    }

    public void setPersonalHistory(String personalHistory) {
        this.personalHistory = personalHistory;
    }

    public String getFamilyHistory() {
        return familyHistory;
    }

    public void setFamilyHistory(String familyHistory) {
        this.familyHistory = familyHistory;
    }

    public String getPhysicalExamination() {
        return physicalExamination;
    }

    public void setPhysicalExamination(String physicalExamination) {
        this.physicalExamination = physicalExamination;
    }

    public String getChineseDisName() {
        return chineseDisName;
    }

    public void setChineseDisName(String chineseDisName) {
        this.chineseDisName = chineseDisName;
    }

    public String getWestDisName() {
        return westDisName;
    }

    public void setWestDisName(String westDisName) {
        this.westDisName = westDisName;
    }

    public String getChineseDisId() {
        return chineseDisId;
    }

    public void setChineseDisId(String chineseDisId) {
        this.chineseDisId = chineseDisId;
    }

    public String getWestDisId() {
        return westDisId;
    }

    public void setWestDisId(String westDisId) {
        this.westDisId = westDisId;
    }

    public String getSymName() {
        return symName;
    }

    public void setSymName(String symName) {
        this.symName = symName;
    }

    public String getSymId() {
        return symId;
    }

    public void setSymId(String symId) {
        this.symId = symId;
    }

    public String getRecordsAdvice() {
        return recordsAdvice;
    }

    public void setRecordsAdvice(String recordsAdvice) {
        this.recordsAdvice = recordsAdvice;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getRecordsTimes() {
        return recordsTimes;
    }

    public void setRecordsTimes(Integer recordsTimes) {
        this.recordsTimes = recordsTimes;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getPatientSex() {
        return patientSex;
    }

    public void setPatientSex(String patientSex) {
        this.patientSex = patientSex;
    }

    public String getPatientPhone() {
        return patientPhone;
    }

    public void setPatientPhone(String patientPhone) {
        this.patientPhone = patientPhone;
    }

    public String getPatientAge() {
        return patientAge;
    }

    public void setPatientAge(String patientAge) {
        this.patientAge = patientAge;
    }

    public String getPatientCardNumber() {
        return patientCardNumber;
    }

    public void setPatientCardNumber(String patientCardNumber) {
        this.patientCardNumber = patientCardNumber;
    }

    public String getVisitNo() {
        return visitNo;
    }

    public void setVisitNo(String visitNo) {
        this.visitNo = visitNo;
    }

    public String getHealthFilesNum() {
        return healthFilesNum;
    }

    public void setHealthFilesNum(String healthFilesNum) {
        this.healthFilesNum = healthFilesNum;
    }

    public String getPatientCardType() {
        return patientCardType;
    }

    public void setPatientCardType(String patientCardType) {
        this.patientCardType = patientCardType;
    }

    public String getPatientHealthCardNum() {
        return patientHealthCardNum;
    }

    public void setPatientHealthCardNum(String patientHealthCardNum) {
        this.patientHealthCardNum = patientHealthCardNum;
    }

    public String getInsuranceTypeCode() {
        return insuranceTypeCode;
    }

    public void setInsuranceTypeCode(String insuranceTypeCode) {
        this.insuranceTypeCode = insuranceTypeCode;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getContactsName() {
        return contactsName;
    }

    public void setContactsName(String contactsName) {
        this.contactsName = contactsName;
    }

    public String getContactsPhone() {
        return contactsPhone;
    }

    public void setContactsPhone(String contactsPhone) {
        this.contactsPhone = contactsPhone;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getEducationCode() {
        return educationCode;
    }

    public void setEducationCode(String educationCode) {
        this.educationCode = educationCode;
    }

    public String getContactsRelationship() {
        return contactsRelationship;
    }

    public void setContactsRelationship(String contactsRelationship) {
        this.contactsRelationship = contactsRelationship;
    }

//    public String getChineseBaseTongueInfo() {
//        return chineseBaseTongueInfo;
//    }
//
//    public void setChineseBaseTongueInfo(String chineseBaseTongueInfo) {
//        this.chineseBaseTongueInfo = chineseBaseTongueInfo;
//    }

//    public String getChineseBasePulse() {
//        return chineseBasePulse;
//    }
//
//    public void setChineseBasePulse(String chineseBasePulse) {
//        this.chineseBasePulse = chineseBasePulse;
//    }

    public String getAuxiliaryInspectionResults() {
        return auxiliaryInspectionResults;
    }

    public void setAuxiliaryInspectionResults(String auxiliaryInspectionResults) {
        this.auxiliaryInspectionResults = auxiliaryInspectionResults;
    }

    public String getSyndromeDifferentiationBasis() {
        return syndromeDifferentiationBasis;
    }

    public void setSyndromeDifferentiationBasis(String syndromeDifferentiationBasis) {
        this.syndromeDifferentiationBasis = syndromeDifferentiationBasis;
    }

    public String getTcmApproach() {
        return tcmApproach;
    }

    public void setTcmApproach(String tcmApproach) {
        this.tcmApproach = tcmApproach;
    }

    public String getEtiologyClassification() {
        return etiologyClassification;
    }

    public void setEtiologyClassification(String etiologyClassification) {
        this.etiologyClassification = etiologyClassification;
    }

    public String getSyndromeDifferentiationMethod() {
        return syndromeDifferentiationMethod;
    }

    public void setSyndromeDifferentiationMethod(String syndromeDifferentiationMethod) {
        this.syndromeDifferentiationMethod = syndromeDifferentiationMethod;
    }

    public String getTcmPathogenesis() {
        return tcmPathogenesis;
    }

    public void setTcmPathogenesis(String tcmPathogenesis) {
        this.tcmPathogenesis = tcmPathogenesis;
    }

    public String getPhysicalAffectingFactors() {
        return physicalAffectingFactors;
    }

    public void setPhysicalAffectingFactors(String physicalAffectingFactors) {
        this.physicalAffectingFactors = physicalAffectingFactors;
    }

    public String getTcmIncidentsType() {
        return tcmIncidentsType;
    }

    public void setTcmIncidentsType(String tcmIncidentsType) {
        this.tcmIncidentsType = tcmIncidentsType;
    }

    public String getTcmIncidentsForm() {
        return tcmIncidentsForm;
    }

    public void setTcmIncidentsForm(String tcmIncidentsForm) {
        this.tcmIncidentsForm = tcmIncidentsForm;
    }

    public String getInspectionInfoString() {
        return inspectionInfoString;
    }

    public void setInspectionInfoString(String inspectionInfoString) {
        this.inspectionInfoString = inspectionInfoString;
    }

    public String getYiShiSign() {
        return yiShiSign;
    }

    public void setYiShiSign(String yiShiSign) {
        this.yiShiSign = yiShiSign;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getOnsetSolarTermCode() {
        return onsetSolarTermCode;
    }

    public void setOnsetSolarTermCode(String onsetSolarTermCode) {
        this.onsetSolarTermCode = onsetSolarTermCode;
    }

    public String getInitialDiagnosisCode() {
        return initialDiagnosisCode;
    }

    public void setInitialDiagnosisCode(String initialDiagnosisCode) {
        this.initialDiagnosisCode = initialDiagnosisCode;
    }

    public String getTreatmentCategoryCode() {
        return treatmentCategoryCode;
    }

    public void setTreatmentCategoryCode(String treatmentCategoryCode) {
        this.treatmentCategoryCode = treatmentCategoryCode;
    }

    public String getClinicalPathwayCode() {
        return clinicalPathwayCode;
    }

    public void setClinicalPathwayCode(String clinicalPathwayCode) {
        this.clinicalPathwayCode = clinicalPathwayCode;
    }

    public String getScientificResearchFlag() {
        return scientificResearchFlag;
    }

    public void setScientificResearchFlag(String scientificResearchFlag) {
        this.scientificResearchFlag = scientificResearchFlag;
    }

    public String getAllergyHistoryFlag() {
        return allergyHistoryFlag;
    }

    public void setAllergyHistoryFlag(String allergyHistoryFlag) {
        this.allergyHistoryFlag = allergyHistoryFlag;
    }

    public String getAllergyHistory() {
        return allergyHistory;
    }

    public void setAllergyHistory(String allergyHistory) {
        this.allergyHistory = allergyHistory;
    }

    public String getAllergicDrugFlag() {
        return allergicDrugFlag;
    }

    public void setAllergicDrugFlag(String allergicDrugFlag) {
        this.allergicDrugFlag = allergicDrugFlag;
    }

    public String getAllergicDrug() {
        return allergicDrug;
    }

    public void setAllergicDrug(String allergicDrug) {
        this.allergicDrug = allergicDrug;
    }

    public String getInfectiousHistoryFlag() {
        return infectiousHistoryFlag;
    }

    public void setInfectiousHistoryFlag(String infectiousHistoryFlag) {
        this.infectiousHistoryFlag = infectiousHistoryFlag;
    }

    public String getInfectiousHistory() {
        return infectiousHistory;
    }

    public void setInfectiousHistory(String infectiousHistory) {
        this.infectiousHistory = infectiousHistory;
    }

    public String getVaccinationHistory() {
        return vaccinationHistory;
    }

    public void setVaccinationHistory(String vaccinationHistory) {
        this.vaccinationHistory = vaccinationHistory;
    }

    public String getSurgicalHistory() {
        return surgicalHistory;
    }

    public void setSurgicalHistory(String surgicalHistory) {
        this.surgicalHistory = surgicalHistory;
    }

    public String getBloodTransfusionHistory() {
        return bloodTransfusionHistory;
    }

    public void setBloodTransfusionHistory(String bloodTransfusionHistory) {
        this.bloodTransfusionHistory = bloodTransfusionHistory;
    }

    public String getPregnancyFlag() {
        return pregnancyFlag;
    }

    public void setPregnancyFlag(String pregnancyFlag) {
        this.pregnancyFlag = pregnancyFlag;
    }

    public String getSucklingPeriodFlag() {
        return sucklingPeriodFlag;
    }

    public void setSucklingPeriodFlag(String sucklingPeriodFlag) {
        this.sucklingPeriodFlag = sucklingPeriodFlag;
    }

    public String getObstetricHistory() {
        return obstetricHistory;
    }

    public void setObstetricHistory(String obstetricHistory) {
        this.obstetricHistory = obstetricHistory;
    }

    public String getMenstrualHistory() {
        return menstrualHistory;
    }

    public void setMenstrualHistory(String menstrualHistory) {
        this.menstrualHistory = menstrualHistory;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getBmi() {
        return bmi;
    }

    public void setBmi(String bmi) {
        this.bmi = bmi;
    }

    public String getAuxiliaryInspectionItems() {
        return auxiliaryInspectionItems;
    }

    public void setAuxiliaryInspectionItems(String auxiliaryInspectionItems) {
        this.auxiliaryInspectionItems = auxiliaryInspectionItems;
    }

    public String getSymptomDescription() {
        return symptomDescription;
    }

    public void setSymptomDescription(String symptomDescription) {
        this.symptomDescription = symptomDescription;
    }

    public String getTongueCondition() {
        return tongueCondition;
    }

    public void setTongueCondition(String tongueCondition) {
        this.tongueCondition = tongueCondition;
    }

    public String getPulseCondition() {
        return pulseCondition;
    }

    public void setPulseCondition(String pulseCondition) {
        this.pulseCondition = pulseCondition;
    }

    public String getWestDisCode() {
        return westDisCode;
    }

    public void setWestDisCode(String westDisCode) {
        this.westDisCode = westDisCode;
    }

    public String getChineseDisCode() {
        return chineseDisCode;
    }

    public void setChineseDisCode(String chineseDisCode) {
        this.chineseDisCode = chineseDisCode;
    }

    public String getSymCode() {
        return symCode;
    }

    public void setSymCode(String symCode) {
        this.symCode = symCode;
    }

    public String getDiagnosticFlag() {
        return diagnosticFlag;
    }

    public void setDiagnosticFlag(String diagnosticFlag) {
        this.diagnosticFlag = diagnosticFlag;
    }

    @Override
    public Serializable pkVal() {
        return this.recordsId;
    }

    @Override
    public String toString() {
        return "MedicalRecords{" +
                "recordsId=" + recordsId +
                ", doctorName=" + doctorName +
                ", doctorId=" + doctorId +
                ", appId=" + appId +
                ", deptName=" + deptName +
                ", insName=" + insName +
                ", insId=" + insId +
                ", insCode=" + insCode +
                ", deptCode=" + deptCode +
                ", deptId=" + deptId +
                ", chiefComplaint=" + chiefComplaint +
                ", presentIllness=" + presentIllness +
                ", pastHistory=" + pastHistory +
                ", personalHistory=" + personalHistory +
                ", familyHistory=" + familyHistory +
                ", physicalExamination=" + physicalExamination +
                ", chineseDisName=" + chineseDisName +
                ", westDisName=" + westDisName +
                ", chineseDisId=" + chineseDisId +
                ", westDisId=" + westDisId +
                ", symName=" + symName +
                ", symId=" + symId +
                ", recordsAdvice=" + recordsAdvice +
                ", recordTime=" + recordTime +
                ", createDate=" + createDate +
                ", recordsTimes=" + recordsTimes +
                ", patientName=" + patientName +
                ", patientId=" + patientId +
                ", patientSex=" + patientSex +
                ", patientPhone=" + patientPhone +
                ", patientAge=" + patientAge +
                ", patientCardNumber=" + patientCardNumber +
                ", visitNo=" + visitNo +
                ", healthFilesNum=" + healthFilesNum +
                ", patientCardType=" + patientCardType +
                ", patientHealthCardNum=" + patientHealthCardNum +
                ", insuranceTypeCode=" + insuranceTypeCode +
                ", maritalStatus=" + maritalStatus +
                ", nation=" + nation +
                ", contactsName=" + contactsName +
                ", contactsPhone=" + contactsPhone +
                ", nationality=" + nationality +
                ", educationCode=" + educationCode +
                ", contactsRelationship=" + contactsRelationship +
//        ", chineseBaseTongueInfo=" + chineseBaseTongueInfo +
//        ", chineseBasePulse=" + chineseBasePulse +
                ", auxiliaryInspectionResults=" + auxiliaryInspectionResults +
                ", syndromeDifferentiationBasis=" + syndromeDifferentiationBasis +
                ", tcmApproach=" + tcmApproach +
                ", etiologyClassification=" + etiologyClassification +
                ", syndromeDifferentiationMethod=" + syndromeDifferentiationMethod +
                ", tcmPathogenesis=" + tcmPathogenesis +
                ", physicalAffectingFactors=" + physicalAffectingFactors +
                ", tcmIncidentsType=" + tcmIncidentsType +
                ", tcmIncidentsForm=" + tcmIncidentsForm +
                ", inspectionInfoString=" + inspectionInfoString +
                ", yiShiSign=" + yiShiSign +
                ", occupation=" + occupation +
                ", onsetSolarTermCode=" + onsetSolarTermCode +
                ", initialDiagnosisCode=" + initialDiagnosisCode +
                ", treatmentCategoryCode=" + treatmentCategoryCode +
                ", clinicalPathwayCode=" + clinicalPathwayCode +
                ", scientificResearchFlag=" + scientificResearchFlag +
                ", allergyHistoryFlag=" + allergyHistoryFlag +
                ", allergyHistory=" + allergyHistory +
                ", allergicDrugFlag=" + allergicDrugFlag +
                ", allergicDrug=" + allergicDrug +
                ", infectiousHistoryFlag=" + infectiousHistoryFlag +
                ", infectiousHistory=" + infectiousHistory +
                ", vaccinationHistory=" + vaccinationHistory +
                ", surgicalHistory=" + surgicalHistory +
                ", bloodTransfusionHistory=" + bloodTransfusionHistory +
                ", pregnancyFlag=" + pregnancyFlag +
                ", sucklingPeriodFlag=" + sucklingPeriodFlag +
                ", obstetricHistory=" + obstetricHistory +
                ", menstrualHistory=" + menstrualHistory +
                ", height=" + height +
                ", weight=" + weight +
                ", bmi=" + bmi +
                ", auxiliaryInspectionItems=" + auxiliaryInspectionItems +
                ", symptomDescription=" + symptomDescription +
                ", tongueCondition=" + tongueCondition +
                ", pulseCondition=" + pulseCondition +
                ", westDisCode=" + westDisCode +
                ", chineseDisCode=" + chineseDisCode +
                ", symCode=" + symCode +
                ", diagnosticFlag=" + diagnosticFlag +
                "}";
    }
}
