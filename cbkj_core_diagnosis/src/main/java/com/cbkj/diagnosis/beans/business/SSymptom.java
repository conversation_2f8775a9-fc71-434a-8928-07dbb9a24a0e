package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SSymptom implements Serializable{

    @Schema(description =  "证型id")
    private String sSymptomId;

    @Schema(description =  "证型名字")
    private String sSymptomName;

    @Schema(description =  "证型代码")
    private String sSymptomCode;


}
