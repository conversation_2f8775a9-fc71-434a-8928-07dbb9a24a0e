package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 业务管理附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Getter
@Setter
@TableName("t_business_annex")
//@Schema(description =  "TBusinessAnnex对象", description = "业务管理附件表")
@Schema(description = "业务管理附件表")
public class TBusinessAnnex implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="ID")
    @TableId("id")
    private String id;

    @Schema(description="上传者id（医生或者是患者id）")
    @TableField("insert_user_id")
    private String insertUserId;

    @Schema(description="第三方ID")
    @TableField("annex_foreign_id")
    private String annexForeignId;

    @Schema(description="附件名称")
    @TableField("annex_name")
    private String annexName;

    @Schema(description="附件原名称")
    @TableField("annex_Original_name")
    private String annexOriginalName;

    @Schema(description="附件后缀名")
    @TableField("annex_suffix_name")
    private String annexSuffixName;

    @Schema(description="附件大小")
    @TableField("annex_size")
    private Double annexSize;

    @Schema(description="附件路径")
    @TableField("annex_path")
    private String annexPath;

    @Schema(description="附件所属业务类型：1系统建议反馈,2系统帮助操作手册3.系统帮助操作视频")
    @TableField("annex_type")
    private Integer annexType;

    @Schema(description="是否删除（0否 1是）")
    @TableField("is_del")
    private Boolean isDel;

    private Date createTime;


}
