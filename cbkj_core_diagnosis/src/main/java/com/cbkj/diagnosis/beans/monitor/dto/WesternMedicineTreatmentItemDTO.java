package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/5 16:24
 *
 * @description：西医处置明细
 */
@Getter
@Setter
@Schema(description =  "西医处置明细")
public class WesternMedicineTreatmentItemDTO {

    @Schema(description =  "西医操作编码")
    private String operationCode;

    @Schema(description =  "操作名称")
    private String operationName;

    @Schema(description =  "目标部位")
    private String operationTarget;

    @Schema(description =  "操作方法")
    private String operationMethod;

    @Schema(description =  "操作次数")
    private String operationNumber;
    @Schema(description =  "操作时间")
    private String operationTime;

}
