package com.cbkj.diagnosis.beans;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class TAdminInfo2 {

    @CBKJEncryptField
    private String idCard;
    @CBKJEncryptField
    private String mobile;
    private String healthCardNum;
    @Schema(description =  "患者证件类型呢")
    private String patientCardType;

    public TAdminInfo2(String idCard) {
        this.idCard = idCard;
    }

}
