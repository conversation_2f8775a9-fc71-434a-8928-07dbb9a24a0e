package com.cbkj.diagnosis.beans.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/6 15:53
 *
 * @description：处方信息
 */
@Getter
@Setter
public class MedicalRecordsPrescriptionsResponse {


    @Schema(description =  "处方编号")
    private String preNo;
    @Schema(description =  "中医处方id")
    private String tcmPreId;
    @Schema(description =  "西医处方id")
    private String westPreId;
    @Schema(description =  "开方科室")
    private String deptName;
    @Schema(description =  "处方类别")
    private String prescriptionCategory;
    @Schema(description =  "开立日期")
    private String prescriptionsTime;
    @Schema(description =  "有效天数")
    private String prescriptionsEffectiveDays;


    @Schema(description =  "中药方剂名称")
    private String cnFormulaName;

    @Schema(description =  "中医疾病名")
    private String chineseDisName;

    @Schema(description =  "中医证型")
    private String symName;


    @Schema(description =  "西医疾病名")
    private String westDisName;

    @Schema(description =  "治则治法")
    private String tcmApproach;

    @Schema(description =  "中药剂数")
    private String prescriptionNum;
    @Schema(description =  "煎煮方法")
    private String decoctingMethod;

    @Schema(description =  "使用频次")
    private String chinesePrescriptionRate;
    @Schema(description =  "用药方法")
    private String medicationMethod;
    @Schema(description =  "服用要求")
    private String medicationRequirements;
    @Schema(description =  "剂型")
    private String drugDosage;

    @Schema(description =  "协定处方标志")
    private String cipherPrescription;
    @Schema(description =  "中药制剂标志")
    private String preparationFlag;
    @Schema(description =  "自煎 / 代煎")
    private String decoctingFlag;
    @Schema(description =  "君臣佐使")
    private String junChenZuoShi;

    @Schema(description =  "开方医师")
    private String prescribingDoctorSign;
    @Schema(description =  "审方药剂师")
    private String reviewedDoctorSign;


    @Schema(description =  "处方备注")
    private String westPrescriptionRemark;
    @Schema(description =  "开方医师")
    private String westPrescribingDoctorSign;
    @Schema(description =  "审方药剂师")
    private String westReviewedDoctorSign;
}
