package com.cbkj.diagnosis.beans.health;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class MedicalRecords extends Model<MedicalRecords> implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 就诊记录id
     */
    @TableId(value = "records_id", type = IdType.INPUT)
    private String recordsId;

    /**
     * 就诊医生名字
     */
    @CBKJDecryptField
    @CBKJEncryptField
    private String doctorName;

    /**
     * 医生ID
     */
    private String doctorId;

    /**
     * 应用ID（由预诊随访系统分配给HIS系统接入的唯一标识）
     */
    private String appId;

    /**
     * 就诊科室名称
     */
    private String deptName;

    /**
     * 机构名
     */
    private String insName;

    private String insId;

    /**
     * 机构代码
     */
    private String insCode;

    /**
     * 科室代码
     */
    private String deptCode;

    /**
     * 患者就诊科室id
     */
    private String deptId;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 现病史
     */
    private String presentIllness;

    /**
     * 既往史
     */
    private String pastHistory;

    /**
     * 个人史
     */
    private String personalHistory;

    /**
     * 家族史
     */
    private String familyHistory;

    /**
     * 体格检查
     */
    private String physicalExamination;

    /**
     * 中医疾病名
     */
    private String chineseDisName;

    /**
     * 西医疾病名
     */
    private String westDisName;

    /**
     * 中医疾病id
     */
    private String chineseDisId;

    /**
     * 西医疾病id
     */
    private String westDisId;

    /**
     * 证型名称
     */
    private String symName;

    /**
     * 证型id
     */
    private String symId;

    /**
     * 治疗意见
     */
    private String recordsAdvice;

    /**
     * 就诊时间
     */
    private Date recordTime;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 诊次
     */
    private Integer recordsTimes;

    /**
     * 患者名字
     */
    @CBKJDecryptField
    @CBKJEncryptField
    private String patientName;

    /**
     * 患者身份id（t_admin_info）
     */
    private String patientId;

    /**
     * 患者性别
     */

    private String patientSex;

    /**
     * 患者手机号
     */
    @CBKJDecryptField
    @CBKJEncryptField
    private String patientPhone;

    /**
     * 患者年龄
     */
    private String patientAge;

    /**
     * 患者身份证
     */
    @CBKJDecryptField
    @CBKJEncryptField
    private String patientCardNumber;

    /**
     * his就诊号
     */
    @TableField("VISIT_NO")
    private String visitNo;

    /**
     * 城乡居民健康档案编号
     */
    private String healthFilesNum;

    /**
     * 身份证件类别代码
     */
    private String patientCardType;

    /**
     * 居民健康卡号
     */
    private String patientHealthCardNum;

    /**
     * 医疗保险类别代码
     */
    private String insuranceTypeCode;

    /**
     * 婚姻状况代码
     */
    private String maritalStatus;

    /**
     * 民族
     */
    private String nation;

    /**
     * 联系人姓名
     */
    private String contactsName;

    /**
     * 联系人电话号码
     */
    private String contactsPhone;

    /**
     * 国籍代码
     */
    private String nationality;

    /**
     * 学历代码
     */
    private String educationCode;

    /**
     * 联系人与患者的关系代码
     */
    private String contactsRelationship;

    /**
     * 中医基本舌象诊断信息
     */
//    private String chineseBaseTongueInfo;

    /**
     * 中医基本脉象诊断信息
     */
//    private String chineseBasePulse;

    /**
     * 辅助检查结果
     */
    private String auxiliaryInspectionResults;

    /**
     * 辨证依据
     */
    private String syndromeDifferentiationBasis;

    /**
     * 治则治法
     */
    private String tcmApproach;

    /**
     * 中医病因分类
     */
    private String etiologyClassification;

    /**
     * 中医辨证方法
     */
    private String syndromeDifferentiationMethod;

    /**
     * 中医基本病机
     */
    private String tcmPathogenesis;

    /**
     * 体质影响因素
     */
    private String physicalAffectingFactors;

    /**
     * 中医发病类型
     */
    private String tcmIncidentsType;

    /**
     * 中医发病形式
     */
    private String tcmIncidentsForm;

    /**
     * 检查检验项目信息
     */
    private String inspectionInfoString;

    /**
     * 医师签名
     */
    private String yiShiSign;

    /**
     * 职业(患者当前职业范畴的完整描述)
     */
    private String occupation;

    /**
     * 发病节气
     */
    private String onsetSolarTermCode;

    /**
     * 初诊标志 Y是 N否
     */
    private String initialDiagnosisCode;

    /**
     * 治疗类别
     */
    private String treatmentCategoryCode;

    /**
     * 实施临床路径
     */
    private String clinicalPathwayCode;

    /**
     * 科研病历标志
     */
    private String scientificResearchFlag;

    /**
     * 过敏史标志
     */
    private String allergyHistoryFlag;

    /**
     * 过敏史
     */
    private String allergyHistory;

    /**
     * 药物过敏标志
     */
    private String allergicDrugFlag;

    /**
     * 过敏药物
     */
    private String allergicDrug;

    /**
     * 传染病历标志
     */
    private String infectiousHistoryFlag;

    /**
     * 传染病史
     */
    private String infectiousHistory;

    /**
     * 预防接种史
     */
    private String vaccinationHistory;

    /**
     * 手术史
     */
    private String surgicalHistory;

    /**
     * 输血史
     */
    private String bloodTransfusionHistory;

    /**
     * 怀孕标志
     */
    private String pregnancyFlag;

    /**
     * 哺乳期标志
     */
    private String sucklingPeriodFlag;

    /**
     * 婚育史
     */
    private String obstetricHistory;

    /**
     * 月经史
     */
    private String menstrualHistory;

    /**
     * 身高
     */
    private String height;

    /**
     * 体重
     */
    private String weight;

    /**
     * BMI
     */
    private String bmi;

    /**
     * 辅助检查项目
     */
    private String auxiliaryInspectionItems;

    /**
     * 中医基本症状描述
     */
    private String symptomDescription;

    /**
     * 中医基本舌象诊断信息
     */
    private String tongueCondition;

    /**
     * 中医基本脉象诊断信息
     */
    private String pulseCondition;

    /**
     * 西医诊断代码
     */
    private String westDisCode;

    /**
     * 中医病名代码
     */
    private String chineseDisCode;

    /**
     * 中医证候代码
     */
    private String symCode;

    /**
     * 诊断标识代码
     */
    private String diagnosticFlag;


    /**
     * 门（急）诊号
     */
    private String diagnosticNo;


    /**
     * 中药处方信息
     */
    private String cnPreInfo ;

    /**
     * 西药、中成药处方
     */
    private String weCnPreInfo;

    /**
     * 一般治疗处置
     */
    private String commonOpInfo;


    /**
     * 医师签名
     */
//    private String doctorSign;
    private String patientCode;

    /**
     * 20250304 新增
     */
    /**
     * 医疗费用结算方式代码
     */
    private String medicalExpensesSettledCode;

    /**
     * 中医病因分类代码
     */
    private String etiologyClassificationCode;

    /**
     * 中医证候分类代码
     */
    private String physicalAffectingFactorsCode;

    /**
     * 状态，0:创建(产集中)，1:结案，2:归档，3:上传
     */
    @TableField("closed_status")
    private String closedStatus;

    /**
     * 结案时间
     */
    @TableField("closed_time")
    private Date closedTime;

    /**
     * 结案号
     */
    @TableField("closed_no")
    private String closedNo;


    @TableField(exist = false)
    private List<MedicalRecordsPrescriptions> medicalRecordsPrescriptionsList;

}
