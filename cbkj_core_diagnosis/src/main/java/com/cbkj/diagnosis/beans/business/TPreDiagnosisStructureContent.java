package com.cbkj.diagnosis.beans.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Getter
@Setter
@TableName("t_pre_diagnosis_structure_content")
@Schema(description =  "TPreDiagnosisStructureContent对象")
public class TPreDiagnosisStructureContent implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="EmrContent 表：存储 EMR 内容，与 EmrItem 表相关联。")
    @TableId(value = "structure_content_id", type = IdType.AUTO)
    private Integer structureContentId;

    @TableField("diagnosis_structure_id")
    private String diagnosisStructureId;


}
