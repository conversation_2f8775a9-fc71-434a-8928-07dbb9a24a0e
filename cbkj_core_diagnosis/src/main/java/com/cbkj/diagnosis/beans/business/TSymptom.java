package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class TSymptom implements Serializable{

    @Schema(description =  "症状id")
    private String symptomId;

    @Schema(description =  "证状名称")
    private String symptomName;

    @Schema(description =  "症状编码")
    private String symptomCode;

    @Schema(description =  "创建时间")
    private Date createDate;

    @Schema(description =  "创建人")
    private String createUser;

    @Schema(description =  "创建人姓名")
    private String createUsername;

    @Schema(description =  "修改时间")
    private Date updateDate;

    @Schema(description =  "修改人")
    private String updateUser;

    @Schema(description =  "修改人姓名")
    private String updateUsername;

    @Schema(description =  "删除时间")
    private Date delDate;

    @Schema(description =  "删除人")
    private String delUser;

    @Schema(description =  "删除人姓名")
    private String delUsername;

    @Schema(description =  "顺序号")
    private String sort;

    @Schema(description =  "状态（0正常   1删除）")
    private String status;
    @Schema(description =  "症状类别id")
    private String symptomClassId;
    @Schema(description =  "症状类别名称")
    private String symptomClassName;


}
