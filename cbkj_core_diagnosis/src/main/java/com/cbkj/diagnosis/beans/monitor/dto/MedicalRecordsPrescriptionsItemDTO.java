package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/5 15:24
 *
 * @description：药品明细
 */
@Getter
@Setter
@Schema(description =  "药品明细")
public class MedicalRecordsPrescriptionsItemDTO {

    @Schema(description =  "药名称")
    private String drugName;

    @Schema(description =  "中药使用次剂量")
    private String drugDose;

    @Schema(description =  "中药使用剂量单位")
    private String drugDoseUnit;

    @Schema(description =  "中药特殊煎煮方法")
    private String drugDecoctingMethod;

}
