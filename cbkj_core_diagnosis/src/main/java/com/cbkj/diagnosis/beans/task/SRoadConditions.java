package com.cbkj.diagnosis.beans.task;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class SRoadConditions implements Serializable{

    @Schema(description =  "随访路径-条件")
    private Integer roadConditionsId;

    @Schema(description =  "随访-路径id")
    private String sRoadId;

    @Schema(description =  "随访-路径名")
    private String sRoadName;

    @Schema(description =  "西医疾病id")
    private String tWestDiseaseId;

    @Schema(description =  "西医疾病名字")
    private String tWestDiseaseName;

    @Schema(description =  "中医疾病名字")
    private String tChineseDiseaseName;

    @Schema(description =  "中医疾病id")
    private String tChineseDiseaseId;

    @Schema(description =  "中医证型id")
    private String sSymptomId;

    @Schema(description =  "中医证型名称")
    private String sSymptomName;

    @Schema(description =  "医生id（如果值是-1代表全部医生）")
    private String doctorId;

    @Schema(description =  "医生名字")
    private String doctorName;

    @Schema(description =  "科室")
    private String sysDeptId;

    @Schema(description =  "科室名字")
    private String sysDeptName;

    @Schema(description =  "1.西医2.中医3.证型4.医生5.科室")
    private String recordType;


}
