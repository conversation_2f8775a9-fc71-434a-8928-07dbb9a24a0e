package com.cbkj.diagnosis.beans.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/5 14:31
 *
 * @description：档案详情-就诊
 */
@Getter
@Setter
@Schema(description =  "档案详情-就诊 ")
public class FileDetailsConsultationDTO {
    @Schema(description =  "就诊记录信息")
    private MedicalRecordsDTO medicalRecordsDTO;

    @Schema(description =  "处方信息")
    private PrescriptionInfoDTO prescriptionInfoDTO;

    @Schema(description =  "处置记录")
    private DisposalRecordDTO disposalRecordDTO;

    @Schema(description =  "费用清单")
    private ManifestFeeDTO manifestFeeDTO;

}
