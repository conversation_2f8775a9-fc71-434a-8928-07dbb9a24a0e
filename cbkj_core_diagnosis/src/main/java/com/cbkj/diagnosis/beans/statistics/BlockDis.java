package com.cbkj.diagnosis.beans.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/26 16:36
 * @Version 1.0
 */
@Data
public class BlockDis {
    @Schema(description="疾病")
    private String name;

    @Schema(description="预诊量")
    private String diaNum;

    @Schema(description="随访量")
    private String flowNum;

    @Schema(description="有效病历")
    private String recordsNum;

    @Schema(description="健康宣教")
    private String healthNum;
}
