package com.cbkj.diagnosis.beans.business;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class TPreDiagnosisChild implements Serializable{

    @Schema(description =  "选项id")
    private Integer optionId;

    @Schema(description =  "选项名称")
    private String optionName;

    @Schema(description =  "子题问题id")
    private Integer childQuestionId;

    @Schema(description =  "子题顺序")
    private Integer sort;
    private Integer masterQuestionId;
    private String diaId;


    @Schema(description =  "暂留前端过来的string类型id")
    private String childQuestionIdStr;


}
