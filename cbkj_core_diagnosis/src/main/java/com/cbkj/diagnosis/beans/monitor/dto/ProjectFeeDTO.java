package com.cbkj.diagnosis.beans.monitor.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by zbh on 2024/6/11 11:31
 *
 * @description：项目费用DTO
 */
@Getter
@Setter
@Schema(description =  "项目费用实体类")
public class ProjectFeeDTO {

    @Schema(description =  "项目名字")
    private String name;

    @Schema(description =  "项目费用")
    private String fee;

    @Schema(description =  "剂型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String dosageForm;
}
