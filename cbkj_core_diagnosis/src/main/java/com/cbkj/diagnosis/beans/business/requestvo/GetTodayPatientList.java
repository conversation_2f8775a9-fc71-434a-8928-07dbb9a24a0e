package com.cbkj.diagnosis.beans.business.requestvo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema
public class GetTodayPatientList {

    @Schema(description="随访任务id")
    private String sRoadTaskId;

    @CBKJEncryptField
    @CBKJDecryptField
    private String patientName;

//    @Schema(description="身份证号码")
//    @CBKJEncryptField
//    @CBKJDecryptField
//    private String patientCardNumber;
    @CBKJEncryptField
    private String patientIdcard;

    @Schema(description="中西医疾病名")
    private String diseaseName;
    @Schema(description =  "时间开始")
    private String startDate;

    @Schema(description =  "时间结束")
    private String endDate;
    private String userId;
    private String recId;
//    private Integer phoneStatus;
    private Integer page;
    private Integer limit;


    @Schema(description="-1过期 0 完成 今日任务也会传1 2 计划中")
    private Integer toDayTaskStatus;

    @Schema(description="事件codes", example = "[1,2,3]",type = "array",hidden = true)
//    @Parameter(name="事件codes")
    private List<String> eventCodesList;
    private String eventCodes;
}
