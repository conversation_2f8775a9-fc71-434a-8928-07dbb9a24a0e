package com.cbkj.diagnosis.beans.task;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@Schema
public class SRoadTaskResponse implements Serializable {

    @Schema(description =  "随访任务")
    private String sRoadTaskId;

    @Schema(description =  "任务名称")
    private String taskName;

    @Schema(description =  "随访主表id")
    private String sRoadId;

    @Schema(description =  "说明")
    private String sRoadTaskContent;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "创建时间")
    private Date createTime;

    @Schema(description =  "创建人id")
    private String createUserId;

    @Schema(description =  "创建者")
    private String createUserName;

    @Schema(description =  "0正常 1删除 2取消执行")
    private String status;

    @Schema(description =  "患者数量")
    private Integer patientNum;

    @Schema(description =  "1.手动2.自动")
    private String sRoadGroupWay;

    private String sRoadName;


    


}
