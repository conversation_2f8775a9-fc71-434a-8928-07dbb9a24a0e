package com.cbkj.diagnosis.beans.business.adviceVo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/2 15:46
 * @Version 1.0
 */
@Data
@Schema
public class AdviceSave {
    @Schema(description="意见反馈主键id")
    private Long id;
    @Schema(description="标题限制20字")
    @TableField("proposal_title")
    private String proposalTitle;

    @Schema(description="建议内容限制500字")
    @TableField("proposal_content")
    private String proposalContent;

    @Schema(description="联系方式")
    @TableField("proposal_liaison")
    private String proposalLiaison;

    @Schema(description="反馈类型：1建议，2问题 3.咨询")
    @TableField("proposal_type")
    private Integer proposalType;

    @Schema(description="建议图片")
    private List<ImageUploadRes> adviceImages;
}
