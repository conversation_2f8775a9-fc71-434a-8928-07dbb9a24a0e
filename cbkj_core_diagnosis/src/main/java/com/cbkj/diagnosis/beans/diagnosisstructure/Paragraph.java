package com.cbkj.diagnosis.beans.diagnosisstructure;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/15 10:17
 * @Version 1.0
 */

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema
@Data
public class Paragraph {
    @JSONField(serialize = false)
    private Integer structureChild;

    @Schema(description =  "如果是文本类型就是文本 ，如果是questionId类型 就是问题主键id")
    private String content;
    @Schema(description =  "text 文本 questionId 问题")
    private String contentType;
    @Schema(description =  "排序")
    private Integer paragraphSort;
    private String variableType;

    @Schema(description =  "是否删除 0 否 1是")
    private Integer questionIsDel;

    /**
     * 如果是questionId类型 返回以下字段
     */
    private String questionType;
    private String questionClassType;
    private Integer questionNumber;


}