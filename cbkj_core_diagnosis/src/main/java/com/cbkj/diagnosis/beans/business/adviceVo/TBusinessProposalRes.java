package com.cbkj.diagnosis.beans.business.adviceVo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业务管理建议问题咨询表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Getter
@Setter
public class TBusinessProposalRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="ID")
    private Long id;


    @Schema(description="标题限制20字")
    private String proposalTitle;

    @Schema(description="建议内容限制500字")
    private String proposalContent;

    @Schema(description="联系方式")
    private String proposalLiaison;

    @Schema(description="反馈类型：1建议，2问题 3.咨询")
    private Integer proposalType;

    @Schema(description="受理状态：0受理，1未受理")
    @TableField("proposal_receive_state")
    private Integer proposalReceiveState;

    @Schema(description="受理意见")
    private String proposalReceiveOpinion;

    @Schema(description="受理人ID")
    private String proposalReceiveId;

    @Schema(description="受理人姓名")
    private String proposalReceiveName;

    @Schema(description="创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description="创建者ID")
    private String createUserId;

    @Schema(description="创建者姓名")
    private String createUserName;

    @Schema(description="修改时间")
    private Date updateTime;

    @Schema(description="修改者ID")
    private String updateUserId;

    @Schema(description="来源（1.医生端2.患者端）")
    private int sourceFrom;



    @Schema(description="建议图片")
    private List<ImageUploadRes> adviceImages;


}
