package com.cbkj.diagnosis.mapper.statistics;

import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayFollow;
import com.cbkj.diagnosis.beans.statistics.PastSevenDaysRe;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.List;


public interface StatisticsErverDayFollowMapper extends BaseMapper<StatisticsErverDayFollow>{


    List<StatisticsErverDayFollow> getPastSevenDays(PastSevenDaysRe pastSevenDaysRe);


    void insertStatisticsEverDayFollowData(PastSevenDaysRe pastSevenDaysRe);

    Long getYesterDayNumber(PastSevenDaysRe pastSevenDaysRe);

    void insertStatisticsEverDayFollowDataPage(PastSevenDaysRe pastSevenDaysRe);
}