package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.SSymptom;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.List;

@Component
public interface SSymptomMapper extends BaseMapper<SSymptom>{

    /**
     * 获取疾病的证型
     * @param keyWord
     * @return
     */
    List<SSymptom> selectListByKeyWord(String keyWord);

    String getOneBySymCode(SSymptom sSymptom);


}