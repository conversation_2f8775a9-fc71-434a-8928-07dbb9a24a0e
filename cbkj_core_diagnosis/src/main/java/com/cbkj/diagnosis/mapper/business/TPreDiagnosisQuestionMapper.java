package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.service.common.vo.NextQVo;
import com.cbkj.diagnosis.service.common.vo.QuestionMobile;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public interface TPreDiagnosisQuestionMapper extends BaseMapper<TPreDiagnosisQuestion> {

    /**
     * 获取第一题
     *
     * @param questionMobile
     * @return
     */
    List<TRecordDia> getFirstQ(QuestionMobile questionMobile);

    /**
     * 获取下一题
     *
     * @param questionIds
     * @return
     */

    List<TRecordDia> getNextQ(NextQVo nextQVo);
    List<TRecordDia> getChildQ(NextQVo nextQVo);

    List<TRecordDia> getFirstQByQuan(QuestionMobile questionMobile);

    String getQuestionStem(Integer questionId);

    void deleteByDiaId(String diaId);

    /**
     * 获取随访第一题
     *
     * @param questionMobile
     * @return
     */
    List<TRecordDia> getFirstQSuiFang(QuestionMobile questionMobile);

    List<TPreDiagnosisQuestion> getAllQuestionListByDiaId(String diaId);

    /**
     * 批量查询多个问卷的问题数据 - 性能优化
     * @param diaIds 问卷ID列表
     * @return 所有问卷的问题数据
     */
    List<TPreDiagnosisQuestion> getAllQuestionsByDiaIds(List<String> diaIds);

    List<HashMap<String,Object>> selectDimensionScoreByList(ArrayList<String> stringArrayList);
}