package com.cbkj.diagnosis.mapper.task;

import com.cbkj.diagnosis.beans.task.SRoadConditions;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.List;


public interface SRoadConditionsMapper extends BaseMapper<SRoadConditions>{

    /**
     * 查询任务路径条件
     * @param roadConditions
     * @return
     */
    List<SRoadConditions> getAllListByObj(SRoadConditions roadConditions);
}