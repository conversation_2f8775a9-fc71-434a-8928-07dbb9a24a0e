package com.cbkj.diagnosis.mapper.health;

import com.cbkj.diagnosis.beans.business.TDiseaseNo;
import com.cbkj.diagnosis.beans.business.requestvo.NoEcrpy;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.health.MedicalRecordsNoEn;
import com.cbkj.diagnosis.beans.health.vo.GetMedicalRecordsDetailsQuery;
import com.cbkj.diagnosis.beans.monitor.dto.RecordInfoDTO;
import com.cbkj.diagnosis.beans.monitor.vo.DataSourceList;
import com.cbkj.diagnosis.beans.monitor.vo.GetPatientInfo;
import com.cbkj.diagnosis.beans.monitor.vo.ResourceListVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cbkj.diagnosis.beans.health.vo.GetMedicalRecordsDetails;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


public interface MedicalRecordsMapper extends BaseMapper<MedicalRecords>{

    int updateByPrimaryKey(MedicalRecords medicalRecords);
    MedicalRecords getObjectById(String recordsId);


    List<MedicalRecords> getMedicalRecordsDetails(GetMedicalRecordsDetailsQuery medicalRecordsDetails);

    MedicalRecords selectOneByCondition(MedicalRecords medicalRecords);

    List<DataSourceList> getDataSourceList(ResourceListVO vo);

    List<DataSourceList> getCaseSelectionList(ResourceListVO vo);

    List<DataSourceList> getCaseClosedList(ResourceListVO vo);


    LinkedList<RecordInfoDTO> getRecordListByCreateDate(GetPatientInfo getPatientInfo);

    MedicalRecords  getMedicalRecordByRecordId(@Param("recordId") String recordId);

    List<MedicalRecordsNoEn> getNoEcrpy(NoEcrpy noEcrpy);


    MedicalRecords getOneBypatientId(String patientId);

    void updateClosedByPatientId(String patientId,String closedNo);

    void updateClosedById(String id,String closedNo);

    void updateClosedByclosdNo(String closedNo, String closedStatus);

    TDiseaseNo getcloseNo(String patientId, String id);

    void updateTDiseaseNo(TDiseaseNo tDiseaseNo);
}