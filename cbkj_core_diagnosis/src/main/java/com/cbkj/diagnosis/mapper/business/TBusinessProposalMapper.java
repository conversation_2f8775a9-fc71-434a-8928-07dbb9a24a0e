package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.TBusinessProposal;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cbkj.diagnosis.beans.business.adviceVo.GetAdviceListRes;
import com.cbkj.diagnosis.beans.business.adviceVo.ImageUploadRes;

import java.util.List;

/**
 * <p>
 * 业务管理建议问题咨询表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
public interface TBusinessProposalMapper extends BaseMapper<TBusinessProposal> {

    List<GetAdviceListRes> getAdviceList(TBusinessProposal tBusinessProposalService);
    List<ImageUploadRes> getAdviceImagesList(String id);
}
