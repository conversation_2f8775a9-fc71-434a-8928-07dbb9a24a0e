package com.cbkj.diagnosis.mapper.health;

import com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptions;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cbkj.diagnosis.beans.response.MedicalRecordsPrescriptionsResponse;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;


public interface MedicalRecordsPrescriptionsMapper extends BaseMapper<MedicalRecordsPrescriptions>{

    int insertList(List<MedicalRecordsPrescriptions> list);

    List<MedicalRecordsPrescriptionsResponse> getMedicalRecordsPrescriptionsByRecordId(@Param("recordId") String recordId);
    List<MedicalRecordsPrescriptionsResponse> getMedicalRecordsWestPrescriptionsByRecordId(@Param("recordId") String recordId);
    String selectIdByCondition(String recordsId);
}