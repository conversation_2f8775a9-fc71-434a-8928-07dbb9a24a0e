package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.SysAdminInfoDisMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cbkj.diagnosis.beans.DoctorDocDisCode;
import com.cbkj.diagnosis.beans.business.statictis.UserDisQuery;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public interface SysAdminInfoDisMappingMapper extends BaseMapper<SysAdminInfoDisMapping> {

    List<DoctorDocDisCode> selectDisCodeList(UserDisQuery userDisQuery);

    List<DoctorDocDisCode> getDisFromGroupBy(String disName);
}
