package com.cbkj.diagnosis.mapper;

import com.cbkj.diagnosis.beans.business.SysDic;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface SysDicMapper extends BaseMapper<SysDic> {

    ArrayList<HashMap<String, String>> getDetailsByDicCodeList(List<String> optionFollowEventCode);
}
