package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.TRecord;
import com.cbkj.diagnosis.beans.business.TRecordHis;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.his.GetPageListByObjNew2;
import com.cbkj.diagnosis.service.common.vo.PreListReVo;
import com.cbkj.diagnosis.service.common.vo.PreListReVoDAO;
import com.cbkj.diagnosis.service.mobileapi.vo.FurtherConsultationRecordListVo;
import com.cbkj.diagnosis.service.mobileapi.vo.GetFurtherConsultationList;
import com.cbkj.diagnosis.service.webapi.business.vo.GetQuestionClassTypeInfo;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;
import com.cbkj.diagnosis.beans.statistics.StatisticsDic;
import java.util.List;

@Component
public interface TRecordMapper extends BaseMapper<TRecord> {

    public List<TRecord> getPageListByObjNew(PreListReVoDAO preListReVo);

    public TRecord getOneByRecId(String recId);

    List<TRecord> getPageListByObj2(TRecord tRecord);

    List<FurtherConsultationRecordListVo> getFurtherRecordList(GetFurtherConsultationList fu);

    List<FurtherConsultationRecordListVo> getFurtherRecordList2(GetFurtherConsultationList fu);

//    Long getYesterDaySuiFangFinishPeopleNumb(String userId);

    List<GetQuestionClassTypeInfo> getQuestionClassTypeInfo(String recId);

    List<TRecordHis> getPageListByObjNew2(GetPageListByObjNew2 getPageListByObjNew2);

    String getCurrentSecondByRecId(TRecord tRecord);

    StatisticsDic getStatisticsDicByRecId(String recId);

    List<TRecord> getDecryptContent(String stime, String etime);

    void updateClosedByPatientId(String patientId,String closedNo);

    void updateClosedById(String id,String closedNo);

    void updateClosedByclosdNo(String closedNo, String closedStatus);
}