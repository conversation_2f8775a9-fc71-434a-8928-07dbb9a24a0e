package com.cbkj.diagnosis.mapper.task;

import com.cbkj.diagnosis.beans.task.*;
import com.cbkj.diagnosis.beans.task.webvo.RoadTaskConditons;
import com.cbkj.diagnosis.service.webapi.business.vo.PatientTaskDelete;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


public interface SRoadTaskMapper extends BaseMapper<SRoadTask>{


    void updateStatusDeleteByPrimaryKey(String sRoadTaskId);

    List<SRoadTaskResponse> getPageListByObjResponse(HashMap<String, String> sRoadTaskPageResponse);

    List<RoadTaskConditonsSQLResult> getRoadTaskConditons(RoadTaskConditonsQuery roadTaskConditonsQuery);

    List<SRoadTask> getAllList();


    List<SRoadTask> getListByObj(SRoadTask sRoadTask);
}