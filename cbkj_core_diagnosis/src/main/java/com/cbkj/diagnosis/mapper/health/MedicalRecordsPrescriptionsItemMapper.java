package com.cbkj.diagnosis.mapper.health;

import com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.ArrayList;
import java.util.List;


public interface MedicalRecordsPrescriptionsItemMapper extends BaseMapper<MedicalRecordsPrescriptionsItem>{

    int insertList(List<MedicalRecordsPrescriptionsItem> list);
    List<MedicalRecordsPrescriptionsItem> getItemByPreId(@Param("preId") String preId);

}