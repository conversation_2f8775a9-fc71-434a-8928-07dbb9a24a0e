package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone;
import com.cbkj.diagnosis.beans.business.requestvo.GetTodayPatientList;
import com.cbkj.diagnosis.beans.business.requestvo.ListByTaskPatientsIdSearch;
import com.cbkj.diagnosis.service.common.vo.*;
import com.cbkj.diagnosis.service.webapi.business.vo.PatientTaskDelete;
import com.cbkj.diagnosis.service.webapi.business.vo.YesterDaySuiFangFinishPeopleNumbSearch;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.ArrayList;
import java.util.List;


public interface SRoadTaskPatientsPhoneMapper extends BaseMapper<SRoadTaskPatientsPhone> {


    List<SRoadTaskPatientsPhone> getPageListByObj2(SRoadTaskPatientsPhone sRoadTaskPatientsPhone1);

    SRoadTaskPatientsPhone getOneByTaskPatientsId(String taskPatientsId);

    ArrayList<SRoadTaskPatientsPhone> getListByRecordsId(String recordsId);

    List<TodayPatientList> getTodayPatientList(GetTodayPatientList getTodayPatientList);
    int countTodayPatientList(GetTodayPatientList getTodayPatientList);

    /**
     * 根据就诊id，获取电话随访的待随访的时间最近一条
     *
     * @param recordsId
     * @return
     */
    TodayPatientList getStartSuiFangInfo(Long sRoadTaskPatientsPhoneId);
    SuiFangInfoPatientList getSuiFangInfo(GetStartSuiFangInfo suiFangInfo);

    /**
     * 根据就诊id，获取这个随访任务相关的排期列表
     *
     * @param recordsId
     * @return
     */
    List<TodayPatientDetail> getTodayPatientDetail(SRoadTaskPatientsPhone sRoadTaskPatientsPhone );

    List<SuiFangPhoneRecordListRes> getSuiFangPhoneRecordList(String patientId);

    StartSuiFangInfoDetailsRes getStartSuiFangInfoDetails(Long sRoadTaskPatientsPhoneId);

    List<TodayPatientDetail> getListByTaskPatientsId(ListByTaskPatientsIdSearch patientsIdSearch);
    List<SuiFangPatientDetail> getIncludePhoneListByTaskPatientsId(ListByTaskPatientsIdSearch patientsIdSearch);

    List<StartSuiFangDiaForm> getDiaFormListBySRoadTaskPatientsPhoneId(Long sRoadTaskPatientsPhoneId);
//    List<StartSuiFangDiaForm> getDiaFormList(Long taskPatientsPhoneId);

    Long getYesterDaySuiFangFinishPeopleNumb(YesterDaySuiFangFinishPeopleNumbSearch userId);
    Long getYesterDaySuiFangFinishPeopleNumb2(YesterDaySuiFangFinishPeopleNumbSearch yesterDaySuiFangFinishPeopleNumbSearch);

    Long getTodayDaySuiFangTotalPeopleNumb(YesterDaySuiFangFinishPeopleNumbSearch yester2);

    Long getXiaoShanPhoneSuiFangStatisticsTopRight(YesterDaySuiFangFinishPeopleNumbSearch yester3);

    Long getXiaoShanPhoneSuiFangStatisticsTopRight2(YesterDaySuiFangFinishPeopleNumbSearch yester4);


    void updatePatientTaskStatusByPAndR(PatientTaskDelete patientTaskDelete);

    SuiFangPatientDetail getLastestSRoadTaskPatientsId(String patientId);

    List<String> getTodayPatientIdsList(GetTodayPatientList getTodayPatientList);
}