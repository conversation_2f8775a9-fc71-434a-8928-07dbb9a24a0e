package com.cbkj.diagnosis.mapper.business;


import com.cbkj.diagnosis.beans.business.AdminDisList;
import com.cbkj.diagnosis.beans.business.TDisease;
import com.cbkj.diagnosis.service.common.strategy.record.impl.TDiseaseRes;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo2;
import com.cbkj.diagnosis.service.webapi.business.vo.MySelfDisList;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Repository
public interface TDiseaseMapper extends BaseMapper<TDisease>{
    /**
     * 排除已经被选入到的预诊单里边
     * @param diseaseVo
     * @return
     */
    List<TDisease> getListByVo(DiseaseVo diseaseVo);

    List<TDiseaseRes> getListByTDisease(TDisease tDisease);

    List<TDisease> getListByVo2(DiseaseVo2 diseaseVo);

    String getOneByDisCode(TDisease tDisease);

    List<AdminDisList> getAdminDisList(String disName);

    List<AdminDisList> getMySelfDisList(MySelfDisList mySelfDisList);

    /**
     * 获取监测的疾病
     * @return
     */
    List<TDisease> getDiseaseDiagnosisMappingList();
}