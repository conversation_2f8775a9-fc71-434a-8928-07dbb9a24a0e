package com.cbkj.diagnosis.mapper.health;

import com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;


public interface MedicalWestPrescriptionsItemMapper extends BaseMapper<MedicalWestPrescriptionsItem>{

    int insertList(List<MedicalWestPrescriptionsItem> list);
    List<MedicalWestPrescriptionsItem> getItemByWestPreId(@Param("westPreId") String westPreId);

}