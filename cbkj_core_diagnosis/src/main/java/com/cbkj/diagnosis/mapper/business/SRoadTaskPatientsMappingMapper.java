package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping;
import com.cbkj.diagnosis.service.webapi.business.vo.RoadEventTaskSchedulerRes;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.List;


public interface SRoadTaskPatientsMappingMapper extends BaseMapper<SRoadTaskPatientsMapping>{


    List<RoadEventTaskSchedulerRes> getRoadEventTaskSchedulerList();
    List<RoadEventTaskSchedulerRes> getRoadEventTaskSchedulerWestPreList();
    List<RoadEventTaskSchedulerRes> getRoadEventTaskSchedulerHandlePreList();
}