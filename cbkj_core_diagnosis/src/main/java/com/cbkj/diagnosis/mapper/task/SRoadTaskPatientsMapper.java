package com.cbkj.diagnosis.mapper.task;

import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.service.common.vo.AiSuiFangListRe;
import com.cbkj.diagnosis.service.common.vo.GetPhoneList;
import com.cbkj.diagnosis.service.common.vo.SuiFangPhoneRe;
import com.cbkj.diagnosis.service.mobileapi.vo.GetFurtherConsultationList;
import com.cbkj.diagnosis.service.mobileapi.vo.GetMobileIndexNumber;
import com.cbkj.diagnosis.service.mobileapi.vo.MessageListVo;
import com.cbkj.diagnosis.service.webapi.business.vo.*;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.List;


public interface SRoadTaskPatientsMapper extends BaseMapper<SRoadTaskPatients> {

    /**
     * 根据随访任务的id 删除任务下的未执行的任务。
     *
     * @param sRoadTaskId
     */
    void deleteStatusByRoadTaskId(String sRoadTaskId);
    void deletePhoneStatusByRoadTaskId(String sRoadTaskId);

    void cancelStatusByRoadTaskId(String sRoadTaskId);

    int getMobileIndexNumber(GetMobileIndexNumber disease);

    int getMobileIndexNumber2(GetMobileIndexNumber disease);

    List<MessageListVo> getMessageList(GetFurtherConsultationList fu);
    List<MessageListVo> getSuiFangMessageList(GetFurtherConsultationList fu);

    /**
     * 随访记录查询
     *
     * @param fu
     * @return
     */
    List<MessageListVo> getSuiFangMessageRecordList(GetFurtherConsultationList fu);

    /**
     * 获取患者任务
     *
     * @param aiSuiFangListRe
     * @return
     */
    List<SRoadTaskPatients> getTaskPageList(AiSuiFangListRe aiSuiFangListRe);

    /**
     * 根据是否传任务id，关联患者任务执行表。
     *
     * @param getPatientListVo
     * @return
     */
    List<TAdminInfo> getPatientList(GetPatientListVo getPatientListVo);
    List<TAdminInfo> getPatientListTask(GetPatientListVo getPatientListVo);

    /**
     * 关联了 就诊记录表，去重处理
     *
     * @param getPatientFilterListVo
     * @return
     */
    List<TAdminInfo> getPatientFilterList(GetPatientFilterListVo getPatientFilterListVo);
    List<TAdminInfo> getPatientFilterList2(GetPatientFilterListVo getPatientFilterListVo);

    List<SRoadTaskPatients> searchScheduling(SearchSchedulingVo searchSchedulingVo);

    int updatePatientTaskStatusByPAndR(PatientTaskDelete patientTaskDelete);

    int updateTaskExcuteStatusByPrimaryKey(String taskPatientsId);

    int updateRecIdByPrimaryKey(SRoadTaskPatients taskPatients);


    List<GetPhoneList> getPhoneList(SuiFangPhoneRe suiFangPhoneRe);

    List<SRoadTaskPatientsPhone> getSchedulingList(SRoadTaskPatientsPhone sRoadTaskPatientsPhone);

    void deleteTaskPatientIdKey(Long taskPatientsId);

    List<PhoneTaskPageListReturnVo> getPhoneTaskPageList(PhoneTaskPageListCore phoneTaskPageListCore);

    List<CompletedQuestionnaireListRes> getCompletedQuestionnaireList(CompletedQuestionnaireCoreRe completedQuestionnaireRe);
    List<CompletedQuestionnairePreDiagnosisListRes> getCompletedQuestionnairePreDiagnosisList(CompletedQuestionnairePreRe completedQuestionnaireRe);
    List<com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCount> getCountCompletedQuestionnaireList(CompletedQuestionnaireCoreRe completedQuestionnaireRe);
    List<CompletedQuestionnaireListExcel> getExcelCompletedQuestionnaireList(CompletedQuestionnaireCoreRe completedQuestionnaireRe);
    List<CompletedQuestionnaireListExcel> getExcelCompletedPreQuestionnaireList(CompletedQuestionnairePreRe completedQuestionnaireRe);
    List<CompletedQuestionnaireListExcel> getExcelCompletedQuestionnaireYuZhenList(CompletedQuestionnaireCoreRe completedQuestionnaireRe);

    void upDateSchedulerTaskStatusById(String nextId);

    void updateReadStatusByPrimaryKey(String taskPatientsId);

    List<SRoadTaskPatients> getPatientListTaskByIds(List<String> list);

    List<com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCount> getCountCompletedQuestionnaireList2(CompletedQuestionnaireCoreRe completedQuestionnaireRe);

    List<CompletedQuestionnairePreCount> getCountQuestionnaireList(CompletedQuestionnairePreRe completedQuestionnaireRe);

    int countByConditons(SRoadTaskPatients roadPatients);

    int getCountNumCompletedQuestionnaireList(CompletedQuestionnaireCoreRe completedQuestionnaireCoreRe);

    void updateClosedByPatientId(String patientId,String closedNo);

    void updateClosedById(String id,String closedNo);

    void updateClosedByclosdNo(String closedNo, String closedStatus);

}