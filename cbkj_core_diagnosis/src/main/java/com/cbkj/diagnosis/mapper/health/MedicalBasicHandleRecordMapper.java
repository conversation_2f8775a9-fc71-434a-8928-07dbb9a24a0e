package com.cbkj.diagnosis.mapper.health;

import com.cbkj.diagnosis.beans.health.MedicalBasicHandleRecord;
import com.cbkj.diagnosis.beans.response.MedicalBasicHandleRecordResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;


public interface MedicalBasicHandleRecordMapper extends BaseMapper<MedicalBasicHandleRecord>{

    int insertList(List<MedicalBasicHandleRecord> list);
    Long selectIdByCondition(MedicalBasicHandleRecord medicalBasicHandleRecord);

    List<MedicalBasicHandleRecordResponse> getMedicalBasicHandleRecordByRecordId(String recordId);
}