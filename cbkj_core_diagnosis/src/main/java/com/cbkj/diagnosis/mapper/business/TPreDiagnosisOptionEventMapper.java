package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisOptionEvent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface TPreDiagnosisOptionEventMapper extends BaseMapper<TPreDiagnosisOptionEvent> {
    @Select("select dic_code from t_pre_diagnosis_option_event where option_id = #{optionId}")
    List<String> getStringListByOptionId(String optionId);
}
