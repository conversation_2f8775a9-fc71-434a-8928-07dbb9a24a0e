package com.cbkj.diagnosis.mapper.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cbkj.diagnosis.beans.statistics.BlockFiveDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayExpenses;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
public interface StatisticsErverDayExpensesMapper extends BaseMapper<StatisticsErverDayExpenses> {

    List<BlockFiveDetail> getBlockFiveList(StatisticsVo statisticsVo);
}
