package com.cbkj.diagnosis.mapper.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cbkj.diagnosis.beans.statistics.BlockOneDetail;
import com.cbkj.diagnosis.beans.statistics.BlockTwoDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth;
import com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
public interface StatisticsErverDayHealthMapper extends BaseMapper<StatisticsErverDayHealth> {

    List<BlockOneDetail> getBlockOneList(StatisticsVo statisticsVo);

    List<BlockTwoDetail> getBlockTwoList(StatisticsVo statisticsVo);

    List<StatisticsErverDayHealth> staticsHistoryHealth(StaticsHistoryData staticsHistoryData);
}
