package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisOptionMutex;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;

import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface TPreDiagnosisOptionMutexMapper extends BaseMapper<TPreDiagnosisOptionMutex>{
    /**
     *
     * @param optionId
     * @return
     */
    @Select("select mutex_option_id from t_pre_diagnosis_option_mutex where option_id = #{optionId}")
    List<String> getStringListByOptionId(String optionId);
}