package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.TSymptom;
import com.cbkj.diagnosis.beans.business.TSymptomClass;
import com.cbkj.diagnosis.service.webapi.business.vo.GetSymptomPageListRe;
import com.cbkj.diagnosis.service.webapi.business.vo.WebTSymptomReVo;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.List;

@Component
public interface TSymptomMapper extends BaseMapper<TSymptom> {


    List<TSymptom> getPageListByObjBySelf(WebTSymptomReVo webTSymptomReVo);

    List<TSymptom> getSymptomPageList(GetSymptomPageListRe getSymptomPageListRe);

    List<TSymptomClass> getSymptomClassPageList(Integer classHierarchy);
}