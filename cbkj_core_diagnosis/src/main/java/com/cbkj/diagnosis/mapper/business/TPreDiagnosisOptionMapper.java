package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisOption;
import com.cbkj.diagnosis.service.common.vo.QuestionOption;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public interface TPreDiagnosisOptionMapper extends BaseMapper<TPreDiagnosisOption>{


    List<QuestionOption> getObjectByQuestionId(Integer questionId);

    void deleteByQuestionId(Integer questionId);

    List<HashMap<String,String>> getDiagnosisEventCode(Integer[] answerContentId);
}