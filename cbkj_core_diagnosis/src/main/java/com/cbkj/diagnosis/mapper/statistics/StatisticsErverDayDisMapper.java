package com.cbkj.diagnosis.mapper.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cbkj.diagnosis.beans.statistics.BlockDis;
import com.cbkj.diagnosis.beans.statistics.BlockOneDetail;
import com.cbkj.diagnosis.beans.statistics.BlockTwoDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis;
import com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
public interface StatisticsErverDayDisMapper extends BaseMapper<StatisticsErverDayDis> {

    List<BlockDis> getBlockDisList(StatisticsVo statisticsVo);

    List<BlockOneDetail> getBlockOneList(StatisticsVo statisticsVo);
    List<BlockOneDetail> getBlockOneList2(StatisticsVo statisticsVo);

    List<BlockTwoDetail> getBlockTwoList(StatisticsVo statisticsVo);
    List<BlockTwoDetail> getBlockTwoListFlow(StatisticsVo statisticsVo);

    List<StatisticsErverDayDis> staticsHistoryYZ(StaticsHistoryData staticsHistoryData);

    List<StatisticsErverDayDis> staticsHistorySF(StaticsHistoryData staticsHistoryData);
}
