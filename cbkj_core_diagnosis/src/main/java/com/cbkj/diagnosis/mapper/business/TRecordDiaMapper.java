package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.service.common.vo.SuiFangPreListReVo;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;
import com.cbkj.diagnosis.service.webapi.business.vo.FaceListVo;

import java.util.HashMap;
import java.util.List;

@Component
public interface TRecordDiaMapper extends BaseMapper<TRecordDia>{


    List<TRecordDia> getObjectByRecId(String recId);


    List<FaceListVo> getFaceList(SuiFangPreListReVo suiFangPreListReVo);

    TRecordDia getRecordDiaByRecIdAndQuestionId(HashMap<String, String> map);
}