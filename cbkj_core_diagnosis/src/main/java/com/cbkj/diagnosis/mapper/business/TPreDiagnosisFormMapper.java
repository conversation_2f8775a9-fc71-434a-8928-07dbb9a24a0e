package com.cbkj.diagnosis.mapper.business;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMapping;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMappingV;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisForm;
import com.cbkj.diagnosis.beans.business.requestvo.DiaIdentificationVo;
import com.cbkj.diagnosis.beans.dao.CheckDisMappingIsMu;
import com.cbkj.diagnosis.service.mobileapi.vo.CountComputer;
import com.cbkj.diagnosis.service.webapi.business.vo.GetSuiFangPaperList;
import com.cbkj.diagnosis.service.webapi.business.vo.LookPaperRecive;
import com.cbkj.diagnosis.service.webapi.business.vo.QuestionOptionSave;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.mapper.BaseMapper;

import java.util.HashMap;
import java.util.List;

@Component
public interface TPreDiagnosisFormMapper extends BaseMapper<TPreDiagnosisForm> {

    /**
     * 获取单个对象
     *
     * @param tPreDiagnosisForm
     * @return
     */
    TPreDiagnosisForm getobjectByDiagnosisForm(TPreDiagnosisForm tPreDiagnosisForm);

    /**
     * 通过预诊单id 获取这个预诊单信息
     *
     * @param diaId
     * @return
     */
    LookPaperRecive getLookPaper(String diaId);

    void updateStatusToDeleteByDiaId(String diaId);

    List<TPreDiagnosisForm> getPaperList(HashMap<String,String> params);

    void deleteFormMapping(String diaId);

    /**
     * 问题明细-包括下面的调题
     *
     * @param questionId
     * @return
     */
    List<QuestionOptionSave> getQuestionOptionSaveList(String questionId);

    void insertPaperMapping(TPreDiagnosisDisMappingV mapping);

    List<TPreDiagnosisDisMappingV> getPaperMappingListByDiaId(String diaId);

    List<TPreDiagnosisForm> getSuiFangPaperList(GetSuiFangPaperList name);

    String getLastSuiFangFormDiaId();

    void insertRe(TPreDiagnosisForm tPreDiagnosisForm);

    int getCountComputer(CountComputer countComputer);

    TPreDiagnosisForm getOneByDisId(String disId);

    int selctCountFormCode(TPreDiagnosisForm tPreDiagnosisForm);

    TPreDiagnosisForm getObjectByFormCode(String diaId);

    void updateFormCodeIfNull(TPreDiagnosisForm tPreDiagnosisForm);

    int checkDisMappingIsMu(CheckDisMappingIsMu checkDisMappingIsMu);

    DiaIdentificationVo getDiaIdentification(String diaId);

    List<TPreDiagnosisDisMapping> getDiaIdentificationItemList(String diaId);

    void updateDiaIdentification(DiaIdentificationVo vo);
}