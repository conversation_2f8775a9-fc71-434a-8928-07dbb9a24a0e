package com.cbkj.diagnosis.mapper.health;

import com.cbkj.diagnosis.beans.health.MedicalPatientRecordCost;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;


public interface MedicalPatientRecordCostMapper extends BaseMapper<MedicalPatientRecordCost>{

    Long selectIdByCondition(MedicalPatientRecordCost medicalPatientRecordCost);

    List<MedicalPatientRecordCost> getRecordCostByRecordId(String recordId);
}