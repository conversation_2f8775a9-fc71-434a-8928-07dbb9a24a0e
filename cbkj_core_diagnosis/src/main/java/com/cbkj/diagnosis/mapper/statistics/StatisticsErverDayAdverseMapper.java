package com.cbkj.diagnosis.mapper.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cbkj.diagnosis.beans.business.TRecordEvent;
import com.cbkj.diagnosis.beans.statistics.BlockFourDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayAdverse;
import com.cbkj.diagnosis.beans.statistics.vo.ListJoinDic;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;


import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
public interface StatisticsErverDayAdverseMapper extends BaseMapper<StatisticsErverDayAdverse> {

    List<BlockFourDetail> getBlockFourList(StatisticsVo statisticsVo);

    List<TRecordEvent> listJoinDic(ListJoinDic recId);
}
