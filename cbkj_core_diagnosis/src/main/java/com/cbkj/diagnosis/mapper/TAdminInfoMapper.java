package com.cbkj.diagnosis.mapper;


import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.TAdminInfo2;
import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping;
import com.cbkj.diagnosis.service.webapi.business.vo.SetPatientMarkVo;
import org.springframework.stereotype.Component;
import com.cbkj.diagnosis.beans.AssociationUserInfo;
import java.util.List;

@Component
public interface TAdminInfoMapper extends BaseMapper<TAdminInfo> {
    TAdminInfo getUserInfoByOpenId(String openId);

    Integer getCheckUserId(String userId);

    TAdminInfo getUserInfoByUserIdAndToken(TAdminInfo TAdminInfo);

    TAdminInfo getUserInfoByToken(TAdminInfo TAdminInfo);

    TAdminInfo getUserInfoByUserPassword(TAdminInfo TAdminInfo);

    TAdminInfo getUserInfoByCardNumber(TAdminInfo2 tAdminInfo2);

    TAdminInfo getTAdminInfoById(String patientId);

    int setPatientMark(SetPatientMarkVo setPatientMarkVo);

    List<TAdminInfo> getListByIds(List<TAdminInfo> tAdminInfoList);
    List<TAdminInfo> getListByIds2(List<String> tAdminInfoList);


    TAdminInfo checkPatientEx(TAdminInfo2 tAdminInfo2);

    List<AssociationUserInfo> getaAssociation(String currentUserId);

    TAdminInfo getTAdminInfoByMobile(String mobile);

    int checkPatientJoinThisTask(SRoadTaskPatientsMapping patientsMapping);

    List<TAdminInfo> getOnePatientAllRecords(SRoadTaskPatientsMapping patientsMapping);

    TAdminInfo getUserByMobile(TAdminInfo2 tAdminInfo2);

    TAdminInfo getHisLoginCheckUserInfo(TAdminInfo2 tAdminInfo2);

    TAdminInfo getHisLoginCheckUserInfoByIdCard(TAdminInfo2 tAdminInfo2);
}