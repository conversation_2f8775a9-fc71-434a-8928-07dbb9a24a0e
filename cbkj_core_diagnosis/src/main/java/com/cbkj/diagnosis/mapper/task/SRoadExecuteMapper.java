package com.cbkj.diagnosis.mapper.task;

import com.cbkj.diagnosis.beans.task.SRoadConditions;
import com.cbkj.diagnosis.beans.task.SRoadExecute;
import com.cbkj.diagnosis.service.webapi.business.vo.GetRecordAndPatientByConditions;
import com.cbkj.diagnosis.mapper.BaseMapper;
import com.cbkj.diagnosis.service.webapi.business.vo.RecordAndPatientByConditions;

import java.util.List;


public interface SRoadExecuteMapper extends BaseMapper<SRoadExecute>{

    /**
     * 获取任务执行内容列表
     * @param sRoadIid
     * @return
     */
    List<SRoadExecute> selectsRoadExecuteList(String sRoadIid);

    List<GetRecordAndPatientByConditions> getRecordAndPatientByConditions(RecordAndPatientByConditions conditions);

    List<SRoadExecute> getRoadEventTwoDetailById(String sRoadIid);
}