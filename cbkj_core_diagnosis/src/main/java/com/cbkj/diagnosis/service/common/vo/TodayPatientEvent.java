package com.cbkj.diagnosis.service.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/6 14:48
 * @Version 1.0
 */
@Data
@Schema
public class TodayPatientEvent {
    @Schema(description = "字典值id")
    private String dicId;
    @Schema(description = "字典值(事件)代码")
    private String dicCode;
    @Schema(description = "字典值（事件）名称")
    private String dicName;
    private String style;
    private String simpleLabel;
}
