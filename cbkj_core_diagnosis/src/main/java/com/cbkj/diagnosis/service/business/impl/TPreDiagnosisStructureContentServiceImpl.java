package com.cbkj.diagnosis.service.business.impl;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisStructureContent;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisStructureContentMapper;
import com.cbkj.diagnosis.service.business.TPreDiagnosisStructureContentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Service
public class TPreDiagnosisStructureContentServiceImpl extends ServiceImpl<TPreDiagnosisStructureContentMapper, TPreDiagnosisStructureContent> implements TPreDiagnosisStructureContentService {

    public void deleteByDiaId(String diaId) {
        TPreDiagnosisStructureContentMapper mapper = (TPreDiagnosisStructureContentMapper)baseMapper;
        mapper.deleteByDiaId(diaId);
    }
}
