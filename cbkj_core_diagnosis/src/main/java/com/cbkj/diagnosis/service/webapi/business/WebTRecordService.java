package com.cbkj.diagnosis.service.webapi.business;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.task.*;
import com.cbkj.diagnosis.beans.task.webvo.UpdateOrInsertRoad;
import com.cbkj.diagnosis.common.openfeign.LargeModelJsonExtractor;
import com.cbkj.diagnosis.mapper.TAdminInfoMapper;
import com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsPhoneMapper;
import com.cbkj.diagnosis.mapper.business.TRecordDiaMapper;
import com.cbkj.diagnosis.mapper.business.TRecordMapper;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.IDUtil;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.task.SRoadExecuteMapper;
import com.cbkj.diagnosis.mapper.task.SRoadMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.SysDicService;
import com.cbkj.diagnosis.service.business.DiagnosisStructureUtils;
import com.cbkj.diagnosis.service.business.SysAdminInfoDisMappingService;
import com.cbkj.diagnosis.service.business.TRecordLargeModelService;
import com.cbkj.diagnosis.service.common.vo.AiSuiFangListRe;
import com.cbkj.diagnosis.service.common.vo.PreListReVo;
import com.cbkj.diagnosis.service.common.vo.PreListReVoDAO;
import com.cbkj.diagnosis.service.common.vo.SuiFangPreListReVo;
import com.cbkj.diagnosis.service.webapi.business.vo.*;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.cbkj.diagnosis.utils.AESEncrypt;
import com.cbkj.diagnosis.utils.SystemConstants;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class WebTRecordService {
    private final SRoadMapper sRoadMapper;
    private final SRoadExecuteMapper sRoadExecuteMapper;

    private TRecordMapper tRecordMapper;
    private TRecordDiaMapper tRecordDiaMapper;
    private final SRoadTaskMapper sRoadTaskMapper;
    private SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    private final SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper;
    private final TAdminInfoMapper tAdminInfoMapper;

    private final SysAdminInfoDisMappingService sysAdminInfoDisMappingService;

    private final SysDicService sysDicService;

    private final DiagnosisStructureUtils diagnosisStructureUtils;
    private final TRecordLargeModelService tRecordLargeModelService;
    WebTRecordService(SRoadMapper sRoadMapper, SRoadExecuteMapper sRoadExecuteMapper, TRecordMapper tRecordMapper,
                      TRecordDiaMapper tRecordDiaMapper,
                      SRoadTaskMapper sRoadTaskMapper, SRoadTaskPatientsMapper sRoadTaskPatientsMapper, SRoadTaskPatientsPhoneMapper sRoadTaskPatientsPhoneMapper, TAdminInfoMapper tAdminInfoMapper, SysAdminInfoDisMappingService sysAdminInfoDisMappingService, SysDicService sysDicService, DiagnosisStructureUtils diagnosisStructureUtils, TRecordLargeModelService tRecordLargeModelService) {
        this.sRoadMapper = sRoadMapper;
        this.sRoadExecuteMapper = sRoadExecuteMapper;
        this.tRecordMapper = tRecordMapper;
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.sRoadTaskMapper = sRoadTaskMapper;
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.sRoadTaskPatientsPhoneMapper = sRoadTaskPatientsPhoneMapper;
        this.tAdminInfoMapper = tAdminInfoMapper;
        this.sysAdminInfoDisMappingService = sysAdminInfoDisMappingService;
        this.sysDicService = sysDicService;
        this.diagnosisStructureUtils = diagnosisStructureUtils;
        this.tRecordLargeModelService = tRecordLargeModelService;
    }

    /**
     * 加载分页数据
     *
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2023-09-05
     */
    public Object getPageDatas(PreListReVo preListReVo, Page page, String userId) {

//        String key = preListReVo.getKey();
//        if (StringUtils.isNotBlank(key)) {
//            preListReVo.setKey(AESEncrypt.aes_encrypt(key, SystemConstants.SYS_STR_KEY));
//        }
        //   log.info("getPageDatas==============1");
        PageHelper.startPage(page.getPage(), page.getLimit());
        //  log.info("getPageDatas==============2");
        PreListReVoDAO preListReVoDAO = new PreListReVoDAO();
        BeanUtils.copyProperties(preListReVo, preListReVoDAO);
        preListReVoDAO.setUserId(userId);
        preListReVoDAO.setDisId(preListReVo.getDisId());
//        QueryWrapper<SysAdminInfoDisMapping> sysAdminInfoDisMappingQueryWrapper = new QueryWrapper<SysAdminInfoDisMapping>();
//        sysAdminInfoDisMappingQueryWrapper.eq("user_id", userId).eq("dis_id","-1");
//        long count = sysAdminInfoDisMappingService.count(sysAdminInfoDisMappingQueryWrapper);

//if (count>0){
//    preListReVoDAO.setDiaTypeBool(true);
//}else
//{
//    preListReVoDAO.setDiaTypeBool(false);
//}
        List<TRecord> list = tRecordMapper.getPageListByObjNew(preListReVoDAO);
        log.info("getPageDatas==============3");
        PageHelper.clearPage();
        if (StringUtils.isNotBlank(preListReVo.getPatientId())) {
            for (TRecord tRecord : list) {
                tRecord.setDiaContent(null);
                List<GetQuestionClassTypeInfo> structureContent = diagnosisStructureUtils.getStructureContent(tRecord.getRecId());
                if (null != structureContent) {
                    tRecord.setQuestionClassTypeInfoList(structureContent);
                } else {
//                List<GetQuestionClassTypeInfo> getQuestionClassTypeInfoList = tRecordMapper.getQuestionClassTypeInfo(tRecord.getRecId());
//                List<GetQuestionClassTypeInfo> getQuestionClassTypeInfos = questionClassTypeInfoListHandle(getQuestionClassTypeInfoList);
                    tRecord.setQuestionClassTypeInfoList(new ArrayList<GetQuestionClassTypeInfo>());
                }


            }
        }


        return Page.getLayUiTablePageData(list);
    }

    public List<GetQuestionClassTypeInfo> questionClassTypeInfoListHandle(List<GetQuestionClassTypeInfo> getQuestionClassTypeInfoList) {


        if (getQuestionClassTypeInfoList == null || getQuestionClassTypeInfoList.size() == 0) {
            return null;
        }
        for (int i = 0; i < getQuestionClassTypeInfoList.size(); i++) {
            GetQuestionClassTypeInfo getQuestionClassTypeInfo = getQuestionClassTypeInfoList.get(i);
            if (Constant.BASIC_STRING_FOUR.equals(getQuestionClassTypeInfo.getQuestionType())
            ) {

                String content = getQuestionClassTypeInfo.getContent();
                if (!StringUtils.isNotBlank(content)) {
                    continue;
                }
//                content = content.replaceAll("\"", "");
//                content = content.replaceAll("\\\\", ""); // 注意：在字符串中，反斜杠需要被转义
//                content = content.replaceAll("\\[", "");
//                content = content.replaceAll("\\]", "");
                String[] split22 = content.split("-");

                if (split22.length == 2) {
                    String[] split = split22[0].split("#");
                    if (split.length != 2) {
                        continue;
                    }

                    String s1 = split[1];
                    String[] split2 = s1.substring(1, s1.length() - 1).split("\",\"");
                    String[] split1 = split22[1].split(",");
//                    if (split2.length != split1.length){
//                        continue;
//                    }
                    StringBuilder split2Builder = new StringBuilder();
                    split2Builder.append(split[0]);
                    // 因为分割后，每部分的开头和结尾都会有一个引号，需要去除  
                    split2[0] = split2[0].substring(1, split2[0].length());
                    if (split2.length > 1) {
                        split2[split2.length - 1] = split2[split2.length - 1].substring(0, split2[split2.length - 1].length() - 1);
                    } else {
                        split2[0] = split2[0].substring(0, split2[0].length() - 1);
                    }

                    // String[] split2 = split[1];


                    for (int i1 = 0; i1 < split1.length; i1++) {
                        if (split2.length <= i1) {
                            break;
                        }
                        String s = split1[i1];
                        if ("1".equals(s)) {
                            if (StringUtils.isNotBlank(split2[i1]) && StringUtils.isNumeric(split2[i1])) {
                                // split2[i1] = Integer.parseInt(split2[i1])+"年";
                                split2Builder.append(Integer.parseInt(split2[i1])).append("年");
                            }

                        }
                        if ("2".equals(s)) {
                            if (StringUtils.isNotBlank(split2[i1]) && StringUtils.isNumeric(split2[i1])) {
                                // split2[i1] =  Integer.parseInt(split2[i1])+"月";
                                split2Builder.append(Integer.parseInt(split2[i1])).append("月");
                            }

                        }
                        if ("3".equals(s)) {
                            if (StringUtils.isNotBlank(split2[i1]) && StringUtils.isNumeric(split2[i1])) {
                                // split2[i1] =  Integer.parseInt(split2[i1])+"日";
                                split2Builder.append(Integer.parseInt(split2[i1])).append("日");
                            }
                        }
                        if ("4".equals(s)) {
                            if (StringUtils.isNotBlank(split2[i1]) && StringUtils.isNumeric(split2[i1])) {
                                //split2[i1] =  Integer.parseInt(split2[i1])+"时";
                                split2Builder.append(Integer.parseInt(split2[i1])).append("时");
                            }
                        }
                    }
//重新赋值
                    //    String s = Arrays.toString(split2).replaceAll("\"","");

                    //  getQuestionClassTypeInfo.setContent(split[0]+s.replace(",",""));
                    //去掉连续两个中文冒号的其中一个。
                    String output = split2Builder.toString().replaceAll("#", "");
                    getQuestionClassTypeInfo.setContent(output);


                }

            } else if (Constant.BASIC_STRING_SIX.equals(getQuestionClassTypeInfo.getQuestionType())) {
                String content = getQuestionClassTypeInfo.getContent();
                if (StringUtils.isBlank(content)) {
                    continue;
                }
                String replace = content.replaceAll("\\\\", "");
                //新增的日期类型
                String substring = replace.replaceAll("\"", "");
                String[] split = substring.split(",");
                StringBuilder split2Builder = new StringBuilder();
                split2Builder.append(Integer.parseInt(split[0].replaceAll("#", "").replaceAll("\\[", ""))).append("年").
                        append(Integer.parseInt(split[1].replaceAll("#", "").replaceAll("\\[", "")))
                        .append("月").append(Integer.parseInt(split[2].replaceAll("#", "").replaceAll("\\[", ""))).append("日");
                getQuestionClassTypeInfo.setContent(split2Builder.toString().replaceAll("#", ""));
            } else {
                String content = getQuestionClassTypeInfo.getContent();
                getQuestionClassTypeInfo.setContent(content.replaceAll("#", ""));
            }
        }
        log.info("------------------------------111111111--------------------");
        //获取字典分类数据
        QueryWrapper<SysDic> sysDicQueryWrapper = new QueryWrapper<>();
        sysDicQueryWrapper.eq("parent_id", "1").orderBy(true, true, "dic_sort");
        List<SysDic> list = sysDicService.list(sysDicQueryWrapper);
        HashMap<String, String> map = new HashMap<>();
        for (SysDic sysDic : list) {
            map.put(sysDic.getDicCode(), sysDic.getDicName());
        }

        //升序排序
        Comparator<GetQuestionClassTypeInfo> questionClassTypeComparator = Comparator.comparingInt(
                questionClassTypeInfoList -> Integer.parseInt(questionClassTypeInfoList.getQuestionClassType()));
        //分组
        Map<String, List<GetQuestionClassTypeInfo>> collect1 = getQuestionClassTypeInfoList.stream().collect(Collectors.groupingBy(GetQuestionClassTypeInfo::getQuestionClassType));

        ArrayList<GetQuestionClassTypeInfo> finalQuestionClassTypeInfos1 = new ArrayList<GetQuestionClassTypeInfo>();
        collect1.forEach(
                ((questionClassType, getQuestionClassTypeInfos) -> {

                    String contents = getQuestionClassTypeInfos.stream()
                            // 替换content字段中的中括号和中文引号
                            .map(
                                    questionClassTypeInfo -> questionClassTypeInfo.getContent().replaceAll("[\\[\\]\"]", "")


                            )
                            .collect(Collectors.joining("。"));
                    GetQuestionClassTypeInfo getQuestionClassTypeInfo = new GetQuestionClassTypeInfo();

                    getQuestionClassTypeInfo.setQuestionClassType(questionClassType);
                    getQuestionClassTypeInfo.setContent(contents);
                    getQuestionClassTypeInfo.setQuestionClassName(map.get(questionClassType));
                    finalQuestionClassTypeInfos1.add(getQuestionClassTypeInfo);
                })
        );
        List<GetQuestionClassTypeInfo> collect = finalQuestionClassTypeInfos1.stream().sorted(questionClassTypeComparator).collect(Collectors.toList());

        return collect;
    }


    /**
     * 加载某条数据
     *
     * @param recId
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    public ResEntity findObj(String recId) {

        if (StringUtils.isBlank(recId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TRecord tRecord = tRecordMapper.getObjectById(recId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tRecord);
    }


    /**
     * 插入新数据
     *
     * @param tRecord
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TRecord tRecord) {

        tRecord.setRecId(IDUtil.getID());
        long rows = tRecordMapper.insert(tRecord);

        return ResEntity.success(tRecord);
    }


    /**
     * 修改
     *
     * @param tRecord
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TRecord tRecord) {

        long rows = tRecordMapper.updateByPrimaryKey(tRecord);

        return ResEntity.success(tRecord);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tRecordMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    public ArrayList<PreResultDifferent> getPreResultDifferent(String patientId, String diaId, String recId) {
//        TRecord tRecord = new TRecord();
//        tRecord.setDiaId(diaId);
//        tRecord.setPatientId(patientId);
//        PageHelper.startPage(1, 2);
        // List<TRecord> pageListByObj = tRecordMapper.getPageListByObj2(tRecord);
//        PageHelper.clearPage();
        //当前
        List<TRecordDia> objectByRecId = tRecordDiaMapper.getObjectByRecId(recId);
        //当前上一条
        List<TRecordDia> objectByRecId2 = null;
        String currentSendRecId = tRecordMapper.getCurrentSecondByRecId(tRecordMapper.getObjectById(recId));
        if (currentSendRecId != null) {
            objectByRecId2 = tRecordDiaMapper.getObjectByRecId(currentSendRecId);
        }

        ArrayList<PreResultDifferent> arrayList = new ArrayList<>();
//        for (int i = 0; i < pageListByObj.size(); i++) {
//            if (i == 0) {
//                objectByRecId = tRecordDiaMapper.getObjectByRecId(pageListByObj.get(i).getRecId());
//            } else {
//                objectByRecId2 = tRecordDiaMapper.getObjectByRecId(pageListByObj.get(i).getRecId());
//            }
//        }


        if (objectByRecId != null) {
            for (TRecordDia recordDia : objectByRecId) {
                PreResultDifferent preResultDifferent = new PreResultDifferent();
                preResultDifferent.setTitleName(recordDia.getQuestionName());
                preResultDifferent.setLastContent(recordDia.getContent());
                if (objectByRecId2 != null) {
                    for (TRecordDia tRecordDia : objectByRecId2) {
                        if (tRecordDia.getQuestionId().equals(recordDia.getQuestionId())) {
                            preResultDifferent.setPastContent(tRecordDia.getContent());
                            break;
                        } else if (tRecordDia.getQuestionName().equals(recordDia.getQuestionName())) {
                            preResultDifferent.setPastContent(tRecordDia.getContent());
                            break;
                        } else if ((StringUtils.isNotBlank(recordDia.getQuestionCode()) && StringUtils.isNotBlank(tRecordDia.getQuestionCode()))
                                && tRecordDia.getQuestionCode().equals(recordDia.getQuestionCode())) {
                            preResultDifferent.setPastContent(tRecordDia.getContent());
                            break;
                        } else {
                            preResultDifferent.setPastContent("无");
                        }
                    }
                } else {
                    preResultDifferent.setPastContent("无");
                }

                arrayList.add(preResultDifferent);
            }
        }
        return arrayList;
    }

    /**
     * 获取面访列表
     *
     * @param suiFangPreListReVo
     * @param page
     */
    public Object getFacePageList(SuiFangPreListReVo suiFangPreListReVo, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<FaceListVo> faceList = tRecordDiaMapper.getFaceList(suiFangPreListReVo);
        PageHelper.clearPage();
        return Page.getLayUiTablePageData(faceList);
    }

    /**
     * 获取患者任务
     *
     * @param aiSuiFangListRe
     * @param page
     * @return
     */
    public Object getTaskPageList(AiSuiFangListRe aiSuiFangListRe, Page page) {
        String text = "尊敬的A,根据您上次就诊的时间,提醒您抽空来我院复诊。为了您的健康,请多关心自己! ";
        PageHelper.startPage(page.getPage(), page.getLimit());
//        if (StringUtils.isNotBlank(aiSuiFangListRe.getStartDate())){
//            aiSuiFangListRe.setStartDate(aiSuiFangListRe.getStartDate().substring(0,10));
//        }
//        if (StringUtils.isNotBlank(aiSuiFangListRe.getEndDate())){
//            aiSuiFangListRe.setEndDate(aiSuiFangListRe.getEndDate().substring(0,11));
//        }
        List<SRoadTaskPatients> a = sRoadTaskPatientsMapper.getTaskPageList(aiSuiFangListRe);
        for (SRoadTaskPatients sRoadTaskPatients : a) {
            if (Constant.BASIC_STRING_THREE.equals(sRoadTaskPatients.getRoadExecuteEventType())) {
                sRoadTaskPatients.setRoadExecuteEventContentName(text.replace("A", sRoadTaskPatients.getPatientName()));
            }
        }
        PageHelper.clearPage();
        return Page.getLayUiTablePageData(a);
    }

    public Object getPhoneTaskPageList(PhoneTaskPageListCore phoneTaskPageListCore, Page page) {
        com.github.pagehelper.Page<Object> objects = PageHelper.startPage(page.getPage(), page.getLimit());

        List<PhoneTaskPageListReturnVo> phoneTaskPageListReturnVoList = sRoadTaskPatientsMapper.getPhoneTaskPageList(phoneTaskPageListCore);

        PageHelper.clearPage();
        return Page.getLayUiTablePageData(phoneTaskPageListReturnVoList);
    }

    public Object addPatientsToTask(String patientIds, String sroadTaskId) {
        SRoadTask objectById = sRoadTaskMapper.getObjectById(sroadTaskId);
        //就取路径事件类型是：就诊后
        UpdateOrInsertRoad updateOrInsertRoad = getRoadEventTwoDetailById(objectById.getSRoadId());
        List<SRoadExecute> sRoadExecuteList = updateOrInsertRoad.getSRoadExecuteList();
        String[] split = patientIds.split(",");
        ArrayList<SRoadTaskPatients> sRoadTaskPatients = new ArrayList<>();
        for (int i = 0; i < split.length; i++) {
            //获取患者信息
            TAdminInfo tAdminInfo = tAdminInfoMapper.getTAdminInfoById(split[i]);
            if (tAdminInfo == null) {
                continue;
            }
            for (SRoadExecute sRoadExecute : sRoadExecuteList) {
                if (sRoadExecute.getRoadExecuteId() == null) {
                    return ResEntity.error("缺少sRoadExecuteList.roadExecuteId");
                }

                SRoadTaskPatients roadPatients = new SRoadTaskPatients();
                BeanUtils.copyProperties(sRoadExecute, roadPatients);
                roadPatients.setHandSend("1");
                List<SRoadExecuteContents> sRoadExecuteContentsList = sRoadExecute.getSRoadExecuteContentsList();
                for (SRoadExecuteContents executeContents : sRoadExecuteContentsList) {
                    BeanUtils.copyProperties(executeContents, roadPatients);
                    roadPatients.setRoadExecuteEventTime(sRoadExecute.getRoadExecuteTime());
                    roadPatients.setPatientId(tAdminInfo.getUserId());
                    roadPatients.setPatientAge(tAdminInfo.getAge());
                    roadPatients.setPatientSex(tAdminInfo.getSex());
                    roadPatients.setPatientName(tAdminInfo.getUserName());
                    roadPatients.setPatientCardNumber(tAdminInfo.getCardNumber());
                    roadPatients.setTaskExcuteStatus(2);
                    //设置为今天
                    roadPatients.setTaskExcuteTime(new Date());
                    roadPatients.setStatus("0");
                    roadPatients.setTaskName(objectById.getTaskName());
                    roadPatients.setSRoadTaskId(objectById.getSRoadTaskId());
                    roadPatients.setHandSend("0");
                    //没有就诊记录啊
                    //roadPatients.setRecordsId(tAdminInfo.getRecordsId());
                    sRoadTaskPatients.add(roadPatients);
                }
            }

        }
        if (sRoadTaskPatients.size() > 0) {
            sRoadTaskPatientsMapper.insertList(sRoadTaskPatients);
            ArrayList<SRoadTaskPatientsPhone> sRoadTaskPatientsPhones = new ArrayList<>();
            //此处都是就诊后，所以病历已经有时间了
            for (SRoadTaskPatients sRoadTaskPatients1 : sRoadTaskPatients) {
                if (sRoadTaskPatients1.getRoadExecuteEventWay().equals(Constant.BASIC_STRING_THREE) &&

                        (sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_TWO) ||
                                sRoadTaskPatients1.getRoadExecuteEventType().equals(Constant.BASIC_STRING_FOUR)
                        )
                ) {
                    SRoadTaskPatientsPhone sRoadTaskPatientsPhone = new SRoadTaskPatientsPhone();
                    BeanUtils.copyProperties(sRoadTaskPatients1, sRoadTaskPatientsPhone);
                    sRoadTaskPatientsPhone.setAllotTaskStatus("1");
                    sRoadTaskPatientsPhone.setPhoneStatus(2);
//                    sRoadTaskPatientsPhones.add(sRoadTaskPatientsOtherService.newSRoadTaskPatientsPhone(sRoadTaskPatients1));
                    sRoadTaskPatientsPhone.setSuiFangTime(new Date());
                    sRoadTaskPatientsPhones.add(sRoadTaskPatientsPhone);
                }
            }
            if (sRoadTaskPatientsPhones.size() > 0) {
                sRoadTaskPatientsPhoneMapper.insertList(sRoadTaskPatientsPhones);
            }


        }
        return ResEntity.error("加入任务失败");
    }

    public UpdateOrInsertRoad getRoadEventTwoDetailById(String sRoadIid) {
        UpdateOrInsertRoad updateOrInsertRoad = new UpdateOrInsertRoad();
        SRoad sRoad = sRoadMapper.getObjectById(sRoadIid);
        List<SRoadExecute> sRoadExecuteList = sRoadExecuteMapper.getRoadEventTwoDetailById(sRoadIid);
        updateOrInsertRoad.setSRoad(sRoad);
        updateOrInsertRoad.setSRoadExecuteList(sRoadExecuteList);

        return updateOrInsertRoad;
    }

    public Object getPreOne(String recId) {
        if (StringUtils.isBlank(recId)) {
            return ResEntity.error("缺少字段");
        }
        TRecord tRecord = tRecordMapper.getOneByRecId(recId);
        if (tRecord != null) {
            List<GetQuestionClassTypeInfo> getQuestionClassTypeInfoList = diagnosisStructureUtils.getStructureContent(recId);
            if (null != getQuestionClassTypeInfoList) {
                tRecord.setQuestionClassTypeInfoList(getQuestionClassTypeInfoList);
            } else {
                tRecord.setQuestionClassTypeInfoList(new ArrayList<GetQuestionClassTypeInfo>());
//                List<GetQuestionClassTypeInfo> getQuestionClassTypeInfoList2 = tRecordMapper.getQuestionClassTypeInfo(recId);
//                if (getQuestionClassTypeInfoList2 != null) {
//                    List<GetQuestionClassTypeInfo> getQuestionClassTypeInfos = questionClassTypeInfoListHandle(getQuestionClassTypeInfoList2);
//                    tRecord.setQuestionClassTypeInfoList(getQuestionClassTypeInfos);
//                }

            }

            if (tRecord.getCueWordStatus() != null && tRecord.getCueWordStatus() == 0) {
                QueryWrapper<TRecordLargeModel> tRecordLargeModelQueryWrapper = new QueryWrapper<>();
                tRecordLargeModelQueryWrapper.eq("rec_id", recId).orderBy(true, false, "insert_time");
                TRecordLargeModel one = tRecordLargeModelService.getOne(tRecordLargeModelQueryWrapper);
                if (one != null){
                    String responseText = one.getResponseText();
                    String s = LargeModelJsonExtractor.extractJson(responseText);
                    try {
                        List<GetQuestionClassTypeInfo> getQuestionClassTypeInfos = JSON.parseArray(s, GetQuestionClassTypeInfo.class);
                        tRecord.setQuestionClassTypeInfoAIList(getQuestionClassTypeInfos);
                    } catch (Exception e) {
                        log.error("解析模型返回数据失败:{}。rec_id={}", e.getMessage(),recId);
                    }
                }
            }

        }
        return ResEntity.success(tRecord);
    }

}
