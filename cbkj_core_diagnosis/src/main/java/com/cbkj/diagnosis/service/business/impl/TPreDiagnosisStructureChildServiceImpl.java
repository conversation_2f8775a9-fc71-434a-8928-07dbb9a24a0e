package com.cbkj.diagnosis.service.business.impl;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisStructureChild;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisStructureChildMapper;
import com.cbkj.diagnosis.service.business.TPreDiagnosisStructureChildService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Service
public class TPreDiagnosisStructureChildServiceImpl extends ServiceImpl<TPreDiagnosisStructureChildMapper, TPreDiagnosisStructureChild> implements TPreDiagnosisStructureChildService {

    public void deleteByDiaId(String diaId) {
        TPreDiagnosisStructureChildMapper mapper = (TPreDiagnosisStructureChildMapper)baseMapper;
        mapper.deleteByDiaId(diaId);
    }
}
