package com.cbkj.diagnosis.service.statistics;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMapping;
import com.cbkj.diagnosis.beans.business.TRecordEvent;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayEffect;
import com.cbkj.diagnosis.beans.statistics.vo.ListJoinDic;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisDisMappingServiceImpl;
import com.cbkj.diagnosis.service.statistics.impl.StatisticsErverDayEffectServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/30 17:18
 * @Version 1.0
 */
@Slf4j
@Service
public class StatisticsEffect {
    private final StatisticsErverDayEffectServiceImpl statisticsErverDayEffectService;
    private final TPreDiagnosisDisMappingServiceImpl tPreDiagnosisDisMappingService;
    public StatisticsEffect(StatisticsErverDayEffectServiceImpl statisticsErverDayEffectService, TPreDiagnosisDisMappingServiceImpl tPreDiagnosisDisMappingService) {
        this.statisticsErverDayEffectService = statisticsErverDayEffectService;
        this.tPreDiagnosisDisMappingService = tPreDiagnosisDisMappingService;
    }

    @Async
    public void writeReadFromRedis(String recId, String diaId,String appId,String insCode ,String insId,String insName,String patientId) {


            log.info("患者答题疗效评价开始写入统计表recId= {}",recId);
            // = AdminUtils.getCurrentHr();
            QueryWrapper<TPreDiagnosisDisMapping> wrapper = new QueryWrapper<>();
            wrapper.eq("dia_id", diaId).eq("dis_type","1");
            List<TPreDiagnosisDisMapping> list1 = tPreDiagnosisDisMappingService.list(wrapper);
            if (!list1.isEmpty()) {
                ArrayList<StatisticsErverDayEffect> statisticsDiagnosisDics = new ArrayList<>();
                /**
                 * 取出当前问卷的疾病ids
                 */
                ListJoinDic listJoinDic = new ListJoinDic();
                listJoinDic.setDiaId(diaId);
                listJoinDic.setRecId(recId);
                for (int i = 0; i < list1.size(); i++) {
                    TPreDiagnosisDisMapping tPreDiagnosisDisMapping = list1.get(i);
//                QueryWrapper<TRecordEvent> tRecordEventQueryWrapper = new QueryWrapper<>();
//                tRecordEventQueryWrapper.eq("rec_id", recId);
                    /**
                     * 取出当前问卷患者作答的选项的事件
                     */
                    List<TRecordEvent> list = statisticsErverDayEffectService.listJoinDic(listJoinDic);
                    if (!list.isEmpty()) {
                        list.forEach(tRecordEvent -> {
                            StatisticsErverDayEffect statisticsDiagnosisDic = new StatisticsErverDayEffect();
                            statisticsDiagnosisDic.setAppId(appId);
                            statisticsDiagnosisDic.setInsCode(insCode);
                            statisticsDiagnosisDic.setInsName(insName);
                            statisticsDiagnosisDic.setInsId(insId);
//                            statisticsDiagnosisDic.setPatientId(patientId);
//                            statisticsDiagnosisDic.setDicId(tRecordEvent.getDicId());
//                            statisticsDiagnosisDic.setDicName(tRecordEvent.getDicName());
//                            statisticsDiagnosisDic.setDicCode(tRecordEvent.getDicCode());
                            statisticsDiagnosisDic.setDisId(tPreDiagnosisDisMapping.getDisId());
                            statisticsDiagnosisDic.setDisName(tPreDiagnosisDisMapping.getDisName());
                            statisticsDiagnosisDic.setDisCode(tPreDiagnosisDisMapping.getDisCode());
                            statisticsDiagnosisDic.setCreateTime(new Date());
                            if ("follow-up-event-effective".equals(tRecordEvent.getDicCode())){
                                statisticsDiagnosisDic.setType(1);
                                statisticsDiagnosisDics.add(statisticsDiagnosisDic);
                            }
                            else if ("follow-up-event-significant".equals(tRecordEvent.getDicCode())){
                                statisticsDiagnosisDic.setType(2);
                                statisticsDiagnosisDics.add(statisticsDiagnosisDic);
                            }
                            else if ("follow-up-event-recovery".equals(tRecordEvent.getDicCode())){
                                statisticsDiagnosisDic.setType(3);
                                statisticsDiagnosisDics.add(statisticsDiagnosisDic);
                            }
                            else if ("follow-up-event-ineffective".equals(tRecordEvent.getDicCode())){
                                statisticsDiagnosisDic.setType(4);
                                statisticsDiagnosisDics.add(statisticsDiagnosisDic);
                            }else {
                                log.info("当前问卷没有填写疗效评价");
                            }

                        });
                    }
                }
                if (!statisticsDiagnosisDics.isEmpty()) {
                    statisticsErverDayEffectService.writeReadFromRedis(statisticsDiagnosisDics, recId);
                }
            }


    }
}
