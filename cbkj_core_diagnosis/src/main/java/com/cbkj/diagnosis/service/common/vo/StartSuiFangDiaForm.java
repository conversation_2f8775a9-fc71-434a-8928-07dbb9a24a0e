package com.cbkj.diagnosis.service.common.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class StartSuiFangDiaForm {
    @Schema(description =  "随访问卷id")
    private String roadExecuteEventContentId;
    @Schema(description =  "任务名称")
    private String roadExecuteEventContentName;
}
