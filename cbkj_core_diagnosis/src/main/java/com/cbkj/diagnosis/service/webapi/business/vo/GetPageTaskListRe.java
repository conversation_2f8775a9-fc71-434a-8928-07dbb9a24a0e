package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
@Schema(description =  "随访任务")
@Data
public class GetPageTaskListRe {
    @Schema(description="随访任务名称")
    private String taskName;
    @Schema(description="随访任务入组方式1.手动2.自动")
    private String sRoadGroupWay;
//    private String diaId;
}
