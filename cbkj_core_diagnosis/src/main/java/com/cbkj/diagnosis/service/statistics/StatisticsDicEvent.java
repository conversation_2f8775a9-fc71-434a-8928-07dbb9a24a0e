package com.cbkj.diagnosis.service.statistics;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMapping;
import com.cbkj.diagnosis.beans.business.TRecordEvent;
import com.cbkj.diagnosis.beans.statistics.StatisticsDiagnosisDic;
import com.cbkj.diagnosis.beans.statistics.vo.ListJoinDic;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisDisMappingServiceImpl;
import com.cbkj.diagnosis.service.business.impl.TRecordEventServiceImpl;
import com.cbkj.diagnosis.service.statistics.impl.StatisticsDiagnosisDicServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
@Slf4j
public class StatisticsDicEvent {

    private final StatisticsDiagnosisDicServiceImpl statisticsDiagnosisDicService;
    private final TRecordEventServiceImpl tRecordEventServiceImpl;

    private final TPreDiagnosisDisMappingServiceImpl tPreDiagnosisDisMappingService;

    public StatisticsDicEvent(StatisticsDiagnosisDicServiceImpl statisticsDiagnosisDicService, TRecordEventServiceImpl tRecordEventServiceImpl, TPreDiagnosisDisMappingServiceImpl tPreDiagnosisDisMappingService) {
        this.statisticsDiagnosisDicService = statisticsDiagnosisDicService;
        this.tRecordEventServiceImpl = tRecordEventServiceImpl;

        this.tPreDiagnosisDisMappingService = tPreDiagnosisDisMappingService;
    }

    @Async
    public void writeReadFromRedis(String recId, String diaId,String appId,String insCode ,String insId,String insName,String patientId ) {
        log.info("患者答题选项事件开始写入统计表recId= {}",recId);
//        TAdminInfo currentHr = AdminUtils.getCurrentHr();
        QueryWrapper<TPreDiagnosisDisMapping> wrapper = new QueryWrapper<>();
        wrapper.eq("dia_id", diaId).eq("dis_type","1");
        List<TPreDiagnosisDisMapping> list1 = tPreDiagnosisDisMappingService.list(wrapper);
        if (!list1.isEmpty()) {
            ArrayList<StatisticsDiagnosisDic> statisticsDiagnosisDics = new ArrayList<>();
            /**
             * 取出当前问卷的疾病ids
             */
            ListJoinDic listJoinDic = new ListJoinDic();
            listJoinDic.setDiaId(diaId);
            listJoinDic.setRecId(recId);
            for (int i = 0; i < list1.size(); i++) {
                TPreDiagnosisDisMapping tPreDiagnosisDisMapping = list1.get(i);
//                QueryWrapper<TRecordEvent> tRecordEventQueryWrapper = new QueryWrapper<>();
//                tRecordEventQueryWrapper.eq("rec_id", recId);
                /**
                 * 取出当前问卷患者作答的选项的事件
                 */
                List<TRecordEvent> list = tRecordEventServiceImpl.listJoinDic(listJoinDic);
                if (!list.isEmpty()) {
                    list.forEach(tRecordEvent -> {
                        StatisticsDiagnosisDic statisticsDiagnosisDic = new StatisticsDiagnosisDic();
                        statisticsDiagnosisDic.setAppId(appId);
                        statisticsDiagnosisDic.setInsCode(insCode);
                        statisticsDiagnosisDic.setInsName(insName);
                        statisticsDiagnosisDic.setInsId(insId);
                        statisticsDiagnosisDic.setPatientId(patientId);
                        statisticsDiagnosisDic.setDicId(tRecordEvent.getDicId());
                        statisticsDiagnosisDic.setDicName(tRecordEvent.getDicName());
                        statisticsDiagnosisDic.setDicCode(tRecordEvent.getDicCode());
                        statisticsDiagnosisDic.setDisId(tPreDiagnosisDisMapping.getDisId());
                        statisticsDiagnosisDic.setDisName(tPreDiagnosisDisMapping.getDisName());
                        statisticsDiagnosisDic.setDisCode(tPreDiagnosisDisMapping.getDisCode());
                        statisticsDiagnosisDic.setCreateTime(new Date());
                        statisticsDiagnosisDics.add(statisticsDiagnosisDic);
                    });
                }
            }
            if (!statisticsDiagnosisDics.isEmpty()) {
                statisticsDiagnosisDicService.statisticsDiagnosisDic(statisticsDiagnosisDics,recId);

            }
        }
    }

}
