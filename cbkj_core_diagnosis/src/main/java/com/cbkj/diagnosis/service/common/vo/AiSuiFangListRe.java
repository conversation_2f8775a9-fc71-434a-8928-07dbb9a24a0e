package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(description =  "智能随访列表查询字段实体类")
public class AiSuiFangListRe {


    @Schema(description =  "1.微信2短信3.电话")
    private String roadDetailEventWay;

    @Schema(description =  "随访任务id")
    private String sRoadTaskId;
    @Schema(description =  "执行的具体内容类型1.健康宣教2.随机问卷3.复诊提醒4.自测量表")
    private String roadDetailEventType;

//    @Schema(description =  "1.缴费后2.就诊后3.服药4.服药结束前")
//    private String roadDetailEvent;

    @Schema(description="执行内容的主键id")
    private String roadExecuteEventContentId;

    @Schema(description =  "1.待执行（未发送）2.已执行（已发送,未读（微信短信））3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 7.已读（微信短信）8.已填写")
    private String taskExcuteStatus;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "身份证")
    private String patientIdcard;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者姓名")
    private String patientName;

    @Schema(description =  "中西医诊断")
    private String diseaseName;

    @Schema(description =  "时间开始")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startDate;
    //    @Schema(description =  "时间结束")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endDate;
    @Schema(hidden = true)
    private String createUserId;
}
