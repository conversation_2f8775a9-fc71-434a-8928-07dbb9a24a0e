package com.cbkj.diagnosis.service.common.strategy.record.impl;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionEventMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMutexMapper;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategy;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.common.vo.QuestionOption;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * 单选
 */
@Component("recordSelect")
public class TypeSelectStrategy implements RecordDiaStrategy {

    private TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;

    private TPreDiagnosisOptionMutexMapper tPreDiagnosisOptionMutexMapper;
    private final TPreDiagnosisOptionEventMapper tPreDiagnosisOptionEventMapper;

    TypeSelectStrategy(TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper, TPreDiagnosisOptionMutexMapper tPreDiagnosisOptionMutexMapper, TPreDiagnosisOptionEventMapper tPreDiagnosisOptionEventMapper) {
        this.tPreDiagnosisOptionMapper = tPreDiagnosisOptionMapper;
        this.tPreDiagnosisOptionMutexMapper = tPreDiagnosisOptionMutexMapper;
        this.tPreDiagnosisOptionEventMapper = tPreDiagnosisOptionEventMapper;
    }


    @Override
    public QuestionMain transRecordDia(TRecordDia tRecord) {
        QuestionMain questionMain = new QuestionMain();

        questionMain.setQuestionType(tRecord.getQuestionType());
        questionMain.setQuestionId(tRecord.getQuestionId());
        questionMain.setQuestionNumber(tRecord.getQuestionNumber());
        questionMain.setQuestionName(tRecord.getQuestionName());
        questionMain.setDiaId(tRecord.getDiaId());
        questionMain.setQuestionCode(tRecord.getQuestionCode());
        //单选回答选项id
        if (StringUtils.isNotBlank(tRecord.getOptionIds())) {
            List<String> strings = JSON.parseArray(tRecord.getOptionIds(), String.class);
            questionMain.setAnswerContentId(strings.toArray(new String[]{}));
        }
        if (StringUtils.isNotBlank(tRecord.getContent())) {
            List<String> strings = JSON.parseArray(tRecord.getContent(), String.class);
            questionMain.setAnswerContent(strings.toArray(new String[]{}));
        }
        //配置选项内容

        //从t_pre_diagnosis_option 表获取
        List<QuestionOption> objectByQuestionId = tPreDiagnosisOptionMapper.getObjectByQuestionId(tRecord.getQuestionId());
        for (QuestionOption questionOption : objectByQuestionId) {
            if (Constant.BASIC_STRING_THREE.equals(questionMain.getQuestionType())) {
                //多选题互斥选项
                List<String> mutexOptionIds = tPreDiagnosisOptionMutexMapper.getStringListByOptionId(questionOption.getOptionId());
               // List<String> eventOptionIds = tPreDiagnosisOptionEventMapper.getStringListByOptionId(questionOption.getOptionId());
                questionOption.setOptionMutexList(mutexOptionIds);
               // questionOption.setOptionFollowEventCode(eventOptionIds);
            }
        }
        questionMain.setQuestionOptionList(objectByQuestionId);
        return questionMain;
    }
}
