package com.cbkj.diagnosis.service.webapi.business.vo;

//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class XiaoShanPhoneSuiFangStatisticsTopRight {

    @Schema(description="累计电话随访人数")
    private Long xiaoShanTopRightAllPatient;

    @Schema(description="累计电话完成随访问卷份数")
    private Long xiaoShanTopRightAllDiaForm;
}
