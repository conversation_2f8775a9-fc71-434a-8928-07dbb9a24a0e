package com.cbkj.diagnosis.service.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis;
import com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.beans.statistics.BlockOneDetail;
import com.cbkj.diagnosis.beans.statistics.BlockTwoDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayHealthMapper;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayHealthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Slf4j
@Service
public class StatisticsErverDayHealthServiceImpl extends ServiceImpl<StatisticsErverDayHealthMapper, StatisticsErverDayHealth> implements StatisticsErverDayHealthService {

    @Override
    public Long getSumAllNum(StatisticsVo statisticsVo) {
        QueryWrapper<StatisticsErverDayHealth> statisticsErverDayHealthQueryWrapper = new QueryWrapper<>();
        statisticsErverDayHealthQueryWrapper.select("sum(num) as num");
        if (StringUtils.isNotBlank(statisticsVo.getDisCode())) {
            statisticsErverDayHealthQueryWrapper.eq("dis_code", statisticsVo.getDisCode());
        }

        if (StringUtils.isNotBlank(statisticsVo.getStartDate())) {
            statisticsErverDayHealthQueryWrapper.ge("create_time", statisticsVo.getStartDate());
        }
        if (StringUtils.isNotBlank(statisticsVo.getEndDate())) {
            statisticsErverDayHealthQueryWrapper.le("create_time", statisticsVo.getEndDate());
        }
        /**
         * DisCodes 不为空，并且 userId 为空，则查询疾病代码
         */
        if (StringUtils.isNotBlank(statisticsVo.getDisCodes()) && StringUtils.isBlank(statisticsVo.getUserId())){
            statisticsErverDayHealthQueryWrapper.in("dis_code", statisticsVo.getDisCodes().substring(0, statisticsVo.getDisCodes().length()-1));
        }
        if (StringUtils.isNotBlank(statisticsVo.getUserId()) && StringUtils.isBlank(statisticsVo.getDisCodes())){
            //不是管理员，但是未配置映射疾病，那么返回0
            return 0L;
        }
        StatisticsErverDayHealth statisticsErverDayHealth = getOne(statisticsErverDayHealthQueryWrapper, false);
        if (statisticsErverDayHealth != null) {
            return statisticsErverDayHealth.getNum().longValue();
        }
        return 0L;
    }

    @Override
    public List<BlockOneDetail> getBlockOneList(StatisticsVo statisticsVo) {
        StatisticsErverDayHealthMapper baseMapper1 = baseMapper;
        List<BlockOneDetail> diagnosisNumList = baseMapper1.getBlockOneList(statisticsVo);
        return diagnosisNumList;
    }

    @Override
    public List<BlockTwoDetail> getBlockTwoList(StatisticsVo statisticsVo) {
        StatisticsErverDayHealthMapper baseMapper1 = baseMapper;
        List<BlockTwoDetail> list = baseMapper1.getBlockTwoList(statisticsVo);
        return list;
    }

    @Override
    public List<StatisticsErverDayHealth> staticsHistoryHealth(StaticsHistoryData staticsHistoryData) {
        StatisticsErverDayHealthMapper baseMapper1 = baseMapper;
        List<StatisticsErverDayHealth> list = baseMapper1.staticsHistoryHealth(staticsHistoryData);
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public void writeReadFromRedis(List<StatisticsErverDayHealth> StatisticsErverDayDisList) {
        List<StatisticsErverDayHealth> updateList = new ArrayList<StatisticsErverDayHealth>();
        List<StatisticsErverDayHealth> insertList = new ArrayList<StatisticsErverDayHealth>();
        for (StatisticsErverDayHealth dayDis : StatisticsErverDayDisList) {
            QueryWrapper<StatisticsErverDayHealth> wrapper = new QueryWrapper<>();
            //获取今天年月日字符串
            String dateFormats = DateUtil.getDateFormats("yyyy-MM-dd", new Date());
            wrapper.eq("create_time", dateFormats);
            wrapper.eq("app_id", dayDis.getAppId());
            wrapper.eq("ins_code", dayDis.getInsCode());
            wrapper.eq("dis_code", dayDis.getDisCode());
            StatisticsErverDayHealth one = getOne(wrapper);
            if (one != null) {
               one.setNum(one.getNum() + 1);
                updateList.add(one);
            } else {
                //StatisticsErverDayHealth erverDayDis = new StatisticsErverDayHealth();
                dayDis.setNum(1);
                    boolean temp = false;
                    for (StatisticsErverDayHealth erverDayDis1 : insertList) {
                        if (erverDayDis1.getDisCode().equals(dayDis.getDisCode()) && erverDayDis1.getAppId().equals(dayDis.getAppId()) && erverDayDis1.getInsCode().equals(dayDis.getInsCode())) {
                            erverDayDis1.setNum(erverDayDis1.getNum() + 1);
                            temp = true;
                        }
                    }
                    if (!temp){
                        insertList.add(dayDis);
                    }
            }
        }
        if (!insertList.isEmpty()){
            saveOrUpdateBatch(insertList);
        }
        if (!updateList.isEmpty()){
            updateBatchById(updateList);
        }
        log.info("患者健康宣教统计结束");
    }
}
