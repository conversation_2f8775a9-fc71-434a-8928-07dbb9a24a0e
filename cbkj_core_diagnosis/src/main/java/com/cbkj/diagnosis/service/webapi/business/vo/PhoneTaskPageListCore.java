package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PhoneTaskPageListCore {
    @Schema(description="电话随访任务路径排期时间开始")
    private String startSuiFangTime;

    @Schema(description="电话随访任务路径排期结束时间")
    private String endSuiFangTime;


    @Schema(description="电话随访任务id")
    private Long taskPatientsId;

    @Schema(description="电话随访任务随访单id")
    private String diaId;

    @Schema(description="电话随访任务随访人id")
    private String doctorId;

    @Schema(description="排期的状态：1.待执行（未发送，未确定电话随访时间）2.已执行（待随访。确定电话随访时间）3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 ）8.已填写(完成电话随访)")
    private Integer phoneStatus;

    @Schema(description="中西医疾病名")
    private String diseaseName;

    @CBKJDecryptField
    @CBKJEncryptField
    @Schema(description="身份证号码")
    private String patientCardNumber;

    @CBKJDecryptField
    @CBKJEncryptField
    @Schema(description="患者名字")
    private String patientName;


    @Schema(description="随访任务id")
    private String sRoadTaskId;
}

