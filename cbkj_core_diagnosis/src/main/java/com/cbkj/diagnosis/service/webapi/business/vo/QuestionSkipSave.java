package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema
public class QuestionSkipSave {
//    @Schema(description =  "跳题id ")
//    private Integer skipId;

    @Schema(description =  "选项id")
    private Integer optionId;

    @Schema(description =  "选项名称")
    private String optionName;

    @Schema(description =  "跳过的题目id")
    private Integer skipQuestionId;
}
