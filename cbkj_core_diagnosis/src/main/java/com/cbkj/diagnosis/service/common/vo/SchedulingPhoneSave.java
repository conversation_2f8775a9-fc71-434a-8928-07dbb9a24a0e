package com.cbkj.diagnosis.service.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class SchedulingPhoneSave {
    @Schema(description =  "任务-触发时间")
    private Date taskExcuteTime;
}
