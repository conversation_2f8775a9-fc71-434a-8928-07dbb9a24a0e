package com.cbkj.diagnosis.service.sys;

import cn.hutool.core.util.IdUtil;
import com.cbkj.diagnosis.beans.business.SysDept;
import com.cbkj.diagnosis.beans.business.SysIns;
import com.cbkj.diagnosis.mapper.business.SysDeptMapper;
import com.cbkj.diagnosis.mapper.business.SysInsMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SysInsService {


    private final SysInsMapper sysInsMapper;
    private final SysDeptMapper sysDeptMapper;
    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;
    public SysInsService(SysInsMapper sysInsMapper, SysDeptMapper sysDeptMapper) {
        this.sysInsMapper = sysInsMapper;
        this.sysDeptMapper = sysDeptMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    public SysIns checkAndInsertIns(String insCode, String insName, String appId) {
        SysIns sysIns = new SysIns();
        sysIns.setStatus("0");
        sysIns.setInsCode(insCode);
        sysIns.setAppId(appId);
        sysIns.setInsName(insName);
        SysIns countByIns = sysInsMapper.getCountByIns(sysIns);
        if (countByIns == null) {
            //需要插入这个
            sysIns.setInsId(IdUtil.getSnowflake(snowflake).nextIdStr());
            sysInsMapper.insert(sysIns);
            return sysIns;
        } else {
            return countByIns;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public SysDept checkAndInsertDept(SysIns sysIns, String deptCode, String deptName) {
        SysDept sysDept = new SysDept();
        sysDept.setDeptCode(deptCode);
        sysDept.setAppId(sysDept.getAppId());
        sysDept.setInsCode(sysIns.getInsCode());
        sysDept.setStatus("0");
        SysDept a = sysDeptMapper.selectOneByObj(sysDept);
        if (a == null) {
            sysDept.setInsId(sysIns.getInsId());
            sysDept.setInsName(sysIns.getInsName());
            sysDept.setDeptId(IdUtil.getSnowflake(snowflake).nextIdStr());
            sysDept.setDeptName(deptName);
            sysDeptMapper.insert(sysDept);
            return sysDept;
        } else {
            return a;
        }
    }
}
