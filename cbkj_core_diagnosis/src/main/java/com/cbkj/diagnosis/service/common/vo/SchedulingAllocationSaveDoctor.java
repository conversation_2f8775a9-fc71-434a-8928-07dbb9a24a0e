package com.cbkj.diagnosis.service.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class SchedulingAllocationSaveDoctor {
    @Schema(description =  "医生id", required = true)
    private String doctorId;

    @Schema(description =  "医生名字")
    private String doctorName;
}
