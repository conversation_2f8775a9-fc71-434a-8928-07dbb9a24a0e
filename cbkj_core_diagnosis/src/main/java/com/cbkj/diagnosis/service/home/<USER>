package com.cbkj.diagnosis.service.home;

import com.cbkj.diagnosis.beans.home.SysHomeShowUrl;
import com.cbkj.diagnosis.mapper.home.SysHomeShowUrlMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysHomeShowUrlService {


    private final SysHomeShowUrlMapper sysHomeShowUrlMapper;


    public SysHomeShowUrlService(SysHomeShowUrlMapper sysHomeShowUrlMapper) {
        this.sysHomeShowUrlMapper = sysHomeShowUrlMapper;
    }


    public List<SysHomeShowUrl> getHomeIndexUrlList() {
        List<SysHomeShowUrl> pageListByObj = sysHomeShowUrlMapper.getPageListByObj(new SysHomeShowUrl());
        return pageListByObj;
    }
}
