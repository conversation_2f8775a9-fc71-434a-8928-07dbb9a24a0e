package com.cbkj.diagnosis.service.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.business.TRecordEvent;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis;
import com.cbkj.diagnosis.beans.statistics.vo.ListJoinDic;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.beans.statistics.BlockThreeDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayEffect;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayEffectMapper;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayEffectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Slf4j
@Service
public class StatisticsErverDayEffectServiceImpl extends ServiceImpl<StatisticsErverDayEffectMapper, StatisticsErverDayEffect> implements StatisticsErverDayEffectService {

    @Override
    public List<BlockThreeDetail> getBlockThreeList(StatisticsVo statisticsVo) {
        StatisticsErverDayEffectMapper base = baseMapper;
        List<BlockThreeDetail> list = base.getBlockThreeList(statisticsVo);
        return list;
    }
    @Transactional(rollbackFor = Exception.class)
    public void writeReadFromRedis(List<StatisticsErverDayEffect> statisticsErverDayEffectList, String recId) {
        List<StatisticsErverDayEffect> updateList = new ArrayList<StatisticsErverDayEffect>();
        List<StatisticsErverDayEffect> insertList = new ArrayList<StatisticsErverDayEffect>();
        for (StatisticsErverDayEffect dayDis : statisticsErverDayEffectList) {
            QueryWrapper<StatisticsErverDayEffect> wrapper = new QueryWrapper<>();
            //获取今天年月日字符串
            String dateFormats = DateUtil.getDateFormats("yyyy-MM-dd", new Date());
            wrapper.eq("create_time", dateFormats)
            .eq("app_id", dayDis.getAppId())
            .eq("ins_code", dayDis.getInsCode()).eq("type", dayDis.getType())
            .eq("dis_code", dayDis.getDisCode());
            StatisticsErverDayEffect one = getOne(wrapper);
            if (one != null) {
                one.setNum(one.getNum()+1);
                updateList.add(one);
            } else {
                //StatisticsErverDayEffect erverDayDis = new StatisticsErverDayEffect();
                dayDis.setNum(1);
                    boolean temp = false;
                    if (!insertList.isEmpty()){
                        for (StatisticsErverDayEffect erverDayDis1 : insertList) {
                            if (Objects.equals(erverDayDis1.getType(), dayDis.getType())
                                    && erverDayDis1.getDisCode().equals(dayDis.getDisCode())
                                    && erverDayDis1.getAppId().equals(dayDis.getAppId())
                                    && erverDayDis1.getInsCode().equals(dayDis.getInsCode())) {
                                erverDayDis1.setNum(erverDayDis1.getNum() + 1);
                                temp = true;
                            }
                        }
                    }

                    if (!temp){
                        insertList.add(dayDis);
                    }

            }
        }
        if (!insertList.isEmpty()){
            saveOrUpdateBatch(insertList);
        }
        if (!updateList.isEmpty()){
            updateBatchById(updateList);
        }
        log.info("患者疗效评价统计结束recId= {}", recId);

    }

    public List<TRecordEvent> listJoinDic(ListJoinDic recId) {
        StatisticsErverDayEffectMapper base = baseMapper;
        return base.listJoinDic(recId);
    }
}
