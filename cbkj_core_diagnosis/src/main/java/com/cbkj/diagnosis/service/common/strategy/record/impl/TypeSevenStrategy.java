package com.cbkj.diagnosis.service.common.strategy.record.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestionRange;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.beans.business.TRecordDiaImage;
import com.cbkj.diagnosis.service.business.TPreDiagnosisQuestionRangeService;
import com.cbkj.diagnosis.service.business.TRecordDiaImageService;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategy;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component("recordSeven")
public class TypeSevenStrategy implements RecordDiaStrategy {



    private final TPreDiagnosisQuestionRangeService tPreDiagnosisQuestionRangeService;

    public TypeSevenStrategy( TPreDiagnosisQuestionRangeService tPreDiagnosisQuestionRangeService) {
        this.tPreDiagnosisQuestionRangeService = tPreDiagnosisQuestionRangeService;
    }

    @Override
    public QuestionMain transRecordDia(TRecordDia tRecord) {
        QuestionMain questionMain = new QuestionMain();
        BeanUtils.copyProperties(tRecord, questionMain);
        //t_pre_diagnosis_question_range
        QueryWrapper<TPreDiagnosisQuestionRange> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("question_id", tRecord.getQuestionId());
        List<TPreDiagnosisQuestionRange> list = tPreDiagnosisQuestionRangeService.list(objectQueryWrapper);
        questionMain.setQuestionRangeList(list);
        return questionMain;
    }
}
