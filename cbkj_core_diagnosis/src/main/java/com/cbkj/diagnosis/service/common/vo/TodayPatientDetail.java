package com.cbkj.diagnosis.service.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@NoArgsConstructor
@Schema
public class TodayPatientDetail {


    @Schema(description =  "电话随访患者任务的排期扩展信息id-确定唯一排期详情信息")
    private Long sRoadTaskPatientsPhoneId;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "排期随访时间")
    private Date suiFangTime;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "分配排期给医生的时间")
    private Date allotTime;

    @Schema(description="排期的状态：1.待执行（未发送，未确定电话随访时间）2.已执行（确定电话随访时间）3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 ）8.已填写(完成电话随访)")
    private Integer phoneStatus;

    @Schema(description="电话随访任务名称")
    private String taskName;

    @Schema(description="随访问卷id")
    private String roadExecuteEventContentId;

    @Schema(description="随访问卷名称")
    private String roadExecuteEventContentName;

    private String recId;


}
