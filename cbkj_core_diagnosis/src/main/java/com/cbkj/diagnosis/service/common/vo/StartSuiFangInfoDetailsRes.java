package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class StartSuiFangInfoDetailsRes {
    private Long sRoadTaskPatientsPhoneId;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description="随访排期的日期")
    private Date suiFangTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description="随访问卷答题完成时间-和问卷答题记录表中时间一致")
    private Date suiFangFinishTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description="分配给医生的时间")
    private Date allotTime;
    private String taskName;
    private String doctorName;
    private String doctorId;
    @CBKJDecryptField
    private String patientMobile;
    private String roadExecuteEventContentId;

    @Schema(description="问卷名称")
    private String roadExecuteEventContentName;
    @Schema(description="电话随访小记")
    private String taskPatientsNode;
}
