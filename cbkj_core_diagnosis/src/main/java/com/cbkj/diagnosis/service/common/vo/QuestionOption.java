package com.cbkj.diagnosis.service.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class QuestionOption implements Serializable{


    @XmlElement(name = "OPTIONCODE")
    @Schema(description =  "选项id")
    private String optionId;

    @XmlElement(name = "OPTIONID")
    private String optionCode;
    private String optionStructureValue;

    @XmlElement(name = "OPTIONNAME")
    @Schema(description =  "选项名称")
    private String optionName;
    @XmlElement(name = "OPTIONSORT")
    @Schema(description =  "排序")
    private String optionSort;


    @Schema(description =  "症状id （症状填）")
    private String symptomId;

    @XmlElement(name = "OPTIONTYPE")
    @Schema(description =  "选项类型（1文本 2症状）")
    private String optionType;

    @XmlElement(name = "OPTIONCONTENT")
    @Schema(description =  "选项文字说明")
    private String optionContent;

    @Schema(description =  "选项图片说明")
    private String optionImage;

    @Schema(description =  "选项填空提示语")
    private String optionFillBlank;

    @Schema(description =  "选项填空填空是否必填 1.是 0否")
    private Integer optionFillCheck = 0;

    @Schema(description =  "选项互斥id")
    private List<String> optionMutexList = new ArrayList<>();
//    @Schema(description =  "选项事件代码")
//    private List<String> optionFollowEventCode = new ArrayList<>();


    @Schema(description="进行随访：返回患者回答选择的随访问卷标签信息")
    private List<TodayPatientEvent> todayPatientEventList;



}
