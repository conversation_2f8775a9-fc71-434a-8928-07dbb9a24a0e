package com.cbkj.diagnosis.service.business;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.diagnosisstructure.EmrContent;
import com.cbkj.diagnosis.beans.diagnosisstructure.EmrItem;
import com.cbkj.diagnosis.beans.diagnosisstructure.EmrList;
import com.cbkj.diagnosis.beans.diagnosisstructure.Paragraph;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.mapper.business.TRecordDiaMapper;
import com.cbkj.diagnosis.mapper.business.TRecordMapper;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisStructureChildServiceImpl;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisStructureContentServiceImpl;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisStructureServiceImpl;
import com.cbkj.diagnosis.service.webapi.business.vo.GetQuestionClassTypeInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/15 09:23
 * @Version 1.0
 */
@Service
public class DiagnosisStructureUtils {

    private final TPreDiagnosisStructureServiceImpl tPreDiagnosisStructureService;
    private final TPreDiagnosisStructureChildServiceImpl tPreDiagnosisStructureChildService;
    private final TPreDiagnosisStructureContentServiceImpl tPreDiagnosisStructureContentService;

    private final TRecordMapper tRecordMapper;

    private final TRecordDiaMapper tRecordDiaMapper;

    private final TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;
    private final TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;

    public DiagnosisStructureUtils(TPreDiagnosisStructureServiceImpl tPreDiagnosisStructureService, TPreDiagnosisStructureChildServiceImpl tPreDiagnosisStructureChildService, TPreDiagnosisStructureContentServiceImpl tPreDiagnosisStructureContentService, TRecordMapper tRecordMapper, TRecordDiaMapper tRecordDiaMapper, TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper, TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper) {
        this.tPreDiagnosisStructureService = tPreDiagnosisStructureService;
        this.tPreDiagnosisStructureChildService = tPreDiagnosisStructureChildService;
        this.tPreDiagnosisStructureContentService = tPreDiagnosisStructureContentService;
        this.tRecordMapper = tRecordMapper;
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.tPreDiagnosisOptionMapper = tPreDiagnosisOptionMapper;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
    }

    public EmrList getDiagnosisStructure(String diaId) {
        QueryWrapper<TPreDiagnosisStructure> tPreDiagnosisStructureQueryWrapper = new QueryWrapper<>();
        tPreDiagnosisStructureQueryWrapper.eq("dia_id", diaId).orderBy(true, true, "sort");
        List<TPreDiagnosisStructure> list = tPreDiagnosisStructureService.list(tPreDiagnosisStructureQueryWrapper);
        if (list.size() == 0) {
            return null;
        }
        EmrList emrLists = new EmrList();
        ArrayList<EmrItem> emrItems = new ArrayList<>();
        for (TPreDiagnosisStructure tPreDiagnosisStructure : list) {
            EmrItem emrItem = new EmrItem();
            emrItem.setDiagnosisStructureId(tPreDiagnosisStructure.getDiagnosisStructureId());
            emrItem.setEmrType(tPreDiagnosisStructure.getEmrType());
            emrItem.setTypeCode(tPreDiagnosisStructure.getTypeCode());
            emrItem.setDiaId(diaId);
            emrItems.add(emrItem);
        }
        emrLists.setEmrItems(emrItems);


        for (int i = 0; i < emrItems.size(); i++) {
            EmrItem emrItem = emrItems.get(i);
            QueryWrapper<TPreDiagnosisStructureContent> wrapper = new QueryWrapper<>();
            wrapper.eq("diagnosis_structure_id", emrItem.getDiagnosisStructureId());
            List<TPreDiagnosisStructureContent> list1 = tPreDiagnosisStructureContentService.list(wrapper);

            ArrayList<EmrContent> emrContents = new ArrayList<>();
            for (int i1 = 0; i1 < list1.size(); i1++) {
                TPreDiagnosisStructureContent tPreDiagnosisStructureContent = list1.get(i1);
                EmrContent emrContent = new EmrContent();
                emrContent.setStructureContentId(tPreDiagnosisStructureContent.getStructureContentId());
                emrContents.add(emrContent);
            }


            emrItem.setEmrContent(emrContents);

        }

        for (int i = 0; i < emrItems.size(); i++) {


            EmrItem emrItem = emrItems.get(i);
            for (EmrContent emrContent : emrItem.getEmrContent()) {
                QueryWrapper<TPreDiagnosisStructureChild> wrapper = new QueryWrapper<>();
                wrapper.eq("structure_content_id", emrContent.getStructureContentId());
                List<TPreDiagnosisStructureChild> list1 = tPreDiagnosisStructureChildService.list(wrapper);

                ArrayList<Paragraph> paragraphs = new ArrayList<>();
                for (int i1 = 0; i1 < list1.size(); i1++) {
                    TPreDiagnosisStructureChild tPreDiagnosisStructureChild = list1.get(i1);
                    Paragraph paragraph = new Paragraph();
                    paragraph.setStructureChild(tPreDiagnosisStructureChild.getStructureChild());
                    paragraph.setContent(tPreDiagnosisStructureChild.getContent());
                    paragraph.setContentType(tPreDiagnosisStructureChild.getContentType());
                    paragraph.setParagraphSort(tPreDiagnosisStructureChild.getParagraphSort());
                    paragraph.setQuestionIsDel((tPreDiagnosisStructureChild.getQuestionIsDel() == null) ? 0 : tPreDiagnosisStructureChild.getQuestionIsDel());
                    //获取问题
                    if ("questionId".equals(paragraph.getContentType())) {
                        String questionId = tPreDiagnosisStructureChild.getContent();
                        TPreDiagnosisQuestion question = tPreDiagnosisQuestionMapper.getObjectById(questionId);
                        if (question != null) {
                            paragraph.setQuestionType(question.getQuestionType());
                            paragraph.setQuestionClassType(question.getQuestionClassType());
                            paragraph.setQuestionNumber(question.getQuestionNumber() + 1);
                        }
                    }


                    paragraphs.add(paragraph);
                }


                emrContent.setParagraph(paragraphs);


            }

        }

        return emrLists;
    }

    public List<GetQuestionClassTypeInfo> getStructureContent(String recId) {
        TRecord objectById = tRecordMapper.getObjectById(recId);
        if (objectById == null) {
            return null;
        }
        ArrayList<GetQuestionClassTypeInfo> getQuestionClassTypeInfos = new ArrayList<>();


        EmrList diagnosisStructure = getDiagnosisStructure(objectById.getDiaId());
        if (diagnosisStructure != null) {
            List<EmrItem> emrItems = diagnosisStructure.getEmrItems();
            if (!emrItems.isEmpty()) {
                for (int i = 0; i < emrItems.size(); i++) {
                    StringBuffer contentStr = new StringBuffer();
                    EmrItem emrItem = emrItems.get(i);
                    GetQuestionClassTypeInfo getQuestionClassTypeInfo = new GetQuestionClassTypeInfo();
                    getQuestionClassTypeInfo.setQuestionClassName(emrItem.getEmrType());
                    getQuestionClassTypeInfo.setQuestionClassType(emrItem.getTypeCode());

                    //这是每个类型（主诉、现病史）中得每个整体内容（文本+变量+文本）
                    List<EmrContent> emrContent = emrItem.getEmrContent();
                    if (null != emrContent && !emrContent.isEmpty()) {
                        for (int i1 = 0; i1 < emrContent.size(); i1++) {
                            //这是每个整体内容中得分块内容，需要识别是文本还是问题，如果是问题在后续判断组装
                            EmrContent emrContent1 = emrContent.get(i1);
                            List<Paragraph> paragraph = emrContent1.getParagraph();
                            if (paragraph != null && !paragraph.isEmpty()) {
                                paragraph.forEach(paragraph1 -> {

                                    String contentType = paragraph1.getContentType();
                                    if ("text".equals(contentType)) {
                                        contentStr.append(paragraph1.getContent());
                                    } else if ("questionId".equals(contentType)) {
                                        //获取患者答题
                                        HashMap<String, String> map = new HashMap<>();
                                        map.put("recId", recId);
                                        map.put("questionId", paragraph1.getContent());
                                        TRecordDia recordDia = tRecordDiaMapper.getRecordDiaByRecIdAndQuestionId(map);
                                        String content = getContent(recordDia);
                                        contentStr.append(content);
                                    }

                                });
                            }
                        }
                    }
                    getQuestionClassTypeInfo.setContent(contentStr.toString());

                    getQuestionClassTypeInfos.add(getQuestionClassTypeInfo);
                }
            }

        }

        return getQuestionClassTypeInfos.isEmpty() ? null : getQuestionClassTypeInfos;
    }

    /**
     * 1、以下题目答案，在实际生成病历时，自动转换填充格式：
     * <p>
     * 时间题：月（X个月），日（X天），时（X小时）
     * <p>
     * 日期题：XXXX年XX月XX日
     * <p>
     * 血压题：收缩压值 / 舒张压值 mmHg
     * <p>
     * <p>
     * <p>
     * 2、多选题的多个答案，在实际生成病历时自动添加 “、” 分隔。
     *
     * @param recordDia
     * @return
     */
    public String getContent(TRecordDia recordDia) {
        if (recordDia == null) {
            return "";
        }
        List<String> strings = JSON.parseArray(recordDia.getContent(), String.class);
        if (Constant.BASIC_STRING_ONE.equals(recordDia.getQuestionType())) {
            //获取文本的单位。
            TPreDiagnosisQuestion question = tPreDiagnosisQuestionMapper.getObjectById(recordDia.getQuestionId() + "");
            if (StringUtils.isNotBlank(question.getQuestionUnit())) {
                //有单位
                return strings.get(0) + question.getQuestionUnit();
            }
            return StringUtils.join(strings, "、");
        } else if (Constant.BASIC_STRING_TWO.equals(recordDia.getQuestionType())) {
            List<String> strings1 = JSON.parseArray(recordDia.getOptionIds(), String.class);
            if (strings1 != null && !strings1.isEmpty()) {
                StringBuilder stringBuffer = new StringBuilder();
                TPreDiagnosisOption objectById = tPreDiagnosisOptionMapper.getObjectById(strings1.get(0));
                if (objectById != null) {
                    int tempInt = 0;
                    if (objectById.getOptionStructureSaveBlank() != null && objectById.getOptionStructureSaveBlank() == 1) {
                        if (StringUtils.isNotBlank(objectById.getOptionStructureValue())) {
                            //使用病历转化术语
                            stringBuffer.append(objectById.getOptionStructureValue());
                            tempInt=1;
                        } else {
                            //保留选项原文
                            stringBuffer.append(objectById.getOptionName());
                            tempInt=1;
                        }
                    }
                    if (objectById.getOptionStructureSavePatient() != null && objectById.getOptionStructureSavePatient() == 1) {
                        //保留患者输入
                        stringBuffer.append(StringUtils.join(strings, "、"));
                        tempInt=1;
                    }

                    if (tempInt == 0) {
                        //判断转换术语是否空
                        if (StringUtils.isNotBlank(objectById.getOptionStructureValue())){
                            stringBuffer.append(objectById.getOptionStructureValue());
                        }else {
                            stringBuffer.append(StringUtils.join(strings, "、"));
                        }
                    }
                    return stringBuffer.toString();
                }
            }

        } else if (Constant.BASIC_STRING_THREE.equals(recordDia.getQuestionType())) {
            List<String> strings1 = JSON.parseArray(recordDia.getOptionIds(), String.class);
            return getContent2(strings1, strings);
        } else if (Constant.BASIC_STRING_FOUR.equals(recordDia.getQuestionType())) {
            StringBuffer stringBuffer = new StringBuffer();
            //取出字段年月日小时
            String year = recordDia.getYear();
            String month = recordDia.getMonth();
            String day = recordDia.getDay();
            String hour = recordDia.getHour();
            String week = recordDia.getWeek();
            //判断不为空就拼接一起
            if (StringUtils.isNotBlank(year) && StringUtils.isNumeric(year) && !"0".equals(year)) {
                stringBuffer.append(Integer.parseInt(year)).append("年");
            }
            if (StringUtils.isNotBlank(month) && StringUtils.isNumeric(month) && !"0".equals(month)) {
                stringBuffer.append(Integer.parseInt(month)).append("个月");
            }
            if (StringUtils.isNotBlank(week) && StringUtils.isNumeric(week) && !"0".equals(week)) {
                stringBuffer.append(Integer.parseInt(week)).append("周");
            }
            if (StringUtils.isNotBlank(day) && StringUtils.isNumeric(day) && !"0".equals(day)) {
                stringBuffer.append(Integer.parseInt(day)).append("天");
            }
            if (StringUtils.isNotBlank(hour) && StringUtils.isNumeric(hour) && !"0".equals(hour)) {
                stringBuffer.append(Integer.parseInt(hour)).append("小时");
            }
            return stringBuffer.toString();

        } else if (Constant.BASIC_STRING_FIVE.equals(recordDia.getQuestionType())) {
            //血压
            return strings.get(0) + "/" + strings.get(1) + "mmHg";

        } else if (Constant.BASIC_STRING_SIX.equals(recordDia.getQuestionType())) {
            //日期类型 日期题：XXXX年XX月XX日
            StringBuffer stringBuffer = new StringBuffer();
            String year = recordDia.getYear();
            String month = recordDia.getMonth();
            String week = recordDia.getWeek();
            String day = recordDia.getDay();

            if (StringUtils.isNotBlank(year) && StringUtils.isNumeric(year) && !"0".equals(year)) {
                stringBuffer.append(Integer.parseInt(year)).append("年");
            }
            if (StringUtils.isNotBlank(month) && StringUtils.isNumeric(month) && !"0".equals(month)) {
                stringBuffer.append(Integer.parseInt(month)).append("月");
            }
            if (StringUtils.isNotBlank(week) && StringUtils.isNumeric(week) && !"0".equals(week)) {
                stringBuffer.append(Integer.parseInt(week)).append("周");
            }
            if (StringUtils.isNotBlank(day) && StringUtils.isNumeric(day) && !"0".equals(day)) {
                stringBuffer.append(Integer.parseInt(day)).append("日");
            }
            return stringBuffer.toString();
        }
        return "";
    }

    /**
     * @param strings1 选择选项得id
     * @param strings  选择选项得内容
     * @return
     */
    public String getContent2(List<String> strings1, List<String> strings) {
        StringBuilder stringBuffer = new StringBuilder();
        if (strings1 != null && !strings1.isEmpty()) {
            for (int i = 0; i < strings1.size(); i++) {
                TPreDiagnosisOption objectById = tPreDiagnosisOptionMapper.getObjectById(strings1.get(i));
                int tempInt = 0;
                if (objectById != null) {
                    if (objectById.getOptionStructureSaveBlank() != null && objectById.getOptionStructureSaveBlank() == 1) {
                        if (StringUtils.isNotBlank(objectById.getOptionStructureValue())) {
                            //使用病历转化术语
                            stringBuffer.append(objectById.getOptionStructureValue());
                            tempInt= 1;
                        } else {
                            //保留选项原文
                            stringBuffer.append(objectById.getOptionName());
                            tempInt= 1;
                        }
                    }
                    if (objectById.getOptionStructureSavePatient() != null && objectById.getOptionStructureSavePatient() == 1) {
                        //保留患者输入
                        if (i < strings.size()) {
                            stringBuffer.append(strings.get(i)).append("、");
                            tempInt= 1;
                        }
                    }

                    if (tempInt == 0) {
                            //判断转换术语是否空
                            if (StringUtils.isNotBlank(objectById.getOptionStructureValue())){
                                stringBuffer.append(objectById.getOptionStructureValue());
                            }else {
                                stringBuffer.append(strings.get(i)).append("、");
                            }
                    }

                }
            }
        }
        //判断stringBuffer中是否空，不为空判断末尾符号是否是、是的话就是截断
        if (stringBuffer.length() > 1) {
            //判断stringBuffer末尾符号是否是、符号结尾
            if ("、".equals(stringBuffer.substring(stringBuffer.length() - 1))) {
                return stringBuffer.substring(0, stringBuffer.length() - 1);
            }

        }
        return stringBuffer.toString();

    }
}
