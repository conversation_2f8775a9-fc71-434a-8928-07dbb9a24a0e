package com.cbkj.diagnosis.service.mobileapi.business;


import com.cbkj.diagnosis.beans.business.TSymptom;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.TSymptomMapper;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MobileTSymptomService {


    private TSymptomMapper tSymptomMapper;

    MobileTSymptomService(TSymptomMapper tSymptomMapper){
        this.tSymptomMapper = tSymptomMapper;
    }

    public Object getSymptomList(Page page, TSymptom tSymptom){
        PageHelper.startPage(page.getPage(), page.getLimit());

        List<TSymptom> pageListByObj = tSymptomMapper.getPageListByObj(tSymptom);

        PageHelper.clearPage();


        return Page.getLayUiTablePageData(pageListByObj);
    }
}
