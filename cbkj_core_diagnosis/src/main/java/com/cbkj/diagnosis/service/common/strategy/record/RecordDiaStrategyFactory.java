package com.cbkj.diagnosis.service.common.strategy.record;

import com.cbkj.diagnosis.common.utils.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Component
public class RecordDiaStrategyFactory {


    Map<String, RecordDiaStrategy> bases;

    @Autowired
    public RecordDiaStrategyFactory(Map<String, RecordDiaStrategy> bases) {
        Assert.notNull(bases, "RecordDiaStrategy must not be null!");
        this.bases = bases;
    }

    public RecordDiaStrategy getNormalParamStrategy(String type) {
        switch (type) {
            case Constant.BASIC_STRING_ONE:
            case Constant.BASIC_STRING_FIVE:
                return bases.get("recordOne");
            case Constant.BASIC_STRING_TWO:
            case Constant.BASIC_STRING_THREE:
                return bases.get("recordSelect");
            case Constant.BASIC_STRING_FOUR:
            case Constant.BASIC_STRING_SIX:
                return bases.get("recordFour");
            case Constant.BASIC_STRING_EIGHT:
            case Constant.BASIC_STRING_NINE:
            case Constant.BASIC_STRING_TEN:
            case Constant.BASIC_STRING_ELEVEN:
                return bases.get("imageStrategy");
            default:
                //如果不存在返回一个默认。
                return bases.get("otherNormalStrategy");
        }

    }
}
