package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone;
import com.cbkj.diagnosis.beans.business.requestvo.SRoadTaskPatientsPhoneRe;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class SchedulingSave {
    @Schema(description =  "患者任务的id", required = true)
    private Long taskPatientsId;

    @Schema(description =  "就诊记录id")
    private String recordsId;


    private List<SRoadTaskPatientsPhoneRe> schedulingPhoneSaveList;

}
