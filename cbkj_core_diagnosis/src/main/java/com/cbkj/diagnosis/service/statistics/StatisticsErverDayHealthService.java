package com.cbkj.diagnosis.service.statistics;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cbkj.diagnosis.beans.statistics.BlockOneDetail;
import com.cbkj.diagnosis.beans.statistics.BlockTwoDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth;
import com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
public interface StatisticsErverDayHealthService extends IService<StatisticsErverDayHealth> {

    Long getSumAllNum(StatisticsVo statisticsVo);

    List<BlockOneDetail> getBlockOneList(StatisticsVo statisticsVo);

    List<BlockTwoDetail> getBlockTwoList(StatisticsVo statisticsVo);

    List<StatisticsErverDayHealth> staticsHistoryHealth(StaticsHistoryData staticsHistoryData);
}
