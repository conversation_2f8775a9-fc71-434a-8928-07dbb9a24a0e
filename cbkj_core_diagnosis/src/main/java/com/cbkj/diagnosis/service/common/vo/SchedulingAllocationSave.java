package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.beans.business.requestvo.SchedulingAllocationSavePatient;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class SchedulingAllocationSave {
    @Schema(description =  "患者任务相关信息",required = true)
    private List<SchedulingAllocationSavePatient> records;


    @Schema(description =  "医生ids",required = true)
    private List<SchedulingAllocationSaveDoctor> doctors;
}
