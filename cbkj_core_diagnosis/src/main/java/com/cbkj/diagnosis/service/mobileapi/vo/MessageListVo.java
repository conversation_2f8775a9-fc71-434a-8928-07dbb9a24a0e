package com.cbkj.diagnosis.service.mobileapi.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@Schema
@NoArgsConstructor
@AllArgsConstructor
public class MessageListVo implements Serializable {

    @Schema(description =  "执行内容所在主键id")
    private String roadExecuteEventContentId;

    @Schema(description="患者任务id")
    private Long taskPatientsId;

    @Schema(description =  "执行内容名称")
    private String roadExecuteEventContentName;
    private String title;
    private String titleAbstract;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "任务-触发时间")
    private Date taskExcuteTime;


    @Schema(description =  "机构名称")
    private String insName;

    @Schema(description =  "随访名称")
    private String formName;

    @Schema(description =  "问诊单类型（1 专病预诊单 2、全科）如果是随访 3.随访问卷4.自测量表")
    private String diaType;
    private String eduAbstract;
    @Schema(description="1.待执行（未发送）2.已执行（已发送,未读（微信短信））3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 7.已读（微信短信）8.已填写")
    private Integer taskExcuteStatus;
}
