package com.cbkj.diagnosis.service.mobileapi.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Schema
@NoArgsConstructor
@AllArgsConstructor
public class FurtherConsultationRecordListVo {

    @Schema(description="记录id")
    private String recId;
    @Schema(description="随访名称")
    private String formName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    private String insName;
//    private Integer taskExcuteStatus;
}
