package com.cbkj.diagnosis.service.webapi.business;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisSkip;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisSkipMapper;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
@Service
public class WebTPreDiagnosisSkipService {

    @Autowired
    private TPreDiagnosisSkipMapper tPreDiagnosisSkipMapper;

    /**
     * 加载分页数据
     *
     * @param tPreDiagnosisSkip 
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2023-09-05
     */
    public Object getPageDatas(TPreDiagnosisSkip tPreDiagnosisSkip, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TPreDiagnosisSkip> list = tPreDiagnosisSkipMapper.getPageListByObj(tPreDiagnosisSkip);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param skipId 
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    public ResEntity findObj(String skipId) {

        if (StringUtils.isBlank(skipId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TPreDiagnosisSkip tPreDiagnosisSkip = tPreDiagnosisSkipMapper.getObjectById(skipId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tPreDiagnosisSkip);
    }


    /**
     * 插入新数据
     *
     * @param tPreDiagnosisSkip 
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TPreDiagnosisSkip tPreDiagnosisSkip){

        //tPreDiagnosisSkip.setSkipId(IDUtil.getID());
        long rows = tPreDiagnosisSkipMapper.insert(tPreDiagnosisSkip);

        return ResEntity.success(tPreDiagnosisSkip);
    }


    /**
     * 修改
     *
     * @param tPreDiagnosisSkip 
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TPreDiagnosisSkip tPreDiagnosisSkip) {

        long rows = tPreDiagnosisSkipMapper.updateByPrimaryKey(tPreDiagnosisSkip);

        return ResEntity.success(tPreDiagnosisSkip);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tPreDiagnosisSkipMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
