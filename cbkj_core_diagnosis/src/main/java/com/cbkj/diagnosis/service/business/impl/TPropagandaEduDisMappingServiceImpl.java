package com.cbkj.diagnosis.service.business.impl;

import com.cbkj.diagnosis.beans.business.TPropagandaEduDisMapping;
import com.cbkj.diagnosis.mapper.business.TPropagandaEduDisMappingMapper;
import com.cbkj.diagnosis.service.business.TPropagandaEduDisMappingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 宣教-疾病映射 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
public class TPropagandaEduDisMappingServiceImpl extends ServiceImpl<TPropagandaEduDisMappingMapper, TPropagandaEduDisMapping> implements TPropagandaEduDisMappingService {

    public List<TPropagandaEduDisMapping> listByList(ArrayList<String> staticsEduEduId) {
        return this.baseMapper.listByList(staticsEduEduId);
    }
}
