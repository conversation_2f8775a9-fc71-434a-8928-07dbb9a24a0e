package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Schema
@Data
@NoArgsConstructor
public class PreListReVoDAO implements  Serializable {
    @CBKJEncryptField
    @Schema(description =  "身份证/患者姓名")
    private String key = "";

    @Schema(description =  "查看历史记录戴上这个患者id字段")
    private String patientId;
    private String diaId;

    @Schema(description =  "时间开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;
    @Schema(description =  "时间结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    private String disId;
    private String userId;
    private boolean diaTypeBool;
}
