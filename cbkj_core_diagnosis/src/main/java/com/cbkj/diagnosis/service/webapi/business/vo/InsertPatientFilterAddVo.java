package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.task.SRoadTask;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class InsertPatientFilterAddVo {
    @Schema(description="需要导入的患者-患者姓名、身份证、患者id、患者年龄、患者性别")
    private List<TAdminInfo> tAdminInfoList;

    @Schema(description="随访任务-传随访任务id、任务名称")
    private SRoadTask sRoadTask;
}
