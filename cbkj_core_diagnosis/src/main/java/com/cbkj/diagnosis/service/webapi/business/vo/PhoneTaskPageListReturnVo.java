package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PhoneTaskPageListReturnVo {
    @Schema(description="电话随访排期的id")
    private Long sRoadTaskPatientsPhoneId;
    @CBKJDecryptField
    @CBKJEncryptField
    @Schema(description="身份证号码")
    private String patientCardNumber;

    @CBKJDecryptField
    @CBKJEncryptField
    private String patientName;
    private String patientSex;
    private String age;
    private String taskName;

    @Schema(description="问卷名称")
    private String roadExecuteEventContentName;


    @Schema(description="问卷id")
    private String roadExecuteEventContentId;


    @Schema(description="随访排期时间")
    private Date suiFangTime;

    @Schema(description="随访问卷完成时间")
    private Date suiFangFinishTime;

    @Schema(description="电话随访排期的状态：排期的状态：1.待执行（未发送，未确定电话随访时间）2.已执行（待随访。确定电话随访时间）3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 ）8.已填写(完成电话随访)")
    private Integer phoneStatus;


    private String doctorId;
    private String doctorName;
    private String patientId;

    @Schema(description="随访电话任务id")
    private String taskPatientsId;

    @Schema(description="就诊记录id")
    private String recordsId;

    @Schema(description="患者电话随访回答的记录id")
    private String recId;
}
