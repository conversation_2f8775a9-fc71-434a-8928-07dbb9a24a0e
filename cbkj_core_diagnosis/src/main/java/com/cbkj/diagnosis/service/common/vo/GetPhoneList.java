package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Schema
@NoArgsConstructor
@Data
public class GetPhoneList {
    @Schema(description =  "患者任务")
    private Long taskPatientsId;

    @Schema(description =  "患者id")
    private String patientId;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者名字")
    private String patientName;

    @Schema(description =  "患者年龄")
    private String patientAge;

    @Schema(description =  "患者性别")
    private String patientSex;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者身份证")
    private String patientCardNumber;

    @Schema(description =  "随访人：医生id")
    private String doctorId;

    @Schema(description =  "随访人：医生名字")
    private String doctorName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "任务执行的时间")
    private Date taskExcuteTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "随访问卷答题完成时间")
    private Date suiFangFinishTime;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "分配给医生的时间")
    private Date allotTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "排期的：随访时间")
    private Date suiFangTime;

    @Schema(description="随访总次数")
    private Integer suiFangTotalNums;

    @Schema(description="随访剩余次数")
    private Integer suiFangOtherNums;

    @Schema(description="电话随访小记备注")
    private String taskPatientsNode;

    @Schema(description="电话随访任务名称")
    private String taskName;
    private String lastChineseDisName;
    private String lastWestDisName;
    @Schema(description="患者就诊记录id")
    private String recordsId;
}
