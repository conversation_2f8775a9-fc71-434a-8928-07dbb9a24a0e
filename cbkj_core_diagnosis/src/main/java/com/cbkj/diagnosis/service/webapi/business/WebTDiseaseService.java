package com.cbkj.diagnosis.service.webapi.business;

import com.cbkj.diagnosis.beans.business.AdminDisList;
import com.cbkj.diagnosis.beans.business.TDisease;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.SysAdminInfoDisMappingMapper;
import com.cbkj.diagnosis.mapper.business.TDiseaseMapper;
import com.cbkj.diagnosis.service.common.strategy.record.impl.TDiseaseRes;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo2;
import com.cbkj.diagnosis.service.common.vo.MobileDisease;
import com.cbkj.diagnosis.service.webapi.business.vo.MySelfDisList;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WebTDiseaseService {

    private final TDiseaseMapper tDiseaseMapper;
    private final SysAdminInfoDisMappingMapper sysAdminInfoDisMappingMapper;

    @Autowired
    WebTDiseaseService(TDiseaseMapper tDiseaseMapper, SysAdminInfoDisMappingMapper sysAdminInfoDisMappingMapper) {
        this.tDiseaseMapper = tDiseaseMapper;
        this.sysAdminInfoDisMappingMapper = sysAdminInfoDisMappingMapper;
    }

    @Cacheable(value = "web::disease", keyGenerator = "cacheKeyGenerator")
    public List<TDisease> getDiseaseList(DiseaseVo diseaseVo) {

        List<TDisease> listByVo = tDiseaseMapper.getListByVo(diseaseVo);
        return listByVo;
    }

    public MobileDisease getMobileDisease(String nameWord) {
        TDisease tDisease = new TDisease();
        tDisease.setDisType("1");
        tDisease.setDisName(nameWord);
        List<TDiseaseRes> a = tDiseaseMapper.getListByTDisease(tDisease);
        tDisease.setDisType("2");
        List<TDiseaseRes> b = tDiseaseMapper.getListByTDisease(tDisease);

        MobileDisease mobileDisease = new MobileDisease();
        mobileDisease.setChineseDisease(a);
        mobileDisease.setWestDisease(b);

        return mobileDisease;
    }

    public Object getDiseaseList2(DiseaseVo2 diseaseVo, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDisease> listByVo = tDiseaseMapper.getListByVo2(diseaseVo);
        PageHelper.clearPage();
        return Page.getLayUiTablePageData(listByVo);
    }

    public Object getAdminDisList(String disName) {
        if (StringUtils.isBlank(disName)) {
            disName = null;
        }
        return tDiseaseMapper.getAdminDisList(disName);
    }

    public Object getMySelfDisList(String disName, String userId) {
        if (StringUtils.isBlank(userId)){
//            return tDiseaseMapper.getAdminDisList(disName);
        return sysAdminInfoDisMappingMapper.getDisFromGroupBy(disName);
        }
        if (StringUtils.isBlank(disName)) {
            disName = null;
        }
        MySelfDisList mySelfDisList1 = new MySelfDisList();
        mySelfDisList1.setDisName(disName);
        mySelfDisList1.setUserId(userId);
        return tDiseaseMapper.getMySelfDisList(mySelfDisList1);

    }

    /**
     * 获取监测的疾病
     * @return
     */
    public List<TDisease> getDiseaseDiagnosisMappingList() {
        return tDiseaseMapper.getDiseaseDiagnosisMappingList();
    }
}
