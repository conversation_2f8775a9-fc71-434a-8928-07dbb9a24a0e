package com.cbkj.diagnosis.service.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema
public class QuestionMobile implements Serializable {
    @Schema(description =  "疾病id")
    private String disId;

    @Schema(description =  "问诊单类型（1 专病预诊单 2、全科）如果是随访 3.随访问卷4.自测量表")
    private String diaType;

    @Schema(description =  "预诊、随访 单id")
    private String diaId;

    @Schema(description =  "题目id 后端用",hidden = true)
    private String questionIds;


}
