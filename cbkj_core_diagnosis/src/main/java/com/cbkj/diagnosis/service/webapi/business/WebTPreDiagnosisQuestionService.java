package com.cbkj.diagnosis.service.webapi.business;

import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategyFactory;
import com.cbkj.diagnosis.service.common.vo.NextQVo;
import com.cbkj.diagnosis.service.common.vo.QuestionListVo;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.common.vo.QuestionMobile;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Log4j2
public class WebTPreDiagnosisQuestionService {

    private TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private RecordDiaStrategyFactory recordDiaStrategyFactory;


    WebTPreDiagnosisQuestionService(TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper, RecordDiaStrategyFactory recordDiaStrategyFactory) {
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.recordDiaStrategyFactory = recordDiaStrategyFactory;
    }



//    public QuestionMain getQuestions(QuestionListVo questionListVo) {
//
//        QuestionMobile questionMobile = questionListVo.getQuestionMobile();
//        List<QuestionMain> questionMobilesList = questionListVo.getQuestionMobilesList();
//
//        ArrayList<Integer> questionIds = new ArrayList<>();
//        if (questionMobilesList != null){
//            for (int i = 0; i < questionMobilesList.size(); i++) {
//                QuestionMain questionMain = questionMobilesList.get(i);
//                questionIds.add(questionMain.getQuestionId());
//            }
//        }
//
//        if (  questionMobilesList == null ||
//
//                questionMobilesList.size() == 0 ||
//                (
//                questionMobilesList.size() == 1 &&
//                        (questionMobilesList.get(0).getQuestionId() == null || questionMobilesList.get(0).getQuestionId() == 0))
//        ) {
//
//            List<TRecordDia> a;
//            if (questionMobile.getDiaType().equals(Constant.BASIC_STRING_TWO)){
//                //全科
//                a = tPreDiagnosisQuestionMapper.getFirstQByQuan(questionMobile);
//            }else {
//                //专科
//                a = tPreDiagnosisQuestionMapper.getFirstQ(questionMobile);
//            }
//
//            for (int i = 0; i < a.size(); i++) {
//                TRecordDia tRecordDia = a.get(i);
//                QuestionMain questionMain = recordDiaStrategyFactory.getNormalParamStrategy(tRecordDia.getQuestionType()).transRecordDia(tRecordDia);
//                return questionMain;
//            }
//
//        } else {
//            if (questionIds.size() == 0){
//                return new QuestionMain();
//            }
//            String diaId = questionMobile.getDiaId();
//            if (StringUtils.isBlank(diaId)){
//                log.error("diaId不能为空");
//                return new QuestionMain();
//            }
//            NextQVo nextQVo = new NextQVo();
//            nextQVo.setQuestionIds(questionIds);
//            nextQVo.setDiaId(questionMobile.getDiaId());
//            List<TRecordDia> b = tPreDiagnosisQuestionMapper.getNextQ(nextQVo);
//            if (b.size()== 0){
//                QuestionMain questionMain = new QuestionMain();
//                questionMain.setNoNextItem(true);
//                return questionMain;
//            }
//            for (int i = 0; i < b.size(); i++) {
//                TRecordDia tRecordDia = b.get(i);
//                QuestionMain questionMain = recordDiaStrategyFactory.getNormalParamStrategy(tRecordDia.getQuestionType()).transRecordDia(tRecordDia);
//                return questionMain;
//            }
//        }
//        return new QuestionMain();
//
//    }



}
