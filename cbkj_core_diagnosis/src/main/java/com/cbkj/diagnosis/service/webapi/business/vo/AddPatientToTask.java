package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class AddPatientToTask {

    @Schema(description =  "患者id",required = true)
    private String patientId;
    @Schema(description =  "随访任务id",required = true)
    private String sRoadTaskId;
}
