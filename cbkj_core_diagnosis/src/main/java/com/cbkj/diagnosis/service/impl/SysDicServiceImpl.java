package com.cbkj.diagnosis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.SysDic;
import com.cbkj.diagnosis.mapper.SysDicMapper;
import com.cbkj.diagnosis.service.SysDicService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
public class SysDicServiceImpl extends ServiceImpl<SysDicMapper, SysDic> implements SysDicService {

    @Override
    public List<SysDic> getSuiFangType() {
        QueryWrapper<SysDic> sysDicQueryWrapper = new QueryWrapper<>();
        sysDicQueryWrapper.eq("parent_id","suifang-type").eq("status",1).orderBy(true,true,"dic_sort");
        SysDicMapper sysDicMapper = (SysDicMapper) baseMapper;
        return sysDicMapper.selectList(sysDicQueryWrapper);
    }

    @Override
    public List<SysDic> getOptionEventCode() {
        QueryWrapper<SysDic> sysDicQueryWrapper = new QueryWrapper<>();
        sysDicQueryWrapper.eq("parent_id","follow-up-event").eq("status",1).orderBy(true,true,"dic_sort");
        SysDicMapper sysDicMapper = (SysDicMapper) baseMapper;
        return sysDicMapper.selectList(sysDicQueryWrapper);
    }

    public ArrayList<HashMap<String, String>> getDetailsByDicCodeList(List<String> optionFollowEventCode) {
        ArrayList<HashMap<String, String>> result = ((SysDicMapper)baseMapper).getDetailsByDicCodeList(optionFollowEventCode);
        return result;
    }
}
