package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class GetPatientListVo implements Serializable {

    @CBKJEncryptField
    @Schema(description="患者身份证id")
    private String patientCardNumber;

    @CBKJEncryptField
    private String patientName;
    private String diseaseName;

    @Schema(description="任务id-传这个值，会关联患者任务执行表，查有这个任务的患者")
    private String sRoadTaskId;

//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startDate;
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endDate;
    private String  doctorName;
    private String  depName;
    private String  joinRoadTask;

    @Schema(description =  "是否有预诊：-1全部 0否 1是")
    private Integer hasDiagnosis;

}
