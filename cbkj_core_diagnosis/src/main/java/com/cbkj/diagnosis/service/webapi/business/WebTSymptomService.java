package com.cbkj.diagnosis.service.webapi.business;


import cn.hutool.core.util.IdUtil;
import com.cbkj.diagnosis.beans.business.TSymptom;
import com.cbkj.diagnosis.beans.business.TSymptomClass;
import com.cbkj.diagnosis.mapper.business.TSymptomMapper;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.service.webapi.business.vo.GetSymptomPageListRe;
import com.cbkj.diagnosis.service.webapi.business.vo.WebTSymptomReVo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class WebTSymptomService {
    @Value(value = "${idutils.Snowflake}")
    private Long snowflake;

    private TSymptomMapper tSymptomMapper;

    WebTSymptomService(TSymptomMapper tSymptomMapper) {
        this.tSymptomMapper = tSymptomMapper;
    }

    public Object getSymptomList(WebTSymptomReVo webTSymptomReVo, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());

        List<TSymptom> pageListByObj = tSymptomMapper.getPageListByObjBySelf(webTSymptomReVo);

        PageHelper.clearPage();


        return Page.getLayUiTablePageData(pageListByObj);
    }

    public Object getSymptomPageList(GetSymptomPageListRe getSymptomPageListRe, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());

        List<TSymptom> pageListByObj = tSymptomMapper.getSymptomPageList(getSymptomPageListRe);

        PageHelper.clearPage();


        return Page.getLayUiTablePageData(pageListByObj);
    }

    /**
     * 症状类别树状
     */
    public List<TSymptomClass> getSymptomClassPageList() {
        //查顶级
        List<TSymptomClass> symptomClassPageList = tSymptomMapper.getSymptomClassPageList(1);
        HashMap<Integer, List<TSymptomClass>> map = new HashMap<>();
        //查子一层
        List<TSymptomClass> symptomClass = tSymptomMapper.getSymptomClassPageList(2);
        for (TSymptomClass aClass : symptomClass) {
            if (!map.containsKey(aClass.getParentClassId())) {
                map.put(aClass.getParentClassId(), new ArrayList<TSymptomClass>());
                List<TSymptomClass> tSymptomClasses = map.get(aClass.getParentClassId());
                tSymptomClasses.add(aClass);
                map.put(aClass.getParentClassId(), tSymptomClasses);
            } else {
                List<TSymptomClass> tSymptomClasses = map.get(aClass.getParentClassId());
                tSymptomClasses.add(aClass);
                map.put(aClass.getParentClassId(), tSymptomClasses);
            }
        }
        for (int i = 0; i < symptomClassPageList.size(); i++) {
            TSymptomClass aClass = symptomClassPageList.get(i);
            if (map.containsKey(aClass.getSymptomClassId())) {
                if (aClass.getChildren() == null) {
                    aClass.setChildren(map.get(aClass.getSymptomClassId()));
                } else {
                    aClass.getChildren().addAll(map.get(aClass.getSymptomClassId()));
                }

            }
        }

        return symptomClassPageList;
    }

    @Transactional(rollbackFor = Exception.class)
    public TSymptom saveSymptom(TSymptom tSymptom) {
        tSymptom.setStatus("0");
        tSymptom.setCreateDate(new Date());
        tSymptom.setSymptomId(IdUtil.getSnowflake(snowflake).nextIdStr());
        tSymptomMapper.insert(tSymptom);
        return tSymptom;
    }

    @Transactional(rollbackFor = Exception.class)
    public void editSymptom(TSymptom tSymptom) {
        tSymptomMapper.updateByPrimaryKey(tSymptom);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteSymptom(String symptomId, String nameZh, String userId) {
        TSymptom objectById = tSymptomMapper.getObjectById(symptomId);
        if (objectById != null) {
            objectById.setStatus("1");
            objectById.setDelDate(new Date());
            objectById.setDelUsername(nameZh);
            objectById.setDelUser(userId);
            tSymptomMapper.updateByPrimaryKey(objectById);
        }
    }
}
