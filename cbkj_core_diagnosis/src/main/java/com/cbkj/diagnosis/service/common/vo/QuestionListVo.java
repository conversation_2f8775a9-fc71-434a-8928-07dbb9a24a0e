package com.cbkj.diagnosis.service.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class QuestionListVo {

    private List<QuestionMain> questionMobilesList;

    @Schema
    private QuestionMobile questionMobile;
    @Schema(description =  "1.预诊2随访")
    private String formType;

    @Schema(description =  "任务id")
    private String taskPatientsId;

    @Schema(description =  "电话随访排期id")
    private Long sRoadTaskPatientsPhoneId;

    @Schema(description =  "电话随访排期状态：2待随访4已访未完成5空号6.错号8.完成排期随访")
    private Integer taskExcuteStatus;

    @Schema(description =  "3.电话（自动+手动）、4.面访（手动）、5.其他（手动）",required = true)
    private String  roadExecuteEventWay;


    @Schema(description =  "患者id。web 面访 需要传这个字段，用来确定保存的是哪个患者")
    private String patientId;

    @Schema(description="电话随访小记")
    private String taskPatientsNode;


}
