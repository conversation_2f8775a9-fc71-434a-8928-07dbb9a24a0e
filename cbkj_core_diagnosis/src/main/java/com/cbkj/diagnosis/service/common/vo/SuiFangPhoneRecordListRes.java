package com.cbkj.diagnosis.service.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description =  "获取电话随访列表")
public class SuiFangPhoneRecordListRes {

    @Schema(description =  "电话随访患者任务映射表id，患者排期，如果更新需要带上这个字段值")
    private Long sRoadTaskPatientsPhoneId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date suiFangTime;



    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date allotTime;


    @Schema(description="随访人id")
    private String doctorId;

    @Schema(description="随访人名字")
    private String doctorName;

    @Schema(description="随访任务名称")
    private String taskName;
    private String recId;

    @Schema(description="排期的状态：1.待执行（未发送，未确定电话随访时间）2.已执行（待随访。确定电话随访时间）3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 ）8.已填写(完成电话随访)")
    private Integer phoneStatus;

}
