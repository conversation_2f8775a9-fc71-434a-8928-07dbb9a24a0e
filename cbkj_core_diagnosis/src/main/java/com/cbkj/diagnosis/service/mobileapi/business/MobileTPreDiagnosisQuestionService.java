package com.cbkj.diagnosis.service.mobileapi.business;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisForm;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisChildMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategyFactory;
import com.cbkj.diagnosis.service.common.vo.NextQVo;
import com.cbkj.diagnosis.service.common.vo.QuestionListVo;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.common.vo.QuestionMobile;
import com.cbkj.diagnosis.service.mobileapi.vo.CountComputer;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Log4j2
public class MobileTPreDiagnosisQuestionService {

    private final TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private final RecordDiaStrategyFactory recordDiaStrategyFactory;

    private final TPreDiagnosisFormMapper tPreDiagnosisFormMapper;


    MobileTPreDiagnosisQuestionService(TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper, RecordDiaStrategyFactory recordDiaStrategyFactory, TPreDiagnosisFormMapper tPreDiagnosisFormMapper) {
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.recordDiaStrategyFactory = recordDiaStrategyFactory;
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
    }

    public String computer(QuestionMain questionMain1, List<QuestionMain> questionMobilesList) {
        if (questionMobilesList == null || questionMobilesList.size() == 0) {
            return "100";
        }

        //计算 当前传过来的最后一题的id和倒数第二题的id的差值，如果最后一题的id比倒数第二题的id小，说明有跳题，需要把前面跳题的数值加上。
        int i = 0;
        if (questionMobilesList.size() >= 2) {
            int size = questionMobilesList.size();
            Integer questionId_1 = questionMobilesList.get(size - 2).getQuestionId();
            Integer questionId_2 = questionMobilesList.get(size - 1).getQuestionId();
            int subNum = questionId_1 - questionId_2;
            if (subNum > 0) {
                i = subNum + 1;
            }
        }
        Integer questionId = questionMobilesList.get(questionMobilesList.size() - 1).getQuestionId();
        //       String masterQuestionId = questionMain1.getMasterQuestionId();
        String diaId = questionMain1.getDiaId();
//        CountComputer countComputer = new CountComputer();
//        countComputer.setQuestionId(questionId);
//        countComputer.setMasterQuestionId(masterQuestionId);
//        countComputer.setDiaId(diaId);
        //  int a = 0;
//        if (StringUtils.isNotBlank(masterQuestionId)){
//             a = tPreDiagnosisFormMapper.getCountComputer(countComputer);
//        }

        TPreDiagnosisForm objectById = tPreDiagnosisFormMapper.getObjectById(diaId);

        Integer longestQuestionNum = objectById.getLongestQuestionNum();
        TPreDiagnosisQuestion objectById1 = tPreDiagnosisQuestionMapper.getObjectById(questionId + "");
        Integer questionNumber = objectById1.getQuestionNumber();
        if (i == 0) {
            i = 1;
        }
        //  int i = questionNumber + a;
        //总共做完的题目
        int t = questionNumber + i;

//        BigDecimal alreadyQ = new BigDecimal(i + "");
        //做完的题目转成bd
        BigDecimal alreadyQ = new BigDecimal(t + "");
        BigDecimal totalNUm = new BigDecimal(longestQuestionNum + "");
        BigDecimal PERCENTAGE_DIVISOR = new BigDecimal("100");


        // 检查除数是否为0
        if (totalNUm.equals(BigDecimal.ZERO)) {
            return "";
        }
        //减掉，获取没做的题。
        BigDecimal subtract = totalNUm.subtract(alreadyQ);
        // 优化后的计算逻辑，考虑了除法的精度控制
        BigDecimal percentage = subtract.divide(totalNUm, 2, RoundingMode.DOWN)
                .multiply(PERCENTAGE_DIVISOR);
        log.info("=======================================总数=" + totalNUm.toString() + "##已经做了=" + alreadyQ.toString() + "##差值=" + subtract.toString());

        return percentage.toString();

    }

    /**
     * 手机端获取下一题
     *
     * @param questionListVo
     * @return
     */
    public ResEntity getQuestions(QuestionListVo questionListVo) {
        QuestionMobile questionMobile = questionListVo.getQuestionMobile();
        //查一下表单
        TPreDiagnosisForm objectById = tPreDiagnosisFormMapper.getObjectById(questionMobile.getDiaId());
        if (null == objectById){
            return ResEntity.error("表单不存在");
        }

        questionMobile.setDiaType(objectById.getDiaType());
        questionListVo.setFormType(objectById.getFormType());
        List<QuestionMain> questionMobilesList = questionListVo.getQuestionMobilesList();
        //计算剩余题目百分比
        String computer = "100";
        ArrayList<Integer> questionIds = new ArrayList<>();
        ArrayList<Long> optionsIds = new ArrayList<>();
        if (questionMobilesList != null && !questionMobilesList.isEmpty()) {
            QuestionMain questionMain1 = questionMobilesList.get(questionMobilesList.size() - 1);
            questionMain1.setDiaId(questionMobile.getDiaId());
            //computer = computer(questionMain1);

            for (int i = 0; i < questionMobilesList.size(); i++) {
                QuestionMain questionMain = questionMobilesList.get(i);
                questionIds.add(questionMain.getQuestionId());
                String[] answerContentId = questionMain.getAnswerContentId();
                if (answerContentId != null && answerContentId.length > 0) {
                    for (int i1 = 0; i1 < answerContentId.length; i1++) {
                        optionsIds.add(Long.parseLong(answerContentId[i1]));
                    }
                }
            }
        }

        if (questionMobilesList == null ||

                questionMobilesList.isEmpty() ||
                (questionMobilesList.size() == 1 && (questionMobilesList.get(0).getQuestionId() == null || questionMobilesList.get(0).getQuestionId() == 0))) {

            List<TRecordDia> a = new ArrayList<>();
        //    if (questionMobile.getDiaType().equals(Constant.BASIC_STRING_TWO)) {
                //全科
            //    a = tPreDiagnosisQuestionMapper.getFirstQByQuan(questionMobile);
          //  } else if (questionMobile.getDiaType().equals(Constant.BASIC_STRING_ONE)) {
                //专科
         //       a = tPreDiagnosisQuestionMapper.getFirstQ(questionMobile);
          //  } else {
                //随访类型
                String diaId = questionMobile.getDiaId();
                if (StringUtils.isBlank(diaId)) {
                    //需要最新的随访单子id
                    diaId = tPreDiagnosisFormMapper.getLastSuiFangFormDiaId();
                }
                questionMobile.setDiaId(diaId);
              //  questionMobile.setDiaType(null);
                a = tPreDiagnosisQuestionMapper.getFirstQSuiFang(questionMobile);
         //   }

            for (TRecordDia tRecordDia : a) {
                QuestionMain questionMain = recordDiaStrategyFactory.getNormalParamStrategy(tRecordDia.getQuestionType()).transRecordDia(tRecordDia);
                questionMain.setDiaId(questionMobile.getDiaId());
                computer = computer(questionMain, questionMobilesList);
                questionMain.setQuestionPercentage(computer);
                return ResEntity.success(questionMain);
            }

        } else {
            if (questionIds.size() == 0) {
                return ResEntity.error("questionId不能为空");
            }
            String diaId = questionMobile.getDiaId();
            if (StringUtils.isBlank(diaId)) {
                log.error("diaId不能为空");
                return ResEntity.error("diaId不能为空");
            }
//             获取子题,最大优先级查询.
            List<QuestionMain> questionMobilesList1 = questionListVo.getQuestionMobilesList();
            int size = questionMobilesList1.size();
            QuestionMain questionMain1 = questionMobilesList1.get(size - 1);
            String masterQuestionId = questionMain1.getMasterQuestionId();

            List<TRecordDia> childQ;
            if (StringUtils.isNotBlank(masterQuestionId)) {
                //查下有没有下一题的子题
                NextQVo nextChildQVo = new NextQVo();
                for (QuestionMain questionMain : questionMobilesList) {
                    if (questionMain.getQuestionId() == Integer.parseInt(masterQuestionId)) {
                        //获取用户的选项。
                        String[] answerContentId = questionMain.getAnswerContentId();
                        if (answerContentId != null && answerContentId.length > 0) {
                            ArrayList<Long> integers = new ArrayList<>();
                            for (String s : answerContentId) {
                                integers.add(Long.parseLong(s));
                            }
                            nextChildQVo.setOptionIds(integers);
                        }
                    }
                }
                nextChildQVo.setDiaId(diaId);
                nextChildQVo.setQuestionIds(questionIds);
                nextChildQVo.setMasterId(Integer.parseInt(masterQuestionId));
                //查当前题目是否有子题
                //还需要排除 没被选择的选项对应的子题。
                ResEntity chilQ = getChilQ(nextChildQVo, masterQuestionId, questionMobilesList);
                if (chilQ.getStatus()) {
                    return chilQ;
                }
                //没取到，在查一下，当前选项是否有存在的子题。不然就直接到下一个不是子题的正常题目，不需要父masterQuestId再查下
                NextQVo nextChildQVo2 = new NextQVo();
                //获取用户当前最新题目的选择的的选项。
                String[] answerContentId = questionMobilesList.get(questionMobilesList.size() - 1).getAnswerContentId();
                ArrayList<Long> integers = new ArrayList<>();
                for (String s : answerContentId) {
                    integers.add(Long.parseLong(s));
                }
                //用户当前题目选择的选项,文本没有选项不设置子题和跳题，所以不需要去查找
                if (!integers.isEmpty()) {
                    nextChildQVo2.setOptionIds(integers);
                    nextChildQVo2.setDiaId(diaId);
                    nextChildQVo2.setQuestionIds(questionIds);
                    //查当前题目是否有子题
                    //有子题目
                    ResEntity chilQ2 = getChilQ(nextChildQVo2, questionMobilesList.get(questionMobilesList.size() - 1).getQuestionId() + "", questionMobilesList);
                    if (chilQ2.getStatus()) {

                        return chilQ2;
                    }
                }
            } else {
                //没有传父题id，查下这个题有没有子题。
                NextQVo nextChildQVo = new NextQVo();
                //获取用户当前最新题目的选择的的选项。
                String[] answerContentId = questionMobilesList.get(questionMobilesList.size() - 1).getAnswerContentId();
                ArrayList<Long> integers = new ArrayList<>();
                for (String s : answerContentId) {
                    integers.add(Long.parseLong(s));
                }
                //用户当前题目选择的选项
                if (integers.size() > 0) {
                    nextChildQVo.setOptionIds(integers);
                    nextChildQVo.setDiaId(diaId);
                    nextChildQVo.setQuestionIds(questionIds);
                    //查当前题目是否有子题
                    //有子题目
                    nextChildQVo.setMasterId(questionMobilesList.get(questionMobilesList.size() - 1).getQuestionId());
                    ResEntity chilQ = getChilQ(nextChildQVo, questionMobilesList.get(questionMobilesList.size() - 1).getQuestionId() + "", questionMobilesList);
                    if (chilQ.getStatus()) {
                        return chilQ;
                    }
                }
            }
            //获取跳题 !!!还需要排除，没被选择的子题！！！
            //获取子题
            NextQVo nextQVo = new NextQVo();
            nextQVo.setQuestionIds(questionIds);
            nextQVo.setDiaId(diaId);
            nextQVo.setOptionIds(optionsIds);
            List<TRecordDia> b = tPreDiagnosisQuestionMapper.getNextQ(nextQVo);
            if (b.isEmpty()) {
                QuestionMain questionMain = new QuestionMain();
                questionMain.setNoNextItem(true);
                questionMain.setQuestionPercentage("0");
                return ResEntity.success(questionMain);
            }
            for (TRecordDia tRecordDia : b) {
                QuestionMain questionMain = recordDiaStrategyFactory.getNormalParamStrategy(tRecordDia.getQuestionType()).transRecordDia(tRecordDia);
                computer = computer(questionMain, questionMobilesList);
                questionMain.setQuestionPercentage(computer);
                return ResEntity.success(questionMain);
            }
        }
        return ResEntity.error("未获取到题目");
    }

    public ResEntity getChilQ(NextQVo nextChildQVo, String masterId, List<QuestionMain> questionMobilesList) {
        List<TRecordDia> childQ = tPreDiagnosisQuestionMapper.getChildQ(nextChildQVo);
        //有子题目
        String computer = "0";
        if (childQ.size() > 0) {
            for (TRecordDia tRecordDia : childQ) {
                QuestionMain questionMain = recordDiaStrategyFactory.getNormalParamStrategy(tRecordDia.getQuestionType()).transRecordDia(tRecordDia);
                //当前题目的题目id
                questionMain.setMasterQuestionId(masterId);
                questionMain.setDiaId(nextChildQVo.getDiaId());
                computer = computer(questionMain, questionMobilesList);
                questionMain.setQuestionPercentage(computer);
                return ResEntity.success(questionMain);
            }
        }
        return ResEntity.error("");
    }


}
