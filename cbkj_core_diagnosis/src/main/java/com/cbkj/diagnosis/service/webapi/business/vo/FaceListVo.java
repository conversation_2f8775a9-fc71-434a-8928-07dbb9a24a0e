package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Schema(description =  "面访列表")
@Data
public class FaceListVo {

    @Schema(description =  "记录id")
    private String recId;

    @Schema(description =  "患者id")
    private String patientId;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者名称")
    private String patientName;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者身份证号")
    private String patientIdcard;

    @Schema(description =  "患者性别（M男   F女）")
    private String patientSex;

    @Schema(description =  "患者年龄")
    private String patientAge;

    @Schema(description =  "预诊单信息")
    private String diaContent;

    @Schema(description =  "预诊单id")
    private String diaId;
    @Schema(description =  "创建时间")
    private Date createDate;

    @Schema(description =  "创建人")
    private String createUser;

    @Schema(description =  "创建人姓名")
    private String createUsername;

    private String formName;

}
