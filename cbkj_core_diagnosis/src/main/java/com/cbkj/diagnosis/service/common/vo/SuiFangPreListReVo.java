package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Schema(description =  "随访面诊列表请求字段")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SuiFangPreListReVo implements  Serializable {
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "身份证")
    private String patientIdcard;

    @Schema(description =  "1预诊。2随访",required = true)
    private String formType;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者姓名")
    private String patientName;
    @Schema(description =  "中西医诊断")
    private String diseaseName;

    @Schema(description =  "随访人员")
    private String doctorName;

    @Schema(description =  "时间开始")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startDate;
    @Schema(description =  "时间结束")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endDate;
}
