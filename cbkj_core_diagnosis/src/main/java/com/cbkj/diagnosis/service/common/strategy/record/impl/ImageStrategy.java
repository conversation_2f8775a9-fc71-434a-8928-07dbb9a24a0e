package com.cbkj.diagnosis.service.common.strategy.record.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.beans.business.TRecordDiaImage;
import com.cbkj.diagnosis.service.business.TRecordDiaImageService;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategy;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component("imageStrategy")
public class ImageStrategy implements RecordDiaStrategy {

    private final TRecordDiaImageService tRecordDiaImageService;

    public ImageStrategy(TRecordDiaImageService tRecordDiaImageService) {
        this.tRecordDiaImageService = tRecordDiaImageService;
    }

    @Override
    public QuestionMain transRecordDia(TRecordDia tRecord) {
        QuestionMain questionMain = new QuestionMain();
        BeanUtils.copyProperties(tRecord, questionMain);
        //把图片的答案从数据库获取
        QueryWrapper<TRecordDiaImage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dia_rec_id", tRecord.getDiaRecId());
        List<TRecordDiaImage> list = tRecordDiaImageService.list(queryWrapper);
        questionMain.setQuestionImages(list);
        return questionMain;
    }
}
