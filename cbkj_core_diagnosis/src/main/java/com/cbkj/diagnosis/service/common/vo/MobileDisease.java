package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.service.common.strategy.record.impl.TDeptRes;
import com.cbkj.diagnosis.service.common.strategy.record.impl.TDiseaseRes;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema
@NoArgsConstructor
@Data
public class MobileDisease implements Serializable {
    List<TDiseaseRes> chineseDisease;
    List<TDiseaseRes> westDisease;
    List<TDeptRes> deptDisease;
    @Schema(description =  "全科预诊id")
    private String diaId;
//    List<TDisease> AllDisease;
}
