package com.cbkj.diagnosis.service.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.TAdminInfo;
import com.cbkj.diagnosis.beans.statistics.StatisticsDiagnosisDic;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayAdverse;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.mapper.statistics.StatisticsDiagnosisDicMapper;
import com.cbkj.diagnosis.service.statistics.StatisticsDiagnosisDicService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Slf4j
@Service
public class StatisticsDiagnosisDicServiceImpl extends ServiceImpl<StatisticsDiagnosisDicMapper, StatisticsDiagnosisDic> implements StatisticsDiagnosisDicService {

    public long getRecordFinishSum(StatisticsVo statisticsVo) {
        QueryWrapper<StatisticsDiagnosisDic> wrapper = new QueryWrapper<>();
        wrapper.select("sum(num) as num");
        if (StringUtils.isNotBlank(statisticsVo.getDisCode())) {
            wrapper.eq("dis_code", statisticsVo.getDisCode());
        }
        if (StringUtils.isNotBlank(statisticsVo.getStartDate())) {
            wrapper.ge("create_time", statisticsVo.getStartDate());
        }
        if (StringUtils.isNotBlank(statisticsVo.getEndDate())) {
            wrapper.le("create_time", statisticsVo.getEndDate());
        }
        wrapper.eq("dic_id", "follow-up-event-archive-closure");

        /**
         * DisCodes 不为空，并且 userId 为空，则查询疾病代码
         */
        if (StringUtils.isNotBlank(statisticsVo.getDisCodes()) && StringUtils.isBlank(statisticsVo.getUserId())){
            wrapper.in("dis_code", statisticsVo.getDisCodes().substring(0, statisticsVo.getDisCodes().length()-1));
        }
        if (StringUtils.isNotBlank(statisticsVo.getUserId()) && StringUtils.isBlank(statisticsVo.getDisCodes())){
            //不是管理员，但是未配置映射疾病，那么返回0
            return 0L;
        }
        StatisticsDiagnosisDic one = getOne(wrapper, false);
        if (one != null) {
            return one.getNum();
        }
        return 0L;

    }

    public long getDiagnosisFoucsSum(StatisticsVo statisticsVo) {
        QueryWrapper<StatisticsDiagnosisDic> wrapper = new QueryWrapper<>();
        wrapper.select("sum(num) as num");
        if (StringUtils.isNotBlank(statisticsVo.getDisCode())) {
            wrapper.eq("dis_code", statisticsVo.getDisCode());
        }
        if (StringUtils.isNotBlank(statisticsVo.getStartDate())) {
            wrapper.ge("create_time", statisticsVo.getStartDate());
        }
        if (StringUtils.isNotBlank(statisticsVo.getEndDate())) {
            wrapper.le("create_time", statisticsVo.getEndDate());
        }
        wrapper.eq("dic_id", "follow-up-event-focus");
        /**
         * DisCodes 不为空，并且 userId 为空，则查询疾病代码
         */
        if (StringUtils.isNotBlank(statisticsVo.getDisCodes()) && StringUtils.isBlank(statisticsVo.getUserId())){
            wrapper.in("dis_code", statisticsVo.getDisCodes().substring(0, statisticsVo.getDisCodes().length()-1));
        }
        if (StringUtils.isNotBlank(statisticsVo.getUserId()) && StringUtils.isBlank(statisticsVo.getDisCodes())){
            //不是管理员，但是未配置映射疾病，那么返回0
            return 0L;
        }
        StatisticsDiagnosisDic one = getOne(wrapper, false);
        if (one != null) {
            return one.getNum();
        }
        return 0L;
    }


    @Transactional(rollbackFor = Exception.class)
    public void statisticsDiagnosisDic(List<StatisticsDiagnosisDic> statisticsDiagnosisDicList, String recId) {
        ArrayList<StatisticsDiagnosisDic> insertList = new ArrayList<>();
        ArrayList<StatisticsDiagnosisDic> updateList = new ArrayList<>();
        for (StatisticsDiagnosisDic statisticsDiagnosisDic : statisticsDiagnosisDicList) {
            //根据create_time app_id ins_code dis_code dic_id  patientId 判断数据是否存在,存在的话就更新，否则插入
            QueryWrapper<StatisticsDiagnosisDic> wrapper = new QueryWrapper<>();
            //获取今天年月日字符串
            String dateFormats = DateUtil.getDateFormats("yyyy-MM-dd", new Date());
            wrapper.eq("create_time", dateFormats)
            .eq("app_id", statisticsDiagnosisDic.getAppId())
            .eq("ins_code", statisticsDiagnosisDic.getInsCode())
            .eq("dis_code", statisticsDiagnosisDic.getDisCode())
            .eq("dic_id", statisticsDiagnosisDic.getDicId());
            StatisticsDiagnosisDic one = getOne(wrapper, false);
            if (one != null) {
                one.setNum(one.getNum() + 1);
                updateList.add(one);

            } else {
                //唯一键
                statisticsDiagnosisDic.setInsertTime(new Date());
                statisticsDiagnosisDic.setNum(1);
                boolean temp = false;
                if (!insertList.isEmpty()){
                    for (StatisticsDiagnosisDic erverDayDis1 : insertList) {
                        if (erverDayDis1.getDicCode().equals(StringUtils.isBlank(statisticsDiagnosisDic.getDicCode())? "000000" : statisticsDiagnosisDic.getDicCode())
                                && erverDayDis1.getDisCode().equals(StringUtils.isBlank(statisticsDiagnosisDic.getDisCode())? "000000" : statisticsDiagnosisDic.getDisCode())
                                && erverDayDis1.getAppId().equals(StringUtils.isBlank(statisticsDiagnosisDic.getAppId())? "000000" : statisticsDiagnosisDic.getAppId())
                                && erverDayDis1.getInsCode().equals(StringUtils.isBlank(statisticsDiagnosisDic.getInsCode())? "000000" : statisticsDiagnosisDic.getInsCode())) {
                            erverDayDis1.setNum(erverDayDis1.getNum() + 1);
                            temp = true;
                        }
                    }
                }

                if (!temp){
                    insertList.add(statisticsDiagnosisDic);
                }
            }
        }
        if (!insertList.isEmpty()){
            saveOrUpdateBatch(insertList);
        }
        if (!updateList.isEmpty()){
            updateBatchById(updateList);
        }
        log.info("患者答题选项事件统计结束recId= {}", recId);
    }
}
