package com.cbkj.diagnosis.service.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.statistics.*;
import com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayDisMapper;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayDisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Slf4j
@Service
public class StatisticsErverDayDisServiceImpl extends ServiceImpl<StatisticsErverDayDisMapper, StatisticsErverDayDis> implements StatisticsErverDayDisService {

    @Override
    public Long getSumAllNumDia(StatisticsVo statisticsVo) {
        QueryWrapper<StatisticsErverDayDis> statisticsErverDayDisQueryWrapper = new QueryWrapper<>();
        statisticsErverDayDisQueryWrapper.select("sum(diagnosis_num) as diagnosisNum");
        if (StringUtils.isNotBlank(statisticsVo.getDisCode())) {
            statisticsErverDayDisQueryWrapper.eq("dis_code", statisticsVo.getDisCode());
        }

        if (StringUtils.isNotBlank(statisticsVo.getStartDate())) {
            statisticsErverDayDisQueryWrapper.ge("create_time", statisticsVo.getStartDate());
        }
        if (StringUtils.isNotBlank(statisticsVo.getEndDate())) {
            statisticsErverDayDisQueryWrapper.le("create_time", statisticsVo.getEndDate());
        }
        /**
         * DisCodes 不为空，并且 userId 为空，则查询疾病代码
         */
        if (StringUtils.isNotBlank(statisticsVo.getDisCodes()) && StringUtils.isBlank(statisticsVo.getUserId())){
            statisticsErverDayDisQueryWrapper.in("dis_code", statisticsVo.getDisCodes().substring(0, statisticsVo.getDisCodes().length()-1));
        }
        if (StringUtils.isNotBlank(statisticsVo.getUserId()) && StringUtils.isBlank(statisticsVo.getDisCodes())){
            //不是管理员，但是未配置映射疾病，那么返回0
            return 0L;
        }
        StatisticsErverDayDis one = getOne(statisticsErverDayDisQueryWrapper, false);
        if (one != null) {
            return one.getDiagnosisNum().longValue();
        }
        return 0L;
    }

    @Override
    public Long getSumAllNumFlow(StatisticsVo statisticsVo) {
        QueryWrapper<StatisticsErverDayDis> statisticsErverDayDisQueryWrapper = new QueryWrapper<>();
        statisticsErverDayDisQueryWrapper.select("sum(follow_num) as followNum");
        if (StringUtils.isNotBlank(statisticsVo.getDisCode())) {
            statisticsErverDayDisQueryWrapper.eq("dis_code", statisticsVo.getDisCode());
        }

        if (StringUtils.isNotBlank(statisticsVo.getStartDate())) {
            statisticsErverDayDisQueryWrapper.ge("create_time", statisticsVo.getStartDate());
        }
        if (StringUtils.isNotBlank(statisticsVo.getEndDate())) {
            statisticsErverDayDisQueryWrapper.le("create_time", statisticsVo.getEndDate());
        }
        /**
         * DisCodes 不为空，并且 userId 为空，则查询疾病代码
         */
        if (StringUtils.isNotBlank(statisticsVo.getDisCodes()) && StringUtils.isBlank(statisticsVo.getUserId())){
            statisticsErverDayDisQueryWrapper.in("dis_code", statisticsVo.getDisCodes().substring(0, statisticsVo.getDisCodes().length()-1));
        }
        if (StringUtils.isNotBlank(statisticsVo.getUserId()) && StringUtils.isBlank(statisticsVo.getDisCodes())){
            //不是管理员，但是未配置映射疾病，那么返回0
            return 0L;
        }
        StatisticsErverDayDis one = getOne(statisticsErverDayDisQueryWrapper, false);
        if (one != null) {
            return one.getFollowNum().longValue();
        }
        return 0L;
    }

    @Override
    public List<BlockDis> getDisList(StatisticsVo statisticsVo) {
        return baseMapper.getBlockDisList(statisticsVo);
    }

    @Override
    public List<BlockOneDetail> getBlockOneList(StatisticsVo statisticsVo) {
        StatisticsErverDayDisMapper baseMapper1 = baseMapper;
        List<BlockOneDetail> diagnosisNumList = baseMapper1.getBlockOneList(statisticsVo);
        return diagnosisNumList;
    }

    @Override
    public List<BlockOneDetail> getBlockOneList2(StatisticsVo statisticsVo) {
        StatisticsErverDayDisMapper baseMapper1 = baseMapper;
        List<BlockOneDetail> followNumList = baseMapper1.getBlockOneList2(statisticsVo);
        return followNumList;
    }

    @Override
    public List<BlockTwoDetail> getBlockTwoList(StatisticsVo statisticsVo) {
        StatisticsErverDayDisMapper baseMapper1 = baseMapper;
        List<BlockTwoDetail> list = baseMapper1.getBlockTwoList(statisticsVo);
        return list;
    }

    @Override
    public List<BlockTwoDetail> getBlockTwoListFlow(StatisticsVo statisticsVo) {
        StatisticsErverDayDisMapper baseMapper1 = baseMapper;
        List<BlockTwoDetail> list = baseMapper1.getBlockTwoListFlow(statisticsVo);
        return list;
    }

    @Override
    public List<StatisticsErverDayDis> staticsHistoryYZ(StaticsHistoryData staticsHistoryData) {
        StatisticsErverDayDisMapper baseMapper1 = baseMapper;
        List<StatisticsErverDayDis> list = baseMapper1.staticsHistoryYZ(staticsHistoryData);
        return list;
    }

    @Override
    public List<StatisticsErverDayDis> staticsHistorySF(StaticsHistoryData staticsHistoryData) {
        StatisticsErverDayDisMapper baseMapper1 = baseMapper;
        List<StatisticsErverDayDis> list = baseMapper1.staticsHistorySF(staticsHistoryData);
        return list;
    }

    /**
     * @param StatisticsErverDayDisList
     * @param recId
     * @param type                      1.预诊数量 2.随诊数量 3.有效病历(手动完成病例标记结案的数量)
     */
    @Transactional(rollbackFor = Exception.class)
    public void writeReadFromRedis(List<StatisticsErverDayDis> StatisticsErverDayDisList, String recId, String type) {
        List<StatisticsErverDayDis> updateList = new ArrayList<StatisticsErverDayDis>();
        List<StatisticsErverDayDis> insertList = new ArrayList<StatisticsErverDayDis>();
        for (StatisticsErverDayDis dayDis : StatisticsErverDayDisList) {
            QueryWrapper<StatisticsErverDayDis> wrapper = new QueryWrapper<>();
            //获取今天年月日字符串
            String dateFormats = DateUtil.getDateFormats("yyyy-MM-dd", new Date());
            wrapper.eq("create_time", dateFormats);
            wrapper.eq("app_id", dayDis.getAppId());
            wrapper.eq("ins_code", dayDis.getInsCode());
            wrapper.eq("dis_code", dayDis.getDisCode());
            StatisticsErverDayDis one = getOne(wrapper);
            if (one != null) {
                if ("1".equals(type)) {
                    one.setDiagnosisNum(one.getDiagnosisNum() + 1);
                }
                if ("2".equals(type)) {
                    one.setFollowNum(one.getFollowNum() + 1);
                }
                if ("3".equals(type)) {
                    one.setRecordsNum( (one.getDiagnosisNum() == null ) ? 0 : one.getDiagnosisNum()+ 1);
                }
                one.setTotalNum(one.getTotalNum() + 1);
                updateList.add(one);
            } else {
                //StatisticsErverDayDis erverDayDis = new StatisticsErverDayDis();
                if ("1".equals(type)) {
                    dayDis.setDiagnosisNum(1);
                    dayDis.setRecordsNum(0);
                    dayDis.setFollowNum(0);
                    boolean temp = false;
                    for (StatisticsErverDayDis erverDayDis1 : insertList) {
                        if (erverDayDis1.getDisCode().equals(dayDis.getDisCode()) && erverDayDis1.getAppId().equals(dayDis.getAppId()) && erverDayDis1.getInsCode().equals(dayDis.getInsCode())) {
                            erverDayDis1.setDiagnosisNum(erverDayDis1.getDiagnosisNum() + 1);
                            erverDayDis1.setTotalNum(erverDayDis1.getDiagnosisNum() + erverDayDis1.getFollowNum());
                            temp = true;
                        }
                    }
                    dayDis.setTotalNum(dayDis.getDiagnosisNum() + dayDis.getFollowNum());
                    if (!temp){
                        insertList.add(dayDis);
                    }
                }
                if ("2".equals(type)) {
                    dayDis.setFollowNum(1);
                    dayDis.setDiagnosisNum(0);
                    dayDis.setRecordsNum(0);
                    boolean temp = false;
                    for (StatisticsErverDayDis erverDayDis1 : insertList) {
                        if (erverDayDis1.getDisCode().equals(dayDis.getDisCode()) && erverDayDis1.getAppId().equals(dayDis.getAppId()) && erverDayDis1.getInsCode().equals(dayDis.getInsCode())) {
                            erverDayDis1.setFollowNum(erverDayDis1.getFollowNum() + 1);
                            erverDayDis1.setTotalNum(erverDayDis1.getDiagnosisNum() + erverDayDis1.getFollowNum());
                            temp = true;
                        }
                    }
                    dayDis.setTotalNum(dayDis.getDiagnosisNum() + dayDis.getFollowNum());
                    if (!temp){
                        insertList.add(dayDis);
                    }

                }
                if ("3".equals(type)) {
                    dayDis.setRecordsNum(1);
                    dayDis.setFollowNum(0);
                    dayDis.setDiagnosisNum(0);

                    boolean temp = false;
                    for (StatisticsErverDayDis erverDayDis1 : insertList) {
                        if (erverDayDis1.getDisCode().equals(dayDis.getDisCode()) && erverDayDis1.getAppId().equals(dayDis.getAppId()) && erverDayDis1.getInsCode().equals(dayDis.getInsCode())) {
                            erverDayDis1.setRecordsNum(erverDayDis1.getRecordsNum() + 1);
                            erverDayDis1.setTotalNum(erverDayDis1.getDiagnosisNum() + erverDayDis1.getFollowNum());
                            temp = true;
                        }
                    }
                    dayDis.setTotalNum(dayDis.getDiagnosisNum() + dayDis.getFollowNum());
                    if (!temp){
                        insertList.add(dayDis);
                    }
                }
//                erverDayDis.setTotalNum(erverDayDis.getTotalNum() + dayDis.getTotalNum());
//                insertList.add(erverDayDis);
            }
        }
        if (!insertList.isEmpty()){
            saveOrUpdateBatch(insertList);
        }
        if (!updateList.isEmpty()){
            updateBatchById(updateList);
        }
        log.info("患者预诊随访统计结束recId= {}", recId);
    }
}
