package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.beans.business.TRecordDiaDimension;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/4 15:20
 * @Version 1.0
 */
@Schema(description = "获取预诊单明细接口参数V2.1.0")
@Data
public class GetPrePaperV2_1_0 {
    @Schema(description =  "题目列表")
    private ArrayList<QuestionMain> questionMainArrayList;
    @Schema(description =  "维度列表")
    private List<TRecordDiaDimension> tRecordDiaDimensionList;
}
