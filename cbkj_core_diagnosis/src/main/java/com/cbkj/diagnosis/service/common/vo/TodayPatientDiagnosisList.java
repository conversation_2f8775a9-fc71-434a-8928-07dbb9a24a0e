package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/24 11:02
 * @Version 1.0
 */
@Data
public class TodayPatientDiagnosisList {
    @Schema(description =  "患者任务")
    private Long taskPatientsId;

    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者名字")
    private String patientName;

    @Schema(description =  "患者年龄")
    private String patientAge;

    @Schema(description =  "患者性别")
    private String patientSex;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者身份证")
    private String patientCardNumber;

}
