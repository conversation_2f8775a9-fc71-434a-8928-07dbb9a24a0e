package com.cbkj.diagnosis.service.mobileapi.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Schema
@NoArgsConstructor
@AllArgsConstructor

public class GetMobileIndexNumber {
    @Schema(description="执行的具体内容类型1.健康宣教2.随访问卷（随访的一种类型）3.复诊提醒4.自测量表(随访的一种类型)")
    private String roadExecuteEventType;
    private String patientId;
    @Schema(description="1.待执行（未发送）2.已执行（已发送,未读（微信短信））3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 7.已读（微信短信）8.已填写")
    private String taskExcuteStatus;
}
