package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.service.common.vo.QuestionOption;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 题目
 */
@Data
@NoArgsConstructor
@Schema
public class LookPaperQuestionMain implements Serializable {
    @Schema(description = "预诊单id")
    private String diaId;
    @Schema(description = "题目id")
    private String questionId;

    @Schema(description = "题目编码")
    private String questionCode;
    @Schema(description = "题目")
    private String questionName;
    @Schema(description = "结构化题目")
    private String questionStem;
    @Schema(description = "题目序号")
    private Integer questionNumber;
    @Schema(description = "题目类型（1文本  2单选  3多选  4时间 5.血压 6日期 7值域滑动样式 8图片 9。AI舌诊 10.OCR药盒识别 11.OCR检查检验单）")
    private String questionType;

    @Schema(description =  "V2.1.0-图示示例")
    private String questionImg;

    @Schema(description =  "V2.1.0-图片最大上传数量")
    private Integer questionImgMaxNum;

    @Schema(description = "V2.1.0-题目维度代码")
    private String questionDimensionCode;
    @Schema(description = "V2.1.0-题目维度名称")
    private String questionDimensionName;

    @Schema(description = "时间单位（1年 2月 3日 可多选,逗号拼接）")
    private String dateUnit;

    @Schema(description = "从字典表取值")
    private String questionClassType;

    @Schema(description = "随访类别code:从字典表取值")
    private String followUpClassTypeCode;

    @Schema(description = "问题单位（只有文本样式有这个值）")
    private String questionUnit;

    @Schema(description = "题目互斥组")
    private String questionOptionGroups;




    @Schema(description = "问题的选项明细列表")
    List<QuestionOptionSave> questionOptionSaveList;

}
