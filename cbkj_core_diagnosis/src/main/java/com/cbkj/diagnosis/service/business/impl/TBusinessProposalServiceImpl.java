package com.cbkj.diagnosis.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TBusinessAnnex;
import com.cbkj.diagnosis.beans.business.TBusinessProposal;
import com.cbkj.diagnosis.beans.business.adviceVo.AdviceSave;
import com.cbkj.diagnosis.beans.business.adviceVo.ImageUploadRes;
import com.cbkj.diagnosis.mapper.business.TBusinessAnnexMapper;
import com.cbkj.diagnosis.mapper.business.TBusinessProposalMapper;
import com.cbkj.diagnosis.service.business.TBusinessProposalService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业务管理建议问题咨询表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Service
public class TBusinessProposalServiceImpl extends ServiceImpl<TBusinessProposalMapper, TBusinessProposal> implements TBusinessProposalService {

    private final TBusinessAnnexMapper tBusinessAnnexMapper;

    public TBusinessProposalServiceImpl(TBusinessAnnexMapper tBusinessAnnexMapper) {
        this.tBusinessAnnexMapper = tBusinessAnnexMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity saveAdvice(AdviceSave adviceSave, String userId, String userName, Integer SourceFrom) {
        if (StringUtils.isNotBlank(adviceSave.getProposalContent())){
            if (adviceSave.getProposalContent().length() > 500) {
                return ResEntity.error("建议内容不能超过500字");
            }
        }
        if (StringUtils.isNotBlank(adviceSave.getProposalTitle())){
            if (adviceSave.getProposalTitle().length() > 20) {
                return ResEntity.error("建议标题不能超过20字");
            }
        }
        TBusinessProposal tBusinessProposal = new TBusinessProposal();
        BeanUtils.copyProperties(adviceSave, tBusinessProposal);
        tBusinessProposal.setCreateTime(new Date());
        tBusinessProposal.setCreateUserId(userId);
        tBusinessProposal.setCreateUserName(userName);
        tBusinessProposal.setProposalReceiveState(1);
        tBusinessProposal.setSourceFrom(SourceFrom);
        this.save(tBusinessProposal);
        List<ImageUploadRes> adviceImages = adviceSave.getAdviceImages();
        if (adviceImages != null && adviceImages.size() > 0) {
            for (ImageUploadRes adviceImage : adviceImages) {
                QueryWrapper<TBusinessAnnex> tBusinessAnnexQueryWrapper = new QueryWrapper<>();
                TBusinessAnnex tBusinessAnnex = new TBusinessAnnex();
                tBusinessAnnex.setAnnexForeignId(String.valueOf(tBusinessProposal.getId()));
                tBusinessAnnexQueryWrapper.eq("id", adviceImage.getUrlId()).eq("insert_user_id", userId);
                tBusinessAnnexMapper.update(tBusinessAnnex, tBusinessAnnexQueryWrapper);
            }
        }


        return ResEntity.success(tBusinessProposal);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity userUpdateAdvice(AdviceSave adviceSave , String userId, String userName, Integer SourceFrom) {
        QueryWrapper<TBusinessProposal> tBusinessProposalQueryWrapper = new QueryWrapper<>();
        tBusinessProposalQueryWrapper.eq("id", adviceSave.getId() ).eq("create_user_id", userId).eq("proposal_receive_state",1);
        TBusinessProposal tBusinessProposal = new TBusinessProposal();
        BeanUtils.copyProperties(adviceSave, tBusinessProposal);
        tBusinessProposal.setUpdateTime(new Date());
        tBusinessProposal.setUpdateUserId(userId);
        boolean update = this.update(tBusinessProposal, tBusinessProposalQueryWrapper);
        if (!update){
            return ResEntity.error("更新失败");
        }
        //删除之前维护的图片
        QueryWrapper<TBusinessAnnex> tBusinessAnnexQueryWrapper2 = new QueryWrapper<>();
        tBusinessAnnexQueryWrapper2.eq("annex_foreign_id", adviceSave.getId());
        TBusinessAnnex tBusinessAnnex2 = new TBusinessAnnex();
        tBusinessAnnex2.setAnnexForeignId("-1");
        tBusinessAnnexMapper.update(tBusinessAnnex2, tBusinessAnnexQueryWrapper2);
        List<ImageUploadRes> adviceImages = adviceSave.getAdviceImages();
        if (adviceImages != null && adviceImages.size() > 0) {
            for (ImageUploadRes adviceImage : adviceImages) {
                QueryWrapper<TBusinessAnnex> tBusinessAnnexQueryWrapper = new QueryWrapper<>();
                TBusinessAnnex tBusinessAnnex = new TBusinessAnnex();
                tBusinessAnnex.setAnnexForeignId(String.valueOf(tBusinessProposal.getId()));
                tBusinessAnnexQueryWrapper.eq("id", adviceImage.getUrlId()).eq("insert_user_id", userId);
                tBusinessAnnexMapper.update(tBusinessAnnex, tBusinessAnnexQueryWrapper);
            }
        }
        return ResEntity.success(tBusinessProposal);
    }
}
