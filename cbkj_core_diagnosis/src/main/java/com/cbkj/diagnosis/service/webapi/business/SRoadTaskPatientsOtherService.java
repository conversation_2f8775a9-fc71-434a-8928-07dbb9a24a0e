package com.cbkj.diagnosis.service.webapi.business;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping;
import com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.task.SRoadTaskPatients;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsMapper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.crypto.spec.PSource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class SRoadTaskPatientsOtherService {

    private final MedicalRecordsMapper medicalRecordsMapper;

    public SRoadTaskPatientsOtherService(MedicalRecordsMapper medicalRecordsMapper) {
        this.medicalRecordsMapper = medicalRecordsMapper;
    }

    /**
     * 检查就诊后事件 是否此刻已经到了，并设置任务时间
     *
     * @return
     */
    public boolean checkJIuZhenHouTimeIsArrived(SRoadTaskPatients roadPatients,Integer prescriptionNum) throws ParseException {
        //获取就诊时间。
        String recordsId = roadPatients.getRecordsId();
        Date recordTime = null;
        if (StringUtils.isNotBlank(recordsId)) {
            MedicalRecords objectById1 = medicalRecordsMapper.getObjectById(recordsId);
            if (objectById1 != null) {
                recordTime = objectById1.getRecordTime();
            }
        } else {
            recordTime = roadPatients.getTaskExcuteTimeTmp();
        }

        if (recordTime != null) {
            //获取路径多久后执行
            String roadExecuteEventTime = roadPatients.getRoadExecuteEventTime();
//                Date recordTime = objectById1.getRecordTime();
            //获取路径时间单位 天（D）小时（H）分钟（m）

            long a = (long) (Double.parseDouble(roadExecuteEventTime) * 60 * 1000);
            String roadExecuteEventUnit = roadPatients.getRoadExecuteEventUnit();
            if ("D".equals(roadExecuteEventUnit)) {
                a = a * 24 * 60;
            } else if ("H".equals(roadExecuteEventUnit)) {
                a = a * 60;
            }

            // 根据单位，计算对应时长，统一转成秒，在把诊疗时间转成秒。相加，再转成date
            //3.服药 4.服药结束前 5.针灸治疗后
            //服药结束前是按照帖数，7贴，一天就按照一贴算
            //服药就是按照就诊时间
            //针灸这个当时说要看数据、
            //就计算中药
            long time = 0l;
            String roadExecuteEvent = roadPatients.getRoadExecuteEvent();
            if (Constant.BASIC_STRING_FOUR.equals(roadExecuteEvent) ){
                //recordTime = recordTime+贴数 -
                time = recordTime.getTime() + (prescriptionNum *24 * 60 * 60 * 1000) - a;

            }else {
                 time = recordTime.getTime() + a;
            }

            Date date = new Date(time);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
            String format = simpleDateFormat.format(date);
            roadPatients.setTaskExcuteTime(simpleDateFormat.parse(format));
            //第二个参数-第一个参数的天数差。
            long between = DateUtil.between(simpleDateFormat.parse(simpleDateFormat.format(new Date())), simpleDateFormat.parse(format), DateUnit.SECOND, false);
            if (between <= 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 处理电话随访的排期 任务时间。
     *
     * @param sRoadTaskPatients1
     * @return
     */
    public SRoadTaskPatientsPhone newSRoadTaskPatientsPhone(SRoadTaskPatients sRoadTaskPatients1) {
        SRoadTaskPatientsPhone sRoadTaskPatientsPhone = new SRoadTaskPatientsPhone();
        BeanUtils.copyProperties(sRoadTaskPatients1, sRoadTaskPatientsPhone);
        sRoadTaskPatientsPhone.setAllotTaskStatus("1");
        sRoadTaskPatientsPhone.setPhoneStatus(1);
        sRoadTaskPatientsPhone.setSuiFangTime(sRoadTaskPatients1.getTaskExcuteTime());
        //获取就诊时间。
        String recordsId = sRoadTaskPatients1.getRecordsId();
        Date recordTime = null;
        if (StringUtils.isNotBlank(recordsId)) {
            MedicalRecords objectById1 = medicalRecordsMapper.getObjectById(recordsId);
            if (objectById1 != null) {
                recordTime = objectById1.getRecordTime();
            }
        } else {
            recordTime = sRoadTaskPatients1.getTaskExcuteTimeTmp();
        }
        if (recordTime != null) {
            //获取路径多久后执行
            String roadExecuteEventTime = sRoadTaskPatients1.getRoadExecuteEventTime();
//                Date recordTime = objectById1.getRecordTime();
            //获取路径时间单位 天（D）小时（H）分钟（m）
            long a = Long.parseLong(roadExecuteEventTime) * 60 * 1000;
            String roadExecuteEventUnit = sRoadTaskPatients1.getRoadExecuteEventUnit();
            if ("D".equals(roadExecuteEventUnit)) {
                a = a * 24 * 60;
                // Long.parseLong(roadExecuteEventTime) * 24 * 60 * 60 * 1000
            } else if ("H".equals(roadExecuteEventUnit)) {
                a = a * 60;
                // Long.parseLong(roadExecuteEventTime) * 60 * 60 * 1000
            }

            // 根据单位，计算对应时长，统一转成秒，在把诊疗时间转成秒。相加，再转成date
            long time = recordTime.getTime() + a;

            Date date = new Date(time);

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = simpleDateFormat.format(date);
            //第二个参数-第一个参数的差。
            try {
                long between = DateUtil.between(simpleDateFormat.parse(simpleDateFormat.format(new Date())), simpleDateFormat.parse(format), DateUnit.SECOND, false);
                if (between <= 0) {
                    sRoadTaskPatientsPhone.setPhoneStatus(2);
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }


            try {
                sRoadTaskPatientsPhone.setSuiFangTime(simpleDateFormat.parse(format));
                log.info("打印计算前-的电话随访随访是按：" + simpleDateFormat.format(recordTime));
                log.info("打印计算后-的电话随访随访是按：" + format);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }

        return sRoadTaskPatientsPhone;

    }

    /**
     * 设置s_road_task_patients_mapping组装信息。一个患者+一次病历+当前任务，组合成唯一主键。
     *
     * @param sRoadTaskPatientsMappings
     * @param patientId
     * @param sRoadTaskId
     * @param recordsId
     */
    public void setsroadtaskpatientsmappinglist
    (ArrayList<SRoadTaskPatientsMapping> sRoadTaskPatientsMappings,
     String patientId
            , String sRoadTaskId
            , String recordsId
    ) {
        SRoadTaskPatientsMapping mapping = new SRoadTaskPatientsMapping();
        mapping.setPatientId(patientId);
        mapping.setSRoadTaskId(sRoadTaskId);
        mapping.setRecordsId(recordsId);
        mapping.setStatus("1");
        mapping.setCreateTime(new Date());
        sRoadTaskPatientsMappings.add(mapping);
    }





    public static void main(String[] args) {
        int i = 3 * 24 * 60 * 60 * 1000;
        Date date = new Date();
        long l = date.getTime() + i;

        Date date1 = new Date(l);

        System.out.println(date1);
    }
}
