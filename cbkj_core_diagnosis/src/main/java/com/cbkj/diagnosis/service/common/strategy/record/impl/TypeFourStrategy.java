package com.cbkj.diagnosis.service.common.strategy.record.impl;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategy;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 时间
 *
 * <AUTHOR>
 */
@Component("recordFour")
public class TypeFourStrategy implements RecordDiaStrategy {
    @Override
    public QuestionMain transRecordDia(TRecordDia tRecord) {
        QuestionMain questionMain = new QuestionMain();

        BeanUtils.copyProperties(tRecord, questionMain);


        //文本回答内容
        if (StringUtils.isNotBlank(tRecord.getContent())){
            List<String> strings = JSON.parseArray(tRecord.getContent(), String.class);
            questionMain.setAnswerContent(strings.toArray(new String[]{}));
        }


        return questionMain;
    }
}
