package com.cbkj.diagnosis.service.mobileapi.vo;

//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Schema
@NoArgsConstructor
@AllArgsConstructor
public class GetFurtherConsultationList {
    @Schema(description="执行的具体内容类型1.健康宣教2.随访问卷（随访的一种类型）3.复诊提醒4.自测量表(随访的一种类型)")
    private String roadExecuteEventType;
    private String patientId;
    @Schema(description="1预诊。2随访")
    private String formType;

}
