package com.cbkj.diagnosis.service.statistics;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cbkj.diagnosis.beans.statistics.BlockFiveDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayExpenses;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;


import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
public interface StatisticsErverDayExpensesService extends IService<StatisticsErverDayExpenses> {

    List<BlockFiveDetail> getBlockFiveList(StatisticsVo statisticsVo);
}
