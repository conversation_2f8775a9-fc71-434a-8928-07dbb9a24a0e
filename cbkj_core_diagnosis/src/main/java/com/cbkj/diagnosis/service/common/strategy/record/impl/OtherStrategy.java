package com.cbkj.diagnosis.service.common.strategy.record.impl;

import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategy;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component("otherStrategy")
public class OtherStrategy implements RecordDiaStrategy {



    @Override
    public QuestionMain transRecordDia(TRecordDia tRecord) {
        return null;
    }
}
