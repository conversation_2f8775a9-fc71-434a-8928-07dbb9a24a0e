package com.cbkj.diagnosis.service.common.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class NextQVo implements Serializable {
    private String diaId;
    private Integer masterId;
    ArrayList<Integer> questionIds;
    ArrayList<Long> optionIds;
    ArrayList<Long> optionIdsAll;

    Long optionIdLast;
    Integer questionIdLast;
}
