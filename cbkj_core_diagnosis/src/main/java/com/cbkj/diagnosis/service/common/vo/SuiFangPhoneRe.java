package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class SuiFangPhoneRe {

    @Schema(description="1.分配时间(只查分配了) 2.最近随访时间（每个患者的多次已经随访过的排期最近一次,只查分配了）3.下次随访时间（每个患者的未随访的排期的时间最近一个,只查分配了）")
    private String dateType;
    @Schema(description =  "时间开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;
    @Schema(description =  "时间结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    @Schema(description =  "随访任务id")
    private String sRoadTaskId;



    @Schema(description =  "分配状态 : 0分配 1 未分配.注意：如果有日期，则按日期已分配。")
    private String allotTaskStatus;

    @Schema(description =  "随访人员（医生）id")
    private String doctorId;

    @Schema(description="1.待执行 3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 8.已填写(随访完成)")
    private String taskExcuteStatus;
    @Schema(description="待随访次数 0. 0次, 1.非0次")
    private Integer waitSuiFangNums;

    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description="患者身份证号码")
    private String patientCardNumber;

    @CBKJEncryptField
    private String patientIdcard;

    @CBKJEncryptField
    @CBKJDecryptField
    private String patientName;
    @Schema(description =  "中西医诊断")
    private String diseaseName;
}
