package com.cbkj.diagnosis.service.statistics;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cbkj.diagnosis.beans.statistics.BlockDis;
import com.cbkj.diagnosis.beans.statistics.BlockOneDetail;
import com.cbkj.diagnosis.beans.statistics.BlockTwoDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis;
import com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
public interface StatisticsErverDayDisService extends IService<StatisticsErverDayDis> {

    Long getSumAllNumDia(StatisticsVo statisticsVo);

    Long getSumAllNumFlow(StatisticsVo statisticsVo);

    List<BlockDis> getDisList(StatisticsVo statisticsVo);

    List<BlockOneDetail> getBlockOneList(StatisticsVo statisticsVo);
    List<BlockOneDetail> getBlockOneList2(StatisticsVo statisticsVo);

    List<BlockTwoDetail> getBlockTwoList(StatisticsVo statisticsVo);

    List<BlockTwoDetail> getBlockTwoListFlow(StatisticsVo statisticsVo);

    List<StatisticsErverDayDis> staticsHistoryYZ(StaticsHistoryData staticsHistoryData);

    List<StatisticsErverDayDis> staticsHistorySF(StaticsHistoryData staticsHistoryData);
}
