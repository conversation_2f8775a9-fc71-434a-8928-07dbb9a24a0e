package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class GetPatientFilterListVo implements Serializable {

    @Schema(description="就诊日期-开始")
    private String startDate;
    @Schema(description="就诊日期-结束")
    private String endDate;

    @CBKJEncryptField
    @Schema(description="患者身份证id")
    private String patientCardNumber;

    @CBKJEncryptField
    @Schema(description="患者姓名")
    private String patientName;

    @Schema(description="医生名称")
    private String doctorName;

    @Schema(description="就诊科室名称")
    private String deptName;

    @Schema(description="中西医诊断")
    private String diseaseName;

    @Schema(description="任务id")
    private String sRoadTaskId;
}
