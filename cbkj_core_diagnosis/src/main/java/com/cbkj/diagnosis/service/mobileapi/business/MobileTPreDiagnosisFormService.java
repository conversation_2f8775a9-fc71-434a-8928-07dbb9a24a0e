package com.cbkj.diagnosis.service.mobileapi.business;

import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class MobileTPreDiagnosisFormService {

    @Autowired
    private TPreDiagnosisFormMapper tPreDiagnosisFormMapper;

    MobileTPreDiagnosisFormService(TPreDiagnosisFormMapper tPreDiagnosisFormMapper){
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
    }

    public void lookPaper(){

    }

}
