package com.cbkj.diagnosis.service.business.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.business.TRecordEvent;
import com.cbkj.diagnosis.beans.statistics.vo.ListJoinDic;
import com.cbkj.diagnosis.mapper.business.TRecordEventMapper;
import com.cbkj.diagnosis.service.business.TRecordEventService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Service
public class TRecordEventServiceImpl extends ServiceImpl<TRecordEventMapper, TRecordEvent> implements TRecordEventService {

    public List<TRecordEvent> listJoinDic(ListJoinDic recId) {
        return this.baseMapper.listJoinDic(recId);
    }
}
