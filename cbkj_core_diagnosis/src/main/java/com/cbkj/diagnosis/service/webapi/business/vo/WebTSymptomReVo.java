package com.cbkj.diagnosis.service.webapi.business.vo;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema
public class WebTSymptomReVo implements Serializable {
//    private Page page;
    @Schema(description =  "症状名称/编码")
    private String key;
}
