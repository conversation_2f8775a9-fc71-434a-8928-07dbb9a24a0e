package com.cbkj.diagnosis.service.business;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TBusinessAnnex;
import com.cbkj.diagnosis.beans.business.TBusinessProposal;
import com.cbkj.diagnosis.beans.business.adviceVo.ImageUploadRes;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.common.utils.UploadComponent;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/28 17:30
 * @Version 1.0
 */
@Service
public class AdviceFileUploadService {

    private final TBusinessAnnexService tBusinessAnnexService;
    private final TBusinessProposalService tBusinessProposalService;

    private final UploadComponent uploadComponent;

    public AdviceFileUploadService(TBusinessAnnexService tBusinessAnnexService, TBusinessProposalService tBusinessProposalService, UploadComponent uploadComponent) {
        this.tBusinessAnnexService = tBusinessAnnexService;
        this.tBusinessProposalService = tBusinessProposalService;
        this.uploadComponent = uploadComponent;
    }

    public Object uploadImage(MultipartFile file, String annexType,String userId) {
        if (StringUtils.isBlank(annexType)){
            return ResEntity.error("上传失败，参数annexType不正确");
        }
        if (Constant.BASIC_STRING_ONE.equals(annexType) || Constant.BASIC_STRING_TWO.equals(annexType)){
        }else {
            return ResEntity.error("上传失败，参数annexType不正确");
        }
//        if (checkUserTodayUpFileNumIsMore(userId)){
//            return ResEntity.error("请明天在提交");
//        }
//        if (checkUserTodayAdvicesIsMore(userId)){
//            return ResEntity.error("上传失败，今日上传图片数量超过");
//        }
        String fileName = file.getOriginalFilename();
        long size = file.getSize();
        //判断文件大小是否超过10M
//        if (size > 10485760) {
//            return ResEntity.error("上传失败，文件大小超过10M");
//        }
        ResEntity resEntity = uploadComponent.uploadImageOnly(file);
        if (!resEntity.getStatus()){
            return resEntity;
        }

        TBusinessAnnex tBusinessAnnex = new TBusinessAnnex();

        if (fileName != null) {
            String suffix = fileName.substring( (fileName.lastIndexOf(".")+1) );
            String originalName = fileName.substring(0, fileName.lastIndexOf("."));
            tBusinessAnnex.setAnnexSuffixName(suffix);
            tBusinessAnnex.setAnnexOriginalName(originalName);
        }
        tBusinessAnnex.setAnnexSize((double) size);
        tBusinessAnnex.setAnnexType(Integer.valueOf(annexType));
        tBusinessAnnex.setIsDel(false);
        tBusinessAnnex.setInsertUserId(userId);
        tBusinessAnnex.setCreateTime(new Date());
        if (resEntity.getStatus()) {
            String data = (String) resEntity.getData();
            tBusinessAnnex.setAnnexPath(data);
            //取
            tBusinessAnnexService.save(tBusinessAnnex);
            ImageUploadRes imageUploadRes = new ImageUploadRes();
            imageUploadRes.setUrl(data);
            imageUploadRes.setUrlId(tBusinessAnnex.getId());
            resEntity.setData(imageUploadRes);
            return resEntity;
        }
        return ResEntity.error("上传失败");
    }

    /**
     * 判断用户今日上传图片是否超数量 40张
     *
     * @return
     */
    public boolean checkUserTodayUpFileNumIsMore(String userId) {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        QueryWrapper<TBusinessAnnex> tBusinessAnnexQueryWrapper = new QueryWrapper<>();
        tBusinessAnnexQueryWrapper.eq("insert_user_id", userId).ge(true,"create_time",simpleDateFormat.format(DateUtil.getToDayZeroDate()))
                .lt("create_time", simpleDateFormat.format(DateUtil.getNextDate()));
        long count = tBusinessAnnexService.count(tBusinessAnnexQueryWrapper);
        if (count >= 40) {
            return true;
        }
        return false;
    }

    public boolean checkUserTodayAdvicesIsMore(String userId) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        QueryWrapper<TBusinessProposal> tBusinessAnnexQueryWrapper = new QueryWrapper<>();
        tBusinessAnnexQueryWrapper.eq("create_user_id", userId).ge(true,"create_time",simpleDateFormat.format(DateUtil.getToDayZeroDate()))
                .lt("create_time", simpleDateFormat.format(DateUtil.getNextDate()));
        long count = tBusinessProposalService.count(tBusinessAnnexQueryWrapper);
        if (count >= 10) {
            return true;
        }
        return false;
    }


}
