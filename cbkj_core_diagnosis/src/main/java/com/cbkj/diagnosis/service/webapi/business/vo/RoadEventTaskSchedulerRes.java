package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RoadEventTaskSchedulerRes {
    private String prescriptionsId;
    private String westPrescriptionsId;
    private String handleRecordId;
    private String recordsId;
    private String sRoadTaskId;
    private String sRoadId;
    private String taskName;
    private String prescriptionNum;
    private String createUserId;
    private String createUserName;


    @CBKJDecryptField
    private String patientCardNumber;
    @Schema(description="患者用户id")
    private String patientId;
    @Schema(description="患者性别")
    private String patientSex;


    @CBKJDecryptField
    private String wxNiceName;



    @CBKJDecryptField
    @Schema(description="患者名字")
    private String patientName;


    @CBKJDecryptField
    @Schema(description="患者手机号")
    private String patientMobile;


    @Schema(description="患者手年龄")
    private String patientAge;

    private String appId;
    private String insId;
    private String insCode;
    private String insName;
    private String  deptCode;
    private String  deptId;
    private String  deptName;
}
