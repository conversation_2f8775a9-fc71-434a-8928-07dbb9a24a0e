package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.beans.business.DisRe;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 接收创建的预诊单子 信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema
public class LookPaperRecive implements Serializable {

    @Schema(description =  "中医疾病名称（多个/拼接）")
    private String chineseDisName;

//    @Schema(description =  "疾病Id（多个/拼接）")
//    private String disIds;
    @Schema(description =  "西医疾病名称（多个/拼接)")
    private String westDisName;
    @Schema(description =  "问诊单类型（1 专病预诊单   2、全科）如果是随访 3.随访问卷4.自测量表")
    private String diaType;

    @Schema(description =  "预诊单id--修改必须带")
    private String diaId;

    @Schema(description =  "1.预诊2随访",required = true)
    private String formType;


//    @Schema(description =  "随访问卷类型（1.随访问卷2.自测量表）,随访必传这个字段")
//    private String formSuiFangType;

    @Schema(description =  "2随访名称")
    private String formName;

    @Schema(description =  "表单编码")
    private String formCode;
    private String questionUnit;

//    private List<TPreDiagnosisDisMapping> tDisease;

    private List<DisRe> chineseDis;
    private List<DisRe> chineseDisDiagnosisList;
    private List<DisRe> westDis;

    @Schema(description =  "题目列表")
    private List<LookPaperQuestionMain> questionMobilesList;


    @Schema(description =  "预诊随访问卷生成二维码地址")
    private String hisDiagnosisFormUrl;

    @Schema(description =  "是否展示大模型：0开1关")
    private Integer cueWordStatus;
}
