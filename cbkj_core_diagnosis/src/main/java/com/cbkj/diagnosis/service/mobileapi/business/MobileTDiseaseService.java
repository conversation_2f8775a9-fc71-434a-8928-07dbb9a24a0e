package com.cbkj.diagnosis.service.mobileapi.business;

import com.cbkj.diagnosis.beans.business.TDisease;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisForm;
import com.cbkj.diagnosis.mapper.business.TDiseaseMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import com.cbkj.diagnosis.service.common.strategy.record.impl.TDiseaseRes;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo;
import com.cbkj.diagnosis.service.common.vo.MobileDisease;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MobileTDiseaseService {

    private final TDiseaseMapper tDiseaseMapper;
    private final TPreDiagnosisFormMapper tPreDiagnosisFormMapper;

    @Autowired
    MobileTDiseaseService(TDiseaseMapper tDiseaseMapper,TPreDiagnosisFormMapper tPreDiagnosisFormMapper) {
        this.tDiseaseMapper = tDiseaseMapper;
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
    }




    public MobileDisease getMobileDisease(String nameWord) {
        MobileDisease mobileDisease = new MobileDisease();
        TDisease tDisease = new TDisease();
        tDisease.setDisType("1");
        tDisease.setDisName(nameWord);
        List<TDiseaseRes> a = tDiseaseMapper.getListByTDisease(tDisease);
        tDisease.setDisType("2");
        List<TDiseaseRes> b = tDiseaseMapper.getListByTDisease(tDisease);

        TPreDiagnosisForm tPreDiagnosisForm = new TPreDiagnosisForm();
        tPreDiagnosisForm.setDiaType("2");
        TPreDiagnosisForm aa = tPreDiagnosisFormMapper.getobjectByDiagnosisForm(tPreDiagnosisForm);
        if (aa !=null){
            mobileDisease.setDiaId(aa.getDiaId());
        }

        mobileDisease.setChineseDisease(a);
        mobileDisease.setWestDisease(b);

        return mobileDisease;
    }
}
