package com.cbkj.diagnosis.service.mobileapi.business;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.cbkj.diagnosis.beans.business.TDisease;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisForm;
import com.cbkj.diagnosis.mapper.business.TDiseaseMapper;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper;
import com.cbkj.diagnosis.service.common.strategy.record.impl.TDeptRes;
import com.cbkj.diagnosis.service.common.strategy.record.impl.TDiseaseRes;
import com.cbkj.diagnosis.service.common.vo.DiseaseVo;
import com.cbkj.diagnosis.service.common.vo.MobileDisease;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MobileTDiseaseService {

    private final TDiseaseMapper tDiseaseMapper;
    private final TPreDiagnosisFormMapper tPreDiagnosisFormMapper;

    @Autowired
    MobileTDiseaseService(TDiseaseMapper tDiseaseMapper,TPreDiagnosisFormMapper tPreDiagnosisFormMapper) {
        this.tDiseaseMapper = tDiseaseMapper;
        this.tPreDiagnosisFormMapper = tPreDiagnosisFormMapper;
    }


    public MobileDisease getMobileDisease(String nameWord) {
        MobileDisease mobileDisease = new MobileDisease();
        TDisease tDisease = new TDisease();
        tDisease.setDisType("1");
        tDisease.setDisName(nameWord);
        List<TDiseaseRes> a = tDiseaseMapper.getListByTDisease(tDisease);
        tDisease.setDisType("2");
        List<TDiseaseRes> b = tDiseaseMapper.getListByTDisease(tDisease);

        List<TDeptRes> c = tDiseaseMapper.getListByDept(tDisease);

        TPreDiagnosisForm tPreDiagnosisForm = new TPreDiagnosisForm();
        tPreDiagnosisForm.setDiaType("2");
        TPreDiagnosisForm aa = tPreDiagnosisFormMapper.getobjectByDiagnosisForm(tPreDiagnosisForm);
        if (aa !=null){
            mobileDisease.setDiaId(aa.getDiaId());
        }

        mobileDisease.setChineseDisease(a);
        mobileDisease.setWestDisease(b);
        mobileDisease.setDeptDisease(mergeTDeptRes(c));

        return mobileDisease;
    }

    public List<TDeptRes> mergeTDeptRes(List<TDeptRes> list) {
        Map<String, List<TDeptRes>> deptMap = list.stream()
                .collect(Collectors.groupingBy(TDeptRes::getDeptName));
        List<TDeptRes> mergedList = new ArrayList<>();
        deptMap.forEach((deptName, deptList) -> {
            TDeptRes mergedDept = new TDeptRes();
            mergedDept.setDeptName(deptName);
            mergedDept.setDeptDiaList(deptList);
            if(StringUtils.isNotEmpty(deptName)){
                mergedList.add(mergedDept);
            }
        });

        return mergedList;
    }
}
