package com.cbkj.diagnosis.service;

import com.cbkj.diagnosis.beans.business.TRecordLargeModel;
import com.cbkj.diagnosis.service.business.TRecordLargeModelService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/11 10:14
 * @Version 1.0
 */
@Service
public class AIModeltLogService {

    private final TRecordLargeModelService tRecordLargeModelService;

    public AIModeltLogService(TRecordLargeModelService tRecordLargeModelService) {
        this.tRecordLargeModelService = tRecordLargeModelService;
    }
    @Async("myAsyncThreadPool")
    public void addLog(TRecordLargeModel tRecordLargeModel) {
        tRecordLargeModelService.save(tRecordLargeModel);
    }
}
