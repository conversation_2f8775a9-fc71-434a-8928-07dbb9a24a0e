package com.cbkj.diagnosis.service.statistics;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMapping;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisDisMappingServiceImpl;
import com.cbkj.diagnosis.service.statistics.impl.StatisticsErverDayDisServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> statistics_erver_day_dis
 * @Description TODO
 * @Date 2024/12/30 15:08
 * @Version 1.0
 */
@Slf4j
@Service
public class StatisticsDis {
    private final StatisticsErverDayDisServiceImpl statisticsErverDayDisService;
    private final TPreDiagnosisDisMappingServiceImpl tPreDiagnosisDisMappingService;

    public StatisticsDis(StatisticsErverDayDisServiceImpl statisticsErverDayDisService, TPreDiagnosisDisMappingServiceImpl tPreDiagnosisDisMappingService) {
        this.statisticsErverDayDisService = statisticsErverDayDisService;
        this.tPreDiagnosisDisMappingService = tPreDiagnosisDisMappingService;
    }

    /**
     *
     * @param recId
     * @param diaId
     * @param appId
     * @param insCode
     * @param insId
     * @param insName
     * @param type 1.预诊数量 2.随诊数量 3.有效病历(手动完成病例标记结案的数量)
     */
    @Async
    public void statisticsDis(String recId, String diaId,String appId,String insCode ,String insId,String insName,String type) {
        log.info("监测病种(预诊、随访数量)统计表recId= {}", recId);
        QueryWrapper<TPreDiagnosisDisMapping> mappingQueryWrapper = new QueryWrapper<>();
        mappingQueryWrapper.eq("dia_id", diaId).eq("dis_type","1");
        List<TPreDiagnosisDisMapping> list1 = tPreDiagnosisDisMappingService.list(mappingQueryWrapper);
        if (!list1.isEmpty()) {
            ArrayList<StatisticsErverDayDis> statisticsDiagnosisDics = new ArrayList<>();
            /**
             * 取出当前问卷的疾病ids
             */
            for (int i = 0; i < list1.size(); i++) {
                StatisticsErverDayDis erverDayDis = new StatisticsErverDayDis();
                erverDayDis.setAppId(appId);
                erverDayDis.setInsCode(insCode);
                erverDayDis.setInsId(insId);
                erverDayDis.setInsName(insName);
                erverDayDis.setDisId(list1.get(i).getDisId());
                erverDayDis.setDisCode(list1.get(i).getDisCode());
                erverDayDis.setDisName(list1.get(i).getDisName());
                erverDayDis.setCreateTime(new Date());
                statisticsDiagnosisDics.add(erverDayDis);
            }
            statisticsErverDayDisService.writeReadFromRedis(statisticsDiagnosisDics, recId, type);
        }
    }
}
