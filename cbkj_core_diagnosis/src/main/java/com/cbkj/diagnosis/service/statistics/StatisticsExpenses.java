package com.cbkj.diagnosis.service.statistics;

import com.cbkj.diagnosis.beans.business.SysIns;
import com.cbkj.diagnosis.beans.health.MedicalPatientRecordCost;
import com.cbkj.diagnosis.beans.health.MedicalRecords;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayExpenses;
import com.cbkj.diagnosis.mapper.business.SysInsMapper;
import com.cbkj.diagnosis.mapper.health.MedicalPatientRecordCostMapper;
import com.cbkj.diagnosis.mapper.health.MedicalRecordsMapper;
import com.cbkj.diagnosis.service.statistics.impl.StatisticsErverDayExpensesServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/6 10:37
 * @Version 1.0
 */
@Slf4j
@Service
public class StatisticsExpenses {

    private final MedicalRecordsMapper medicalRecordsMapper;

    private final MedicalPatientRecordCostMapper medicalPatientRecordCostMapper;

    private final SysInsMapper sysInsMapper;

    private final StatisticsErverDayExpensesServiceImpl statisticsErverDayExpensesService;

    public StatisticsExpenses(MedicalRecordsMapper medicalRecordsMapper, MedicalPatientRecordCostMapper medicalPatientRecordCostMapper, SysInsMapper sysInsMapper, StatisticsErverDayExpensesServiceImpl statisticsErverDayExpensesService) {
        this.medicalRecordsMapper = medicalRecordsMapper;
        this.medicalPatientRecordCostMapper = medicalPatientRecordCostMapper;
        this.sysInsMapper = sysInsMapper;
        this.statisticsErverDayExpensesService = statisticsErverDayExpensesService;
    }

    @Async
    public void writeReadFromRedis(String recordsId, String appId,String insCode ,String insName,String patientId) {
        log.info("患者答题选项费用事件开始写入统计表recordsId= {}",recordsId);
        MedicalRecords objectById = medicalRecordsMapper.getObjectById(recordsId);
        if (objectById == null){
            log.error("患者答题选项费用事件写入统计表错误：病历对象不存在。recordsId= {}",recordsId);
            return;
        }
        if (StringUtils.isNotBlank(objectById.getChineseDisId())){
            SysIns sysIns = new SysIns();
            sysIns.setStatus("0");
            sysIns.setInsCode(insCode);
            sysIns.setAppId(appId);
            sysIns.setInsName(insName);
            SysIns countByIns = sysInsMapper.getCountByIns(sysIns);

            ArrayList<StatisticsErverDayExpenses> statisticsDiagnosisDics = new ArrayList<>();
            //获取medical_patient_record_cost表中的分类
            List<MedicalPatientRecordCost> recordCostByRecordId = medicalPatientRecordCostMapper.getRecordCostByRecordId(recordsId);
            if (recordCostByRecordId != null && !recordCostByRecordId.isEmpty()){
                recordCostByRecordId.forEach(medicalPatientRecordCost -> {

                    if (StringUtils.isNotBlank(medicalPatientRecordCost.getMedicalPatientRecordBigType()) && StringUtils.isNotBlank(medicalPatientRecordCost.getMedicalPatientRecordItemType())){
                        //统计患者诊断费用
                        StatisticsErverDayExpenses statisticsErverDayExpenses = new StatisticsErverDayExpenses();
                        statisticsErverDayExpenses.setAppId(appId);
                        statisticsErverDayExpenses.setInsCode(insCode);
                        statisticsErverDayExpenses.setInsName(insName);
                        if (countByIns != null){
                            statisticsErverDayExpenses.setInsId(countByIns.getInsId());
                        }
                        statisticsErverDayExpenses.setDisCode(objectById.getChineseDisCode());
                        statisticsErverDayExpenses.setDisName(objectById.getChineseDisName());
                        statisticsErverDayExpenses.setDisId(objectById.getChineseDisId());
                        statisticsErverDayExpenses.setCreateTime(new Date());
                        statisticsErverDayExpenses.setType(Integer.valueOf(medicalPatientRecordCost.getMedicalPatientRecordBigType()));
                        statisticsDiagnosisDics.add(statisticsErverDayExpenses);
                    }
                });
                if (!statisticsDiagnosisDics.isEmpty()){
                    statisticsErverDayExpensesService.writeReadFromRedis(statisticsDiagnosisDics);
                }
            }

        }

    }
}
