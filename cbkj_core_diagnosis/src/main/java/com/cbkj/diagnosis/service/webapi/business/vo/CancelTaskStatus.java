package com.cbkj.diagnosis.service.webapi.business.vo;

//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class CancelTaskStatus {
@Schema(description="")
    private String taskExcuteStatus;
}
