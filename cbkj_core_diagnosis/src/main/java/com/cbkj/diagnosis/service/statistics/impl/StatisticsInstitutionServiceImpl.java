//package com.cbkj.diagnosis.service.statistics.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.cbkj.diagnosis.beans.statistics.StatisticsInstitution;
//import com.cbkj.diagnosis.mapper.statistics.StatisticsInstitutionMapper;
//import com.cbkj.diagnosis.service.statistics.StatisticsInstitutionService;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * <p>
// *  服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2024-01-26
// */
//@Service
//public class StatisticsInstitutionServiceImpl extends ServiceImpl<StatisticsInstitutionMapper, StatisticsInstitution> implements StatisticsInstitutionService {
//
//    @Override
//    public List<StatisticsInstitution> getInsList() {
//        QueryWrapper<StatisticsInstitution> statisticsInstitutionQueryWrapper = new QueryWrapper<>();
//        List<StatisticsInstitution> statisticsInstitutions = baseMapper.selectList(statisticsInstitutionQueryWrapper);
//        return statisticsInstitutions;
//    }
//}
