package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class XiaoShanPhoneSuiFangStatisticsInfo {

    @Schema(description="昨日完成随访（人）")
    private Long yesterDaySuiFangFinishPeopleNumb;
    @Schema(description="昨日新增待随访（人）")
    private Long yesterDaySuiFangAddPeopleNumb;

    @Schema(description="今日待随访（人）")
    private Long todayDaySuiFangWaitPeopleNumb;

    @Schema(description="当前待随访总数（人）")
    private Long currentDaySuiFangTotalPeopleNumb;
}
