package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class YesterDaySuiFangFinishPeopleNumbSearch {

    private String startDate;
    private String endDate;

    private String doctorId;
    private Integer phoneStatus;


//    private Integer notPhoneStatus;
}
