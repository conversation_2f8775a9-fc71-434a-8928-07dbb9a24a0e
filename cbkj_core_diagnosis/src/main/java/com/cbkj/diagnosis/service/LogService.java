package com.cbkj.diagnosis.service;


import com.cbkj.diagnosis.beans.SysLogInterface;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.SysLogInterfaceMapper;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
@Transactional(rollbackFor = Exception.class)
public class LogService {

    private final SysLogInterfaceMapper logInterfaceMapper;

    public LogService(SysLogInterfaceMapper logInterfaceMapper) {
        this.logInterfaceMapper = logInterfaceMapper;
    }


    /**
     * 插入新数据
     *
     * @param sysLogInterface
     * @return
     * @throws Exception
     */
    public ResEntity insert(SysLogInterface sysLogInterface) {

        long rows = logInterfaceMapper.insert(sysLogInterface);
        if (rows > 0) {
            return new ResEntity(true, Constant.SUCCESS_DX, sysLogInterface);
        }
        return new ResEntity(false, "保存失败，数据库异常！！", null);
    }








}
