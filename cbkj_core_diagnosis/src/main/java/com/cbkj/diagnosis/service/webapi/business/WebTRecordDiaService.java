package com.cbkj.diagnosis.service.webapi.business;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TPropagandaEdu;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.beans.business.TRecordDiaDimension;
import com.cbkj.diagnosis.beans.business.TRecordUpdateInfo;
import com.cbkj.diagnosis.common.utils.IDUtil;
import com.cbkj.diagnosis.mapper.business.TPropagandaEduMapper;
import com.cbkj.diagnosis.mapper.business.TRecordDiaMapper;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.mapper.business.TRecordUpdateInfoMapper;
import com.cbkj.diagnosis.service.business.TRecordDiaDimensionService;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategyFactory;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.service.webapi.business.vo.GetPrePaperV2_1_0;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class WebTRecordDiaService {


    private TRecordDiaMapper tRecordDiaMapper;

    private RecordDiaStrategyFactory recordDiaStrategyFactory;

    private final TPropagandaEduMapper tPropagandaEduMapper;
    private final TRecordDiaDimensionService tRecordDiaDimensionService;
    @Autowired
    private TRecordUpdateInfoMapper tRecordUpdateInfoMapper;

    WebTRecordDiaService(TRecordDiaMapper tRecordDiaMapper, RecordDiaStrategyFactory recordDiaStrategyFactory, TPropagandaEduMapper tPropagandaEduMapper, TRecordDiaDimensionService tRecordDiaDimensionService) {
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.recordDiaStrategyFactory = recordDiaStrategyFactory;
        this.tPropagandaEduMapper = tPropagandaEduMapper;
        this.tRecordDiaDimensionService = tRecordDiaDimensionService;
    }


    /**
     * 加载某条数据
     *
     * @param recId
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    public ResEntity<ArrayList<QuestionMain>> getPrePaper(String recId) {

        if (StringUtils.isBlank(recId)) {
//            return ResEntity.entity(false, "recId参数不能为空哦", null);
            return new ResEntity<>(false, "recId参数不能为空哦", null);
        }
        List<TRecordDia> objectByRecId = tRecordDiaMapper.getObjectByRecId(recId);
        ArrayList<QuestionMain> list = new ArrayList<>();
        for (TRecordDia tRecordDia : objectByRecId) {
            list.add(recordDiaStrategyFactory.getNormalParamStrategy(tRecordDia.getQuestionType()).transRecordDia(tRecordDia));
        }
        QueryWrapper<TRecordDiaDimension> tRecordDiaDimensionQueryWrapper = new QueryWrapper<>();
        tRecordDiaDimensionQueryWrapper.eq("t_record", recId);
        List<TRecordDiaDimension> list1 = tRecordDiaDimensionService.list(tRecordDiaDimensionQueryWrapper);

        return new ResEntity<>(true, Constant.SUCCESS_DX, list);
    }

    public ResEntity<GetPrePaperV2_1_0> getPrePaperV2_1_0(String recId) {
        GetPrePaperV2_1_0 getPrePaperV210 = new GetPrePaperV2_1_0();
        if (StringUtils.isBlank(recId)) {
//            return ResEntity.entity(false, "recId参数不能为空哦", null);
            return new ResEntity<>(false, "recId参数不能为空哦", null);
        }
        List<TRecordDia> objectByRecId = tRecordDiaMapper.getObjectByRecId(recId);
        ArrayList<QuestionMain> list = new ArrayList<>();
        for (TRecordDia tRecordDia : objectByRecId) {
            list.add(recordDiaStrategyFactory.getNormalParamStrategy(tRecordDia.getQuestionType()).transRecordDia(tRecordDia));
        }
        QueryWrapper<TRecordDiaDimension> tRecordDiaDimensionQueryWrapper = new QueryWrapper<>();
        tRecordDiaDimensionQueryWrapper.eq("t_record", recId);
        List<TRecordDiaDimension> list1 = tRecordDiaDimensionService.list(tRecordDiaDimensionQueryWrapper);
        getPrePaperV210.setTRecordDiaDimensionList(list1);
        getPrePaperV210.setQuestionMainArrayList(list);
        return new ResEntity<>(true, Constant.SUCCESS_DX, getPrePaperV210);
    }

    @Transactional
    public ResEntity updatePrePaper(List<TRecordDia> list,String name) {
        if (list != null && list.size() > 0) {
            TRecordUpdateInfo info = new TRecordUpdateInfo();
            info.setId(IDUtil.getID());
            info.setRecId(list.get(0).getRecId());
            info.setCreateDate(new Date());
            info.setNewRecId(IDUtil.getID());
            info.setCreateName(name);
            tRecordUpdateInfoMapper.insert(info);
            for (int i = 0; i < list.size(); i++) {
                TRecordDia tRecordDia = list.get(i);
                tRecordDia.setRecId(info.getNewRecId());
            }
            tRecordDiaMapper.insertList(list);
        }
        return ResEntity.success();
    }


    /**
     * 插入新数据
     *
     * @param tRecordDia
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TRecordDia tRecordDia) {

        //tRecordDia.setDiaRecId(IDUtil.getID());
        long rows = tRecordDiaMapper.insert(tRecordDia);

        return ResEntity.success(tRecordDia);
    }


    /**
     * 修改
     *
     * @param tRecordDia
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TRecordDia tRecordDia) {

        long rows = tRecordDiaMapper.updateByPrimaryKey(tRecordDia);

        return ResEntity.success(tRecordDia);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tRecordDiaMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    public Object eduDetails(Integer tPropagandaEduId) {
        TPropagandaEdu objectById = tPropagandaEduMapper.getObjectById(tPropagandaEduId + "");
        return objectById;
    }

}
