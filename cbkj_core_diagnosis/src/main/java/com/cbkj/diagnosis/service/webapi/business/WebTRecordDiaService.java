package com.cbkj.diagnosis.service.webapi.business;

import com.cbkj.diagnosis.beans.business.TPropagandaEdu;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.mapper.business.TPropagandaEduMapper;
import com.cbkj.diagnosis.mapper.business.TRecordDiaMapper;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategyFactory;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class WebTRecordDiaService {


    private TRecordDiaMapper tRecordDiaMapper;

    private RecordDiaStrategyFactory recordDiaStrategyFactory;

    private final TPropagandaEduMapper tPropagandaEduMapper;

    WebTRecordDiaService(TRecordDiaMapper tRecordDiaMapper, RecordDiaStrategyFactory recordDiaStrategyFactory, TPropagandaEduMapper tPropagandaEduMapper) {
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.recordDiaStrategyFactory = recordDiaStrategyFactory;
        this.tPropagandaEduMapper = tPropagandaEduMapper;
    }





    /**
     * 加载某条数据
     *
     * @param diaRecId
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    public ResEntity<ArrayList<QuestionMain>> getPrePaper(String recId) {

        if (StringUtils.isBlank(recId)) {
//            return ResEntity.entity(false, "recId参数不能为空哦", null);
        return new ResEntity<>(false, "recId参数不能为空哦", null);
        }
        List<TRecordDia> objectByRecId = tRecordDiaMapper.getObjectByRecId(recId);
        ArrayList<QuestionMain> list = new ArrayList<>();
        for (TRecordDia tRecordDia : objectByRecId) {
            list.add(recordDiaStrategyFactory.getNormalParamStrategy(tRecordDia.getQuestionType()).transRecordDia(tRecordDia));
        }
        return new ResEntity<>(true, Constant.SUCCESS_DX, list);
    }



    /**
     * 插入新数据
     *
     * @param tRecordDia
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TRecordDia tRecordDia) {

        //tRecordDia.setDiaRecId(IDUtil.getID());
        long rows = tRecordDiaMapper.insert(tRecordDia);

        return ResEntity.success(tRecordDia);
    }


    /**
     * 修改
     *
     * @param tRecordDia
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TRecordDia tRecordDia) {

        long rows = tRecordDiaMapper.updateByPrimaryKey(tRecordDia);

        return ResEntity.success(tRecordDia);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tRecordDiaMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    public Object eduDetails(Integer tPropagandaEduId) {
        TPropagandaEdu objectById = tPropagandaEduMapper.getObjectById(tPropagandaEduId + "");
        return objectById;
    }

}
