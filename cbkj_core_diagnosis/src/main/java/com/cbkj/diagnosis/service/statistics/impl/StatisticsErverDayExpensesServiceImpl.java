package com.cbkj.diagnosis.service.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayEffect;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.beans.statistics.BlockFiveDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayExpenses;
import com.cbkj.diagnosis.common.enums.MedicalExpenseType;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayExpensesMapper;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayExpensesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Slf4j
@Service
public class StatisticsErverDayExpensesServiceImpl extends ServiceImpl<StatisticsErverDayExpensesMapper, StatisticsErverDayExpenses> implements StatisticsErverDayExpensesService {

    @Override
    public List<BlockFiveDetail> getBlockFiveList(StatisticsVo statisticsVo) {
        StatisticsErverDayExpensesMapper base = baseMapper;
        List<BlockFiveDetail> blockFiveList = base.getBlockFiveList(statisticsVo);
        blockFiveList.forEach(blockFiveDetail -> {
            blockFiveDetail.setX(MedicalExpenseType.getNameByCode(Integer.parseInt(blockFiveDetail.getX())));
        });
        return blockFiveList;
    }
    @Transactional(rollbackFor = Exception.class)
    public void writeReadFromRedis(List<StatisticsErverDayExpenses> statisticsErverDayExpensesList) {

        List<StatisticsErverDayExpenses> updateList = new ArrayList<>();
        List<StatisticsErverDayExpenses> insertList = new ArrayList<>();
        for (StatisticsErverDayExpenses dayDis : statisticsErverDayExpensesList) {
            QueryWrapper<StatisticsErverDayExpenses> wrapper = new QueryWrapper<>();
            //获取今天年月日字符串
            String dateFormats = DateUtil.getDateFormats("yyyy-MM-dd", new Date());
            wrapper.eq("create_time", dateFormats)
            .eq("app_id", dayDis.getAppId())
            .eq("ins_code", dayDis.getInsCode()).eq("type", dayDis.getType())
            .eq("dis_code", dayDis.getDisCode());
            StatisticsErverDayExpenses one = getOne(wrapper);
            if (one != null) {
                one.setNum(one.getNum()+1);
                updateList.add(one);
            } else {
                dayDis.setNum(1);
                boolean temp = false;
                for (StatisticsErverDayExpenses erverDayDis1 : insertList) {
                    if (Objects.equals(erverDayDis1.getType(), dayDis.getType()) && erverDayDis1.getDisCode().equals(dayDis.getDisCode()) && erverDayDis1.getAppId().equals(dayDis.getAppId()) && erverDayDis1.getInsCode().equals(dayDis.getInsCode())) {
                        erverDayDis1.setNum(erverDayDis1.getNum() + 1);
                        temp = true;
                    }
                }
                if (!temp){
                    insertList.add(dayDis);
                }

            }
        }
        if (!insertList.isEmpty()){
            saveOrUpdateBatch(insertList);
        }
        if (!updateList.isEmpty()){
            updateBatchById(updateList);
        }
        log.info("患者诊疗费用统计统计结束");


    }
}
