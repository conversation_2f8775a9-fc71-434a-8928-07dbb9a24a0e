package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@NoArgsConstructor
@Schema
public class PaperListReVo implements Serializable {

    @Schema(description =  "疾病id")
    private String disId;

    @Schema(description =  "1.预诊2随访" , required = true)
    private String formType;

    @Schema(description =  "问诊单类型（1 专病预诊单 2、全科）如果是随访 3.随访问卷4.自测量表" )
    private String diaType;

    @Schema(description =  "随访名字")
    private String formName;

    @Schema(hidden = true)
    private String[] sroadIds;


    private String sroadIdsstr;

    private Boolean showStatus;

    public String getDisId() {
        return disId;
    }

    public void setDisId(String disId) {
        this.disId = disId;
    }

    public String getFormType() {
        return formType;
    }

    public void setFormType(String formType) {
        this.formType = formType;
    }

    public String getDiaType() {
        return diaType;
    }

    public void setDiaType(String diaType) {
        this.diaType = diaType;
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public String[] getSroadIds() {
        return sroadIds;
    }

    public void setSroadIds(String[] sroadIds) {
        this.sroadIds = sroadIds;
    }

    public String getSroadIdsstr() {
        return sroadIdsstr;
    }

    public void setSroadIdsstr(String sroadIdsstr) {
        this.sroadIdsstr = sroadIdsstr;
    }



    public void setShowStatus(boolean showStatus) {
        this.showStatus = showStatus;
    }

    public Boolean getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Boolean showStatus) {
        this.showStatus = showStatus;
    }
}
