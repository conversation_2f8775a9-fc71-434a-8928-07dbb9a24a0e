package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema
public class QuestionOptionSave
{

    @Schema(description =  "选项id")
    private String optionId;

    @Schema(description =  "选项编码")
    private String optionCode;

    @Schema(description =  "问题id")
    private String questionId;
    @Schema(description =  "选项名称")
    private String optionName;
    @Schema(description =  "排序")
    private String optionSort;
    @Schema(description =  "症状id （症状填）")
    private String symptomId;
    @Schema(description =  "选项类型（1文本 2症状）")
    private String optionType;
    @Schema(description =  "选项文字说明")
    private String optionContent;

    @Schema(description =  "选项图片说明")
    private String optionImage;

    @Schema(description =  "选项填空提示语")
    private String optionFillBlank;

    @Schema(description =  "V2.1.0-选项维度分数")
    private Double optionDimensionScore;

    @Schema(description =  "V2.1.0-选项维度分数开关，1开0关")
    private Integer optionDimensionScoreSwitch;

    @Schema(description =  "V2.1.0-是否发送复诊提醒1开0关")
    private Integer followUpVisitStatus;

    @Schema(description =  "V2.1.0-复诊提醒id")
    private Integer followUpVisitId;

    @Schema(description =  "病历转换术语")
    private String optionStructureValue;

    @Schema(description =  "随访事件代码")
    private List<String> optionFollowEventCode;

    @Schema(description =  "选项填空填空是否必填 1.是 0否")
    private Integer optionFillCheck = 0;

    @Schema(description =  "本题该选项设置的跳过的题目列表")
    private List<String> questionSkipSaveList;

    @Schema(description =  "本题该选项设置的子题的题目列表")
    private List<String> questionChildSaveList;

    @Schema(description =  "本题该选项设置的互斥的选项（限制本题的其它选项）列表")
    private List<String> optionMutexList;


}
