package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class SetPatientMarkVo {
    private String patientId;
    @Schema(description =  "是否能自动加入计划0.可以 1.不行")
    private String joinRoadTask;
}
