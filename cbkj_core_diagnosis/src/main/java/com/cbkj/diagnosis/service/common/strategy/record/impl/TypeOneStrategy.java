package com.cbkj.diagnosis.service.common.strategy.record.impl;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.service.common.strategy.record.RecordDiaStrategy;
import com.cbkj.diagnosis.service.common.vo.QuestionMain;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文本
 */
@Component("recordOne")
public class TypeOneStrategy implements RecordDiaStrategy {
    @Override
    public QuestionMain transRecordDia(TRecordDia tRecord) {
        QuestionMain questionMain = new QuestionMain();

        questionMain.setQuestionType(tRecord.getQuestionType());
        questionMain.setQuestionId(tRecord.getQuestionId());
        questionMain.setDiaId(tRecord.getDiaId());
        //questionMain.setDateUnit(tRecord.getDateUnit());
        questionMain.setQuestionNumber(tRecord.getQuestionNumber());
        questionMain.setQuestionName(tRecord.getQuestionName());
        questionMain.setQuestionUnit(tRecord.getQuestionUnit());
        questionMain.setQuestionCode(tRecord.getQuestionCode());

        //文本回答内容
        if (StringUtils.isNotBlank(tRecord.getContent())){
            List<String> strings = JSON.parseArray(tRecord.getContent(), String.class);
            questionMain.setAnswerContent(strings.toArray(new String[]{}));
        }
        //配置选项内容，文本没有选项。
        //ArrayList<QuestionOption> questionOptions = new ArrayList<QuestionOption>();

        return questionMain;
    }
}
