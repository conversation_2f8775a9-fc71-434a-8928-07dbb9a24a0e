package com.cbkj.diagnosis.service.mobileapi.business;

import com.cbkj.diagnosis.beans.business.TRecord;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.IDUtil;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.TRecordMapper;
import com.cbkj.diagnosis.service.common.vo.PreListReVo;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class MobileTRecordService {

    @Autowired
    private TRecordMapper tRecordMapper;

    /**
     * 加载分页数据
     *
     * @param tRecord
     * @param page    分页
     * @return Object
     * <AUTHOR>
     * @date 2023-09-05
     */
//    public Object getPageDatas(PreListReVo preListReVo, Page page) {
//
//        PageHelper.startPage(page.getPage(), page.getLimit());
//        List<TRecord> list = tRecordMapper.getPageListByObjNew(preListReVo);
//        PageHelper.clearPage();
//        return Page.getLayUiTablePageData(list);
//    }

    /**
     * 加载某条数据
     *
     * @param recId
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    public ResEntity findObj(String recId) {

        if (StringUtils.isBlank(recId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TRecord tRecord = tRecordMapper.getObjectById(recId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tRecord);
    }


    /**
     * 插入新数据
     *
     * @param tRecord
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TRecord tRecord) {

        tRecord.setRecId(IDUtil.getID());
        long rows = tRecordMapper.insert(tRecord);

        return ResEntity.success(tRecord);
    }


    /**
     * 修改
     *
     * @param tRecord
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TRecord tRecord) {

        long rows = tRecordMapper.updateByPrimaryKey(tRecord);

        return ResEntity.success(tRecord);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tRecordMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
