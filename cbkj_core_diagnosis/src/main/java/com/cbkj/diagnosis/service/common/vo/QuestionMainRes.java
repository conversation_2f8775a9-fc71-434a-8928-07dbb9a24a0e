package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.utils.StringArrayAdapter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class QuestionMainRes implements Serializable {



    @XmlElement(name = "QUESTIONCODE")
    private Integer questionId;

    @XmlElement(name = "QUESTIONID")
    private String questionCode;

    @XmlElement(name = "QUESTIONNAME")
    @Schema(description =  "题目")
    private String questionName;

    @Schema(description =  "题目单位（只有文本样式有这个值）")
    private String questionUnit;

    @XmlElement(name = "QUESTIONNUMBER")
    @Schema(description =  "题目序号")
    private Integer questionNumber;

    @XmlElement(name = "QUESTIONTYPE")
    @Schema(description =  "题目类型（1文本 2单选 3多选 4时间）")
    private String questionType;

    @XmlElement(name = "ANSWERCONTENT")
    //@XmlList
    @XmlJavaTypeAdapter(StringArrayAdapter.class)
    @Schema(description =  "患者回答文本、单选多选存放处（多选用,分割）")
    private String[] answerContent;

    @XmlElement(name = "ANSWERCONTENTID")
    //@XmlList
    @XmlJavaTypeAdapter(StringArrayAdapter.class)
    @Schema(description =  "患者回答单选多选的id存放处（多选用,分割）")
    private String[] answerContentId;

    @Schema(description =  "患者回答单选的选项填空")
    private String[] optionFillBlank;

    @XmlElement(name = "YEAR")
    @Schema(description =  "患者回答的年")
    private String year;

    @XmlElement(name = "MONTH")
    @Schema(description =  "患者回答的月")
    private String month;

    @XmlElement(name = "DAY")
    @Schema(description =  "患者回答的日")
    private String day;

    @XmlElement(name = "HOUR")
    @Schema(description =  "患者回答的小时")
    private String hour;

    @XmlElement(name = "WEEK")
    private String week;

    @XmlElement(name = "DIAID")
    @Schema(description =  "预诊单id")
    private String diaId;

    public boolean isNoNextItem() {
        return noNextItem;
    }

    public void setNoNextItem(boolean noNextItem) {
        this.noNextItem = noNextItem;
    }

    @Schema(description =  "标志",hidden = true)
    private boolean noNextItem = false;

    @Schema(description =  "答题剩余题目百分比")
    private String questionPercentage;

    @XmlElement(name = "DATEUNIT")
    @Schema(description =  "时间单位（1年 2月 3日 可多选,逗号拼接）")
    private String dateUnit;

    @Schema(description="上级题目id")
    private String masterQuestionId;
    private String recId;
    @XmlElement(name = "optionList")
    @Schema(description =  "问题的选项明细列表")
    List<QuestionOption> questionOptionList;

    private boolean showBar = true;

}
