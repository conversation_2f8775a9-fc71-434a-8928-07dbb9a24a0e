package com.cbkj.diagnosis.service.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cbkj.diagnosis.beans.business.TRecordEvent;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth;
import com.cbkj.diagnosis.beans.statistics.vo.ListJoinDic;
import com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo;
import com.cbkj.diagnosis.beans.statistics.BlockFourDetail;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayAdverse;
import com.cbkj.diagnosis.common.utils.DateUtil;
import com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayAdverseMapper;
import com.cbkj.diagnosis.service.statistics.StatisticsErverDayAdverseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Slf4j
@Service
public class StatisticsErverDayAdverseServiceImpl extends ServiceImpl<StatisticsErverDayAdverseMapper, StatisticsErverDayAdverse> implements StatisticsErverDayAdverseService {

    @Override
    public List<BlockFourDetail> getBlockFourList(StatisticsVo statisticsVo) {
        StatisticsErverDayAdverseMapper base = baseMapper;
        return base.getBlockFourList(statisticsVo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void writeReadFromRedis(List<StatisticsErverDayAdverse> StatisticsErverDayAdverse) {
        List<StatisticsErverDayAdverse> updateList = new ArrayList<>();
        List<StatisticsErverDayAdverse> insertList = new ArrayList<>();
        for (StatisticsErverDayAdverse dayDis : StatisticsErverDayAdverse) {
            QueryWrapper<StatisticsErverDayAdverse> wrapper = new QueryWrapper<>();
            //获取今天年月日字符串
            String dateFormats = DateUtil.getDateFormats("yyyy-MM-dd", new Date());
            wrapper.eq("create_time", dateFormats);
            wrapper.eq("app_id", dayDis.getAppId());
            wrapper.eq("ins_code", dayDis.getInsCode());
            wrapper.eq("dis_code", dayDis.getDisCode());
            StatisticsErverDayAdverse one = getOne(wrapper);
            if (one != null) {
                one.setNum(one.getNum() + 1);
                updateList.add(one);
            }else {
                dayDis.setNum(1);
                boolean temp = false;
                for (StatisticsErverDayAdverse erverDayDis1 : insertList) {
                    if (erverDayDis1.getDisCode().equals(dayDis.getDisCode()) && erverDayDis1.getAppId().equals(dayDis.getAppId()) && erverDayDis1.getInsCode().equals(dayDis.getInsCode())) {
                        erverDayDis1.setNum(erverDayDis1.getNum() + 1);
                        temp = true;
                    }
                }
                if (!temp){
                    insertList.add(dayDis);
                }

//                dayDis.setNum(1);
//                insertList.add(dayDis);
            }
        }
        log.info("updateList size = {}", updateList.size());
        log.info("insertList size = {}", insertList.size());
        if (!insertList.isEmpty()){
            saveOrUpdateBatch(insertList);
        }
        if (!updateList.isEmpty()){
            updateBatchById(updateList);
        }
    }

    public List<TRecordEvent> listJoinDic(ListJoinDic recId) {
        return this.baseMapper.listJoinDic(recId);
    }
}
