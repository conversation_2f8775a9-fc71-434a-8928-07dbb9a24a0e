package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Schema
public class UpdateTaskStatus {
    @Schema(description="0正常 1删除 2 取消")

    private String status;

    @Schema(description="任务id")
    private String sRoadTaskId;
}
