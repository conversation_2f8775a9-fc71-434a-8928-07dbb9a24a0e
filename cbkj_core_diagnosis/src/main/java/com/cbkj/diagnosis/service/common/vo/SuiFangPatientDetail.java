package com.cbkj.diagnosis.service.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@Schema
public class SuiFangPatientDetail {




    @Schema(description =  "患者任务id")
    private String taskPatientsId;

    @Schema(description =  "患者电话随访排期id，可能是空的这个字段。")
    private Long sRoadTaskPatientsPhoneId;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "随访任务时间")
    private Date taskExcuteTime;


//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @Schema(description =  "分配排期给医生的时间")
//    private Date allotTime;

    @Schema(description="1.待执行（未发送）2.已执行（已发送,未读（微信短信））3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 7.已读（微信短信）8.已填写")
    private Integer taskExcuteStatus;

    @Schema(description="随访任务名称")
    private String taskName;

    @Schema(description="随访问卷id")
    private String roadExecuteEventContentId;
//    private String roadExecuteEventContentId;

    @Schema(description="随访问卷名称")
    private String roadExecuteEventContentName;

    @Schema(description="患者随访记录表id")
    private String recId;
    @Schema(description="随访方式")
    private String roadExecuteEventWay;
    @Schema(description="随访小记")
    private String taskPatientsNode;
    @Schema(description="随访事件")
    private List<TodayPatientEvent> todayPatientEventList;

}
