package com.cbkj.diagnosis.service.webapi.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/17 13:39
 * @Version 1.0
 */
@Getter
@Setter
public class RecordAndPatientByConditions {
    private List<String> tWestDiseaseId;
    private List<String> tChineseDiseaseId;
    private List<String> sSymptomId;
    private List<String> sysDeptId;
    private List<String> doctorId;

    private String recordsId;

    private List<RoadTaskCondition> roadTaskConditionList;

    @Schema(description =  "自动入组的条件：就诊开始时间")
    private String recordStartTime;


    @Schema(description =  "自动入组的条件：就诊结束时间")
    private String recordEndTime;
    @Schema(description =  "自动入组条件：就诊当日几天内有预诊信息的患者")
    private Integer limitDiagnosisDaysInfo;

    @Schema(description =  "自动入组条件：几日内重复就诊的患者不入组")
    private Integer limitRepeatRecord;
    @Schema(description="是否过滤不愿意随访的患者0.是 1.否")
    private String joinRoadTask;

    public void setWestDiseaseId(String tW) {
        if (tWestDiseaseId == null && StringUtils.isNotBlank(tW)) {
            tWestDiseaseId = new ArrayList<>();
        }
        tWestDiseaseId.add(tW);
    }

    public void setChineseDisease(String chineseDisease) {
        if (tChineseDiseaseId == null && StringUtils.isNotBlank(chineseDisease)) {
            tChineseDiseaseId = new ArrayList<>();
        }
        tChineseDiseaseId.add(chineseDisease);
    }

    public void setSymptomId(String symptomId) {
        if (sSymptomId == null && StringUtils.isNotBlank(symptomId)) {
            sSymptomId = new ArrayList<>();
        }
        sSymptomId.add(symptomId);
    }

    public void setDeptId(String sD) {
        if (sysDeptId == null && StringUtils.isNotBlank(sD)) {
            sysDeptId = new ArrayList<>();
        }
        sysDeptId.add(sD);
    }

    public void setDoctorId(String dD) {
        if (doctorId == null && StringUtils.isNotBlank(dD)) {
            doctorId = new ArrayList<>();
        }
        if (StringUtils.isNotBlank(dD)) {
            doctorId.add(dD);
        }
    }


}
