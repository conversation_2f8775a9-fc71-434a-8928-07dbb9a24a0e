package com.cbkj.diagnosis.service.statistics;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMapping;
import com.cbkj.diagnosis.beans.business.TRecordEvent;
import com.cbkj.diagnosis.beans.statistics.StatisticsErverDayAdverse;
import com.cbkj.diagnosis.beans.statistics.vo.ListJoinDic;
import com.cbkj.diagnosis.service.business.impl.TPreDiagnosisDisMappingServiceImpl;
import com.cbkj.diagnosis.service.statistics.impl.StatisticsErverDayAdverseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/31 14:30
 * @Version 1.0
 */
@Slf4j
@Service
public class StatisticsAdverse {

    private final StatisticsErverDayAdverseServiceImpl statisticsErverDayAdverseService;
    private final TPreDiagnosisDisMappingServiceImpl tPreDiagnosisDisMappingService;
    public StatisticsAdverse(StatisticsErverDayAdverseServiceImpl statisticsErverDayAdverseService, TPreDiagnosisDisMappingServiceImpl tPreDiagnosisDisMappingService) {
        this.statisticsErverDayAdverseService = statisticsErverDayAdverseService;
        this.tPreDiagnosisDisMappingService = tPreDiagnosisDisMappingService;
    }
//    @Async
    public void writeReadFromRedis(String recId, String diaId,String appId,String insCode ,String insId,String insName,String patientId) {
        log.info("患者答题选项不良反应事件开始写入统计表recId= {}",recId);
//        TAdminInfo currentHr = AdminUtils.getCurrentHr();
        QueryWrapper<TPreDiagnosisDisMapping> wrapper = new QueryWrapper<>();
        wrapper.eq("dia_id", diaId).eq("dis_type","1");
        List<TPreDiagnosisDisMapping> list1 = tPreDiagnosisDisMappingService.list(wrapper);
        if (!list1.isEmpty()) {
            ArrayList<StatisticsErverDayAdverse> statisticsDiagnosisDics = new ArrayList<>();
            ListJoinDic listJoinDic = new ListJoinDic();
            listJoinDic.setRecId(recId);
            listJoinDic.setDiaId(diaId);
            for (int i = 0; i < list1.size(); i++) {
                TPreDiagnosisDisMapping tPreDiagnosisDisMapping = list1.get(i);
//                QueryWrapper<TRecordEvent> tRecordEventQueryWrapper = new QueryWrapper<>();
//                tRecordEventQueryWrapper.eq("rec_id", recId);
                /**
                 * 取出当前问卷患者作答的选项的事件
                 */

                List<TRecordEvent> list = statisticsErverDayAdverseService.listJoinDic(listJoinDic);
                if (!list.isEmpty()) {
                    list.forEach(tRecordEvent -> {
                        StatisticsErverDayAdverse statisticsDiagnosisDic = new StatisticsErverDayAdverse();
                        statisticsDiagnosisDic.setAppId(appId);
                        statisticsDiagnosisDic.setInsCode(insCode);
                        statisticsDiagnosisDic.setInsName(insName);
                        statisticsDiagnosisDic.setInsId(insId);
//                        statisticsDiagnosisDic.setPatientId(currentHr.getUserId());
//                        statisticsDiagnosisDic.setDicId(tRecordEvent.getDicId());
//                        statisticsDiagnosisDic.setDicName(tRecordEvent.getDicName());
//                        statisticsDiagnosisDic.setDicCode(tRecordEvent.getDicCode());
                        statisticsDiagnosisDic.setDisId(tPreDiagnosisDisMapping.getDisId());
                        statisticsDiagnosisDic.setDisName(tPreDiagnosisDisMapping.getDisName());
                        statisticsDiagnosisDic.setDisCode(tPreDiagnosisDisMapping.getDisCode());
                        statisticsDiagnosisDic.setCreateTime(new Date());
                        statisticsDiagnosisDics.add(statisticsDiagnosisDic);
                    });
                }
            }
            if (!statisticsDiagnosisDics.isEmpty()) {
                statisticsErverDayAdverseService.writeReadFromRedis(statisticsDiagnosisDics);

            }

        }

    }

}
