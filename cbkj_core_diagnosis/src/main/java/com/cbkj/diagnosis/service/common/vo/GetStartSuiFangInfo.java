package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/22 10:09
 * @Version 1.0
 */
@Getter
@Setter
@Schema
public class GetStartSuiFangInfo {
    @Schema(description="患者任务明细id")
    private Long taskPatientsId;
    @Schema(description="患者任务明细下的电话随访任务id")
    private Long sroadTaskPatientsPhoneId;





    @CBKJEncryptField
    @CBKJDecryptField
    private String patientName;

    @Schema(description="患者id")
    private String patientId;

    @Schema(description="身份证号码")
    @CBKJEncryptField
    @CBKJDecryptField
    private String patientCardNumber;
    @CBKJEncryptField
    private String patientIdcard;

    @Schema(description="中西医疾病名")
    private String diseaseName;
    @Schema(description =  "时间开始")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startDate;

    @Schema(description =  "时间结束")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endDate;
    private String userId;
    private String recId;
    private Integer phoneStatus;


    @Schema(description="-1过期 0 完成 今日任务也会传1 2 计划中")
    private Integer toDayTaskStatus; ;
}
