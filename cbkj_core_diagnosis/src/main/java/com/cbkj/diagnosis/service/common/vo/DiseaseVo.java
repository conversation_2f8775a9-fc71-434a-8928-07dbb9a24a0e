package com.cbkj.diagnosis.service.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema
@Data
public class DiseaseVo {
    @Schema(description =  "疾病类型（1中医2西医）")
    private String disType;
    private String diaName;
    private String disCode;
    private String disIid;
    @Schema(description =  "是否搜索全部疾病（0不是 1 全部)")
    private String queryAll = "1";
}
