package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class PatientTaskDelete {

    @Schema(description="患者id")
    private String patientId;
    private String sRoadTaskId;


    @Schema(description="患者就诊记录id")
    private String recordsId;

    @Schema(description="1正常 3 .取消执行")
    private Integer taskPatientStatus;
    private Integer taskPatientStatusPast;
}
