package com.cbkj.diagnosis.service.mobileapi.business;

import com.cbkj.diagnosis.beans.business.TPreDiagnosisOption;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMapper;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
@Service
public class MobileTPreDiagnosisOptionService {

    @Autowired
    private TPreDiagnosisOptionMapper tPreDiagnosisOptionMapper;

    /**
     * 加载分页数据
     *
     * @param tPreDiagnosisOption 
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2023-09-05
     */
    public Object getPageDatas(TPreDiagnosisOption tPreDiagnosisOption, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TPreDiagnosisOption> list = tPreDiagnosisOptionMapper.getPageListByObj(tPreDiagnosisOption);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param optionId 
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    public ResEntity findObj(String optionId) {

        if (StringUtils.isBlank(optionId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TPreDiagnosisOption tPreDiagnosisOption = tPreDiagnosisOptionMapper.getObjectById(optionId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tPreDiagnosisOption);
    }


    /**
     * 插入新数据
     *
     * @param tPreDiagnosisOption 
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TPreDiagnosisOption tPreDiagnosisOption){

        //tPreDiagnosisOption.setOptionId(IDUtil.getID());
        long rows = tPreDiagnosisOptionMapper.insert(tPreDiagnosisOption);

        return ResEntity.success(tPreDiagnosisOption);
    }


    /**
     * 修改
     *
     * @param tPreDiagnosisOption 
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TPreDiagnosisOption tPreDiagnosisOption) {

        long rows = tPreDiagnosisOptionMapper.updateByPrimaryKey(tPreDiagnosisOption);

        return ResEntity.success(tPreDiagnosisOption);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2023-09-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tPreDiagnosisOptionMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
