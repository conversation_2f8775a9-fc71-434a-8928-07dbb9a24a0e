package com.cbkj.diagnosis.service.webapi.business.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class GetRecordAndPatientByConditions implements Serializable {

    @Schema(description="就诊记录id")
    private String recordsId;




    @Schema(description =  "患者id")
    private String patientId;
    @CBKJDecryptField
    @CBKJEncryptField
    @Schema(description =  "患者名字")
    private String patientName;

    @Schema(description =  "患者年龄")
    private String patientAge;

    @Schema(description =  "患者性别")
    private String patientSex;
    @CBKJDecryptField
    @CBKJEncryptField
    @Schema(description =  "患者身份证")
    private String patientCardNumber;
    private String doctorName;
    private String doctorId;
    private String appId;
    private String insId;
    private String insName;
    private String insCode;
    private String deptId;
    private String deptCode;
    private String deptName;


//    @Schema(description =  "医生id",hidden = true)
//    private String doctorId;
//
//    @Schema(description =  "医生姓名",hidden = true)
//    private String doctorName;




}
