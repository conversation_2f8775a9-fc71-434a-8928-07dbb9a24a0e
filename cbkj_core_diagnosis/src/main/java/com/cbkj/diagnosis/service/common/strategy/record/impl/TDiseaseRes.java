package com.cbkj.diagnosis.service.common.strategy.record.impl;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Schema
public class TDiseaseRes {
    @Schema
    private String disId;

    @Schema(description =  "预诊单id")
    private String diaId;

    @Schema
    private String disName;

    @Schema(description =  "疾病编码")
    private String disCode;
}
