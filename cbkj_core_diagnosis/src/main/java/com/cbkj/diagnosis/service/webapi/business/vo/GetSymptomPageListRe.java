package com.cbkj.diagnosis.service.webapi.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Schema()
public class GetSymptomPageListRe {


    @Schema(description =  "症状类别id")
    private String symptomClassId;


    @Schema(description =  "症状名称")
    private String symptomName;
}
