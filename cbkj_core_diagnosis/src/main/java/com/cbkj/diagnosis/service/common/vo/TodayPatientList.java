package com.cbkj.diagnosis.service.common.vo;

import com.cbkj.diagnosis.common.interceptor.advice.CBKJDecryptField;
import com.cbkj.diagnosis.common.interceptor.advice.CBKJEncryptField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Schema
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TodayPatientList {

    @Schema(description =  "患者任务")
    private Long taskPatientsId;

    @Schema(description =  "电话随访患者任务的排期扩展信息id")
    private Long sRoadTaskPatientsPhoneId;
    private Long roadExecuteId;

    @Schema(description =  "患者id")
    private String patientId;
    private String sRoadTaskId;

    @Schema(description =  "末次诊断中医疾病名称")
    private String lastChineseDisName;
    @Schema(description =  "末次诊断西医疾病名称")
    private String lastWestDisName;

    @Schema(description="就诊记录id")
    private String recordsId;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者名字")
    private String patientName;

    @Schema(description =  "患者年龄")
    private String patientAge;

    @Schema(description =  "患者性别")
    private String patientSex;
    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者身份证")
    private String patientCardNumber;

    @CBKJEncryptField
    @CBKJDecryptField
    @Schema(description =  "患者电话")
    private String patientMobile;

    @Schema(description =  "随访人：医生id")
    private String doctorId;

    @Schema(description =  "随访人：医生名字")
    private String doctorName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "排期的随访日期")
    private Date suiFangTime;
    private Date suiFangFinishTime;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "任务时间")
    private Date taskExcuteTime;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description =  "排期给医生的时间")
    private Date allotTime;

//    @Schema(description="随访总次数")
//    private Integer suiFangTotalNums;
//
//    @Schema(description="随访剩余次数")
//    private Integer suiFangOtherNums;

    @Schema(description="电话随访小记备注")
    private String taskPatientsNode;

    @Schema(description="患者信息备注")
    private String remark;

    @Schema(description="0分配 1 未分配")
    private String allotTaskStatus;

    @Schema(description =  "排期的状态：1.待执行（未发送，未确定电话随访时间）2.已执行（确定电话随访时间）3.取消执行（取消随访） 4.已访未完成 5.空号未通 6.错号未通 ）8.已填写(完成电话随访)")
    private Integer phoneStatus;

    @Schema(description="电话随访任务名称")
    private String taskName;



    @Schema(description="电话随访任务的状态（任务下的路径状态），不是任务路径下的排期的状态")
    private String taskExcuteStatus;
    private String recId;

    @Schema(description="进行随访：电话随访所有排期信息列表")
    public List<TodayPatientDetail> phoneSuiFangAllList;

    @Schema(description="进行随访：返回排期的随访问卷信息")
    private List<StartSuiFangDiaForm> startSuiFangDiaFormList;

    @Schema(description="进行随访：返回患者回答选择的随访问卷标签信息")
    private List<TodayPatientEvent> todayPatientEventList;
}
