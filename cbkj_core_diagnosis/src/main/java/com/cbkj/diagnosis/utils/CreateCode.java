//package com.cbkj.diagnosis.utils;
//
//import com.cbkj.diagnosis.common.utils.Constant;
//import com.github.pagehelper.util.StringUtil;
//import freemarker.template.Configuration;
//import freemarker.template.Template;
//import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
//
//import java.io.File;
//import java.io.FileOutputStream;
//import java.io.OutputStreamWriter;
//import java.sql.*;
//import java.util.*;
//
///**
// * 代码生成器
// */
//public class CreateCode {
//
//    private String sqlName = "cbkj_pre_diagnosis?useSSL=false";//数据库名称
//
//    private String URL = "******************************/";//数据路路径
//
//    private String NAME = "root";  //用户名
//
//    private String PASS = "sayschi_2347";  //密码
//
//    private String DRIVER = "com.mysql.jdbc.Driver";
//
//    //项目名称
//    private String PROJECTNAME = "demo";
////    private String PROJECTNAME = "pre_api/cbkj_web_api";
//
//    //模板路径
////    private String TEMPLATEPATH = "pre_api/cbkj_web_api/src/main/resources/templates/ftl";
//    private String TEMPLATEPATH = "pre_diagnosis_main/cbkj_core_diagnosis/src/main/resources/templates/ftl";
//
//    //服务路径
//    private String SERVICEPATH = PROJECTNAME + "/src/main/java/com/cbkj/diagnosis/service";
//
//    //控制器
//    private String CONTROLLERPATH = PROJECTNAME + "/src/main/java/com/cbkj/diagnosis/controller";
//
//    //实体路径
//    private String BEANSPATH = PROJECTNAME + "/src/main/java/com/cbkj/diagnosis/beans";
//
//    //mapper路径
//    private String MAPPERPATH = PROJECTNAME + "/src/main/java/com/cbkj/diagnosis/mapper";
//
//    //脚本路径
//    private String JSPATH = PROJECTNAME + "/src/main/resources/static/sys";
//
//    //页面路径
//    private String LISTPAGEPATH = PROJECTNAME + "/src/main/resources/views";
//
//    //xml路径
//    private String MAPPERSPATH = PROJECTNAME + "/src/main/resources/mappers";
//
//    //列名集合
//
//
//    private Map<String, String> colnameM = new LinkedHashMap<>();
//
//    private Map<String, String> colTypeM = new LinkedHashMap<>();
//
//    private Map<String, String> javaTypeM = new LinkedHashMap<>();
//
//    private List<String> commentM = new ArrayList<>();
//
//    private String colnames;
//
//    private String tablename;//表名称
//
//    private String class_desc;//表描述（类描述）
//
//    private String entity_ids;//表主键
//
//    private String businessType;//业务类型路径
//
//    private String oldBusinessType;//业务类型路径旧的
//
//    private Map<String, Integer> colSizes = new LinkedHashMap<>(); //列名大小数组
//
//    /**
//     * @param tablename    表名
//     * @param class_desc   表描述
//     * @param entity_ids   主键（跟数据库对应主键对应）
//     * @param businessType 业务类型
//     */
//    public CreateCode(String tablename, String class_desc, String entity_ids, String businessType) {
//
//        this.tablename = tablename;
//        this.class_desc = class_desc;
//        this.entity_ids = entity_ids;
//        if (StringUtil.isNotEmpty(businessType)) {
//            this.oldBusinessType = businessType;
//            this.businessType = "/" + businessType.replaceAll("\\.", "/");
//        }
//        sqlUtil();
//        sqlComment();
//    }
//
//
//    /**
//     * 创建源文件
//     *
//     * @param path
//     * @param content
//     */
//    private static boolean createFile(String path, String content) {
//
//        OutputStreamWriter osw = null;
//        File file = new File(new File(new File("").getAbsolutePath()).getParent() + "/" + path);
//        File fileParent;
//        Long start = System.currentTimeMillis();
//        System.out.println("---------开始执行：" + start);
//        try {
//            fileParent = file.getParentFile();
//            if (!fileParent.exists()) {
//                fileParent.mkdirs();
//            }
//            if (!file.exists()) {
//                file.createNewFile();
//                osw = new OutputStreamWriter(new FileOutputStream(file, false), "UTF-8");
//                osw.write(content);
//                System.out.println(">>执行完成");
//            } else {
//                System.out.println(">>文件已存在");
//            }
//            return true;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        } finally {
//            if (osw != null) {
//                try {
//                    osw.close();
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//            Long end = System.currentTimeMillis();
//            System.out.println("---------执行结束" + end + ",总花费：" + (end - start) + "毫秒");
//        }
//    }
//
//
//    /**
//     * 构建代码
//     *
//     * @param path
//     * @param ftlName
//     * @param map
//     * @return
//     * @throws Exception
//     */
//    private static String getFtlCode(String path, String ftlName, Map map) {
//
//        Configuration configurer = new Configuration(Configuration.VERSION_2_3_22);
//        configurer.setDefaultEncoding("UTF-8");
//        try {
//            configurer.setDirectoryForTemplateLoading(new File(new File(new File("").getAbsolutePath()).getParent() + "/" + path));
//            Template template = configurer.getTemplate(ftlName);
//            String text = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
//            return text;
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.out.println("构建代码获取模板发生异常");
//            return null;
//        }
//    }
//
//    /**
//     * 连接数据库
//     */
//    public void sqlUtil() {
//
//        Connection con = null;
//        String sql = "select * from " + tablename;
//
//        try {
//            StringBuffer colSb = new StringBuffer();
//            con = DriverManager.getConnection(URL + sqlName, NAME, PASS);
//            PreparedStatement pStemt = con.prepareStatement(sql);
//            ResultSetMetaData rsmd = pStemt.getMetaData();
//
//            int size = rsmd.getColumnCount();
//            for (int i = 0; i < size; i++) {
//                String colName = rsmd.getColumnName(i + 1);
//                colSb.append(colName).append(Constant.ENGLISH_COMMA);
//                colnameM.put(colName, StringUtils.underlineToCamel2(colName.toLowerCase()));
//                String type = rsmd.getColumnTypeName(i + 1);
//                if ("INT".equals(type)) {
//                    colTypeM.put(colName, "INTEGER");
//                } else if ("DATETIME".equals(type)) {
//                    colTypeM.put(colName, "TIMESTAMP");
//                } else {
//                    colTypeM.put(colName, type);
//                }
//                javaTypeM.put(colName, sqlType2JavaType(type));
//                colSizes.put(colName, rsmd.getColumnDisplaySize(i + 1));
//
//            }
//            colSb.deleteCharAt(colSb.length() - 1);
//            colnames = colSb.toString();
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.out.println("连接数据失败！！");
//        } finally {
//            try {
//                con.close();
//            } catch (Exception e2) {
//                System.out.println("关闭连接出现异常！！" + e2.getMessage());
//            }
//        }
//    }
//
//    public void sqlComment() {
//
//        Connection con = null;
//        String sql = "select * from " + tablename;
//
//        try {
//            con = DriverManager.getConnection(URL + sqlName, NAME, PASS);
//            PreparedStatement pStemt = con.prepareStatement(sql);
//            ResultSet rs = pStemt.executeQuery("show full columns from " + tablename);
//            while (rs.next()) {
//                String com = rs.getString("Comment");
//                commentM.add(com);
//            }
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.out.println("连接数据失败！！");
//        } finally {
//            try {
//                con.close();
//            } catch (Exception e2) {
//                System.out.println("关闭连接出现异常！！" + e2.getMessage());
//            }
//        }
//    }
//
//    /**
//     * 首字母大写
//     *
//     * @param str
//     * @return
//     */
//    public String indexCap(String str) {
//        char[] chs = str.toCharArray();
//        if (chs[0] >= 97) {
//            if (chs[0] >= 'a' || chs[0] <= 'z') {
//                chs[0] = (char) (chs[0] - 32);
//            }
//        }
//        return new String(chs);
//    }
//
//    /**
//     * 数据库类型跟java类型转换
//     *
//     * @param sqlType
//     * @return
//     */
//    public String sqlType2JavaType(String sqlType) {
//        if (sqlType.equalsIgnoreCase("bit")) {
//            return "Boolean";
//        } else if (sqlType.equalsIgnoreCase("tinyint")) {
//            return "Byte";
//        } else if (sqlType.equalsIgnoreCase("smallint")) {
//            return "Short";
//        } else if (sqlType.equalsIgnoreCase("int") || sqlType.equalsIgnoreCase("integer")) {
//            return "Integer";
//        } else if (sqlType.equalsIgnoreCase("bigint")) {
//            return "Long";
//        } else if (sqlType.equalsIgnoreCase("float")) {
//            return "Float";
//        } else if (sqlType.equalsIgnoreCase("decimal") || sqlType.equalsIgnoreCase("numeric")
//                || sqlType.equalsIgnoreCase("real") || sqlType.equalsIgnoreCase("money")
//                || sqlType.equalsIgnoreCase("smallmoney")) {
//            return "Double";
//        } else if (sqlType.equalsIgnoreCase("varchar") || sqlType.equalsIgnoreCase("char")
//                || sqlType.equalsIgnoreCase("nvarchar") || sqlType.equalsIgnoreCase("nchar")
//                || sqlType.equalsIgnoreCase("text")) {
//            return "String";
//        } else if (sqlType.equalsIgnoreCase("datetime") || sqlType.equalsIgnoreCase("date")) {
//            return "Date";
//        } else if (sqlType.equalsIgnoreCase("image")) {
//            return "Blod";
//        }
//        return "String";
//    }
//
//    public boolean createCode() {
//
//        Map<String, Object> map = new HashMap<>();
//        map.put("entityName", indexCap(StringUtils.underlineToCamel2(tablename)));
//        map.put("tableName", tablename);
//        map.put("maps", colnameM);
//        map.put("colTypeM", colTypeM);
//        map.put("primary", entity_ids);
//        map.put("entityPrimary", indexCap(StringUtils.underlineToCamel2(colnameM.get(entity_ids))));
//        map.put("entityPrimary2", StringUtils.underlineToCamel2(colnameM.get(entity_ids)));
//        map.put("colNames", colnames);
//        map.put("comments", commentM);
//        map.put("javaType", javaTypeM);
//        map.put("desc", class_desc);
//        map.put("lengthM", colSizes);
//        if (StringUtil.isNotEmpty(oldBusinessType)) {
//            map.put("businessPath", "." + oldBusinessType);
//        } else {
//            map.put("businessPath", "");
//        }
//        boolean flog = true;
//        //xml文件创建
//        String mapperXml = getFtlCode(TEMPLATEPATH, "mapperXml.ftl", map);
//        flog = createFile((MAPPERSPATH + "/" + businessType + "/" + map.get("entityName") + "Mapper.xml"), mapperXml);
//        if (flog) {
//            //实体文件
//            String model = getFtlCode(TEMPLATEPATH, "model.ftl", map);
//            flog = createFile((BEANSPATH + "/" + businessType + "/" + map.get("entityName") + ".java"), model);
//        }
//        if (flog) {
//            //mapper文件创建
//            String mapper = getFtlCode(TEMPLATEPATH, "mapper.ftl", map);
//            flog = createFile((MAPPERPATH + "/" + businessType + "/" + map.get("entityName") + "Mapper.java"), mapper);
//        }
//        if (flog) {
//            //service文件
//            String service = getFtlCode(TEMPLATEPATH, "service.ftl", map);
//            flog = createFile((SERVICEPATH + "/" + businessType + "/" + map.get("entityName") + "Service.java"), service);
//        }
//        if (flog) {
//            //控制器文件
//            String controller = getFtlCode(TEMPLATEPATH, "controller.ftl", map);
//            flog = createFile((CONTROLLERPATH + "/" + businessType + "/" + map.get("entityName") + "Controller.java"), controller);
//        }
//        /*
//        if (flog) {
//            //首页文件
//            String list = getFtlCode(TEMPLATEPATH, "list.ftl", map);
//            flog = createFile((LISTPAGEPATH + "/" + StringUtils.underlineToCamel2(tablename) + "/index.html"), list);
//        }
//        if (flog) {
//            //新增修改
//            String update = getFtlCode(TEMPLATEPATH, "addupdate.ftl", map);
//            flog = createFile((LISTPAGEPATH + "/" + StringUtils.underlineToCamel2(tablename) + "/addOrUpdateP.html"), update);
//        }
//        if (flog) {
//            //首页js文件
//            String listJs = getFtlCode(TEMPLATEPATH, "js.ftl", map);
//            flog = createFile((JSPATH + "/" + StringUtils.underlineToCamel2(tablename) + "/index.js"), listJs);
//        }
//        if (flog) {
//            //新增修改js文件
//            String updateJs = getFtlCode(TEMPLATEPATH, "addupdateJs.ftl", map);
//            flog = createFile((JSPATH + "/" + StringUtils.underlineToCamel2(tablename) + "/addupdate.js"), updateJs);
//        }*/
//        return flog;
//    }
//
//    /**
//     * 执行
//     *
//     * @param args
//     */
//    public static void main(String[] args) {
//
//
//        new CreateCode("medical_records", "", "records_id", "health").createCode();
//
//
//
//
//
//
//
//
//    }
//}