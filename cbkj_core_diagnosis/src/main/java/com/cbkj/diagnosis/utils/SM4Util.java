package com.cbkj.diagnosis.utils;
import org.bouncycastle.crypto.BlockCipher;
import org.bouncycastle.crypto.BufferedBlockCipher;
import org.bouncycastle.crypto.engines.SM4Engine;
import org.bouncycastle.crypto.modes.CFBBlockCipher;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.params.ParametersWithIV;
import org.bouncycastle.util.encoders.Hex;

import java.security.SecureRandom;
import java.util.Arrays;

public class SM4Util {

    private static final int KEY_SIZE_BITS = 128; // 128位密钥
    public static final byte[] KEY_SIZE_BITS_A = {-106, 34, 26, 4, 4, 44, 76, 61, -42, -45, -114, 52, -50, 10, 73, 63};
    private static final int IV_SIZE_BYTES = 16;  // 128位IV
    public static final byte[] IV_SIZE_BYTES_A = {-6, -116, 13, 114, 110, -89, 21, 126, 102, -43, -127, 43, 33, 120, 45, 124};

    public static byte[] generateSecureRandomKey() {
        byte[] key = new byte[KEY_SIZE_BITS / 8];
        SecureRandom secureRandom = new SecureRandom();
        secureRandom.nextBytes(key);
        return key;
    }

    public static byte[] generateSecureRandomIV() {
        byte[] iv = new byte[IV_SIZE_BYTES];
        SecureRandom secureRandom = new SecureRandom();
        secureRandom.nextBytes(iv);
        return iv;
    }

    public static byte[] sm4Encrypt(byte[] key, byte[] iv, byte[] data) throws Exception {
        BlockCipher engine = new SM4Engine();
        BufferedBlockCipher cipher = new BufferedBlockCipher(new CFBBlockCipher(engine, engine.getBlockSize() * 8));
        cipher.init(true, new ParametersWithIV(new KeyParameter(key), iv));

        byte[] output = new byte[cipher.getOutputSize(data.length)];
        int bytesProcessed = cipher.processBytes(data, 0, data.length, output, 0);
        cipher.doFinal(output, bytesProcessed);

        return output;
    }

    public static byte[] sm4Decrypt(byte[] key, byte[] iv, byte[] data) throws Exception {
        BlockCipher engine = new SM4Engine();
        BufferedBlockCipher cipher = new BufferedBlockCipher(new CFBBlockCipher(engine, engine.getBlockSize() * 8));
        cipher.init(false, new ParametersWithIV(new KeyParameter(key), iv));

        byte[] output = new byte[cipher.getOutputSize(data.length)];
        int bytesProcessed = cipher.processBytes(data, 0, data.length, output, 0);
        cipher.doFinal(output, bytesProcessed);

        return output;
    }

    public static void main(String[] args) throws Exception {
        // 生成SM4密钥和IV
        byte[] sm4Key = generateSecureRandomKey();
        byte[] sm4Iv = generateSecureRandomIV();

        System.out.println("sm4Key :"+ new String(Hex.encode(KEY_SIZE_BITS_A)));
        System.out.println("sm4Iv :"+ new String(Hex.encode(IV_SIZE_BYTES_A)));

        System.out.println("sm4Key :"+ Arrays.toString(sm4Key));
        System.out.println("sm4Iv :"+ Arrays.toString(sm4Iv));
        // 示例数据
        String plaintext = "Hello, SM4 Encryption and Decryption!";

        // 加密
        byte[] encryptedData = sm4Encrypt(sm4Key, sm4Iv, plaintext.getBytes("UTF-8"));
        System.out.println("Encrypted Data: " + new String(Hex.encode(encryptedData)));

        String s = new String(Hex.encode(encryptedData));
        byte[] decode = Hex.decode(s);
        // 解密
        byte[] decryptedData = sm4Decrypt(sm4Key, sm4Iv, decode);
        System.out.println("Decrypted Data: " + new String(decryptedData, "UTF-8"));
    }
}


