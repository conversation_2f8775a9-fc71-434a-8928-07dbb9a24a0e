package com.cbkj.diagnosis.utils;


//import com.cbkj.diagnosis.common.constants.SystemConstants;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

/**
 * 本程序生成一个AES密钥，并且转换它为RAW字节，然后根据这个密钥重新创建一个AES密钥，这个新构建的密钥用于初始化一个加密解密的AES密码
 */

public class AESEncrypt {

	private static String data_pwd_fir = Constant.DATA_PWD_FIR;
	private static String data_pwd_sec = Constant.DATA_PWD_SEC;

	public static String aes_encrypt(String password, String strKey) {
		try {
			byte[] keyBytes = Arrays.copyOf(strKey.getBytes("ASCII"), 16);

			SecretKey key = new SecretKeySpec(keyBytes, "AES");
			Cipher cipher = Cipher.getInstance("AES");
			cipher.init(Cipher.ENCRYPT_MODE, key);

			byte[] cleartext = password.getBytes("UTF-8");
			byte[] ciphertextBytes = cipher.doFinal(cleartext);

			return new String(Hex.encodeHex(ciphertextBytes)).toUpperCase();

		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (NoSuchPaddingException e) {
			e.printStackTrace();
		} catch (InvalidKeyException e) {
			e.printStackTrace();
		} catch (IllegalBlockSizeException e) {
			e.printStackTrace();
		} catch (BadPaddingException e) {
			e.printStackTrace();
		}
		return "";
	}

	public static String aes_decrypt(String encryptResult, String strKey) {
		try {
			byte[] keyBytes = Arrays.copyOf(strKey.getBytes(StandardCharsets.US_ASCII), 16);

			SecretKey key = new SecretKeySpec(keyBytes, "AES");
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
			Cipher cipher = Cipher.getInstance("AES");
			cipher.init(Cipher.DECRYPT_MODE, key);

			byte[] cleartext = Hex.decodeHex(encryptResult.toCharArray());
			byte[] ciphertextBytes = cipher.doFinal(cleartext);

			return new String(ciphertextBytes, StandardCharsets.UTF_8);

		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (NoSuchPaddingException e) {
			e.printStackTrace();
		} catch (InvalidKeyException e) {
			e.printStackTrace();
		} catch (IllegalBlockSizeException e) {
			e.printStackTrace();
		} catch (BadPaddingException e) {
			e.printStackTrace();
		} catch (DecoderException e) {
			// TODO Auto-generated catch block
//			e.printStackTrace();
		}
		return "";
	}

	// 默认解密
	public static String default_aes_decrypt(String encryptResult) {
		if (StringUtils.isBlank(encryptResult)) {
			return "";
		}
		String ret = aes_decrypt(aes_decrypt(encryptResult, data_pwd_sec),
				data_pwd_fir);
		return ret;
	}

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		String content =  "演示患者";
//3F7AC35B674F1B6BFE317AB9D9FDE33E4B31C62953B14517EFF5E005FF21367A

		String encryptResult = aes_encrypt(content, SystemConstants.SYS_STR_KEY);
		System.out.println("加密后：" + encryptResult);

		// 解密
		String decryptResult = aes_decrypt(encryptResult,  SystemConstants.SYS_STR_KEY);
		System.out.println("解密后：" + decryptResult);

	}

}