package com.cbkj.diagnosis.utils;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.security.spec.AlgorithmParameterSpec;
import java.util.HashMap;
import java.util.Map;
@Slf4j
public class AESPKCS7Util {

    private static final String CHARSET_NAME = "UTF-8";

    private static final String AES_NAME = "AES";

    // 加密模式

    public static final String ALGORITHM = "AES/ECB/PKCS7Padding";

    // 密钥

    public static final String KEY = "OUT3041JT51Y7HF2";

    // 偏移量

    public static final String IV = "OUT3041JT51Y7HF2";

    static {

        Security.addProvider(new BouncyCastleProvider());

    }

    /**

     * 加密

     *

     * @param content

     * @param

     * @return

     */

    public String encrypt(String content) {

        byte[] result = null;

        try {

            Cipher cipher = Cipher.getInstance(ALGORITHM);

            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(CHARSET_NAME), AES_NAME);

            AlgorithmParameterSpec paramSpec = new IvParameterSpec(IV.getBytes());

            cipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);

            result = cipher.doFinal(content.getBytes(CHARSET_NAME));

        } catch (Exception e) {

            e.printStackTrace();

        }

        return Base64.encodeBase64String(result);

    }

    /**

     * 解密

     *

     * @param content

     * @param

     * @return

     */

    public String decrypt( String content) {

        try {

            Cipher cipher = Cipher.getInstance(ALGORITHM);

            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(CHARSET_NAME), AES_NAME);

            AlgorithmParameterSpec paramSpec = new IvParameterSpec(IV.getBytes());

            cipher.init(Cipher.DECRYPT_MODE, keySpec, paramSpec);

            return new String(cipher.doFinal(Base64.decodeBase64(content)), CHARSET_NAME);

        } catch (Exception e) {

            e.printStackTrace();

        }

        return StringUtils.EMPTY;

    }
    public static void main(String[] args) throws Exception {

       String encrypt = AESPKCS7Util.encrypt("{\"insCode\":\"42660069-535020011A2101\",\"insName\":\"测试医院名字\",\"appId\":\"361000\",\"mobile\":\"13211111111\",\"userName\":\"李梅\",\"timestamp\":\"1725010297040\",\"regPlanId\":\"20241128142819001\"}", KEY, "base64");
       String encrypt2 = AESPKCS7Util.encrypt("{\"appId\":\"361000\",\"insCode\":\"42660069-535020011A2101\",\"insName\":\"测试医院名字\",\"timestamp\":1699675970312, \"doctorInfo\":{\"doctorName\":\"admin\",\"employeeId\": \"admin\",\"doctorCardNumber\": \"330124199902240331\"}}", KEY, "base64");
        System.out.println(encrypt);
 //       System.out.println(AESPKCS7Util.encrypt("{\"appId\":\"31000501\",\"insCode\":\"47000413733010211D1211\",\"insName\":\"浙江中医药大学附属第三医院\",\"timestamp\":1724918760692,\"doctorInfo\":{\"doctorName\":\"超级用户\",\"employeeId\":\"DBA\",\"doctorCardNumber\":\"341003199908170050\"},\"patientInfo\":{\"patientName\":\"清水1\",\"patientCardNumber\":\"230903198811110752\",\"patientCardType\":\"01\",\"healthCardNum\":\"CS0001\",\"patientPhone\":\"18375338077\",\"patientSex\":\"F\",\"patientAge\":\"35\"},\"recordInfo\":{\"visitNo\":\"**********\",\"insCode\":\"47000413733010211D1211\",\"insName\":\"浙江中医药大学附属第三医院\",\"deptCode\":\"11006\",\"deptName\":\"针灸科(莫干山)\",\"recordTime\":\"2024-08-29 16:06:16\"}}", KEY, "base64"));
       //System.out.println(AESPKCS7Util.decrypt("4P0jJXE7IW0lnVCcR2g5qTUnFP/8hAWcuMRi6wjFYtwCmAzE68z592PNY00VwsJhpBJAtEaR4BB9PWMsi8e+ac+LyjipwrIWPRyf14hO79LkJW2KQN3o98iAgOSJwYDJQKPxyGWKzVWoqB12bSMS+x//DzLk8qej0DwmNxgp69gfvnoD0XFrTNj0o7hXPyl/SiKSz2oSnSQhoHyq0oFSWu/vKk1n50OZD1mOPg3m7G+X+2Her2nvDmo9AY6aGkEtn4XJxcWn8PJkD1LBUITa0dxj6ghlpy7jPIF9dtkX1c8=", "OUT3041JT51Y7HF2", "base64"));
     //   System.out.println(base64);

    //    System.out.println(AESEncrypt.aes_decrypt("8D9DDA79373F0A9D0AA0E2BF93AE16CF", SystemConstants.SYS_STR_KEY) );
    }


    /**
     * encrypt input text
     * @param input
     * @param key
     * @param handle_style
     * @return
     */
    public static String encrypt(String input, String key,String handle_style) throws Exception {
        if(handle_style.equals("hex")){
            return encrypt_hex(input,key);
        }else if (handle_style.equals("base64")){
            return encrypt_base64(input,key);
        }else{
            throw new Exception("must be hex or base64");
        }
    }

    public static String encrypt_hex(String input,String key){
        byte[] crypted = null;
        try {
            SecretKeySpec skey = new SecretKeySpec(key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(Cipher.ENCRYPT_MODE, skey);
            crypted = cipher.doFinal(input.getBytes());
        } catch (Exception e) {
            System.out.println(e.toString());
            e.printStackTrace();
        }
        return new String(Hex.encodeHex(crypted));
    }

//    public static String encrypt_base64(String input,String key){
//        byte[] crypted = null;
//        try {
//            SecretKeySpec skey = new SecretKeySpec(key.getBytes(), "AES");
//            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
//            cipher.init(Cipher.ENCRYPT_MODE, skey);
//            crypted = cipher.doFinal(input.getBytes());
//        } catch (Exception e) {
//            System.out.println(e.toString());
//            e.printStackTrace();
//        }
//        return new String(Base64.encodeBase64(crypted));
//    }
    public static String encrypt_base64(String input, String key) {
        byte[] crypted = null;
        try {
            SecretKeySpec skey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(Cipher.ENCRYPT_MODE, skey);
            crypted = cipher.doFinal(input.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (crypted != null) {
            return new String(Base64.encodeBase64(crypted), StandardCharsets.UTF_8);
        } else {
            return null;
        }
    }

    /**
     * decrypt input text
     *
     * @param input
     * @param key
     * @param handle_style
     * @return
     */
    public static String decrypt(String input, String key,String handle_style) throws Exception {
        input=input.replaceAll(" ","+");
        if(input.indexOf("%")!=-1){
            input = URLDecoder.decode(input, "UTF-8");;
        }
        if(handle_style.equals("hex")){
            return decrypt_hex(input,key);
        }else if (handle_style.equals("base64")){
            return decrypt_base64(input,key);
        }else{
            log.error("解密失败：must be hex or base64");
            throw new Exception("must be hex or base64");
        }
    }

    public static String decrypt_hex(String input, String key) {
        byte[] output = null;
        try {
            SecretKeySpec skey = new SecretKeySpec(key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(Cipher.DECRYPT_MODE, skey);
            output = cipher.doFinal(Hex.decodeHex(input.toCharArray()));
        } catch (Exception e) {
            System.out.println(e.toString());
        }
        return new String(output);
    }

//    public static String decrypt_base64(String input, String key) {
//        byte[] output = null;
//        try {
//            SecretKeySpec skey = new SecretKeySpec(key.getBytes(), "AES");
//            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
//            cipher.init(Cipher.DECRYPT_MODE, skey);
//            output = cipher.doFinal(Base64.decodeBase64(input));
//        } catch (Exception e) {
//            System.out.println(e.toString());
//        }
//        return new String(output);
//    }


    public static String decrypt_base64(String input, String key) {
        byte[] output = null;
        try {
            SecretKeySpec skey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(Cipher.DECRYPT_MODE, skey);
            output = cipher.doFinal(Base64.decodeBase64(input.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("解密失败,检查密钥或者入参是否正确。");
            throw new RuntimeException("登录失败，请检查入参或配置");
        }
        if (output != null) {
            return new String(output, StandardCharsets.UTF_8);
        } else {
            return null;
        }
    }


    public static Map jsonToMap(String jsonStr) {
        Map map = new HashMap<>();
        try {
            JSONObject jsonObject =  JSONObject.parseObject(jsonStr);
            for (String key : jsonObject.keySet()) {
                Object value = jsonObject.get(key);
                if (value instanceof JSONObject) {
                    value = jsonToMap(value.toString());
                }
                map.put(key, value);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return map;
    }
}