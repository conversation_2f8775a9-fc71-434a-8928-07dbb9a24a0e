package com.cbkj.diagnosis.utils;

//import com.github.pagehelper.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cbkj.diagnosis.common.utils.Constant;
//import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 
 * 由此对象将page对象转换成json对象，传到前台处理
 * <AUTHOR>
 * 由于定义的page对象里面的字段和mybatisplus的不一样
 * 所以这个由这个中间对象来转换
 * @param <T>
 */
@Schema
@Data
@Accessors(chain = true)
public class CustomPage<T>{
	
	//当前页数
//	@Schema(description =  "当前页数", example = "1")
//	private long page;
//
//	//每页显示数量
//	@Schema(description =  "每页显示数量", example = "10")
//	private long pageSize;
	
	//总条数
	@Schema(description =  "总条数", example = "100")
	private long count;
	
	//数据列表
	@Schema(description =  "数据列表")
	private List<T> data;
	
	//总页数
//	@Schema(description =  "总页数", example = "10")
//	private long total;
	
	//排序字段
	//private String orderByField;
	
	//是否升序
	//private String isAsc = "desc";
	@Schema(description =  "状态", example = "0")
	private Integer status = 0;
	@Schema(description =  "消息", example = "success")
	private String message = Constant.SUCCESS;
	@Schema(description =  "是否有下一页", example = "true")
	private boolean isHasNextPage;
	public CustomPage(){}
	
	public CustomPage(Page<T> page){
//		this.page = page.getCurrent();
//		this.pageSize = page.getSize();
		this.count = page.getTotal();
		this.data = page.getRecords();
//		this.total = page.getPages();
		this.isHasNextPage = page.hasNext();
		//this.page*this.pageSize > count ? this.isHasNextPage = true : this.isHasNextPage = false;
		//this.orderByField = page.getOrders();
//		this.isAsc = page.isAsc();
	}
}
