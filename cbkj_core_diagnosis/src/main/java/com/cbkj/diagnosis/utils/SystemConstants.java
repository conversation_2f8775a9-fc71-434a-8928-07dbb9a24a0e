package com.cbkj.diagnosis.utils;

import java.util.Arrays;
import java.util.List;

/**
 * 常量
 */
public class SystemConstants {

	/************************* session ************************/
	public static final String SESSION_USER = "user";
	public static final Integer BASIC_MINUS_NUMBER_1 = -1;
	public static final String BASIC_NUMBER_0 = "0";
	public static final String BASIC_NUMBER_1 = "1";
	public static final String BASIC_NUMBER_2 = "2";
	public static final String BASIC_NUMBER_3 = "3";
	public static final String BASIC_NUMBER_4 = "4";
	public static final String BASIC_DEL_YES = "1";
	public static final String BASIC_DEL_NO = "0";
	public static final String BASIC_DISABLE_NO = "1";
	public static final String BASIC_DISABLE_YES = "0";
	public static final String BASIC_TYPE_INSERT = "insert";
	public static final String BASIC_TYPE_UPDATE = "update";
	public static final String BASIC_TYPE_DETAIL = "detail";
	public static final String BASIC_STYPE_SAVE = "save";
	public static final String BASIC_STYPE_UPDATE = "update";

	public static final String BASIC_SYN_SOURCE_MAT = "1";
	public static final String BASIC_SYN_SOURCE_CLA = "2";
	public static final String BASIC_SYN_SOURCE_DIS = "3";
	public static final String BASIC_SYN_SOURCE_SYM = "4";


	public static final String BASIC_RESULT_SUCCESS = "success";
	public static final String BASIC_RESULT_FAIL = "fail";


	public static final String SYS_TEMPLATE_SEND_CODE = "Q2yD71";    //发送验证码模版

	public static final String SYS_STR_KEY = "1234567890123456";

	public static final String SYS_AES_KEY = "OUT3041JT51Y7HF2";


	public static final List<String> TONGUE_PHY_LIST = Arrays.asList("气虚", "阳虚", "阳虚", "痰湿", "湿热", "血瘀", "气郁", "特禀", "平和", "阳盛");
	public static final List<String> ACU_TYPE_LIST = Arrays.asList("穴位按摩", "穴位灸", "穴位拔罐、刮痧", "拔罐、刮痧");

	//	文件路径 外网
	public static final String UPLOADFILE = "http://tzbsapi.tcmbrain.com/sysz/preview/{0}/{1}";

	public static final String UPLOAD_FILE = "http://tzbsapi.tcmbrain.com/sysz/preview/{0}";


	//	文件路径 内网
	public static final String UPLOADFILE_V2 = "http://192.168.2.80:8081/sysz/preview/{0}/{1}";

	public static final String UPLOAD_FILE_V2 = "http://192.168.2.80:8081/sysz/preview/{0}";

	/*
	* 日志相关
	* */
	public static final String INTERFACE_REQUEST_PARAM = "接口请求入参:{0}";
	public static final String INTERFACE_RETURN_PARAM = "接口请求返参:{0}";
	public static final String EXTERNAL_INTERFACE_REQUEST_PARAM = "第三方接口请求地址:{0}入参:{1}";
	public static final String EXTERNAL_INTERFACE_RETURN_PARAM = "第三方接口请求返参:{0}";
	public static final String METHOD_REQUEST_PARAM = "方法请求入参:{0}";
	public static final String METHOD_RETURN_PARAM = "方法请求返参:{0}";

	/**
	 * 颜色
	 */
	public static final String COLOR_LIGHT_RED = "淡红";
	public static final String COLOR_WHITE_FUR = "白苔";
	public static final String COLOR_THIN_COATING = "薄苔";
	public static final String COLOR_NORMAL = "正常";

	/**
	 * 字段
	 */

	public static final String FIELD_TONGUE_COLOR = "tongueColor";
	public static final String FIELD_MOSS_COLOR = "mossColor";
	public static final String FIELD_TONGUE_SHAPE = "tongueShape";
	public static final String FIELD_MOSS_QUALITY = "mossQuality";
	public static final String FIELD_BODY_FLUID = "bodyFluid";
	public static final String FIELD_SUBLINGUAL_PULSE = "sublingualPulse";


	public static final String USER_ERROR = "用户信息异常";

}