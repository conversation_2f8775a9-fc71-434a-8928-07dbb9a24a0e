package com.cbkj.diagnosis.utils;

import javax.xml.bind.annotation.adapters.XmlAdapter;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/30 16:52
 * @Version 1.0
 */
public class StringArrayAdapter extends XmlAdapter<String, String[]> {
    @Override
    public String[] unmarshal(String v) throws Exception {
        if (v == null) {
            return null;
        }
        return v.split("\\、");
    }

    @Override
    public String marshal(String[] v) throws Exception {
        if (v == null) {
            return null;
        }
        return String.join("、", v);
    }
}
