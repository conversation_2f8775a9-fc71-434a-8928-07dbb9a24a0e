package com.cbkj.diagnosis.utils;



import com.cbkj.diagnosis.beans.SysLogInterface;
import com.cbkj.diagnosis.service.LogService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


@Component
public class LogAsync {


    private final LogService logService;

    public LogAsync(LogService logService) {
        this.logService = logService;
    }


    @Async
    public void logRecord(SysLogInterface sysLogInterface) {
        logService.insert(sysLogInterface);
    }



}