package com.cbkj.diagnosis.utils;

public class Constant {

    public static final String PREDIAGNOSIS = "预诊";
    public static final String VISIT_CLINIC = "就诊";
    public static final String PROPAGANDA_EDUCATION = "宣教";
    public static final String FOLLOW_UP = "随访";
    //菜单键
    public static final String MENUS = "MENUS";

    public static final String SUCCESS = "success";

    public static final String ENCODED = "UTF-8";

    public static final String FAILMESSAGE = "服务异常，请稍后重试！";

    public static final String MESSAGE = "message";

    public static final String NOTPARAMS = "缺少参数，请检查！";

    public static final String UNDEFINED = "未知错误，未定义！";

    public static final String QUARTZFACTORYPACKAGE = "com.example.cbkj_core.common.factory.QuartzFactory";

    public static final String YES = "yes";

    public static final String NO = "no";

    public static final String BLOCKED = "BLOCKED";

    public static final String BLOCKEDMSG = "请别着急，当前操作正在进行中！";

    public static final String REPEATMSG = "请不要频繁操作！";

    public static final String TOKENNAME = "token";

    public static final String DATA = "data";

    public static final boolean TRUE = true;

    public static final boolean FALSE = false;

    public static final String AUTHYES = "1";

    public static final String AUTHNO = "0";

    public static final String WEBSERVICEFAIL = "webService暂时不可用！！";

    public static final String ERRORPAGE = "error/400";

    //超级管理员角色(神级)
    public static final String ROLEGRADE = "b4a17b6b635c4de48f95178676905aa5";

    //超级管理员角色(非神级)
    public static final String MANAGER = "f85aa731d9d144d1bc1d72cf3e877a4d";

    public static final String XLS = "xls";

    public static final String XLSX = "xlsx";


    /************************* 加密密钥 ************************/
    public static final String DATA_PWD_FIR = "S@T#K$J";
    public static final String DATA_PWD_SEC = "TWGDH@BTZHY$";

    //	================日志相关===================
    public static final String INTERFACE_REQUEST_PARAM = "接口请求入参:{0}";
    public static final String INTERFACE_RETURN_PARAM = "接口请求返参:{0}";
    public static final String METHOD_EXECUTE_PARAM = "方法执行参数:{0}";
    public static final String METHOD_EXECUTE_RESULT = "方法执行结果:{0}";

}

