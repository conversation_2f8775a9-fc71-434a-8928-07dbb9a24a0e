package com.cbkj.diagnosis.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.Security;
import java.security.spec.AlgorithmParameterSpec;
import java.util.HashMap;
import java.util.Map;


public class AESUtil {

    private static final String CHARSET_NAME = "UTF-8";

    private static final String AES_NAME = "AES";

// 加密模式

    public static final String ALGORITHM = "AES/ECB/PKCS7Padding";

// 密钥

    public static final String KEY = "OUT3041JT51Y7HF2";

// 偏移量

    public static final String IV = "OUT3041JT51Y7HF2";

    static {

        Security.addProvider(new BouncyCastleProvider());

    }

    /**

     * 加密

     *

     * @param content

     * @param

     * @return

     */

    public String encrypt( String content) {

        byte[] result = null;

        try {

            Cipher cipher = Cipher.getInstance(ALGORITHM);

            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(CHARSET_NAME), AES_NAME);

            AlgorithmParameterSpec paramSpec = new IvParameterSpec(IV.getBytes());

            cipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);

            result = cipher.doFinal(content.getBytes(CHARSET_NAME));

        } catch (Exception e) {

            e.printStackTrace();

        }

        return Base64.encodeBase64String(result);

    }

    /**

     * 解密

     *

     * @param content

     * @param

     * @return

     */

    public String decrypt(String content) {

        try {

            Cipher cipher = Cipher.getInstance(ALGORITHM);

            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(CHARSET_NAME), AES_NAME);

            AlgorithmParameterSpec paramSpec = new IvParameterSpec(IV.getBytes());

            cipher.init(Cipher.DECRYPT_MODE, keySpec, paramSpec);

            return new String(cipher.doFinal(Base64.decodeBase64(content)), CHARSET_NAME);

        } catch (Exception e) {

            e.printStackTrace();

        }

        return StringUtils.EMPTY;

    }

    public static void main(String[] args) {
        Map<String,Object> mp1 = new HashMap<>();
        mp1.put("userName","张三");
        mp1.put("idcardValue","140431199003040011");
        mp1.put("phone","13511111111");
        AESUtil aes = new AESUtil();

        String contents = JSONObject.toJSONString(mp1);
        System.out.println("解密前:" + contents);
        String encrypt = aes.encrypt(contents);

        System.out.println("加密后:" + encrypt);
        try {
            String bfhencode = URLEncoder.encode(encrypt, "UTF-8");
            System.out.println("百分号加密后：" +bfhencode);
            String bfhdecode = URLDecoder.decode(bfhencode, "UTF-8");
            System.out.println("百分号解密后：" + bfhdecode);
            String decrypt = aes.decrypt(bfhdecode);
            System.out.println("最终解密后：" + decrypt);
        }catch (Exception e){
            e.printStackTrace();
        }


    }

}