package com.cbkj.diagnosis.utils;

import java.util.Collections;

/**
 * Created by zbh on 2024/6/19 11:22
 *
 * @description：脱敏工具类
 */
public class DesensitizeUtils {

    /**
     * 手机号脱敏
     * @param mobile
     * @return
     */
    public static String mobileDesensitize(String mobile) {
        if (mobile == null || mobile.length() != 11) {
            return mobile;
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }

    /**
     * 身份证脱敏
     * @param idCard
     * @return
     */
    public static String idCardDesensitize(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return idCard;
        }
        return idCard.substring(0, 3) + "**** ****" + idCard.substring(14);
    }

    /**
     * 对姓名进行脱敏处理。
     *
     * @param fullName 原始姓名字符串。
     * @return 脱敏后的姓名字符串。
     */
    public static String nameDesensitize(String fullName) {
        if (fullName == null || fullName.isEmpty()) {
            return "";
        }

        // 取得姓名的第一个字符
        char firstChar = fullName.charAt(0);

        StringBuilder maskedName = new StringBuilder();
        // 如果姓名只有一个字符，直接返回该字符
        if (fullName.length() == 1) {
            return String.valueOf(firstChar);
        }else if(fullName.length() == 2){
            // 第一个字除外 其余字符用星号替换
            maskedName.append(firstChar); // 添加第一个字符
            for (int i = 1; i < fullName.length(); i++) {
                maskedName.append('*');
            }
            return maskedName.toString();
        }else{
            String prefix = fullName.substring(0, 1);
            String suffix = fullName.substring(fullName.length() - 1);
            String middle = String.join("", Collections.nCopies(fullName.length() - 2, "*"));
            return prefix + middle + suffix;
        }

    }

    public static void main(String[] args) {
        String name = "张贤仁";
        String s = nameDesensitize(name);
        System.out.println(s);
    }

}
