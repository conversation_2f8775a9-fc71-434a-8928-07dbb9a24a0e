package com.cbkj.diagnosis.utils;

import com.alibaba.fastjson.JSONObject;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.spec.AlgorithmParameterSpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class AESUtils {
    private static String aesKey = "NhL8tiakF25BWEYS";

    private static final String CHARSET_NAME = "UTF-8";

    private static final String AES_NAME = "AES";

    // 加密模式
    public static final String ALGORITHM = "AES/CBC/PKCS7Padding";

    // 密钥
    public static final String KEY = "1954682168745975";
    // 偏移量
    public static final String IV = "1954682168745975";

    /**
     * AES算法128位加密
     *
     * @param cleartext
     * @return
     */
    public static String encrypt(String cleartext) {
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            SecretKeySpec skeySpec = new SecretKeySpec(aesKey.getBytes("UTF-8"), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
            byte[] encrypted = cipher.doFinal(cleartext.getBytes("UTF-8"));

            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static  String newencrypt(String content) {

        byte[] result = null;

        try {

            Cipher cipher = Cipher.getInstance(ALGORITHM);

            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(CHARSET_NAME), AES_NAME);

            AlgorithmParameterSpec paramSpec = new IvParameterSpec(IV.getBytes());

            cipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);

            result = cipher.doFinal(content.getBytes(CHARSET_NAME));

        } catch (Exception e) {

            e.printStackTrace();

        }

        return Base64.getEncoder().encodeToString(result);

    }

    /**
     * AES算法128位解密
     *
     * @param encrypMsg
     * @return
     */
    public static String decrypt(String encrypMsg) {
        encrypMsg=encrypMsg.replaceAll(" ","+");
        try {
            Cipher cipher = Cipher.getInstance("AES");
            SecretKeySpec keySpec = new SecretKeySpec(aesKey.getBytes(), "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] decbbdt = cipher.doFinal(Base64.getDecoder().decode(encrypMsg));

            return new String(decbbdt, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        Map<String,Object> mp1 = new HashMap<>();
        mp1.put("userName","张三");
        mp1.put("idcardValue","140431199003040011");
        mp1.put("phone","13511111111");
        String content = JSONObject.toJSONString(mp1);
        System.out.println("加密前：" + content);
        String encrypt = newencrypt(content);
        System.out.println("加密后：" + encrypt);
        try {
            String bfhencode = URLEncoder.encode(encrypt, "UTF-8");
            System.out.println("百分号加密后：" +bfhencode);
            String bfhdecode = URLDecoder.decode("ZMmQWky31ilEp0BDFYPkXPWViIs3gYKX%2F5gqR1cJRxZGp9m5QCBqW%2B345Xr7h8aC7boMppTJCD0ifHxdiugqvF%2FVmWy2ImUSc6HPfQIA%2BWU%3D", "UTF-8");
            System.out.println("百分号解密后：" + bfhdecode);
            String decrypt = decrypt(bfhdecode);
            System.out.println("最终解密后：" + decrypt);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}