package com.cbkj.diagnosis.mybatis.mybatisLike;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 通用参数的转换器
 * @author: wang<PERSON>
 * @time: 2024/01/04 11:07
 */
@Slf4j
public class ObjectLikeSqlConverter extends AbstractLikeSqlConverter<Object> {

    @Override
    public void transferWrapper(String field, Object parameter) {
        // 尚未发现这种情况
    }

    @Override
    public void transferSelf(String field, Object parameter) {
        // 尚未发现这种情况
    }

    @Override
    public void transferSplice(String field, Object parameter) {
        this.resolveObj(field, parameter);
    }

}
