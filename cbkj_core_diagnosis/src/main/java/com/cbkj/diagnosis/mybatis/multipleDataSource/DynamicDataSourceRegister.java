package com.cbkj.diagnosis.mybatis.multipleDataSource;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.wall.WallConfig;
import com.alibaba.druid.wall.WallFilter;
import com.cbkj.diagnosis.common.utils.Constant;
import com.cbkj.diagnosis.common.utils.JasyptUtils;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.druid.filter.Filter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotationMetadata;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.*;

/**
 * 多数据源注册
 * <AUTHOR>
 */
@Slf4j
//@Configuration
//@MapperScan("com.cbkj.diagnosis.mapper*")
//@EnableTransactionManagement(proxyTargetClass = true)
public class DynamicDataSourceRegister implements ImportBeanDefinitionRegistrar, EnvironmentAware {

    /**
     * 默认数据源
     */
    private DataSource defaultDataSource;

    /**
     * 自定义数据源
     */
    private final Map<String, DataSource> slaveDataSources = new HashMap<>();

    /**
     * 读取主数据源
     */
    private void initDefaultDataSource(Environment env) throws SQLException {
        String mainUrl = env.getProperty("spring.datasource.primary.url");
        log.info("主数据源配置：" + mainUrl);

        Map<String, Object> dsMap = new HashMap<>();
        dsMap.put("driver", env.getProperty("spring.datasource.primary.driver-class-name"));
        dsMap.put("url", mainUrl);
        dsMap.put("username", JasyptUtils.decyptPwd(env.getProperty("root.encryptor.password"), env.getProperty("spring.datasource.primary.username")));
        dsMap.put("password", JasyptUtils.decyptPwd(env.getProperty("root.encryptor.password"), env.getProperty("spring.datasource.primary.password")));
        defaultDataSource = buildDataSource(env, dsMap);
    }

    /**
     *     读取多数据源
     */
    private void initslaveDataSources(Environment env) throws SQLException {
        String slaveNameFixs = env.getProperty("spring.datasource.slave.names");

        if (StringUtils.isNotBlank(slaveNameFixs)) {
            for (String prefix : slaveNameFixs.split(Constant.ENGLISH_COMMA)) {
                Map<String, Object> dsMap = new HashMap<>(16);
                dsMap.put("driver", env.getProperty("spring.datasource." + prefix + ".driver-class-name"));
                dsMap.put("url", env.getProperty("spring.datasource." + prefix + ".url"));
                dsMap.put("username", JasyptUtils.decyptPwd(env.getProperty("root.encryptor.password"), env.getProperty("spring.datasource." + prefix + ".username")));
                dsMap.put("password", JasyptUtils.decyptPwd(env.getProperty("root.encryptor.password"), env.getProperty("spring.datasource." + prefix + ".password")));
                DataSource ds = buildDataSource(env, dsMap);
                slaveDataSources.put(prefix, ds);
            }
        }
    }

    @Bean
    public WallFilter wallFilter() {
        WallFilter wallFilter = new WallFilter();
//        WebApplicationContext wac = ContextLoader.getCurrentWebApplicationContext();
//        WallConfig wallConfig = (WallConfig) wac.getBean("wallConfig");
        wallFilter.setConfig(wallConfig());
        return wallFilter;
    }

    @Bean(value = "wallConfig")
    public WallConfig wallConfig() {
        WallConfig config = new WallConfig();
        //允许一次执行多条语句
        config.setMultiStatementAllow(true);
        //允许非基本语句的其他语句
        config.setNoneBaseStatementAllow(true);
        config.setCommentAllow(true);
        return config;

    }

    /**
     *     构建数据源
     */
    public DataSource buildDataSource(Environment env, Map<String, Object> dataSourceMap) throws SQLException {

        DruidDataSource dataSource = new DruidDataSource();
        String common = env.getProperty("spring.datasource.common");
        if (StringUtils.isNotBlank(common) && Constant.YES.equals(common)) {
            //配置连接池参数
            dataSource.setInitialSize(Integer.parseInt(Objects.requireNonNull(env.getProperty("spring.datasource.common.initialSize"))));
            dataSource.setMinIdle(Integer.parseInt(Objects.requireNonNull(env.getProperty("spring.datasource.common.minIdle"))));
            dataSource.setMaxActive(Integer.parseInt(Objects.requireNonNull(env.getProperty("spring.datasource.common.maxActive"))));
            dataSource.setMaxWait(Integer.parseInt(Objects.requireNonNull(env.getProperty("spring.datasource.common.maxWait"))));
            dataSource.setTimeBetweenEvictionRunsMillis(Integer.parseInt(Objects.requireNonNull(env.getProperty("spring.datasource.common.timeBetweenEvictionRunsMillis"))));
            dataSource.setMinEvictableIdleTimeMillis(Integer.parseInt(Objects.requireNonNull(env.getProperty("spring.datasource.common.minEvictableIdleTimeMillis"))));
            dataSource.setValidationQuery(env.getProperty("spring.datasource.common.validationQuery"));
            dataSource.setTestWhileIdle(Boolean.parseBoolean(env.getProperty("spring.datasource.common.testWhileIdle")));
            dataSource.setTestOnBorrow(Boolean.parseBoolean(env.getProperty("spring.datasource.common.testOnBorrow")));
            dataSource.setTestOnReturn(Boolean.parseBoolean(env.getProperty("spring.datasource.common.testOnReturn")));
            dataSource.setPoolPreparedStatements(Boolean.parseBoolean(env.getProperty("spring.datasource.common.poolPreparedStatements")));
            dataSource.setFilters(env.getProperty("spring.datasource.common.filters"));
            dataSource.setMaxPoolPreparedStatementPerConnectionSize(Integer.parseInt(Objects.requireNonNull(env.getProperty("spring.datasource.common.maxPoolPreparedStatementPerConnectionSize"))));
            dataSource.setConnectionProperties(env.getProperty("spring.datasource.common.connectionProperties"));
            dataSource.setUseGlobalDataSourceStat(Boolean.parseBoolean(env.getProperty("spring.datasource.common.useGlobalDataSourceStat")));
            dataSource.setRemoveAbandoned(Boolean.parseBoolean(env.getProperty("spring.datasource.common.removeAbandoned")));
            dataSource.setRemoveAbandonedTimeout(Integer.parseInt(Objects.requireNonNull(env.getProperty("spring.datasource.common.removeAbandonedTimeout"))));
            dataSource.setLogAbandoned(Boolean.parseBoolean(env.getProperty("spring.datasource.common.logAbandoned")));
            dataSource.setSocketTimeout(Integer.parseInt(Objects.requireNonNull(env.getProperty("spring.datasource.common.socketTimeout"))));
            dataSource.setConnectTimeout(Integer.parseInt(Objects.requireNonNull(env.getProperty("spring.datasource.common.connectTimeout"))));
        }



        String driverClassName = dataSourceMap.get("driver").toString();
        String url = dataSourceMap.get("url").toString();
        String username = dataSourceMap.get("username").toString();
        String password = dataSourceMap.get("password").toString();

        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setDriverClassName(driverClassName);

        List<Filter> filterList = dataSource.getProxyFilters();
        boolean isExists = false;
        for (Filter filter : filterList) {
            if (filter instanceof WallFilter) {
                ((WallFilter) filter).setConfig(wallConfig());
                isExists = true;
            }
        }
        if (!isExists) {
            filterList = new ArrayList<>();
            filterList.add(wallFilter());
            dataSource.setProxyFilters(filterList);
        }
        dataSource.init();
        return dataSource;
    }


    @Override
    public void setEnvironment(Environment environment) {
        try {
            initDefaultDataSource(environment);
            initslaveDataSources(environment);
        } catch (SQLException e) {
            log.error("加载数据源异常", e);
        }
    }

    @Override
    public void registerBeanDefinitions(AnnotationMetadata annotationMetadata, BeanDefinitionRegistry beanDefinitionRegistry) {

        Map<Object, Object> targetDataSources = new HashMap<>();
        //添加默认数据源
        targetDataSources.put("dataSource", this.defaultDataSource);
        DynamicDataSourceContextHolder.dataSourceIds.add("TargetDataSource");
        //添加其他数据源
        targetDataSources.putAll(slaveDataSources);
        DynamicDataSourceContextHolder.dataSourceIds.addAll(slaveDataSources.keySet());
        //创建DynamicDataSource
        GenericBeanDefinition beanDefinition = new GenericBeanDefinition();
        beanDefinition.setBeanClass(DynamicDataSource.class);
        beanDefinition.setSynthetic(true);
        MutablePropertyValues mpv = beanDefinition.getPropertyValues();
        mpv.addPropertyValue("defaultTargetDataSource", defaultDataSource);
        mpv.addPropertyValue("targetDataSources", targetDataSources);
        //注册 - BeanDefinitionRegistry
        beanDefinitionRegistry.registerBeanDefinition("TargetDataSource", beanDefinition);
        log.info("Dynamic DataSource Registry");
    }


}