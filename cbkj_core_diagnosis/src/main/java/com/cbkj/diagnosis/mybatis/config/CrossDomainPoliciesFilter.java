package com.cbkj.diagnosis.mybatis.config;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/4 14:39
 * @Version 1.0
 */
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@WebFilter("/*")
public class CrossDomainPoliciesFilter implements javax.servlet.Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (response instanceof HttpServletResponse) {
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            httpResponse.setHeader("X-Permitted-Cross-Domain-Policies", "master-only");
        }
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
    }
}

