package com.cbkj.diagnosis.common.enums;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/6 16:45
 * @Version 1.0
 */
public enum MedicalExpenseType {
    // 定义枚举类型及其对应的编码
    COMPREHENSIVE_MEDICAL_SERVICE(1, "综合医疗服务类"),
    TRADITIONAL_CHINESE_MEDICINE(2, "中医类"),
    DIAGNOSIS(3, "诊断类"),
    CHINESE_MEDICINE(4, "中药类"),
    WESTERN_MEDICINE(5, "西药类"),
    TREATMENT(6, "治疗类"),
    OTHER(7, "其他类"),
    DECOCTION_FEE(10, "煎药费用金额"),
    PERSONAL_COST(11, "个人承担费"),
    MEDICAL_INSURANCE_REIMBURSEMENT(12, "医保报销金额"),
    OUTPATIENT_EMERGENCY_FEE(13, "门 (急) 诊费用金额（元）"),
    MEDICAL_EXPENSE_SETTLEMENT_METHOD(14, "医疗费用结算方式代码");

    private final int code;
    private final String name;

    // 构造函数
    MedicalExpenseType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    // 获取编码
    public int getCode() {
        return code;
    }

    // 通过编码获取枚举名称
    public static String getNameByCode(int code) {
        for (MedicalExpenseType type : MedicalExpenseType.values()) {
            if (type.getCode() == code) {
                return type.name;
            }
        }
        return "未知编码";
    }

    // 重写 toString 方法返回名称
    @Override
    public String toString() {
        return name;
    }

    public static void main(String[] args) {
        // 测试通过编码获取名称
        int testCode = 10;
        String name = MedicalExpenseType.getNameByCode(testCode);
        System.out.println("编码 " + testCode + " 对应的名称是: " + name);
    }
}
