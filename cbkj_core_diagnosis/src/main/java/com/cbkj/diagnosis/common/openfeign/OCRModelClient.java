package com.cbkj.diagnosis.common.openfeign;

import com.cbkj.diagnosis.common.openfeign.reqAndres.OCRModelRes;
import com.cbkj.diagnosis.common.openfeign.reqAndres.OcrResponseDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/10 14:14
 * @Version 1.0
 */
@FeignClient(value = "ocr.api", url = "${ocr.api.base.url}", fallback = FallBackOCRModelClient.class)
public interface OCRModelClient {

    @PostMapping(
            value = "/files/upload",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE
    )
    public OCRModelRes ocrImageUpload(@RequestPart("file") MultipartFile file,
                                      @RequestPart("user") String user,
                                      @RequestHeader("Authorization") String authorization);


    @PostMapping(
            value = "/workflows/run",
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    public OcrResponseDTO ocrResult(@RequestBody HashMap<String, Object>  params, @RequestHeader("Authorization") String authorization);

}
