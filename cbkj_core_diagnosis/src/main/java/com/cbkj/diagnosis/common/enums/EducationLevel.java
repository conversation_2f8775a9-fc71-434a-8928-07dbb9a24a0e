package com.cbkj.diagnosis.common.enums;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/7 14:20
 * @Version 1.0
 */
public enum EducationLevel {
    GRADUATE_EDUCATION("10", "研究生教育"),
    DOCTORAL_GRADUATE("11", "博士研究生毕业"),
    DOCTORAL_COMPLETION("12", "博士研究生结业"),
    DOCTORAL_DISCONTINUED("13", "博士研究生肄业"),
    MASTER_GRADUATE("14", "硕士研究生毕业"),
    MASTER_COMPLETION("15", "硕士研究生结业"),
    MASTER_DISCONTINUED("16", "硕士研究生肄业"),
    GRADUATE_CLASS_GRADUATE("17", "研究生班毕业"),
    GRADUATE_CLASS_COMPLETION("18", "研究生班结业"),
    GRADUATE_CLASS_DISCONTINUED("19", "研究生班肄业"),
    BACHELOR_EDUCATION("20", "大学本科教育"),
    BACHELOR_GRADUATE("21", "大学本科毕业"),
    BACHELOR_COMPLETION("22", "大学本科结业"),
    BACHELOR_DISCONTINUED("23", "大学本科肄业"),
    REGULAR_COLLEGE_GRADUATE("28", "大学普通班毕业"),
    COLLEGE_EDUCATION("30", "大学专科教育"),
    COLLEGE_GRADUATE("31", "大学专科毕业"),
    COLLEGE_COMPLETION("32", "大学专科结业"),
    COLLEGE_DISCONTINUED("33", "大学专科肄业"),
    SECONDARY_VOCATIONAL_EDUCATION("40", "中等职业教育"),
    SECONDARY_SPECIALIZED_GRADUATE("41", "中等专科毕业"),
    SECONDARY_SPECIALIZED_COMPLETION("42", "中等专科结业"),
    SECONDARY_SPECIALIZED_DISCONTINUED("43", "中等专科肄业"),
    VOCATIONAL_HIGH_SCHOOL_GRADUATE("44", "职业高中毕业"),
    VOCATIONAL_HIGH_SCHOOL_COMPLETION("45", "职业高中结业"),
    VOCATIONAL_HIGH_SCHOOL_DISCONTINUED("46", "职业高中肄业"),
    TECHNICIAN_SCHOOL_GRADUATE("47", "技工学校毕业"),
    TECHNICIAN_SCHOOL_COMPLETION("48", "技工学校结业"),
    TECHNICIAN_SCHOOL_DISCONTINUED("49", "技工学校肄业"),
    HIGH_SCHOOL_EDUCATION("60", "普通高级中学教育"),
    HIGH_SCHOOL_GRADUATE("61", "普通高中毕业"),
    HIGH_SCHOOL_COMPLETION("62", "普通高中结业"),
    HIGH_SCHOOL_DISCONTINUED("63", "普通高中肄业"),
    JUNIOR_HIGH_SCHOOL_EDUCATION("70", "初级中学教育"),
    JUNIOR_HIGH_SCHOOL_GRADUATE("71", "初中毕业"),
    JUNIOR_HIGH_SCHOOL_DISCONTINUED("73", "初中肄业"),
    PRIMARY_SCHOOL_EDUCATION("80", "小学教育"),
    PRIMARY_SCHOOL_GRADUATE("81", "小学毕业"),
    PRIMARY_SCHOOL_DISCONTINUED("83", "小学肄业"),
    OTHER("90", "其他");

    private final String code;
    private final String name;

    EducationLevel(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    // 根据代码获取枚举实例
    public static EducationLevel fromCode(String code) {
        for (EducationLevel level : EducationLevel.values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        throw new IllegalArgumentException("No enum constant " + EducationLevel.class.getName() + " for code: " + code);
    }

    // 根据代码获取名称
    public static String getNameByCode(String code) {
        for (EducationLevel level : EducationLevel.values()) {
            if (level.getCode().equals(code)) {
                return level.getName();
            }
        }
        return "未知";
    }
}
