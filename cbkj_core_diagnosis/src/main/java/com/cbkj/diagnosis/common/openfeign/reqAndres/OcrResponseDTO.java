package com.cbkj.diagnosis.common.openfeign.reqAndres;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/10 15:20
 * @Version 1.0
 */
import lombok.Data;
import java.util.List;

@Data
public class OcrResponseDTO {
    private String task_id;
    private String workflow_run_id;
    private DataDTO data;

    @Data
    public static class DataDTO {
        private String id;
        private String workflow_id;
        private String status;
        private OutputsDTO outputs;
        private String error;
        private double elapsed_time;
        private int total_tokens;
        private int total_steps;
        private long created_at;
        private long finished_at;
    }

    @Data
    public static class OutputsDTO {
        private List<ResultDTO> result;
    }

    @Data
    public static class ResultDTO {
        private String status;
        private String message;
        private InnerResultDTO result;
    }

    @Data
    public static class InnerResultDTO {
        private String category;
        private String summary;
    }
}


