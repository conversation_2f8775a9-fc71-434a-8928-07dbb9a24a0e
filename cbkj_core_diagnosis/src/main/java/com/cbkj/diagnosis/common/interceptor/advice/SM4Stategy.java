package com.cbkj.diagnosis.common.interceptor.advice;


import com.cbkj.diagnosis.common.interceptor.SecretStrategy;
import com.cbkj.diagnosis.utils.SM4Util;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service("sm4Stategy")
public class SM4Stategy implements SecretStrategy {
    @Override
    public String encrypt(String input) {
        if (StringUtils.isNotBlank(input)) {
            try {
                return new String(Hex.encode(SM4Util.sm4Encrypt(SM4Util.KEY_SIZE_BITS_A, SM4Util.IV_SIZE_BYTES_A, input.getBytes(StandardCharsets.UTF_8))));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return input;
        }
    }

    @Override
    public String decrypt(String input) {
        if (StringUtils.isNotBlank(input)) {
            try {
                byte[] decode = Hex.decode(input);
                byte[] decryptedData = SM4Util.sm4Decrypt(SM4Util.KEY_SIZE_BITS_A, SM4Util.IV_SIZE_BYTES_A, decode);
                return new String(decryptedData, StandardCharsets.UTF_8);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return input;
    }

    public static void main(String[] args) {
        //6b24ac6d764483fbbb190f
        //330183199406110036
        try {
//            byte[] decode = Hex.decode("6b24ac6d764483fbbb190f");
//            byte[] decryptedData = SM4Util.sm4Decrypt(SM4Util.KEY_SIZE_BITS_A, SM4Util.IV_SIZE_BYTES_A, decode);
//            System.out.println(new String(decryptedData, StandardCharsets.UTF_8));
            System.out.println(new String(Hex.encode(SM4Util.sm4Encrypt(SM4Util.KEY_SIZE_BITS_A, SM4Util.IV_SIZE_BYTES_A, "13700000000".getBytes(StandardCharsets.UTF_8)))));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
