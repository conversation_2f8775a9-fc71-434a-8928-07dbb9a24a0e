package com.cbkj.diagnosis.common.interceptor;


import com.cbkj.diagnosis.common.interceptor.advice.TianAnDecryptIntercept;
import com.cbkj.diagnosis.common.interceptor.advice.TianAnEncryptIntercept;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 天安加密配置
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = "interceptor.tianan", havingValue = "true")
public class CBKJSecretConfiguration {

    @Bean
    public TianAnDecryptIntercept tianAnDecryptIntercept() {
        return new TianAnDecryptIntercept();
    }

    @Bean
    public TianAnEncryptIntercept tianAnEncryptIntercept() {
        return new TianAnEncryptIntercept();
    }

}
