package com.cbkj.diagnosis.common;


public interface MyThreadPoolTaskSchedulerMBean {

    public int getCorePoolSize() ;

    public void setCorePoolSize(int corePoolSize) ;

    public int getMaxPoolSize() ;

    public void setMaxPoolSize(int maxPoolSize) ;

    public int getQueueCapacity() ;

    public void setQueueCapacity(int queueCapacity) ;

    public int getKeepAliveSeconds() ;

    public void setKeepAliveSeconds(int keepAliveSeconds) ;
}
