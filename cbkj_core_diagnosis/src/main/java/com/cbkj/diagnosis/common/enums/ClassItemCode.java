package com.cbkj.diagnosis.common.enums;

/**
 * Created by zbh on 2024/6/11 09:02
 *
 * @description：大类子代码
 */
public enum ClassItemCode implements EnumItem{

    TCM_FEE("1.1", "一般医疗服务费-中医辨证论治费"),
    TCM_TREATMENTFEE("2.1", "中医治疗费"),
    IMAGING_FEES("3.1", "影像学诊断费"),
    FEES_FOR_LABORATORY_TESTS("3.2", "实验室诊断费"),
    CHINESE_HERBAL_MEDICINE_FEE("4.1", "中草药费"),
    COST_OF_CHINESE_MEDICINE("4.2", "中成药费"),
    COST_OF_CHINESE_MEDICINE_INS("4.3", "中成药费-医疗机构中药制剂费"),
    EXPENSES_FOR_WESTERN_MEDICINE("5.1", "西药费"),
    SURGICAL_TREATMENT_COST("6.1", "手术治疗费"),
    OTHER_EXPENSES("7.1", "其他费"),
    PERSONAL_EXPENSE("10.1", "个人承担费"),
    INSURANCE_REIMBURSEMENT_AMOUNT("11.1", "医保报销金额"),
    CONSULTATION_FEE("12.1", "门 (急) 诊费用金额（元）"),
    SETTLEMENT_CODE("13.1", "医疗费用结算方式代码"),
    ;
    private String code;
    private String message;


    ClassItemCode(String code, String message) {
        this.code = code;
        this.message = message;
    }


    @Override
    public Object getName() {
        return name();
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }


    public static String getMassage(String code) {
        ClassItemCode[] enums = values();
        for (ClassItemCode decoctingEnum : enums) {
            if ((decoctingEnum.getCode()).equals(code)) {
                return decoctingEnum.getMessage();
            }
        }
        return null;
    }
}
