//package com.cbkj.diagnosis.common.openfeign;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.codec.Decoder;
//
///**
// * <AUTHOR>
// * @Description TODO
// * @Date 2025/4/29 11:38
// * @Version 1.0
// */
//@Configuration
//public class FeignStreamConfig {
//
//    @Bean
//    public Decoder feignDecoder() {
//        return new ReactiveDecoder(
//                new JacksonDecoder(),
//                // 处理不完整JSON的容错逻辑
//                str -> {
//                    String json = str.trim();
//                    if (!json.startsWith("{")) json = "{" + json;
//                    if (!json.endsWith("}")) json += "}";
//                    return json;
//                }
//        );
//    }
//
//    @Bean
//    public Logger.Level feignLogger() {
//        return Logger.Level.FULL; // 调试时开启
//    }
//}
