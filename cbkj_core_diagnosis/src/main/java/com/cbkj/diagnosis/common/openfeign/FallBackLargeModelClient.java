package com.cbkj.diagnosis.common.openfeign;

import com.cbkj.diagnosis.beans.business.GPTAskRequest;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/27 15:42
 * @Version 1.0
 */
@Component
public class FallBackLargeModelClient implements LargeModelClient {
    @Override
    public ResEntity<LargeModelRes> gptAsk(GPTAskRequest gptAskRequest) {
        ResEntity<LargeModelRes> largeModelResResEntity = new ResEntity<>();
        largeModelResResEntity.setStatus(false);
        largeModelResResEntity.setCode(1);
        largeModelResResEntity.setMessage("服务异常，请稍后重试");
        return largeModelResResEntity;
    }

//    @Override
//    public ResEntity<Object> gptAskFlux(GPTAskRequest gptAskRequest) {
//        return ResEntity.error("服务降级");
//    }
}
