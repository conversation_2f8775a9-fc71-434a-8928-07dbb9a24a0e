package com.cbkj.diagnosis.common.openfeign;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/27 17:16
 * @Version 1.0
 */
public class LargeModelJsonExtractor {
    public static String extractJson(String text) {
        Pattern pattern = Pattern.compile("```json\\n(.*?)\\n```", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(text);

        String jsonContent = "";
        if (matcher.find()) {
            // 提取匹配到的内容并去除所有空白字符
            jsonContent = matcher.group(1).replaceAll("\\s", "");
        }
        return jsonContent;
    }
}
