package com.cbkj.diagnosis.common.openfeign;

import com.cbkj.diagnosis.beans.business.GPTAskRequest;
import com.cbkj.diagnosis.common.openfeign.reqAndres.LargeModelRes;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/27 14:38
 * @Version 1.0
 */
@FeignClient(value = "gpt.api", url = "${gpt.api.base.url}", fallback = FallBackLargeModelClient.class)
public interface LargeModelClient {

    /**
     * 调用大模型接口
     * 设置接口返回格式是Content-Type: text
     * @return
     */
    @PostMapping(value = "/api/v1/agents/ask",  consumes = "application/json", produces = "text/plain")
    ResEntity<LargeModelRes> gptAsk(@RequestBody GPTAskRequest gptAskRequest);



}
