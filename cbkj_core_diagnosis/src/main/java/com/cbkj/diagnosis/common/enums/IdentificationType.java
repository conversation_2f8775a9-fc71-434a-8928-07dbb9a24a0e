package com.cbkj.diagnosis.common.enums;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/7 14:09
 * @Version 1.0
 */
public enum IdentificationType {
    RESIDENT_ID_CARD("01", "居民身份证"),
    RESIDENT_HOUSEHOLD_REGISTER("02", "居民户口簿"),
    PASSPORT("03", "护照"),
    MILITARY_ID("04", "军官证"),
    DRIVER_LICENSE("05", "驾驶证"),
    HK_MACAO_RESIDENT_ENTRY_PERMIT("06", "港澳居民来往内地通行证"),
    TAIWAN_RESIDENT_ENTRY_PERMIT("07", "台湾居民来往内地通行证"),
    OTHER_LEGAL_DOCUMENT("99", "其他法定有效证件");

    private final String code;
    private final String name;

    IdentificationType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    // 你可以根据需要添加其他方法，比如通过代码获取枚举实例等
    public static IdentificationType fromCode(String code) {
        for (IdentificationType type : IdentificationType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant " + IdentificationType.class.getName() + "." + code);
    }
    // 根据证件类型代码获取证件类型名称
    public static String getValueName(String code) {
        for (IdentificationType type : IdentificationType.values()) {
            if (type.getCode().equals(code)) {
                return type.getName();
            }
        }
        return "未知";
    }
}
