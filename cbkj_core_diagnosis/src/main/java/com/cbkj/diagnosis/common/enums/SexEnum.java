package com.cbkj.diagnosis.common.enums;

/**
 * Created by zbh on 2024/6/11 10:23
 *
 * @description：性别枚举
 */
public enum SexEnum implements EnumItem{

    M("男"),
    F("女"),

    ;

    private String message;

    SexEnum(String message) {
        this.message = message;
    }


    public String message() {
        return this.message;
    }

    @Override
    public Object getName() {
        return name();
    }

    @Override
    public String getCode() {
        return this.message;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
