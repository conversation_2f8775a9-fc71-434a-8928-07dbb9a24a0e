package com.cbkj.diagnosis.common.enums;

/**
 * Created by zbh on 2022/8/10 10:23
 *
 * @description：煎煮枚举
 */
public enum FlagEnum implements EnumItem{

    Y("1","是"),
    N("2","否"),
    ;
    private String code;
    private String message;


    FlagEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }


    @Override
    public Object getName() {
        return name();
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }


    public static String getMassage(String code) {
        FlagEnum[] enums = values();
        for (FlagEnum flagEnum : enums) {
            if ((flagEnum.getCode()).equals(code)) {
                return flagEnum.getMessage();
            }
        }
        return null;
    }

}
