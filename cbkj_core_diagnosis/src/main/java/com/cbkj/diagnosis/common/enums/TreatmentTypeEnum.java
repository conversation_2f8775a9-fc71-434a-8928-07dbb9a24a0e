package com.cbkj.diagnosis.common.enums;

/**
 * Created by zbh on 2024/6/7 9:36
 *
 * @description：就诊类型枚举
 */
public enum TreatmentTypeEnum implements EnumItem{

    TCM("1", "中医"),
    WESTERN_MEDICINE("2", "西医"),
            ;
    private String code;
    private String message;


    TreatmentTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }


    @Override
    public Object getName() {
        return name();
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }


    public static String getMassage(String code) {
        TreatmentTypeEnum[] enums = values();
        for (TreatmentTypeEnum typeEnum : enums) {
            if ((typeEnum.getCode()).equals(code)) {
                return typeEnum.getMessage();
            }
        }
        return null;
    }
}
