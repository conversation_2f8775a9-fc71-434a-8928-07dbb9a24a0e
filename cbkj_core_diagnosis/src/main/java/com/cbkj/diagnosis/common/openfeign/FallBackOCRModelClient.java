package com.cbkj.diagnosis.common.openfeign;

import com.cbkj.diagnosis.common.openfeign.reqAndres.OCRModelRes;
import com.cbkj.diagnosis.common.openfeign.reqAndres.OcrResponseDTO;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/10 14:15
 * @Version 1.0
 */
@Component
public class FallBackOCRModelClient implements OCRModelClient{


    @Override
    public OCRModelRes ocrImageUpload(MultipartFile file, String user, String authorization) {
        return null;
    }

    @Override
    public OcrResponseDTO ocrResult(HashMap<String, Object> params, String authorization) {
        return new OcrResponseDTO ();
    }


}
