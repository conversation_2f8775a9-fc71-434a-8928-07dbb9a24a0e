package com.cbkj.diagnosis.common;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class MyThreadPoolTaskScheduler implements MyThreadPoolTaskSchedulerMBean {
    private int corePoolSize;
    private int maxPoolSize;
    private int queueCapacity;
    private int keepAliveSeconds;

    private ThreadPoolTaskExecutor taskExecutor;

    public ThreadPoolTaskExecutor getExecutor() {
        return taskExecutor;
    }

    public MyThreadPoolTaskScheduler() {
        init();
    }

    /**
     * 初始化
     */
    private void init() {
        synchronized (MyThreadPoolTaskSchedulerMBean.class) {
            int logicCpus = Runtime.getRuntime().availableProcessors();
            corePoolSize = logicCpus;
            maxPoolSize = logicCpus * 2;
            queueCapacity = 50;
            keepAliveSeconds = 100;
            taskExecutor = newAsync();
        }
    }




    public ThreadPoolTaskExecutor newAsync() {

        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置核心线程数
        //io密集型线程池设定最佳线程数目 = （ （线程池设定的线程等待时间+线程 CPU 时间） /线程 CPU 时间 ） * CPU 数目
        //cpu密集型，一般就是cpu核心数 或者是 cpu核心数+1
        taskExecutor.setCorePoolSize(corePoolSize);
        // 线程池维护线程的最大数量，只有在缓冲队列满了以后才会申请超过核心线程数的线程,
        taskExecutor.setMaxPoolSize(maxPoolSize);
        //缓存队列
        taskExecutor.setQueueCapacity(queueCapacity);
        //允许的空闲时间，当超过了核心线程数之外的线程在空闲时间到达之后会被销毁
        taskExecutor.setKeepAliveSeconds(keepAliveSeconds);
        //异步方法内部线程名称
        taskExecutor.setThreadNamePrefix("my-AsyncExecutor-");
        //拒绝策略 :调用者线程执行
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        taskExecutor.initialize();
        return taskExecutor;
    }




    @Override
    public int getCorePoolSize() {
        return corePoolSize;
    }

    @Override
    public void setCorePoolSize(int corePoolSize) {

    }

    @Override
    public int getMaxPoolSize() {
        return maxPoolSize;
    }

    @Override
    public void setMaxPoolSize(int maxPoolSize) {
        this.maxPoolSize = maxPoolSize;
        //每次修改必须重新发布才可以生效 ,可以使用 Apollo来实现动态配置 线程池参数
        taskExecutor.setCorePoolSize(maxPoolSize);
    }

    @Override
    public int getQueueCapacity() {
        return queueCapacity;
    }

    @Override
    public void setQueueCapacity(int queueCapacity) {

    }

    @Override
    public int getKeepAliveSeconds() {
        return keepAliveSeconds;
    }

    @Override
    public void setKeepAliveSeconds(int keepAliveSeconds) {

    }
}
