package com.cbkj.diagnosis.common.openfeign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24 10:58
 * @Version 1.0
 */
@FeignClient(value = "oath.self", url = "${oath.self.address}", fallback = FallbackService.class
//        ,decode404 = true
)
public interface TestFeignClient {
    @GetMapping("/randomImage/{key}")
    Object test(@PathVariable String key);
}
