package com.cbkj.diagnosis.common.openfeign;

import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24 16:38
 * @Version 1.0
 */
@Component
public class FallbackService implements TestFeignClient{


    @Override
    public Object test(String key) {
        return ResEntity.error("请求资源不存在（已降级处理）");
    }

    
}
