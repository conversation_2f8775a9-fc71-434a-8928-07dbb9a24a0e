package com.cbkj.diagnosis.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * Created by zbh on 2022/8/10 10:23
 *
 * @description：记录类型枚举
 */
public enum RecordTypeEnum {

    PREDIAGNOSIS("1","预诊"),
    VISIT_CLINIC("2","就诊"),
    PROPAGANDA_EDUCATION("3","宣教"),
    FOLLOW_UP("4","随访"),
    ;
    private String code;
    private String message;

    @JsonCreator
    public static RecordTypeEnum fromCode(int code) {
        RecordTypeEnum[] preStatusEnums = values();
        for (RecordTypeEnum preStatusEnum : preStatusEnums) {
            if ((preStatusEnum.code()).equals(code)) {
                return preStatusEnum;
            }
        }
        return null;
    }


    RecordTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    RecordTypeEnum(String message) {
        this.message = message;
    }

    public String code() {
        return this.code;
    }

    public String message() {
        return this.message;
    }

    public static String getCode(String message) {
        RecordTypeEnum[] preStatusEnums = values();
        for (RecordTypeEnum preStatusEnum : preStatusEnums) {
            if ((preStatusEnum.message()).equals(message)) {
                return preStatusEnum.name();
            }
        }
        return null;
    }

    public static String getMassage(String code) {
        RecordTypeEnum[] preStatusEnums = values();
        for (RecordTypeEnum preStatusEnum : preStatusEnums) {
            if ((preStatusEnum.code()).equals(code)) {
                return preStatusEnum.message();
            }
        }
        return null;
    }

    public String getName(){
        return name();
    }

}
