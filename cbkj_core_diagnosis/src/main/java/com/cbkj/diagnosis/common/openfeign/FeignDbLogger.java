package com.cbkj.diagnosis.common.openfeign;

import com.alibaba.fastjson.JSON;
import feign.*;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/11 10:36
 * @Version 1.0
 */
//@Component
public class FeignDbLogger extends Logger {
    private static final org.slf4j.Logger log = LoggerFactory.getLogger(FeignDbLogger.class);

    @Override
    protected void log(String configKey, String format, Object... args) {
        log.info(String.format(methodTag(configKey) + format, args));
    }

    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        super.logRequest(configKey, logLevel, request);
//        super.logRequest(configKey, logLevel, request);
//        RequestTemplate requestTemplate = request.requestTemplate();
//        String fullUrl = requestTemplate.feignTarget().url() + requestTemplate.url();
//        // 请求先不落库，和响应一起保存
////        super.logRequest(configKey, logLevel, request);
//        byte[] bodyBytes = requestTemplate.body();
//        String body = null;
//        if (bodyBytes != null && bodyBytes.length > 0) {
//            body = new String(bodyBytes, requestTemplate.requestCharset());
//        }else {
//            Map<String, Collection<String>> queries = requestTemplate.queries();
//            if (!queries.isEmpty()){
//                body = JSON.toJSONString( queries);
//            }
//        }
//        log.info("Feign Request: {} {} 请求头: {} 入参: {}",
//                request.httpMethod(),
//                fullUrl,
//                request.headers(),
//                body);

    }

    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime) throws IOException {
        byte[] bodyData = Util.toByteArray(response.body().asInputStream());
        Request request = response.request();
        RequestTemplate requestTemplate = request.requestTemplate();
        String fullUrl = requestTemplate.feignTarget().url() + requestTemplate.url();
        byte[] bodyBytes = requestTemplate.body();
        String body = null;
        if (bodyBytes != null && bodyBytes.length > 0) {
            body = new String(bodyBytes, requestTemplate.requestCharset());
        }else {
            Map<String, Collection<String>> queries = requestTemplate.queries();
            if (!queries.isEmpty()){
                body = JSON.toJSONString( queries);
            }
        }
        //出参
        String bodyText = bodyData.length > 0 ? new String(bodyData, response.charset()) : "<empty>";
        log.info("Feign Request: {} {} 请求头: {} 入参: {} 出参: {}",
                request.httpMethod(),
                fullUrl,
                request.headers(),
                body,
                bodyText);

        // 重新包装响应体，防止被消费掉
        return response.toBuilder().body(bodyData).build();
    }

}
