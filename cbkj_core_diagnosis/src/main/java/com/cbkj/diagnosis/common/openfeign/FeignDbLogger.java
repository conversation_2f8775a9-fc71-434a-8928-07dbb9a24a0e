package com.cbkj.diagnosis.common.openfeign;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.config.FeignLoggerConfig;
import feign.*;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/11 10:36
 * @Version 1.0
 */
//@Component
public class FeignDbLogger extends Logger {
    private static final org.slf4j.Logger log = LoggerFactory.getLogger(FeignDbLogger.class);

    private final FeignLoggerConfig config;

    public FeignDbLogger(FeignLoggerConfig config) {
        this.config = config;
    }


    @Override
    protected void log(String configKey, String format, Object... args) {
        log.info(String.format(methodTag(configKey) + format, args));
    }

    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        super.logRequest(configKey, logLevel, request);
//        super.logRequest(configKey, logLevel, request);
//        RequestTemplate requestTemplate = request.requestTemplate();
//        String fullUrl = requestTemplate.feignTarget().url() + requestTemplate.url();
//        // 请求先不落库，和响应一起保存
////        super.logRequest(configKey, logLevel, request);
//        byte[] bodyBytes = requestTemplate.body();
//        String body = null;
//        if (bodyBytes != null && bodyBytes.length > 0) {
//            body = new String(bodyBytes, requestTemplate.requestCharset());
//        }else {
//            Map<String, Collection<String>> queries = requestTemplate.queries();
//            if (!queries.isEmpty()){
//                body = JSON.toJSONString( queries);
//            }
//        }
//        log.info("Feign Request: {} {} 请求头: {} 入参: {}",
//                request.httpMethod(),
//                fullUrl,
//                request.headers(),
//                body);

    }

    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime) throws IOException {
        byte[] bodyData = Util.toByteArray(response.body().asInputStream());
        Request request = response.request();
        RequestTemplate requestTemplate = request.requestTemplate();
        String fullUrl = requestTemplate.feignTarget().url() + requestTemplate.url();
        byte[] bodyBytes = requestTemplate.body();
        String body = null;

        // 检查是否需要过滤URL
        boolean shouldFilterUrl = shouldFilterByUrl(fullUrl);

        // 处理请求参数
        if (bodyBytes != null && bodyBytes.length > 0) {
            body = shouldFilterUrl ? "[URL匹配过滤规则，内容已省略1]" :
                    filterLargeContent(new String(bodyBytes, requestTemplate.requestCharset()), "请求体");
        } else {
            Map<String, Collection<String>> queries = requestTemplate.queries();
            if (!queries.isEmpty()) {
                String queryString = JSON.toJSONString(queries);
                body = shouldFilterUrl ? "[URL匹配过滤规则，内容已省略2]" :
                        filterLargeContent(queryString, "查询参数");
            }
        }

        // 处理响应参数
        String bodyText = bodyData.length > 0 ?
                (shouldFilterUrl ? "[URL匹配过滤规则，内容已省略3]" :
                        filterLargeContent(new String(bodyData, response.charset()), "响应体")) : "<empty>";

        log.info("Feign Request: {} {} 请求头: {} 入参: {} 出参: {}",
                request.httpMethod(),
                fullUrl,
                request.headers(),
                body,
                bodyText);

        // 重新包装响应体，防止被消费掉
        return response.toBuilder().body(bodyData).build();
    }

    /**
     * 检查URL是否需要过滤
     */
    private boolean shouldFilterByUrl(String url) {
        if (config == null || !config.isEnableContentFilter()) {
            return false;
        }
        if (config.getFilterUrlKeywords() == null || config.getFilterUrlKeywords().isEmpty()) {
            return false;
        }
        return config.getFilterUrlKeywords().stream()
                .anyMatch(keyword -> url.toLowerCase().contains(keyword.toLowerCase()));
    }

    /**
     * 过滤大内容和图片内容，避免日志过长
     */
    private String filterLargeContent(String content, String contentType) {
        if (content == null) {
            return null;
        }

        // 如果禁用了内容过滤，直接返回原内容（但仍然限制长度）
        if (config == null || !config.isEnableContentFilter()) {
            int maxLength = config != null ? config.getMaxContentLength() : 2000;
            if (content.length() > maxLength) {
                return content.substring(0, maxLength) +
                        String.format("...[内容过长，已截断，总长度: %d字符]", content.length());
            }
            return content;
        }

        // 检查是否为图片内容
        if (isImageContent(content)) {
            return String.format("[%s包含图片数据，长度: %d字符，已省略显示]", contentType, content.length());
        }

        // 检查是否为二进制内容（包含大量不可打印字符）
        if (containsBinaryContent(content)) {
            return String.format("[%s包含二进制数据，长度: %d字符，已省略显示]", contentType, content.length());
        }

        // 限制内容长度，避免日志过长
        int maxLength = config.getMaxContentLength();
        if (content.length() > maxLength) {
            return content.substring(0, maxLength) +
                    String.format("...[内容过长，已截断，总长度: %d字符]", content.length());
        }

        return content;
    }

    /**
     * 检查是否为图片内容
     */
    private boolean isImageContent(String content) {
        if (config == null) {
            return content.startsWith("data:image/") || content.contains("base64,");
        }

        return config.getImageKeywords().stream()
                .anyMatch(keyword -> content.toLowerCase().contains(keyword.toLowerCase()));
    }

    /**
     * 检查内容是否包含二进制数据
     */
    private boolean containsBinaryContent(String content) {
        if (content.length() < 100) {
            return false; // 短内容不太可能是二进制
        }

        int binaryCharCount = 0;
        int sampleSize = Math.min(content.length(),
                config != null ? config.getBinarySampleSize() : 500);

        for (int i = 0; i < sampleSize; i++) {
            char c = content.charAt(i);
            // 检查是否为不可打印字符（除了常见的空白字符）
            if (c < 32 && c != '\n' && c != '\r' && c != '\t') {
                binaryCharCount++;
            }
        }

        // 使用配置的阈值判断是否为二进制内容
        double threshold = config != null ? config.getBinaryThreshold() : 0.1;
        return (double) binaryCharCount / sampleSize > threshold;
    }

}
