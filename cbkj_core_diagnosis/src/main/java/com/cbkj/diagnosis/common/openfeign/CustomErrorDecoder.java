package com.cbkj.diagnosis.common.openfeign;

import com.cbkj.diagnosis.common.exception.CustomRuntimeException;
import feign.FeignException;
import feign.Response;
import feign.codec.ErrorDecoder;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/25 14:53
 * @Version 1.0
 */
public class CustomErrorDecoder implements ErrorDecoder {
    @Override
    public Exception decode(String methodKey, Response response) {
        if (response.status() == 404) {
            return new CustomRuntimeException(404,"资源不存在");  // 自定义404异常:ml-citation{ref="3,8" data="citationList"}
        }
        return FeignException.errorStatus(methodKey, response);  // 其他错误透传:ml-citation{ref="3" data="citationList"}
    }
}
