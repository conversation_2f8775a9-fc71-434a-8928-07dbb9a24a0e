package com.cbkj.diagnosis.common.enums;

/**
 * Created by zbh on 2022/8/10 10:23
 *
 * @description：煎煮枚举
 */
public enum DecoctingEnum implements EnumItem{

    TAKE_THEIR("1", "代煎"),
    DISTRIBUTION("2", "自煎"),
    ;
    private String code;
    private String message;


    DecoctingEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }


    @Override
    public Object getName() {
        return name();
    }

    @Override
    public String getCode() {
       return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }


    public static String getMassage(String code) {
        DecoctingEnum[] enums = values();
        for (DecoctingEnum decoctingEnum : enums) {
            if ((decoctingEnum.getCode()).equals(code)) {
                return decoctingEnum.getMessage();
            }
        }
        return null;
    }
}
