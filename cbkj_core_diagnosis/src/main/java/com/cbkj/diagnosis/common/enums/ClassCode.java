package com.cbkj.diagnosis.common.enums;

/**
 * Created by zbh on 2024/6/7 17:02
 *
 * @description：大类代码
 */
public enum ClassCode implements EnumItem{

    TCM("1", "中医类"),
    Deagnostic("2", "诊断类"),
    CHINESE_MEDICINE("3", "中药类"),
    WESTERN_MEDICINE("4", "西药类"),
    Therapeutic("5", "治疗类"),
    OTHER_CATEGORIES("6", "其他类"),
    AMOUNT_OF_DECOCTING_COST("7", "煎药费用金额"),
    PERSONAL_EXPENSE("10", "个人承担费"),
    INSURANCE_REIMBURSEMENT_AMOUNT("11", "医保报销金额"),
    CONSULTATION_FEE("12", "门 (急) 诊费用金额（元）"),
    SETTLEMENT_CODE("13", "医疗费用结算方式代码"),
    ;
    private String code;
    private String message;


    ClassCode(String code, String message) {
        this.code = code;
        this.message = message;
    }


    @Override
    public Object getName() {
        return name();
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }


    public static String getMassage(String code) {
        ClassCode[] enums = values();
        for (ClassCode decoctingEnum : enums) {
            if ((decoctingEnum.getCode()).equals(code)) {
                return decoctingEnum.getMessage();
            }
        }
        return null;
    }
}
