package com.cbkj.diagnosis.common.enums;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/7 14:23
 * @Version 1.0
 */
public enum MaritalStatus {
    UNMARRIED("10", "未婚"),
    MARRIED("20", "已婚"),
    FIRST_MARRIAGE("21", "初婚"),
    REMARRIAGE("22", "再婚"),
    RENEWED_MARRIAGE("23", "复婚"),
    WIDOWED("30", "丧偶"),
    DIVORCED("40", "离婚"),
    UNSPECIFIED("90", "未说明的婚姻状况");

    private final String code;
    private final String name;

    MaritalStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    // 根据代码获取枚举实例
    public static MaritalStatus fromCode(String code) {
        for (MaritalStatus status : MaritalStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant " + MaritalStatus.class.getName() + " for code: " + code);
    }

    // 根据代码获取名称
    public static String getNameByCode(String code) {
        for (MaritalStatus status : MaritalStatus.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return "未知";
    }
}
