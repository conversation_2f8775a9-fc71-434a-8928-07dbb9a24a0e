package com.cbkj.diagnosis.common.interceptor.advice;

import com.cbkj.diagnosis.common.interceptor.SecretStrategy;
import com.cbkj.diagnosis.utils.AESEncrypt;
import com.cbkj.diagnosis.utils.SystemConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service("adminInfoStrategy")
public class AdminInfoStrategy implements SecretStrategy {
    @Override
    public String encrypt(String input) {
        if (StringUtils.isNotBlank(input)) {
            return AESEncrypt.aes_encrypt(input, SystemConstants.SYS_STR_KEY);
        } else {
            return input;
        }
    }

    @Override
    public String decrypt(String input) {
        if (StringUtils.isNotBlank(input)) {
            return AESEncrypt.aes_decrypt(input, SystemConstants.SYS_STR_KEY);
        } else {
            return input;
        }

    }
}
