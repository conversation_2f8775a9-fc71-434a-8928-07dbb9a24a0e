package com.cbkj.diagnosis.common.interceptor.factory;


import com.cbkj.diagnosis.common.interceptor.SecretStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class SecretStrategyFactory {
    private static String type;
    @Value(value="${interceptor.secret.type:defaultSecret}")
    public void setIp(String typeT){
        type = typeT;
    }
    Map<String, SecretStrategy> bases;
    @Autowired
    public SecretStrategyFactory(Map<String, SecretStrategy> bases) {
        Assert.notNull(bases, "NormalParamStrategyFactory must not be null!");
        this.bases = bases;
    }
    public SecretStrategy getSecretStrategy() {
        return bases.get(type);
    }
}
