//package com.cbkj.diagnosis.common.openfeign;
//
//import feign.RequestInterceptor;
//import feign.RequestTemplate;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * @Description TODO
// * @Date 2025/4/24 17:48
// * @Version 1.0
// */
//@Component
//public class FeignRequestInterceptor implements RequestInterceptor {
//    private static final Logger log = LoggerFactory.getLogger(FeignRequestInterceptor.class);
//
//    @Override
//    public void apply(RequestTemplate template) {
//        String fullUrl = template.feignTarget().url() + template.url();
//        log.info("Feign Request URL: {} {}", template.method(), fullUrl);
//        // 可追加查询参数打印
//
//        log.info("Feign Request Query Params: {}", template.queries());
//
//
//        // 打印 body（只对 POST/PUT/PATCH 这类有请求体的方法有意义）
//        if ("POST".equalsIgnoreCase(template.method())
//                || "PUT".equalsIgnoreCase(template.method())
//                || "PATCH".equalsIgnoreCase(template.method())) {
//            byte[] bodyBytes = template.body();
//            if (bodyBytes != null && bodyBytes.length > 0) {
//                String body = new String(bodyBytes, template.requestCharset());
//                log.info("Feign Request Body: {}", body);
//            } else {
//                log.info("Feign Request Body: <empty>");
//            }
//        }
//
//    }
//}
//
