package com.cbkj.diagnosis.common.webclient;

import com.cbkj.diagnosis.beans.business.GPTAskRequest;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import org.springframework.beans.factory.annotation.Value;
import com.cbkj.diagnosis.beans.business.GPTAskRequest;
import com.cbkj.diagnosis.sysBeans.ResEntity;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/29 15:38
 * @Version 1.0
 */
@Configuration
public class AIWebClient {

    @Value("${gpt.api.base.url}")
    private String baseUrl;
//    @Bean
public Flux<ResEntity> webClient(GPTAskRequest gptAskRequest) {
    // 创建忽略SSL证书的HttpClient
    HttpClient httpClient = HttpClient.create();

    try {
        SslContext sslContext = SslContextBuilder.forClient()
                .trustManager(InsecureTrustManagerFactory.INSTANCE)
                .build();

        httpClient = httpClient.secure(ssl -> ssl.sslContext(sslContext));
    } catch (Exception e) {
        throw new RuntimeException("Failed to create insecure SSL context", e);
    }

    return WebClient.builder()
            .baseUrl(baseUrl)
            .clientConnector(new ReactorClientHttpConnector(httpClient))
            .build()
            .post()
            .uri("/api/v1/agents/ask")
            .contentType(MediaType.APPLICATION_JSON)
            .body(BodyInserters.fromValue(gptAskRequest))
            .retrieve()
            .bodyToFlux(ResEntity.class)
            .timeout(Duration.ofSeconds(30),
                    Flux.defer(() -> Flux.just(
                            ResEntity.<Object>error(500, "调用模型超时")
                    ))
            );
}
}
