package com.cbkj.diagnosis.common.interceptor.advice;


import com.cbkj.diagnosis.common.interceptor.SecretStrategy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service(value = "defaultSecret")
public class DefaultSecretUtils implements SecretStrategy {
    @Override
    public String encrypt(String input) {
        return input;
    }

    @Override
    public String decrypt(String input) {
        return input;
    }
}
