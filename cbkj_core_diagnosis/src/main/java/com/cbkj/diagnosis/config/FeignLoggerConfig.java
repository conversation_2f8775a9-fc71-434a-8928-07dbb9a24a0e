package com.cbkj.diagnosis.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description Feign日志配置
 * @Date 2025/9/11 15:30
 * @Version 1.0
 */
@Component
@ConfigurationProperties(prefix = "feign.logger")
public class FeignLoggerConfig {
    
    /**
     * 最大日志内容长度
     */
    private int maxContentLength = 2000;
    
    /**
     * 是否启用内容过滤
     */
    private boolean enableContentFilter = true;
    
    /**
     * 图片内容的识别关键词
     */
    private List<String> imageKeywords = Arrays.asList(
        "data:image/", 
        "base64,", 
        "image/jpeg", 
        "image/png", 
        "image/gif",
        "image/webp"
    );
    
    /**
     * 需要过滤的URL路径关键词
     */
    private List<String> filterUrlKeywords ;
    
    /**
     * 二进制内容检测的阈值（不可打印字符占比）
     */
    private double binaryThreshold = 0.1;
    
    /**
     * 二进制内容检测的样本大小
     */
    private int binarySampleSize = 500;

    // Getters and Setters
    public int getMaxContentLength() {
        return maxContentLength;
    }

    public void setMaxContentLength(int maxContentLength) {
        this.maxContentLength = maxContentLength;
    }

    public boolean isEnableContentFilter() {
        return enableContentFilter;
    }

    public void setEnableContentFilter(boolean enableContentFilter) {
        this.enableContentFilter = enableContentFilter;
    }

    public List<String> getImageKeywords() {
        return imageKeywords;
    }

    public void setImageKeywords(List<String> imageKeywords) {
        this.imageKeywords = imageKeywords;
    }

    public List<String> getFilterUrlKeywords() {
        return filterUrlKeywords;
    }

    public void setFilterUrlKeywords(List<String> filterUrlKeywords) {
        this.filterUrlKeywords = filterUrlKeywords;
    }

    public double getBinaryThreshold() {
        return binaryThreshold;
    }

    public void setBinaryThreshold(double binaryThreshold) {
        this.binaryThreshold = binaryThreshold;
    }

    public int getBinarySampleSize() {
        return binarySampleSize;
    }

    public void setBinarySampleSize(int binarySampleSize) {
        this.binarySampleSize = binarySampleSize;
    }
}
