<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.SRoadTaskExMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.SRoadTaskEx">
        <result column="s_road_task_id" property="sRoadTaskId" />
        <result column="dept_id" property="deptId" />
        <result column="app_id" property="appId" />
        <result column="dept_code" property="deptCode" />
        <result column="ins_code" property="insCode" />
        <result column="ins_id" property="insId" />
        <result column="record_doctor_id" property="recordDoctorId" />
        <result column="diagnosis_doctor_id" property="diagnosisDoctorId" />
        <result column="ex_type" property="exType" />
        <result column="dept_name" property="deptName" />
        <result column="record_doctor_name" property="recordDoctorName" />
        <result column="diagnosis_doctor_name" property="diagnosisDoctorName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        s_road_task_id, dept_id, app_id, dept_code, ins_code, ins_id, record_doctor_id, diagnosis_doctor_id, ex_type,
            dept_name,record_doctor_name,diagnosis_doctor_name
    </sql>
    <insert id="saveBatch" parameterType="ArrayList">
        INSERT INTO s_road_task_ex (s_road_task_id, dept_id, app_id, dept_code, ins_code, ins_id, record_doctor_id, diagnosis_doctor_id, ex_type,dept_name,record_doctor_name,diagnosis_doctor_name) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.sRoadTaskId}, #{item.deptId}, #{item.appId}, #{item.deptCode}, #{item.insCode}, #{item.insId},#{item.recordDoctorId}, #{item.diagnosisDoctorId},
             #{item.exType},#{item.deptName},#{item.recordDoctorName},#{item.diagnosisDoctorName} )
        </foreach>
    </insert>

</mapper>
