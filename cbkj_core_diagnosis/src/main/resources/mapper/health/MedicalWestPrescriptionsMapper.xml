<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.health.MedicalWestPrescriptionsMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptions">
        <id column="west_prescriptions_id" jdbcType="VARCHAR"  property="westPrescriptionsId" />
        <result column="records_id" jdbcType="VARCHAR" property="recordsId" />
        <result column="prescriptions_time" jdbcType="TIMESTAMP" property="prescriptionsTime" />
        <result column="prescriptions_effective_days" jdbcType="DOUBLE" property="prescriptionsEffectiveDays" />
        <result column="prescription_remark" jdbcType="VARCHAR" property="prescriptionRemark" />
        <result column="prescribing_doctor_sign" jdbcType="VARCHAR" property="prescribingDoctorSign" />
        <result column="reviewed_doctor_sign" jdbcType="VARCHAR" property="reviewedDoctorSign" />
    </resultMap>


    <sql id="Base_Column_List">
    west_prescriptions_id,records_id,prescriptions_time,prescriptions_effective_days,prescription_remark,prescribing_doctor_sign,reviewed_doctor_sign
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptions">
        delete from medical_west_prescriptions where west_prescriptions_id = #{ westPrescriptionsId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from medical_west_prescriptions where west_prescriptions_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptions">
        insert into medical_west_prescriptions (<include refid="Base_Column_List" />) values
        (#{westPrescriptionsId},#{recordsId},#{prescriptionsTime},#{prescriptionsEffectiveDays},#{prescriptionRemark},#{prescribingDoctorSign},#{reviewedDoctorSign})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into medical_west_prescriptions (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.westPrescriptionsId},#{item.recordsId},#{item.prescriptionsTime},#{item.prescriptionsEffectiveDays},#{item.prescriptionRemark},#{item.prescribingDoctorSign},#{item.reviewedDoctorSign})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptions">
        update medical_west_prescriptions
        <set>
             <if test="recordsId != null">
                records_id = #{ recordsId },
             </if>
             <if test="prescriptionsTime != null">
                prescriptions_time = #{ prescriptionsTime },
             </if>
             <if test="prescriptionsEffectiveDays != null">
                prescriptions_effective_days = #{ prescriptionsEffectiveDays },
             </if>
             <if test="prescriptionRemark != null">
                prescription_remark = #{ prescriptionRemark },
             </if>
             <if test="prescribingDoctorSign != null">
                prescribing_doctor_sign = #{ prescribingDoctorSign },
             </if>
             <if test="reviewedDoctorSign != null">
                reviewed_doctor_sign = #{ reviewedDoctorSign },
             </if>
        </set>
        where west_prescriptions_id = #{ westPrescriptionsId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from medical_west_prescriptions where west_prescriptions_id = #{ westPrescriptionsId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptions" resultMap="BaseResultMap">
        SELECT west_prescriptions_id,records_id,prescriptions_time,prescriptions_effective_days,prescription_remark,prescribing_doctor_sign,reviewed_doctor_sign
        from medical_west_prescriptions
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="selectIdByCondition" resultType="java.lang.String" parameterType="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptions">
        select west_prescriptions_id
        from medical_west_prescriptions where records_id = #{recordsId} limit 1
    </select>

</mapper>