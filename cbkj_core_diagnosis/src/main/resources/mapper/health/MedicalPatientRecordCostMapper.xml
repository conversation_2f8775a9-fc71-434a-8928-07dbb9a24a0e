<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.health.MedicalPatientRecordCostMapper">

    <!-- 通用查询映射结果 -->

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.health.MedicalPatientRecordCost">
        <id column="record_cost_id" property="recordCostId" />
        <result column="records_id" property="recordsId" />
        <result column="medical_patient_record_big_type_name" property="medicalPatientRecordBigTypeName" />
        <result column="medical_patient_record_item__type_name" property="medicalPatientRecordItemTypeName" />
        <result column="medical_patient_record_actual_name" property="medicalPatientRecordActualName" />
        <result column="medical_patient_record_cost" property="medicalPatientRecordCost" />
        <result column="medical_patient_record_big_type" property="medicalPatientRecordBigType" />
        <result column="medical_patient_record_item_type" property="medicalPatientRecordItemType" />
        <result column="medical_patient_record_dosage" property="medicalPatientRecordDosage" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        record_cost_id, records_id, medical_patient_record_big_type_name, medical_patient_record_item__type_name, medical_patient_record_actual_name, medical_patient_record_cost, medical_patient_record_big_type, medical_patient_record_item_type, medical_patient_record_dosage
    </sql>
    <select id="getRecordCostByRecordId" resultType="com.cbkj.diagnosis.beans.health.MedicalPatientRecordCost">

        select
        <include refid="Base_Column_List"/>
        from medical_patient_record_cost
        where records_id = #{recordId}
    </select>

    <select id="selectIdByCondition" resultType="java.lang.Long" parameterType="com.cbkj.diagnosis.beans.health.MedicalPatientRecordCost">
        SELECT record_cost_id
        FROM medical_patient_record_cost WHERE records_id = #{recordsId} limit 1
    </select>
</mapper>