<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.health.MedicalRecordsPrescriptionsItemMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem">
        <id column="item_id" jdbcType="BIGINT"  property="itemId" />
        <result column="prescriptions_id" jdbcType="VARCHAR" property="prescriptionsId" />
        <result column="drug_name" jdbcType="VARCHAR" property="drugName" />
        <result column="drug_code" jdbcType="VARCHAR" property="drugCode" />
        <result column="drug_dose" jdbcType="VARCHAR" property="drugDose" />
        <result column="te_shu_zhu_fa" jdbcType="VARCHAR" property="teShuZhuFa" />
        <result column="drug_dosage" jdbcType="VARCHAR" property="drugDosage" />
        <result column="drug_dose_unit" jdbcType="VARCHAR" property="drugDoseUnit" />
        <result column="drug_frequency" jdbcType="VARCHAR" property="drugFrequency" />
        <result column="administration_route" jdbcType="VARCHAR" property="administrationRoute" />
        <result column="drug_total_dosage" jdbcType="VARCHAR" property="drugTotalDosage" />
        <result column="drug_level_code" jdbcType="VARCHAR" property="drugLevelCode" />
        <result column="drug_category_code" jdbcType="VARCHAR" property="drugCategoryCode" />
        <result column="drug_decocting_method" jdbcType="VARCHAR" property="drugDecoctingMethod" />
        <result column="mmag_code" jdbcType="VARCHAR" property="mmagCode" />
        <result column="drug_order" jdbcType="VARCHAR" property="drugOrder" />
        <result column="drug_frequency_code" jdbcType="VARCHAR" property="drugFrequencyCode" />
        <result column="administration_route_code" jdbcType="VARCHAR" property="administrationRouteCode" />
    </resultMap>


    <sql id="Base_Column_List">
        item_id,prescriptions_id,drug_name,drug_code,drug_dose,te_shu_zhu_fa,drug_dosage,drug_dose_unit,drug_frequency,administration_route,drug_total_dosage,drug_level_code,drug_category_code,drug_decocting_method,mmag_code,drug_order,drug_frequency_code,administration_route_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem">
        delete from medical_records_prescriptions_item where item_id = #{ itemId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from medical_records_prescriptions_item where item_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem">
        insert into medical_records_prescriptions_item (<include refid="Base_Column_List" />) values
        (#{itemId},#{prescriptionsId},#{drugName},#{drugCode},#{drugDose},#{teShuZhuFa},#{drugDosage},#{drugDoseUnit},#{drugFrequency},#{administrationRoute},#{drugTotalDosage},#{drugLevelCode},#{drugCategoryCode},#{drugDecoctingMethod},#{mmagCode},#{drugOrder})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into medical_records_prescriptions_item (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.itemId},#{item.prescriptionsId},#{item.drugName},#{item.drugCode},#{item.drugDose},#{item.teShuZhuFa},#{item.drugDosage},#{item.drugDoseUnit},#{item.drugFrequency},#{item.administrationRoute},#{item.drugTotalDosage},#{item.drugLevelCode},#{item.drugCategoryCode},#{item.drugDecoctingMethod},#{item.mmagCode},#{item.drugOrder},#{item.drugFrequencyCode},#{item.administrationRouteCode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem">
        update medical_records_prescriptions_item
        <set>
            <if test="prescriptionsId != null">
                prescriptions_id = #{ prescriptionsId },
            </if>
            <if test="drugName != null">
                drug_name = #{ drugName },
            </if>
            <if test="drugCode != null">
                drug_code = #{ drugCode },
            </if>
            <if test="drugDose != null">
                drug_dose = #{ drugDose },
            </if>
            <if test="teShuZhuFa != null">
                te_shu_zhu_fa = #{ teShuZhuFa },
            </if>
            <if test="drugDosage != null">
                drug_dosage = #{ drugDosage },
            </if>
            <if test="drugDoseUnit != null">
                drug_dose_unit = #{ drugDoseUnit },
            </if>
            <if test="drugFrequency != null">
                drug_frequency = #{ drugFrequency },
            </if>
            <if test="administrationRoute != null">
                administration_route = #{ administrationRoute },
            </if>
            <if test="drugTotalDosage != null">
                drug_total_dosage = #{ drugTotalDosage },
            </if>
            <if test="drugLevelCode != null">
                drug_level_code = #{ drugLevelCode },
            </if>
            <if test="drugCategoryCode != null">
                drug_category_code = #{ drugCategoryCode },
            </if>
            <if test="drugDecoctingMethod != null">
                drug_decocting_method = #{ drugDecoctingMethod },
            </if>
            <if test="mmagCode != null">
                mmag_code = #{ mmagCode },
            </if>
            <if test="drugOrder != null">
                drug_order = #{ drugOrder },
            </if>
        </set>
        where item_id = #{ itemId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from medical_records_prescriptions_item where item_id = #{ itemId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem" resultMap="BaseResultMap">
        SELECT item_id,prescriptions_id,drug_name,drug_code,drug_dose,te_shu_zhu_fa,drug_dosage,drug_dose_unit,drug_frequency,administration_route,drug_total_dosage,drug_level_code,drug_category_code,drug_decocting_method,mmag_code,drug_order
        from medical_records_prescriptions_item
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="getItemByPreId" resultType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem">
        select <include refid="Base_Column_List"/> from medical_records_prescriptions_item where prescriptions_id = #{preId}

    </select>

</mapper>