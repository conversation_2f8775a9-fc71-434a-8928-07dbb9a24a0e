<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.health.MedicalBasicHandleRecordMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.health.MedicalBasicHandleRecord">
        <id column="handle_record_id" jdbcType="BIGINT"  property="handleRecordId" />
        <result column="records_id" jdbcType="VARCHAR" property="recordsId" />
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode" />
        <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
        <result column="operation_target" jdbcType="VARCHAR" property="operationTarget" />
        <result column="operation_method" jdbcType="VARCHAR" property="operationMethod" />
        <result column="operation_number" jdbcType="VARCHAR" property="operationNumber" />
        <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
        <result column="treatment_instruction" jdbcType="VARCHAR" property="treatmentInstruction" />
        <result column="invasive_diagnosis" jdbcType="VARCHAR" property="invasiveDiagnosis" />
        <result column="process_description" jdbcType="VARCHAR" property="processDescription" />
        <result column="future_treatment_plan" jdbcType="VARCHAR" property="futureTreatmentPlan" />
        <result column="follow_up_method_code" jdbcType="VARCHAR" property="followUpMethodCode" />
        <result column="follow_up_date" jdbcType="TIMESTAMP" property="followUpDate" />
        <result column="follow_up_period" jdbcType="VARCHAR" property="followUpPeriod" />
        <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
        <result column="prescribing_doctor_sign" jdbcType="VARCHAR" property="prescribingDoctorSign" />
        <result column="executive_doctor_sign" jdbcType="VARCHAR" property="executiveDoctorSign" />
    </resultMap>


    <sql id="Base_Column_List">
    handle_record_id,records_id,operation_code,operation_name,operation_target,operation_method,operation_number,operation_time,treatment_instruction,invasive_diagnosis,process_description,future_treatment_plan,follow_up_method_code,follow_up_date,follow_up_period,sign_time,prescribing_doctor_sign,executive_doctor_sign
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalBasicHandleRecord">
        delete from medical_basic_handle_record where handle_record_id = #{ handleRecordId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from medical_basic_handle_record where handle_record_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.health.MedicalBasicHandleRecord">
        insert into medical_basic_handle_record (<include refid="Base_Column_List" />) values
        (#{handleRecordId},#{recordsId},#{operationCode},#{operationName},#{operationTarget},#{operationMethod},#{operationNumber},#{operationTime},#{treatmentInstruction},#{invasiveDiagnosis},#{processDescription},#{futureTreatmentPlan},#{followUpMethodCode},#{followUpDate},#{followUpPeriod},#{signTime},#{prescribingDoctorSign},#{executiveDoctorSign})
    </insert>

    <insert id="insertList" parameterType="List">
        replace into medical_basic_handle_record (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.handleRecordId},#{item.recordsId},#{item.operationCode},#{item.operationName},#{item.operationTarget},#{item.operationMethod},#{item.operationNumber},#{item.operationTime},#{item.treatmentInstruction},#{item.invasiveDiagnosis},#{item.processDescription},#{item.futureTreatmentPlan},#{item.followUpMethodCode},#{item.followUpDate},#{item.followUpPeriod},#{item.signTime},#{item.prescribingDoctorSign},#{item.executiveDoctorSign})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalBasicHandleRecord">
        update medical_basic_handle_record
        <set>
             <if test="recordsId != null">
                records_id = #{ recordsId },
             </if>
             <if test="operationCode != null">
                operation_code = #{ operationCode },
             </if>
             <if test="operationName != null">
                operation_name = #{ operationName },
             </if>
             <if test="operationTarget != null">
                operation_target = #{ operationTarget },
             </if>
             <if test="operationMethod != null">
                operation_method = #{ operationMethod },
             </if>
             <if test="operationNumber != null">
                operation_number = #{ operationNumber },
             </if>
             <if test="operationTime != null">
                operation_time = #{ operationTime },
             </if>
             <if test="treatmentInstruction != null">
                treatment_instruction = #{ treatmentInstruction },
             </if>
             <if test="invasiveDiagnosis != null">
                invasive_diagnosis = #{ invasiveDiagnosis },
             </if>
             <if test="processDescription != null">
                process_description = #{ processDescription },
             </if>
             <if test="futureTreatmentPlan != null">
                future_treatment_plan = #{ futureTreatmentPlan },
             </if>
             <if test="followUpMethodCode != null">
                follow_up_method_code = #{ followUpMethodCode },
             </if>
             <if test="followUpDate != null">
                follow_up_date = #{ followUpDate },
             </if>
             <if test="followUpPeriod != null">
                follow_up_period = #{ followUpPeriod },
             </if>
             <if test="signTime != null">
                sign_time = #{ signTime },
             </if>
             <if test="prescribingDoctorSign != null">
                prescribing_doctor_sign = #{ prescribingDoctorSign },
             </if>
             <if test="executiveDoctorSign != null">
                executive_doctor_sign = #{ executiveDoctorSign },
             </if>
        </set>
        where handle_record_id = #{ handleRecordId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from medical_basic_handle_record where handle_record_id = #{ handleRecordId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.health.MedicalBasicHandleRecord" resultMap="BaseResultMap">
        SELECT handle_record_id,records_id,operation_code,operation_name,operation_target,operation_method,operation_number,operation_time,treatment_instruction,invasive_diagnosis,process_description,future_treatment_plan,follow_up_method_code,follow_up_date,follow_up_period,sign_time,prescribing_doctor_sign,executive_doctor_sign
        from medical_basic_handle_record
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="selectIdByCondition" resultType="java.lang.Long" parameterType="com.cbkj.diagnosis.beans.health.MedicalBasicHandleRecord">
        select handle_record_id
        from medical_basic_handle_record where records_id = #{recordsId} limit 1
    </select>

    <select id="getMedicalBasicHandleRecordByRecordId"
            resultType="com.cbkj.diagnosis.beans.response.MedicalBasicHandleRecordResponse">
        SELECT
            mr.diagnostic_no, h.ele_app_form, mr.`dept_name` , mr.patient_age , mr.weight, mr.allergy_history, mr.allergic_drug ,
            '1' as treatment_type ,h.operation_code   ,h.operation_name	  ,h.operation_target	,h.operation_method	,h.operation_number
                ,h.operation_time	 ,h.prescribing_doctor_sign	 ,h.executive_doctor_sign	,h.process_description
        FROM
            medical_records mr
                LEFT JOIN medical_basic_handle_record h ON mr.`records_id`= h.`records_id`

        WHERE mr.records_id =#{recordId}
    </select>

</mapper>