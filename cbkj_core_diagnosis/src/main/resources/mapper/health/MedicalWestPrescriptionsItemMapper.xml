<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.health.MedicalWestPrescriptionsItemMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem">
        <id column="west_item_id" jdbcType="BIGINT"  property="westItemId" />
        <result column="drug_dose" jdbcType="VARCHAR" property="drugDose" />
        <result column="drug_dose_unit" jdbcType="VARCHAR" property="drugDoseUnit" />
        <result column="drug_frequency" jdbcType="VARCHAR" property="drugFrequency" />
        <result column="drug_name" jdbcType="VARCHAR" property="drugName" />
        <result column="drug_specifications" jdbcType="VARCHAR" property="drugSpecifications" />
        <result column="west_prescriptions_id" jdbcType="VARCHAR" property="westPrescriptionsId" />
        <result column="drug_dosage" jdbcType="VARCHAR" property="drugDosage" />
        <result column="administration_route" jdbcType="VARCHAR" property="administrationRoute" />
        <result column="drug_total_dosage" jdbcType="VARCHAR" property="drugTotalDosage" />
        <result column="drug_frequency_code" jdbcType="VARCHAR" property="drugFrequencyCode" />
        <result column="administration_route_code" jdbcType="VARCHAR" property="administrationRouteCode" />
    </resultMap>


    <sql id="Base_Column_List">
    west_item_id,drug_dose,drug_dose_unit,drug_frequency,drug_name,drug_specifications,west_prescriptions_id,drug_dosage,administration_route,drug_total_dosage,drug_frequency_code,administration_route_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem">
        delete from medical_west_prescriptions_item where west_item_id = #{ westItemId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from medical_west_prescriptions_item where west_item_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem">
        insert into medical_west_prescriptions_item (<include refid="Base_Column_List" />) values
        (#{westItemId},#{drugDose},#{drugDoseUnit},#{drugFrequency},#{drugName},#{drugSpecifications},#{westPrescriptionsId},#{drugDosage},#{administrationRoute},#{drugTotalDosage})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into medical_west_prescriptions_item (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.westItemId},#{item.drugDose},#{item.drugDoseUnit},#{item.drugFrequency},#{item.drugName},#{item.drugSpecifications},#{item.westPrescriptionsId},#{item.drugDosage},#{item.administrationRoute},#{item.drugTotalDosage},#{item.drugFrequencyCode},#{item.administrationRouteCode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem">
        update medical_west_prescriptions_item
        <set>
             <if test="drugDose != null">
                drug_dose = #{ drugDose },
             </if>
             <if test="drugDoseUnit != null">
                drug_dose_unit = #{ drugDoseUnit },
             </if>
             <if test="drugFrequency != null">
                drug_frequency = #{ drugFrequency },
             </if>
             <if test="drugName != null">
                drug_name = #{ drugName },
             </if>
             <if test="drugSpecifications != null">
                drug_specifications = #{ drugSpecifications },
             </if>
             <if test="westPrescriptionsId != null">
                west_prescriptions_id = #{ westPrescriptionsId },
             </if>
             <if test="drugDosage != null">
                drug_dosage = #{ drugDosage },
             </if>
             <if test="administrationRoute != null">
                administration_route = #{ administrationRoute },
             </if>
             <if test="drugTotalDosage != null">
                drug_total_dosage = #{ drugTotalDosage },
             </if>
        </set>
        where west_item_id = #{ westItemId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from medical_west_prescriptions_item where west_item_id = #{ westItemId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem" resultMap="BaseResultMap">
        SELECT west_item_id,drug_dose,drug_dose_unit,drug_frequency,drug_name,drug_specifications,west_prescriptions_id,drug_dosage,administration_route,drug_total_dosage
        from medical_west_prescriptions_item
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="getItemByWestPreId" resultType="com.cbkj.diagnosis.beans.health.MedicalWestPrescriptionsItem">
        select <include refid="Base_Column_List"/> from medical_west_prescriptions_item where west_prescriptions_id = #{westPreId}
    </select>

</mapper>