<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.health.MedicalRecordsPrescriptionsMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptions">
        <id column="prescriptions_id" jdbcType="VARCHAR"  property="prescriptionsId" />
        <result column="prescription_name" jdbcType="VARCHAR" property="prescriptionName" />
        <result column="prescription_num" jdbcType="VARCHAR" property="prescriptionNum" />
        <result column="chinese_prescription_usage" jdbcType="VARCHAR" property="chinesePrescriptionUsage" />
        <result column="chinese_prescription_rate" jdbcType="VARCHAR" property="chinesePrescriptionRate" />
        <result column="records_id" jdbcType="VARCHAR" property="recordsId" />
        <result column="prescriptions_time" jdbcType="TIMESTAMP" property="prescriptionsTime" />
        <result column="prescriptions_effective_days" jdbcType="DOUBLE" property="prescriptionsEffectiveDays" />
        <result column="chinese_prescription_cost" jdbcType="VARCHAR" property="chinesePrescriptionCost" />
        <result column="prescription_category" jdbcType="VARCHAR" property="prescriptionCategory" />
        <result column="decocting_method" jdbcType="VARCHAR" property="decoctingMethod" />
        <result column="medication_method" jdbcType="VARCHAR" property="medicationMethod" />
        <result column="medication_requirements" jdbcType="VARCHAR" property="medicationRequirements" />
        <result column="medication_contraindications" jdbcType="VARCHAR" property="medicationContraindications" />
        <result column="prescribing_doctor_sign" jdbcType="VARCHAR" property="prescribingDoctorSign" />
        <result column="reviewed_doctor_sign" jdbcType="VARCHAR" property="reviewedDoctorSign" />
        <result column="prescription_propertie_code" jdbcType="VARCHAR" property="prescriptionPropertieCode" />
        <result column="cipher_prescription" jdbcType="VARCHAR" property="cipherPrescription" />
        <result column="decocting_flag" jdbcType="VARCHAR" property="decoctingFlag" />
        <result column="preparation_flag" jdbcType="VARCHAR" property="preparationFlag" />
        <result column="prescription_no" jdbcType="VARCHAR" property="prescriptionNo" />
        <result column="prescribing_doctor_sign" jdbcType="VARCHAR" property="prescribingDoctorSign" />
        <result column="reviewed_doctor_sign" jdbcType="VARCHAR" property="reviewedDoctorSign" />
    </resultMap>


    <sql id="Base_Column_List">
        prescriptions_id,prescription_name,prescription_num,chinese_prescription_usage,
            chinese_prescription_rate,records_id,prescriptions_time,prescriptions_effective_days,chinese_prescription_cost,prescription_category,decocting_method,
            medication_method,medication_requirements,medication_contraindications,prescribing_doctor_sign,reviewed_doctor_sign,prescription_propertie_code,cipher_prescription,decocting_flag,preparation_flag,prescription_no
    </sql>
    <!--
    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptions">
        delete from medical_records_prescriptions where prescriptions_id = #{ prescriptionsId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from medical_records_prescriptions where prescriptions_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptions">
        insert into medical_records_prescriptions (<include refid="Base_Column_List" />) values
        (#{prescriptionsId},#{prescriptionName},#{prescriptionNum},#{chinesePrescriptionUsage},#{chinesePrescriptionRate},#{recordsId},#{prescriptionsTime},#{prescriptionsEffectiveDays},#{chinesePrescriptionCost},
         #{prescriptionCategory},#{decoctingMethod},#{medicationMethod},#{medicationRequirements},#{medicationContraindications},#{prescribingDoctorSign},#{reviewedDoctorSign},#{prescriptionPropertieCode},
         #{cipherPrescription},#{decoctingFlag},#{preparationFlag},#{prescriptionNo})
    </insert>
-->

    <insert id="insertList" parameterType="List">
        insert into medical_records_prescriptions (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.prescriptionsId},#{item.prescriptionName},#{item.prescriptionNum},#{item.chinesePrescriptionUsage},#{item.chinesePrescriptionRate},#{item.recordsId},#{item.prescriptionsTime},
             #{item.prescriptionsEffectiveDays},#{item.chinesePrescriptionCost},#{item.prescriptionCategory},#{item.decoctingMethod},#{item.medicationMethod},#{item.medicationRequirements},#{item.medicationContraindications},
             #{item.prescribingDoctorSign},#{item.reviewedDoctorSign},#{item.prescriptionPropertieCode},#{item.cipherPrescription},#{item.decoctingFlag},#{item.preparationFlag},#{item.prescriptionNo})
        </foreach>
    </insert>
<!--
    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptions">
        update medical_records_prescriptions
        <set>
            <if test="prescriptionName != null">
                prescription_name = #{ prescriptionName },
            </if>
            <if test="prescriptionNum != null">
                prescription_num = #{ prescriptionNum },
            </if>
            <if test="chinesePrescriptionUsage != null">
                chinese_prescription_usage = #{ chinesePrescriptionUsage },
            </if>
            <if test="chinesePrescriptionRate != null">
                chinese_prescription_rate = #{ chinesePrescriptionRate },
            </if>
            <if test="recordsId != null">
                records_id = #{ recordsId },
            </if>
            <if test="prescriptionsTime != null">
                prescriptions_time = #{ prescriptionsTime },
            </if>
            <if test="prescriptionsEffectiveDays != null">
                prescriptions_effective_days = #{ prescriptionsEffectiveDays },
            </if>
            <if test="chinesePrescriptionCost != null">
                chinese_prescription_cost = #{ chinesePrescriptionCost },
            </if>
            <if test="prescriptionCategory != null">
                prescription_category = #{ prescriptionCategory },
            </if>
            <if test="decoctingMethod != null">
                decocting_method = #{ decoctingMethod },
            </if>
            <if test="medicationMethod != null">
                medication_method = #{ medicationMethod },
            </if>
            <if test="medicationRequirements != null">
                medication_requirements = #{ medicationRequirements },
            </if>
            <if test="medicationContraindications != null">
                medication_contraindications = #{ medicationContraindications },
            </if>
            <if test="prescribingDoctorSign != null">
                prescribing_doctor_sign = #{ prescribingDoctorSign },
            </if>
            <if test="reviewedDoctorSign != null">
                reviewed_doctor_sign = #{ reviewedDoctorSign },
            </if>
            <if test="prescriptionPropertieCode != null">
                prescription_propertie_code = #{ prescriptionPropertieCode },
            </if>
            <if test="cipherPrescription != null">
                cipher_prescription = #{ cipherPrescription },
            </if>
            <if test="decoctingFlag != null">
                decocting_flag = #{ decoctingFlag },
            </if>
            <if test="preparationFlag != null">
                preparation_flag = #{ preparationFlag },
            </if>
            <if test="prescriptionNo != null">
                prescription_no = #{prescriptionNo},
            </if>
        </set>
        where prescriptions_id = #{ prescriptionsId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from medical_records_prescriptions where prescriptions_id = #{ prescriptionsId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptions" resultMap="BaseResultMap">
        SELECT prescriptions_id,prescription_name,prescription_num,chinese_prescription_usage,chinese_prescription_rate,records_id,prescriptions_time,prescriptions_effective_days,chinese_prescription_cost,
               prescription_category,decocting_method,medication_method,medication_requirements,medication_contraindications,prescribing_doctor_sign,reviewed_doctor_sign,prescription_propertie_code,cipher_prescription,
               decocting_flag,preparation_flag,prescription_no
        from medical_records_prescriptions
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    -->
    <select id="selectIdByCondition" resultType="java.lang.String" parameterType="String">
        select prescriptions_id
        from medical_records_prescriptions where records_id = #{recordsId} limit 1
    </select>

    <select id="getMedicalRecordsPrescriptionsByRecordId"
            resultType="com.cbkj.diagnosis.beans.response.MedicalRecordsPrescriptionsResponse">

        SELECT
            mr.chinese_dis_name AS chineseDisName,
            mr.sym_name AS symName,
            mr.west_dis_name AS westDisName,
            mr.tcm_approach AS tcmApproach,
            mr.`dept_name` AS deptName,
            mrp.prescription_no AS preNo,
            mrp.prescription_category AS prescriptionCategory,
            mrp.prescriptions_time AS prescriptionsTime,
            mrp.prescriptions_effective_days AS prescriptionsEffectiveDays,
            mrp.cn_formula_name AS cnFormulaName,
            mrp.prescription_num AS prescriptionNum,
            mrp.decocting_method AS decoctingMethod,
            mrp.chinese_prescription_rate AS chinesePrescriptionRate,
            mrp.medication_method AS medicationMethod,
            mrp.medication_requirements AS medicationRequirements,
            mrp.pre_dosage AS drugDosage,
            mrp.cipher_prescription AS cipherPrescription,
            mrp.preparation_flag AS preparationFlag,
            mrp.decocting_flag AS decoctingFlag,
            mrp.jun_chen_zuo_shi AS junChenZuoShi,
            mrp.prescribing_doctor_sign AS prescribingDoctorSign,
            mrp.reviewed_doctor_sign AS reviewedDoctorSign,
            mrp.prescriptions_id AS tcmPreId
        FROM
            medical_records mr
                LEFT JOIN medical_records_prescriptions mrp ON mr.`records_id`= mrp.`records_id`

        WHERE mr.records_id =#{recordId}

    </select>


    <select id="getMedicalRecordsWestPrescriptionsByRecordId"
            resultType="com.cbkj.diagnosis.beans.response.MedicalRecordsPrescriptionsResponse">

        SELECT
            mr.`dept_name` as deptName,
            mr.chinese_dis_name as chineseDisName,
            mr.sym_name as symName,
            mr.west_dis_name as westDisName,
            mr.tcm_approach as tcmApproach,
            w.prescription_remark  as westPrescriptionRemark  ,
            w.prescribing_doctor_sign  as westPrescribingDoctorSign ,
            w.reviewed_doctor_sign as westReviewedDoctorSign,
            w.west_prescriptions_id as westPreId
        FROM
            medical_records mr

                LEFT JOIN medical_west_prescriptions w ON mr.`records_id`= w.`records_id`
        WHERE mr.records_id =#{recordId}

    </select>

</mapper>