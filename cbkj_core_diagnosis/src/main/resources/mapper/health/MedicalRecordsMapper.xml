<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.health.MedicalRecordsMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.health.MedicalRecords">
        <id column="records_id" jdbcType="VARCHAR" property="recordsId"/>
        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName"/>
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="ins_name" jdbcType="VARCHAR" property="insName"/>
        <result column="ins_id" jdbcType="VARCHAR" property="insId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="diagnostic_no" jdbcType="VARCHAR" property="diagnosticNo"/>
        <result column="chief_complaint" jdbcType="VARCHAR" property="chiefComplaint"/>
        <result column="present_illness" jdbcType="VARCHAR" property="presentIllness"/>
        <result column="past_history" jdbcType="VARCHAR" property="pastHistory"/>
        <result column="personal_history" jdbcType="VARCHAR" property="personalHistory"/>
        <result column="family_history" jdbcType="VARCHAR" property="familyHistory"/>
        <result column="physical_examination" jdbcType="VARCHAR" property="physicalExamination"/>
        <result column="chinese_dis_name" jdbcType="VARCHAR" property="chineseDisName"/>
        <result column="west_dis_name" jdbcType="VARCHAR" property="westDisName"/>
        <result column="chinese_dis_id" jdbcType="VARCHAR" property="chineseDisId"/>
        <result column="west_dis_id" jdbcType="VARCHAR" property="westDisId"/>
        <result column="sym_name" jdbcType="VARCHAR" property="symName"/>
        <result column="sym_id" jdbcType="VARCHAR" property="symId"/>
        <result column="records_advice" jdbcType="VARCHAR" property="recordsAdvice"/>
        <result column="record_time" jdbcType="TIMESTAMP" property="recordTime"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="records_times" jdbcType="INTEGER" property="recordsTimes"/>
        <result column="patient_name" jdbcType="VARCHAR" property="patientName"/>
        <result column="patient_code" jdbcType="VARCHAR" property="patientCode"/>
        <result column="patient_id" jdbcType="VARCHAR" property="patientId"/>
        <result column="patient_sex" jdbcType="VARCHAR" property="patientSex"/>
        <result column="patient_phone" jdbcType="VARCHAR" property="patientPhone"/>
        <result column="patient_age" jdbcType="VARCHAR" property="patientAge"/>
        <result column="patient_card_number" jdbcType="VARCHAR" property="patientCardNumber"/>
        <result column="VISIT_NO" jdbcType="VARCHAR" property="visitNo"/>
        <result column="health_files_num" jdbcType="VARCHAR" property="healthFilesNum"/>
        <result column="patient_card_type" jdbcType="VARCHAR" property="patientCardType"/>
        <result column="patient_health_card_num" jdbcType="VARCHAR" property="patientHealthCardNum"/>
        <result column="insurance_type_code" jdbcType="VARCHAR" property="insuranceTypeCode"/>
        <result column="marital_status" jdbcType="VARCHAR" property="maritalStatus"/>
        <result column="nation" jdbcType="VARCHAR" property="nation"/>
        <result column="contacts_name" jdbcType="VARCHAR" property="contactsName"/>
        <result column="contacts_phone" jdbcType="VARCHAR" property="contactsPhone"/>
        <result column="nationality" jdbcType="VARCHAR" property="nationality"/>
        <result column="education_code" jdbcType="VARCHAR" property="educationCode"/>
        <result column="contacts_relationship" jdbcType="VARCHAR" property="contactsRelationship"/>
        <result column="auxiliary_inspection_results" jdbcType="VARCHAR" property="auxiliaryInspectionResults"/>
        <result column="syndrome_differentiation_basis" jdbcType="VARCHAR" property="syndromeDifferentiationBasis"/>
        <result column="tcm_approach" jdbcType="VARCHAR" property="tcmApproach"/>
        <result column="etiology_classification" jdbcType="VARCHAR" property="etiologyClassification"/>
        <result column="syndrome_differentiation_method" jdbcType="VARCHAR" property="syndromeDifferentiationMethod"/>
        <result column="tcm_pathogenesis" jdbcType="VARCHAR" property="tcmPathogenesis"/>
        <result column="physical_affecting_factors" jdbcType="VARCHAR" property="physicalAffectingFactors"/>
        <result column="tcm_incidents_type" jdbcType="VARCHAR" property="tcmIncidentsType"/>
        <result column="tcm_incidents_form" jdbcType="VARCHAR" property="tcmIncidentsForm"/>
        <result column="inspection_info_string" jdbcType="VARCHAR" property="inspectionInfoString"/>
        <result column="occupation" jdbcType="VARCHAR" property="occupation"/>
        <result column="onset_solar_term_code" jdbcType="VARCHAR" property="onsetSolarTermCode"/>
        <result column="initial_diagnosis_code" jdbcType="VARCHAR" property="initialDiagnosisCode"/>
        <result column="treatment_category_code" jdbcType="VARCHAR" property="treatmentCategoryCode"/>
        <result column="clinical_pathway_code" jdbcType="VARCHAR" property="clinicalPathwayCode"/>
        <result column="scientific_research_flag" jdbcType="VARCHAR" property="scientificResearchFlag"/>
        <result column="allergy_history_flag" jdbcType="VARCHAR" property="allergyHistoryFlag"/>
        <result column="allergy_history" jdbcType="VARCHAR" property="allergyHistory"/>
        <result column="allergic_drug_flag" jdbcType="VARCHAR" property="allergicDrugFlag"/>
        <result column="allergic_drug" jdbcType="VARCHAR" property="allergicDrug"/>
        <result column="infectious_history_flag" jdbcType="VARCHAR" property="infectiousHistoryFlag"/>
        <result column="infectious_history" jdbcType="VARCHAR" property="infectiousHistory"/>
        <result column="vaccination_history" jdbcType="VARCHAR" property="vaccinationHistory"/>
        <result column="surgical_history" jdbcType="VARCHAR" property="surgicalHistory"/>
        <result column="blood_transfusion_history" jdbcType="VARCHAR" property="bloodTransfusionHistory"/>
        <result column="pregnancy_flag" jdbcType="VARCHAR" property="pregnancyFlag"/>
        <result column="suckling_period_flag" jdbcType="VARCHAR" property="sucklingPeriodFlag"/>
        <result column="obstetric_history" jdbcType="VARCHAR" property="obstetricHistory"/>
        <result column="menstrual_history" jdbcType="VARCHAR" property="menstrualHistory"/>
        <result column="height" jdbcType="VARCHAR" property="height"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="bmi" jdbcType="VARCHAR" property="bmi"/>
        <result column="auxiliary_inspection_items" jdbcType="VARCHAR" property="auxiliaryInspectionItems"/>
        <result column="symptom_description" jdbcType="VARCHAR" property="symptomDescription"/>
        <result column="tongue_condition" jdbcType="VARCHAR" property="tongueCondition"/>
        <result column="pulse_condition" jdbcType="VARCHAR" property="pulseCondition"/>
        <result column="west_dis_code" jdbcType="VARCHAR" property="westDisCode"/>
        <result column="chinese_dis_code" jdbcType="VARCHAR" property="chineseDisCode"/>
        <result column="sym_code" jdbcType="VARCHAR" property="symCode"/>
        <result column="diagnostic_flag" jdbcType="VARCHAR" property="diagnosticFlag"/>
        <result column="yi_shi_sign" jdbcType="VARCHAR" property="yiShiSign"/>
        <result column="cn_pre_info" jdbcType="VARCHAR" property="cnPreInfo"/>
        <result column="we_cn_pre_info" jdbcType="VARCHAR" property="weCnPreInfo"/>
        <result column="common_op_info" jdbcType="VARCHAR" property="commonOpInfo"/>
        <result column="medical_expenses_settled_code" jdbcType="VARCHAR" property="medicalExpensesSettledCode"/>
        <result column="etiology_classification_code" jdbcType="VARCHAR" property="etiologyClassificationCode"/>
        <result column="physical_affecting_factors_code" jdbcType="VARCHAR" property="physicalAffectingFactorsCode"/>
    </resultMap>


    <resultMap id="BaseResultMapNoEn" type="com.cbkj.diagnosis.beans.health.MedicalRecordsNoEn">
        <id column="records_id" jdbcType="VARCHAR" property="recordsId"/>
        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName"/>
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="ins_name" jdbcType="VARCHAR" property="insName"/>
        <result column="ins_id" jdbcType="VARCHAR" property="insId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="diagnostic_no" jdbcType="VARCHAR" property="diagnosticNo"/>
        <result column="chief_complaint" jdbcType="VARCHAR" property="chiefComplaint"/>
        <result column="present_illness" jdbcType="VARCHAR" property="presentIllness"/>
        <result column="past_history" jdbcType="VARCHAR" property="pastHistory"/>
        <result column="personal_history" jdbcType="VARCHAR" property="personalHistory"/>
        <result column="family_history" jdbcType="VARCHAR" property="familyHistory"/>
        <result column="physical_examination" jdbcType="VARCHAR" property="physicalExamination"/>
        <result column="chinese_dis_name" jdbcType="VARCHAR" property="chineseDisName"/>
        <result column="west_dis_name" jdbcType="VARCHAR" property="westDisName"/>
        <result column="chinese_dis_id" jdbcType="VARCHAR" property="chineseDisId"/>
        <result column="west_dis_id" jdbcType="VARCHAR" property="westDisId"/>
        <result column="sym_name" jdbcType="VARCHAR" property="symName"/>
        <result column="sym_id" jdbcType="VARCHAR" property="symId"/>
        <result column="records_advice" jdbcType="VARCHAR" property="recordsAdvice"/>
        <result column="record_time" jdbcType="TIMESTAMP" property="recordTime"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="records_times" jdbcType="INTEGER" property="recordsTimes"/>
        <result column="patient_name" jdbcType="VARCHAR" property="patientName"/>
        <result column="patient_code" jdbcType="VARCHAR" property="patientCode"/>
        <result column="patient_id" jdbcType="VARCHAR" property="patientId"/>
        <result column="patient_sex" jdbcType="VARCHAR" property="patientSex"/>
        <result column="patient_phone" jdbcType="VARCHAR" property="patientPhone"/>
        <result column="patient_age" jdbcType="VARCHAR" property="patientAge"/>
        <result column="patient_card_number" jdbcType="VARCHAR" property="patientCardNumber"/>
        <result column="VISIT_NO" jdbcType="VARCHAR" property="visitNo"/>
        <result column="health_files_num" jdbcType="VARCHAR" property="healthFilesNum"/>
        <result column="patient_card_type" jdbcType="VARCHAR" property="patientCardType"/>
        <result column="patient_health_card_num" jdbcType="VARCHAR" property="patientHealthCardNum"/>
        <result column="insurance_type_code" jdbcType="VARCHAR" property="insuranceTypeCode"/>
        <result column="marital_status" jdbcType="VARCHAR" property="maritalStatus"/>
        <result column="nation" jdbcType="VARCHAR" property="nation"/>
        <result column="contacts_name" jdbcType="VARCHAR" property="contactsName"/>
        <result column="contacts_phone" jdbcType="VARCHAR" property="contactsPhone"/>
        <result column="nationality" jdbcType="VARCHAR" property="nationality"/>
        <result column="education_code" jdbcType="VARCHAR" property="educationCode"/>
        <result column="contacts_relationship" jdbcType="VARCHAR" property="contactsRelationship"/>
        <result column="auxiliary_inspection_results" jdbcType="VARCHAR" property="auxiliaryInspectionResults"/>
        <result column="syndrome_differentiation_basis" jdbcType="VARCHAR" property="syndromeDifferentiationBasis"/>
        <result column="tcm_approach" jdbcType="VARCHAR" property="tcmApproach"/>
        <result column="etiology_classification" jdbcType="VARCHAR" property="etiologyClassification"/>
        <result column="syndrome_differentiation_method" jdbcType="VARCHAR" property="syndromeDifferentiationMethod"/>
        <result column="tcm_pathogenesis" jdbcType="VARCHAR" property="tcmPathogenesis"/>
        <result column="physical_affecting_factors" jdbcType="VARCHAR" property="physicalAffectingFactors"/>
        <result column="tcm_incidents_type" jdbcType="VARCHAR" property="tcmIncidentsType"/>
        <result column="tcm_incidents_form" jdbcType="VARCHAR" property="tcmIncidentsForm"/>
        <result column="inspection_info_string" jdbcType="VARCHAR" property="inspectionInfoString"/>
        <result column="occupation" jdbcType="VARCHAR" property="occupation"/>
        <result column="onset_solar_term_code" jdbcType="VARCHAR" property="onsetSolarTermCode"/>
        <result column="initial_diagnosis_code" jdbcType="VARCHAR" property="initialDiagnosisCode"/>
        <result column="treatment_category_code" jdbcType="VARCHAR" property="treatmentCategoryCode"/>
        <result column="clinical_pathway_code" jdbcType="VARCHAR" property="clinicalPathwayCode"/>
        <result column="scientific_research_flag" jdbcType="VARCHAR" property="scientificResearchFlag"/>
        <result column="allergy_history_flag" jdbcType="VARCHAR" property="allergyHistoryFlag"/>
        <result column="allergy_history" jdbcType="VARCHAR" property="allergyHistory"/>
        <result column="allergic_drug_flag" jdbcType="VARCHAR" property="allergicDrugFlag"/>
        <result column="allergic_drug" jdbcType="VARCHAR" property="allergicDrug"/>
        <result column="infectious_history_flag" jdbcType="VARCHAR" property="infectiousHistoryFlag"/>
        <result column="infectious_history" jdbcType="VARCHAR" property="infectiousHistory"/>
        <result column="vaccination_history" jdbcType="VARCHAR" property="vaccinationHistory"/>
        <result column="surgical_history" jdbcType="VARCHAR" property="surgicalHistory"/>
        <result column="blood_transfusion_history" jdbcType="VARCHAR" property="bloodTransfusionHistory"/>
        <result column="pregnancy_flag" jdbcType="VARCHAR" property="pregnancyFlag"/>
        <result column="suckling_period_flag" jdbcType="VARCHAR" property="sucklingPeriodFlag"/>
        <result column="obstetric_history" jdbcType="VARCHAR" property="obstetricHistory"/>
        <result column="menstrual_history" jdbcType="VARCHAR" property="menstrualHistory"/>
        <result column="height" jdbcType="VARCHAR" property="height"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="bmi" jdbcType="VARCHAR" property="bmi"/>
        <result column="auxiliary_inspection_items" jdbcType="VARCHAR" property="auxiliaryInspectionItems"/>
        <result column="symptom_description" jdbcType="VARCHAR" property="symptomDescription"/>
        <result column="tongue_condition" jdbcType="VARCHAR" property="tongueCondition"/>
        <result column="pulse_condition" jdbcType="VARCHAR" property="pulseCondition"/>
        <result column="west_dis_code" jdbcType="VARCHAR" property="westDisCode"/>
        <result column="chinese_dis_code" jdbcType="VARCHAR" property="chineseDisCode"/>
        <result column="sym_code" jdbcType="VARCHAR" property="symCode"/>
        <result column="diagnostic_flag" jdbcType="VARCHAR" property="diagnosticFlag"/>
        <result column="yi_shi_sign" jdbcType="VARCHAR" property="yiShiSign"/>
        <result column="cn_pre_info" jdbcType="VARCHAR" property="cnPreInfo"/>
        <result column="we_cn_pre_info" jdbcType="VARCHAR" property="weCnPreInfo"/>
        <result column="common_op_info" jdbcType="VARCHAR" property="commonOpInfo"/>
    </resultMap>

    <sql id="Base_Column_List">
        records_id
        ,doctor_name,doctor_id,app_id,ins_id,dept_name,ins_name,ins_code,dept_code,dept_id,chief_complaint,present_illness,past_history,personal_history,family_history,physical_examination,chinese_dis_name,west_dis_name,chinese_dis_id,west_dis_id,sym_name,sym_id,records_advice,record_time,create_date,records_times,patient_name,patient_id,patient_sex,patient_phone,patient_age,patient_card_number,VISIT_NO,health_files_num,patient_card_type,patient_health_card_num,insurance_type_code,marital_status,nation,contacts_name,contacts_phone,nationality,education_code,contacts_relationship,auxiliary_inspection_results,syndrome_differentiation_basis,tcm_approach,etiology_classification,syndrome_differentiation_method,tcm_pathogenesis,physical_affecting_factors,tcm_incidents_type,tcm_incidents_form,inspection_info_string,occupation,onset_solar_term_code,initial_diagnosis_code,treatment_category_code,clinical_pathway_code,scientific_research_flag,allergy_history_flag,allergy_history,allergic_drug_flag,allergic_drug,infectious_history_flag,infectious_history,vaccination_history,surgical_history,blood_transfusion_history,pregnancy_flag,suckling_period_flag,obstetric_history,menstrual_history,height,weight,bmi,auxiliary_inspection_items,symptom_description,tongue_condition,pulse_condition,west_dis_code,chinese_dis_code,sym_code,diagnostic_flag,medical_expenses_settled_code,etiology_classification_code,physical_affecting_factors_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalRecords">
        delete
        from medical_records
        where records_id = #{ recordsId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from medical_records where records_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.cbkj.diagnosis.beans.health.MedicalRecords">
        insert into medical_records (<include refid="Base_Column_List"/>) values
        (#{recordsId},#{doctorName},#{doctorId},#{appId},#{insId},#{deptName},#{insName},#{insCode},#{deptCode},#{deptId},#{chiefComplaint},#{presentIllness},#{pastHistory},#{personalHistory},#{familyHistory},#{physicalExamination},#{chineseDisName},#{westDisName},#{chineseDisId},#{westDisId},#{symName},#{symId},#{recordsAdvice},#{recordTime},#{createDate},#{recordsTimes},#{patientName},#{patientId},#{patientSex},#{patientPhone},#{patientAge},#{patientCardNumber},#{visitNo},#{healthFilesNum},#{patientCardType},#{patientHealthCardNum},#{insuranceTypeCode},#{maritalStatus},#{nation},#{contactsName},#{contactsPhone},#{nationality},#{educationCode},#{contactsRelationship},#{auxiliaryInspectionResults},#{syndromeDifferentiationBasis},#{tcmApproach},#{etiologyClassification},#{syndromeDifferentiationMethod},#{tcmPathogenesis},#{physicalAffectingFactors},#{tcmIncidentsType},#{tcmIncidentsForm},#{inspectionInfoString},#{occupation},#{onsetSolarTermCode},#{initialDiagnosisCode},#{treatmentCategoryCode},#{clinicalPathwayCode},#{scientificResearchFlag},#{allergyHistoryFlag},#{allergyHistory},#{allergicDrugFlag},#{allergicDrug},#{infectiousHistoryFlag},#{infectiousHistory},#{vaccinationHistory},#{surgicalHistory},#{bloodTransfusionHistory},#{pregnancyFlag},#{sucklingPeriodFlag},#{obstetricHistory},#{menstrualHistory},#{height},#{weight},#{bmi},#{auxiliaryInspectionItems},#{symptomDescription},#{tongueCondition},#{pulseCondition},#{westDisCode},#{chineseDisCode},#{symCode},#{diagnosticFlag},#{medicalExpensesSettledCode},#{etiologyClassificationCode},#{physicalAffectingFactorsCode})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into medical_records (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.recordsId},#{item.doctorName},#{item.doctorId},#{item.appId},#{item.insId},#{item.deptName},#{item.insName},#{item.insCode},#{item.deptCode},#{item.deptId},#{item.chiefComplaint},#{item.presentIllness},#{item.pastHistory},#{item.personalHistory},#{item.familyHistory},#{item.physicalExamination},#{item.chineseDisName},#{item.westDisName},#{item.chineseDisId},#{item.westDisId},#{item.symName},#{item.symId},#{item.recordsAdvice},#{item.recordTime},#{item.createDate},#{item.recordsTimes},#{item.patientName},#{item.patientId},#{item.patientSex},#{item.patientPhone},#{item.patientAge},#{item.patientCardNumber},#{item.visitNo},#{item.healthFilesNum},#{item.patientCardType},#{item.patientHealthCardNum},#{item.insuranceTypeCode},#{item.maritalStatus},#{item.nation},#{item.contactsName},#{item.contactsPhone},#{item.nationality},#{item.educationCode},#{item.contactsRelationship},#{item.auxiliaryInspectionResults},#{item.syndromeDifferentiationBasis},#{item.tcmApproach},#{item.etiologyClassification},#{item.syndromeDifferentiationMethod},#{item.tcmPathogenesis},#{item.physicalAffectingFactors},#{item.tcmIncidentsType},#{item.tcmIncidentsForm},#{item.inspectionInfoString},#{item.occupation},#{item.onsetSolarTermCode},#{item.initialDiagnosisCode},#{item.treatmentCategoryCode},#{item.clinicalPathwayCode},#{item.scientificResearchFlag},#{item.allergyHistoryFlag},#{item.allergyHistory},#{item.allergicDrugFlag},#{item.allergicDrug},#{item.infectiousHistoryFlag},#{item.infectiousHistory},#{item.vaccinationHistory},#{item.surgicalHistory},#{item.bloodTransfusionHistory},#{item.pregnancyFlag},#{item.sucklingPeriodFlag},#{item.obstetricHistory},#{item.menstrualHistory},#{item.height},#{item.weight},#{item.bmi},#{item.auxiliaryInspectionItems},#{item.symptomDescription},#{item.tongueCondition},#{item.pulseCondition},#{item.westDisCode},#{item.chineseDisCode},#{item.symCode},#{item.diagnosticFlag})
        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.health.MedicalRecords">
        update medical_records
        <set>
            <if test="doctorName != null">
                doctor_name = #{ doctorName },
            </if>
            <if test="doctorId != null">
                doctor_id = #{ doctorId },
            </if>
            <if test="appId != null">
                app_id = #{ appId },
            </if>
            <if test="deptName != null">
                dept_name = #{ deptName },
            </if>
            <if test="insName != null">
                ins_name = #{ insName },
            </if>
            <if test="insId != null">
                ins_id = #{ insId },
            </if>
            <if test="insCode != null">
                ins_code = #{ insCode },
            </if>
            <if test="deptCode != null">
                dept_code = #{ deptCode },
            </if>
            <if test="deptId != null">
                dept_id = #{ deptId },
            </if>
            <if test="diagnosticNo != null">
                diagnostic_no = #{ diagnosticNo },
            </if>
            <if test="chiefComplaint != null">
                chief_complaint = #{ chiefComplaint },
            </if>
            <if test="presentIllness != null">
                present_illness = #{ presentIllness },
            </if>
            <if test="pastHistory != null">
                past_history = #{ pastHistory },
            </if>
            <if test="personalHistory != null">
                personal_history = #{ personalHistory },
            </if>
            <if test="familyHistory != null">
                family_history = #{ familyHistory },
            </if>
            <if test="physicalExamination != null">
                physical_examination = #{ physicalExamination },
            </if>
            <if test="chineseDisName != null">
                chinese_dis_name = #{ chineseDisName },
            </if>
            <if test="westDisName != null">
                west_dis_name = #{ westDisName },
            </if>
            <if test="chineseDisId != null">
                chinese_dis_id = #{ chineseDisId },
            </if>
            <if test="westDisId != null">
                west_dis_id = #{ westDisId },
            </if>
            <if test="symName != null">
                sym_name = #{ symName },
            </if>
            <if test="symId != null">
                sym_id = #{ symId },
            </if>
            <if test="recordsAdvice != null">
                records_advice = #{ recordsAdvice },
            </if>
            <if test="recordTime != null">
                record_time = #{ recordTime },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
            <if test="recordsTimes != null">
                records_times = #{ recordsTimes },
            </if>
            <if test="patientName != null">
                patient_name = #{ patientName },
            </if>
            <if test="patientCode != null">
                patient_code = #{ patientCode },
            </if>
            <if test="patientId != null">
                patient_id = #{ patientId },
            </if>
            <if test="patientSex != null">
                patient_sex = #{ patientSex },
            </if>
            <if test="patientPhone != null">
                patient_phone = #{ patientPhone },
            </if>
            <if test="patientAge != null">
                patient_age = #{ patientAge },
            </if>
            <if test="patientCardNumber != null">
                patient_card_number = #{ patientCardNumber },
            </if>
            <if test="visitNo != null">
                VISIT_NO = #{ visitNo },
            </if>
            <if test="healthFilesNum != null">
                health_files_num = #{ healthFilesNum },
            </if>
            <if test="patientCardType != null">
                patient_card_type = #{ patientCardType },
            </if>
            <if test="patientHealthCardNum != null">
                patient_health_card_num = #{ patientHealthCardNum },
            </if>
            <if test="insuranceTypeCode != null">
                insurance_type_code = #{ insuranceTypeCode },
            </if>
            <if test="maritalStatus != null">
                marital_status = #{ maritalStatus },
            </if>
            <if test="nation != null">
                nation = #{ nation },
            </if>
            <if test="contactsName != null">
                contacts_name = #{ contactsName },
            </if>
            <if test="contactsPhone != null">
                contacts_phone = #{ contactsPhone },
            </if>
            <if test="nationality != null">
                nationality = #{ nationality },
            </if>
            <if test="educationCode != null">
                education_code = #{ educationCode },
            </if>
            <if test="contactsRelationship != null">
                contacts_relationship = #{ contactsRelationship },
            </if>
            <if test="auxiliaryInspectionResults != null">
                auxiliary_inspection_results = #{ auxiliaryInspectionResults },
            </if>
            <if test="syndromeDifferentiationBasis != null">
                syndrome_differentiation_basis = #{ syndromeDifferentiationBasis },
            </if>
            <if test="tcmApproach != null">
                tcm_approach = #{ tcmApproach },
            </if>
            <if test="etiologyClassification != null">
                etiology_classification = #{ etiologyClassification },
            </if>
            <if test="syndromeDifferentiationMethod != null">
                syndrome_differentiation_method = #{ syndromeDifferentiationMethod },
            </if>
            <if test="tcmPathogenesis != null">
                tcm_pathogenesis = #{ tcmPathogenesis },
            </if>
            <if test="physicalAffectingFactors != null">
                physical_affecting_factors = #{ physicalAffectingFactors },
            </if>
            <if test="tcmIncidentsType != null">
                tcm_incidents_type = #{ tcmIncidentsType },
            </if>
            <if test="tcmIncidentsForm != null">
                tcm_incidents_form = #{ tcmIncidentsForm },
            </if>
            <if test="inspectionInfoString != null">
                inspection_info_string = #{ inspectionInfoString },
            </if>
            <if test="occupation != null">
                occupation = #{ occupation },
            </if>
            <if test="onsetSolarTermCode != null">
                onset_solar_term_code = #{ onsetSolarTermCode },
            </if>
            <if test="initialDiagnosisCode != null">
                initial_diagnosis_code = #{ initialDiagnosisCode },
            </if>
            <if test="treatmentCategoryCode != null">
                treatment_category_code = #{ treatmentCategoryCode },
            </if>
            <if test="clinicalPathwayCode != null">
                clinical_pathway_code = #{ clinicalPathwayCode },
            </if>
            <if test="scientificResearchFlag != null">
                scientific_research_flag = #{ scientificResearchFlag },
            </if>
            <if test="allergyHistoryFlag != null">
                allergy_history_flag = #{ allergyHistoryFlag },
            </if>
            <if test="allergyHistory != null">
                allergy_history = #{ allergyHistory },
            </if>
            <if test="allergicDrugFlag != null">
                allergic_drug_flag = #{ allergicDrugFlag },
            </if>
            <if test="allergicDrug != null">
                allergic_drug = #{ allergicDrug },
            </if>
            <if test="infectiousHistoryFlag != null">
                infectious_history_flag = #{ infectiousHistoryFlag },
            </if>
            <if test="infectiousHistory != null">
                infectious_history = #{ infectiousHistory },
            </if>
            <if test="vaccinationHistory != null">
                vaccination_history = #{ vaccinationHistory },
            </if>
            <if test="surgicalHistory != null">
                surgical_history = #{ surgicalHistory },
            </if>
            <if test="bloodTransfusionHistory != null">
                blood_transfusion_history = #{ bloodTransfusionHistory },
            </if>
            <if test="pregnancyFlag != null">
                pregnancy_flag = #{ pregnancyFlag },
            </if>
            <if test="sucklingPeriodFlag != null">
                suckling_period_flag = #{ sucklingPeriodFlag },
            </if>
            <if test="obstetricHistory != null">
                obstetric_history = #{ obstetricHistory },
            </if>
            <if test="menstrualHistory != null">
                menstrual_history = #{ menstrualHistory },
            </if>
            <if test="height != null">
                height = #{ height },
            </if>
            <if test="weight != null">
                weight = #{ weight },
            </if>
            <if test="bmi != null">
                bmi = #{ bmi },
            </if>
            <if test="auxiliaryInspectionItems != null">
                auxiliary_inspection_items = #{ auxiliaryInspectionItems },
            </if>
            <if test="symptomDescription != null">
                symptom_description = #{ symptomDescription },
            </if>
            <if test="tongueCondition != null">
                tongue_condition = #{ tongueCondition },
            </if>
            <if test="pulseCondition != null">
                pulse_condition = #{ pulseCondition },
            </if>
            <if test="westDisCode != null">
                west_dis_code = #{ westDisCode },
            </if>
            <if test="chineseDisCode != null">
                chinese_dis_code = #{ chineseDisCode },
            </if>
            <if test="symCode != null">
                sym_code = #{ symCode },
            </if>
            <if test="diagnosticFlag != null">
                diagnostic_flag = #{ diagnosticFlag },
            </if>
            <if test="yiShiSign != null">
                yi_shi_sign = #{ yiShiSign },
            </if>
            <if test="cnPreInfo != null">
                cn_pre_info = #{ cnPreInfo },
            </if>
            <if test="weCnPreInfo != null">
                we_cn_pre_info = #{ weCnPreInfo },
            </if>
            <if test="commonOpInfo != null">
                common_op_info = #{ commonOpInfo },
            </if>
            <if test="medicalExpensesSettledCode != null">
                medical_expenses_settled_code = #{medicalExpensesSettledCode},
            </if>
            <if test="etiologyClassificationCode != null">
                etiology_classification_code = #{etiologyClassificationCode},
            </if>
            <if test="physicalAffectingFactorsCode != null">
                physical_affecting_factors_code = #{physicalAffectingFactorsCode},
            </if>
        </set>
        where records_id = #{ recordsId }
    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from medical_records where records_id = #{ recordsId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.health.MedicalRecords"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from medical_records
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>


    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.beans.health.MedicalRecords">
        <id column="records_id" jdbcType="VARCHAR" property="recordsId"/>
        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName"/>
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_id" jdbcType="VARCHAR" property="insId"/>
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="ins_name" jdbcType="VARCHAR" property="insName"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode"/>
        <result column="chief_complaint" jdbcType="VARCHAR" property="chiefComplaint"/>
        <result column="present_illness" jdbcType="VARCHAR" property="presentIllness"/>
        <result column="past_history" jdbcType="VARCHAR" property="pastHistory"/>
        <result column="personal_history" jdbcType="VARCHAR" property="personalHistory"/>
        <result column="family_history" jdbcType="VARCHAR" property="familyHistory"/>
        <result column="physical_examination" jdbcType="VARCHAR" property="physicalExamination"/>
        <result column="chinese_dis_name" jdbcType="VARCHAR" property="chineseDisName"/>
        <result column="west_dis_name" jdbcType="VARCHAR" property="westDisName"/>
        <result column="chinese_dis_id" jdbcType="VARCHAR" property="chineseDisId"/>
        <result column="west_dis_id" jdbcType="VARCHAR" property="westDisId"/>
        <result column="sym_name" jdbcType="VARCHAR" property="symName"/>
        <result column="sym_id" jdbcType="VARCHAR" property="symId"/>
        <result column="records_advice" jdbcType="VARCHAR" property="recordsAdvice"/>
        <result column="record_time" jdbcType="TIMESTAMP" property="recordTime"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="records_times" jdbcType="INTEGER" property="recordsTimes"/>
        <result column="patient_name" jdbcType="VARCHAR" property="patientName"/>
        <result column="patient_id" jdbcType="VARCHAR" property="patientId"/>
        <result column="patient_sex" jdbcType="VARCHAR" property="patientSex"/>
        <result column="patient_phone" jdbcType="VARCHAR" property="patientPhone"/>
        <result column="patient_age" jdbcType="VARCHAR" property="patientAge"/>
        <result column="patient_card_number" jdbcType="VARCHAR" property="patientCardNumber"/>
        <result column="VISIT_NO" jdbcType="VARCHAR" property="visitNo"/>
        <result column="health_files_num" jdbcType="VARCHAR" property="healthFilesNum"/>
        <result column="patient_card_type" jdbcType="VARCHAR" property="patientCardType"/>
        <result column="patient_health_card_num" jdbcType="VARCHAR" property="patientHealthCardNum"/>
        <result column="insurance_type_code" jdbcType="VARCHAR" property="insuranceTypeCode"/>
        <result column="marital_status" jdbcType="VARCHAR" property="maritalStatus"/>
        <result column="nation" jdbcType="VARCHAR" property="nation"/>
        <result column="contacts_name" jdbcType="VARCHAR" property="contactsName"/>
        <result column="contacts_phone" jdbcType="VARCHAR" property="contactsPhone"/>
        <result column="nationality" jdbcType="VARCHAR" property="nationality"/>
        <result column="education_code" jdbcType="VARCHAR" property="educationCode"/>
        <result column="contacts_relationship" jdbcType="VARCHAR" property="contactsRelationship"/>
        <result column="auxiliary_inspection_results" jdbcType="VARCHAR" property="auxiliaryInspectionResults"/>
        <result column="syndrome_differentiation_basis" jdbcType="VARCHAR" property="syndromeDifferentiationBasis"/>
        <result column="tcm_approach" jdbcType="VARCHAR" property="tcmApproach"/>
        <result column="etiology_classification" jdbcType="VARCHAR" property="etiologyClassification"/>
        <result column="syndrome_differentiation_method" jdbcType="VARCHAR" property="syndromeDifferentiationMethod"/>
        <result column="tcm_pathogenesis" jdbcType="VARCHAR" property="tcmPathogenesis"/>
        <result column="physical_affecting_factors" jdbcType="VARCHAR" property="physicalAffectingFactors"/>
        <result column="tcm_incidents_type" jdbcType="VARCHAR" property="tcmIncidentsType"/>
        <result column="tcm_incidents_form" jdbcType="VARCHAR" property="tcmIncidentsForm"/>
        <result column="inspection_info_string" jdbcType="VARCHAR" property="inspectionInfoString"/>
        <result column="occupation" jdbcType="VARCHAR" property="occupation"/>
        <result column="onset_solar_term_code" jdbcType="VARCHAR" property="onsetSolarTermCode"/>
        <result column="initial_diagnosis_code" jdbcType="VARCHAR" property="initialDiagnosisCode"/>
        <result column="treatment_category_code" jdbcType="VARCHAR" property="treatmentCategoryCode"/>
        <result column="clinical_pathway_code" jdbcType="VARCHAR" property="clinicalPathwayCode"/>
        <result column="scientific_research_flag" jdbcType="VARCHAR" property="scientificResearchFlag"/>
        <result column="allergy_history_flag" jdbcType="VARCHAR" property="allergyHistoryFlag"/>
        <result column="allergy_history" jdbcType="VARCHAR" property="allergyHistory"/>
        <result column="allergic_drug_flag" jdbcType="VARCHAR" property="allergicDrugFlag"/>
        <result column="allergic_drug" jdbcType="VARCHAR" property="allergicDrug"/>
        <result column="infectious_history_flag" jdbcType="VARCHAR" property="infectiousHistoryFlag"/>
        <result column="infectious_history" jdbcType="VARCHAR" property="infectiousHistory"/>
        <result column="vaccination_history" jdbcType="VARCHAR" property="vaccinationHistory"/>
        <result column="surgical_history" jdbcType="VARCHAR" property="surgicalHistory"/>
        <result column="blood_transfusion_history" jdbcType="VARCHAR" property="bloodTransfusionHistory"/>
        <result column="pregnancy_flag" jdbcType="VARCHAR" property="pregnancyFlag"/>
        <result column="suckling_period_flag" jdbcType="VARCHAR" property="sucklingPeriodFlag"/>
        <result column="obstetric_history" jdbcType="VARCHAR" property="obstetricHistory"/>
        <result column="menstrual_history" jdbcType="VARCHAR" property="menstrualHistory"/>
        <result column="height" jdbcType="VARCHAR" property="height"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="bmi" jdbcType="VARCHAR" property="bmi"/>
        <result column="auxiliary_inspection_items" jdbcType="VARCHAR" property="auxiliaryInspectionItems"/>
        <result column="symptom_description" jdbcType="VARCHAR" property="symptomDescription"/>
        <result column="tongue_condition" jdbcType="VARCHAR" property="tongueCondition"/>
        <result column="pulse_condition" jdbcType="VARCHAR" property="pulseCondition"/>
        <result column="west_dis_code" jdbcType="VARCHAR" property="westDisCode"/>
        <result column="chinese_dis_code" jdbcType="VARCHAR" property="chineseDisCode"/>
        <result column="sym_code" jdbcType="VARCHAR" property="symCode"/>
        <result column="diagnostic_flag" jdbcType="VARCHAR" property="diagnosticFlag"/>
        <collection property="medicalRecordsPrescriptionsList"
                    ofType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptions" javaType="List">
            <result column="prescriptions_id" jdbcType="VARCHAR" property="prescriptionsId"/>
            <result column="prescription_name" jdbcType="VARCHAR" property="prescriptionName"/>
            <result column="prescription_num" jdbcType="VARCHAR" property="prescriptionNum"/>
            <result column="chinese_prescription_usage" jdbcType="VARCHAR" property="chinesePrescriptionUsage"/>
            <result column="chinese_prescription_rate" jdbcType="VARCHAR" property="chinesePrescriptionRate"/>
            <result column="records_id" jdbcType="VARCHAR" property="recordsId"/>
            <result column="prescriptions_time" jdbcType="TIMESTAMP" property="prescriptionsTime"/>
            <result column="prescriptions_effective_days" jdbcType="DOUBLE" property="prescriptionsEffectiveDays"/>
            <result column="chinese_prescription_cost" jdbcType="VARCHAR" property="chinesePrescriptionCost"/>
            <result column="prescription_category" jdbcType="VARCHAR" property="prescriptionCategory"/>
            <result column="decocting_method" jdbcType="VARCHAR" property="decoctingMethod"/>
            <result column="medication_method" jdbcType="VARCHAR" property="medicationMethod"/>
            <result column="medication_requirements" jdbcType="VARCHAR" property="medicationRequirements"/>
            <result column="medication_contraindications" jdbcType="VARCHAR" property="medicationContraindications"/>
            <result column="prescribing_doctor_sign" jdbcType="VARCHAR" property="prescribingDoctorSign"/>
            <result column="reviewed_doctor_sign" jdbcType="VARCHAR" property="reviewedDoctorSign"/>
            <result column="prescription_propertie_code" jdbcType="VARCHAR" property="prescriptionPropertieCode"/>
            <result column="cipher_prescription" jdbcType="VARCHAR" property="cipherPrescription"/>
            <result column="decocting_flag" jdbcType="VARCHAR" property="decoctingFlag"/>
            <result column="preparation_flag" jdbcType="VARCHAR" property="preparationFlag"/>
            <collection property="prescriptionsItemList"
                        ofType="com.cbkj.diagnosis.beans.health.MedicalRecordsPrescriptionsItem">
                <result column="item_id" jdbcType="BIGINT" property="itemId"/>
                <result column="prescriptions_id" jdbcType="VARCHAR" property="prescriptionsId"/>
                <result column="drug_name" jdbcType="VARCHAR" property="drugName"/>
                <result column="drug_code" jdbcType="VARCHAR" property="drugCode"/>
                <result column="drug_dose" jdbcType="VARCHAR" property="drugDose"/>
                <result column="te_shu_zhu_fa" jdbcType="VARCHAR" property="teShuZhuFa"/>
                <result column="drug_dosage" jdbcType="VARCHAR" property="drugDosage"/>
                <result column="drug_dose_unit" jdbcType="VARCHAR" property="drugDoseUnit"/>
                <result column="drug_frequency" jdbcType="VARCHAR" property="drugFrequency"/>
                <result column="administration_route" jdbcType="VARCHAR" property="administrationRoute"/>
                <result column="drug_total_dosage" jdbcType="VARCHAR" property="drugTotalDosage"/>
                <result column="drug_level_code" jdbcType="VARCHAR" property="drugLevelCode"/>
                <result column="drug_category_code" jdbcType="VARCHAR" property="drugCategoryCode"/>
                <result column="drug_decocting_method" jdbcType="VARCHAR" property="drugDecoctingMethod"/>
                <result column="mmag_code" jdbcType="VARCHAR" property="mmagCode"/>
                <result column="drug_order" jdbcType="VARCHAR" property="drugOrder"/>
            </collection>
        </collection>
    </resultMap>

    <select id="getMedicalRecordsDetails" resultMap="BaseResultMap2"
            parameterType="com.cbkj.diagnosis.beans.health.vo.GetMedicalRecordsDetailsQuery">
        select
        a.*,
        b.*,
        c.*
        from medical_records as a join medical_records_prescriptions as b on(a.records_id = b.records_id) join
        medical_records_prescriptions_item as c
        on(c.prescriptions_id = b.prescriptions_id)
        <where>
            <if test="patientId != null and patientId != ''">
                a.patient_id = #{patientId}
            </if>
            <if test="queryYearStart != null and queryYearStart != ''">
                a.record_time >= #{queryYearStart} and a.record_time &lt;= #{queryYearStart}
            </if>
        </where>

    </select>

    <select id="getRecordListByCreateDate" parameterType="com.cbkj.diagnosis.beans.monitor.vo.GetPatientInfo"
            resultType="com.cbkj.diagnosis.beans.monitor.dto.RecordInfoDTO">

        <choose>
            <when test="recordTypeCode == 1 || recordTypeCode == 4">
                <!-- 1 预诊 4 随访 -->
                SELECT t.rec_id AS id2,t.rec_id AS id,(CASE WHEN (t.form_type = '1') THEN '1' ELSE '4' END) AS 'recordTypeCode' ,
                t.create_date AS timedate, t.closed_status as closedStatus
                FROM t_record t

                WHERE t.patient_id=#{patientId}
                <if test=" null != disId  and disId != ''">
                    <!--      AND mr.chinese_dis_id =#{disId} -->
                    and (SELECT COUNT(*) FROM t_pre_diagnosis_dis_mapping AS tpd WHERE tpd.`dia_id` = t.`dia_id` AND
                    tpd.dis_id = #{disId} ) > 0
                </if>
                <if test="null != closedNo and closedNo != '' ">
                    AND t.closed_no = #{closedNo}
                </if>
                <if test="recordTypeCode  == 1 ">
                    AND t.form_type = '1'
                </if>
                <if test="recordTypeCode  == 4">
                    AND t.form_type = '2'
                </if>
                <!--        GROUP BY t.form_type,t.create_date -->
                ORDER BY timedate asc
            </when>
            <when test="recordTypeCode == 2">
                <!-- 2 就诊 -->
                SELECT s.records_id AS id2,s.records_id AS id,'2' AS 'recordTypeCode' , s.record_time AS timedate,
                s.closed_status as closedStatus FROM medical_records s
                <!-- LEFT JOIN medical_records mr ON  s.patient_id=mr.patient_id -->
                WHERE s.patient_id= #{patientId}
                <if test=" null != disId  and disId != ''">
                    AND s.chinese_dis_id =#{disId}
                </if>
                <if test="null != closedNo and closedNo != '' ">
                    AND s.closed_no = #{closedNo}
                </if>
                ORDER BY timedate
            </when>
            <when test="recordTypeCode == 3">
                <!-- 3 宣教 -->
                SELECT s.task_patients_id AS id2,s.road_execute_event_content_id AS id ,'3' AS 'recordTypeCode' , s.task_excute_time AS timedate,
                s.closed_status as closedStatus
                FROM
                `s_road_task_patients` s
                JOIN medical_records mr ON s.patient_id=mr.patient_id
                WHERE s.road_execute_event_type='1' AND s.patient_id= #{patientId}
                <if test=" null != disId  and disId != ''">
                    AND mr.chinese_dis_id =#{disId}
                </if>
                <if test="null != closedNo and closedNo != '' ">
                    AND s.closed_no = #{closedNo}
                </if>
                ORDER BY timedate
            </when>
            <otherwise>
                select a.* from (
                SELECT s.task_patients_id AS id2,s.road_execute_event_content_id AS id , '3' AS 'recordTypeCode' , s.task_excute_time AS timedate,
                s.closed_status as closedStatus
                FROM `s_road_task_patients` s
                JOIN medical_records mr ON s.records_id=mr.records_id
                WHERE s.road_execute_event_type='1' AND s.patient_id=#{patientId}
                <if test=" null != disId  and disId != ''">
                    AND mr.chinese_dis_id =#{disId}
                </if>
                <if test="null != closedNo and closedNo != '' ">
                    AND s.closed_no = #{closedNo}
                </if>
                UNION ALL
                SELECT records_id AS id2,records_id AS id, '2' AS 'recordTypeCode' , record_time AS timedate,
                closed_status as closedStatus
                FROM medical_records
                WHERE patient_id=#{patientId}
                <if test=" null != disId  and disId != ''">
                    AND chinese_dis_id =#{disId}
                </if>
                <if test="null != closedNo and closedNo != '' ">
                    AND closed_no = #{closedNo}
                </if>
                UNION ALL
                SELECT rec_id AS id2,rec_id AS id, '1' AS 'recordTypeCode' , t.create_date AS timedate,
                t.closed_status as closedStatus
                FROM t_record t

                WHERE t.patient_id=#{patientId} AND t.form_type = '1'
                <if test=" null != disId  and disId != ''">
                    <!--      AND mr.chinese_dis_id =#{disId} -->
                    and (SELECT COUNT(*) FROM t_pre_diagnosis_dis_mapping AS tpd WHERE tpd.`dia_id` = t.`dia_id` AND
                    tpd.dis_id = #{disId} ) > 0
                </if>
                <if test="null != closedNo and closedNo != '' ">
                    AND t.closed_no = #{closedNo}
                </if>

                UNION ALL
                SELECT rec_id AS id2,rec_id AS id, '4' AS 'recordTypeCode' , t.create_date AS timedate,
                t.closed_status as closedStatus
                FROM t_record t

                WHERE t.patient_id=#{patientId} AND t.form_type = '2'
                <if test=" null != disId  and disId != ''">
                    <!--          AND mr.chinese_dis_id =#{disId} -->
                    and (SELECT COUNT(*) FROM t_pre_diagnosis_dis_mapping AS tpd WHERE tpd.`dia_id` = t.`dia_id` AND
                    tpd.dis_id = #{disId} ) > 0
                </if>
                <if test="null != closedNo and closedNo != '' ">
                    AND t.closed_no = #{closedNo}
                </if>

                <!-- GROUP BY t.form_type,t.create_date -->
                <!-- ORDER BY timedate -->
                ) as a order by a.timedate asc
            </otherwise>
        </choose>

    </select>

    <resultMap id="BaseResultMap222" type="com.cbkj.diagnosis.beans.monitor.vo.DataSourceList">
        <result column="chinese_dis_name" property="chineseDisName"/>
        <result column="patient_id" property="patientId"/>
        <result column="patient_code" property="patientCode"/>
        <result column="patient_name" property="patientName"/>
        <result column="patient_age" jdbcType="INTEGER" property="patientAge"/>
        <result column="patient_sex" property="patientSex"/>
        <result column="doctor_name" property="doctorName"/>
        <result column="closed_no" property="closedNo"/>
        <result column="closed_status" property="closedStatus"/>
        <result column="ins_name" property="insName"/>
        <result column="endTime" property="monitorEndDate"/>
        <result column="startTime" property="monitorStartDate"/>

        <result column="totalMedicineVisit" property="totalMedicineVisit"/>
        <result column="totalXuanJiao" property="totalXuanJiao"/>
        <result column="totalSuiFang" property="totalSuiFang"/>
        <result column="totalYuZhen" property="totalYuZhen"/>
        <result column="chinese_dis_id" property="chineseDisId"/>
    </resultMap>


    <select id="getDataSourceList" resultMap="BaseResultMap222"
            parameterType="com.cbkj.diagnosis.beans.monitor.vo.ResourceListVO">
        SELECT
        a.chinese_dis_name , a.chinese_dis_id,
        a.`patient_id`,a.`patient_code`,a.`patient_name`,a.`patient_age`,a.`patient_sex`,a.`ins_name`,
        (SELECT aa.create_date FROM medical_records AS aa WHERE aa.patient_id = a.patient_id ORDER BY aa.create_date
        DESC LIMIT 1 ) endTime,
        (SELECT aa.create_date FROM medical_records AS aa WHERE aa.patient_id = a.patient_id ORDER BY aa.create_date ASC
        LIMIT 1 ) startTime,
        (SELECT COUNT(*) FROM medical_records AS aaa WHERE aaa.patient_id=a.patient_id ) AS totalMedicineVisit,
        (SELECT COUNT(*) FROM s_road_task_patients AS bb WHERE bb.patient_id =a.patient_id AND
        bb.road_execute_event_type='1') AS totalXuanJiao,
        (SELECT COUNT(*) FROM t_record AS bb WHERE bb.patient_id =a.patient_id AND bb.form_type='2') AS totalSuiFang,
        (SELECT COUNT(*) FROM t_record AS bb WHERE bb.patient_id =a.patient_id AND bb.form_type='1') AS totalYuZhen
        FROM `medical_records` AS a
        <where>

            <if test="null != chineseDisIds and chineseDisIds != '' ">
                <foreach collection="chineseDisIds" item="chineseDisId" separator="," open="AND a.chinese_dis_id IN ("
                         close=")">
                    #{chineseDisId}
                </foreach>
            </if>
            <if test="insCodes != null and insCodes.size() > 0">
                <foreach collection="insCodes" item="insCode" separator="," open="AND a.ins_code IN (" close=")">
                    #{insCode}
                </foreach>
            </if>
            <if test="null != patientSex and patientSex != '' ">
                AND a.patient_sex =#{patientSex}
            </if>
            <if test="null != patientMinAge and patientMinAge != '' and null != patientMaxAge and patientMaxAge != '' ">
                AND a.patient_age BETWEEN #{patientMinAge ,jdbcType=INTEGER} and #{patientMaxAge ,jdbcType=INTEGER}
            </if>
            <if test="null != monitorStartDateStr and monitorStartDateStr != '' and null != monitorEndDateStr and monitorEndDateStr != '' ">
                AND a.create_date BETWEEN #{monitorStartDateStr} and #{monitorEndDateStr}
            </if>
        </where>
        GROUP BY a.chinese_dis_name,a.`patient_id`

    </select>

    <select id="getCaseSelectionList" resultMap="BaseResultMap222"
            parameterType="com.cbkj.diagnosis.beans.monitor.vo.ResourceListVO">
        SELECT e.* FROM (
        SELECT a.*,b.totalMedicineVisit AS totalMedicineVisit
        ,IFNULL(c.totalXuanJiao,0) AS totalXuanJiao
        ,IFNULL(d.totalSuiFang,0) AS totalSuiFang
        ,IFNULL(d.totalYuZhen,0) AS totalYuZhen
        FROM (SELECT
        (SELECT i.dis_name FROM t_record h JOIN `t_pre_diagnosis_dis_mapping` i ON h.dia_id = i.dia_id WHERE h.form_type
        = '1' AND i.dis_type='1' AND STATUS='0' AND h.patient_id=a.`patient_id` ORDER BY create_date LIMIT 1) AS
        chinese_dis_name ,
        (SELECT g.doctor_name FROM medical_records g WHERE g.patient_id=a.`patient_id` and g.chinese_dis_id =
        a.chinese_dis_id ORDER BY create_date LIMIT 1) AS doctor_name ,
        a.chinese_dis_id,
        a.`patient_id`,a.`patient_code`,a.patient_card_number,a.`patient_name`,a.`patient_age`,a.`patient_sex`,a.`ins_name`,MAX(create_date)
        endTime,MIN(create_date) startTime
        FROM `medical_records` AS a
        where a.closed_status = '0'

        <if test="null != chineseDisIds and chineseDisIds != '' ">
            <foreach collection="chineseDisIds" item="chineseDisId" separator="," open="AND a.chinese_dis_id IN ("
                     close=")">
                #{chineseDisId}
            </foreach>
        </if>
        <if test="insCodes != null and insCodes.size() > 0">
            <foreach collection="insCodes" item="insCode" separator="," open="AND a.ins_code IN (" close=")">
                #{insCode}
            </foreach>
        </if>
        <if test="null != patientSex and patientSex != '' ">
            AND a.patient_sex =#{patientSex}
        </if>
        <if test="null != patientName and patientName != '' ">
            AND (a.patient_name like CONCAT('%',trim(#{patientName}),'%') or a.patient_card_number like
            CONCAT('%',trim(#{patientName}),'%') )
        </if>
        <if test="null != patientMinAge and patientMinAge != '' and null != patientMaxAge and patientMaxAge != '' ">
            AND a.patient_age BETWEEN #{patientMinAge ,jdbcType=INTEGER} and #{patientMaxAge ,jdbcType=INTEGER}
        </if>
        <if test="null != monitorStartDateStr and monitorStartDateStr != '' and null != monitorEndDateStr and monitorEndDateStr != '' ">
            AND a.create_date BETWEEN #{monitorStartDateStr} and #{monitorEndDateStr}
        </if>

        GROUP BY a.`patient_id`
        ) a JOIN (SELECT patient_id ,COUNT(*) totalMedicineVisit FROM medical_records where closed_status = '0' GROUP BY
        patient_id) b ON a.patient_id=b.patient_id
        LEFT JOIN (SELECT patient_id ,COUNT(*) totalXuanJiao FROM s_road_task_patients AS bb WHERE
        bb.road_execute_event_type='1' and bb.closed_status = '0' GROUP BY patient_id ) c ON a.patient_id=c.patient_id
        LEFT JOIN (SELECT patient_id ,COUNT(CASE WHEN bb.form_type='2' THEN 1 END) AS totalSuiFang
        ,COUNT(CASE WHEN bb.form_type='1' THEN 1 END) AS totalYuZhen
        FROM t_record AS bb where bb.closed_status = '0' GROUP BY patient_id ) d ON a.patient_id=d.patient_id

        <where>
            <if test="null != recordTypeCode1 and recordTypeCode1 != '' ">
                AND d.totalYuZhen > 0
            </if>
            <if test="null != recordTypeCode3 and recordTypeCode3 != '' ">
                AND c.totalXuanJiao > 0
            </if>
            <if test="null != recordTypeCode4 and recordTypeCode4 != '' ">
                AND d.totalSuiFang > 0
            </if>
        </where>
        ) e

    </select>

    <select id="getCaseClosedList" resultMap="BaseResultMap222"
            parameterType="com.cbkj.diagnosis.beans.monitor.vo.ResourceListVO">
        SELECT
            (SELECT i.dis_name FROM t_record h JOIN `t_pre_diagnosis_dis_mapping` i ON h.dia_id = i.dia_id WHERE h.form_type = '1' AND i.dis_type='1'  AND STATUS='0' AND h.patient_id=a.`patient_id` ORDER BY create_date LIMIT 1) AS chinese_dis_name ,
        a.chinese_dis_id, a.`patient_id`,a.`patient_code`,a.`patient_name`,a.`patient_age`,a.`patient_sex`,a.`ins_name`,MAX(create_date) endTime,MIN(create_date)  startTime
        ,(SELECT COUNT(1) FROM t_record b WHERE a.closed_no = b.closed_no AND b.form_type='2') AS totalSuiFang
        ,(SELECT COUNT(1) FROM t_record d WHERE a.closed_no = d.closed_no AND d.form_type='1') AS totalYuZhen
        ,(SELECT COUNT(1) FROM medical_records c WHERE a.closed_no = c.closed_no) AS totalMedicineVisit
        ,(SELECT COUNT(1) FROM s_road_task_patients e  WHERE a.closed_no = e.closed_no AND e.road_execute_event_type= '1') AS totalXuanJiao
        ,a.patient_card_number
        ,MIN( CAST(a.closed_status AS SIGNED)) AS closed_status
        ,a.closed_no AS closed_no
        FROM `medical_records` a  WHERE closed_status != '0'

        <if test="null != chineseDisIds and chineseDisIds != '' ">
            <foreach collection="chineseDisIds" item="chineseDisId" separator="," open="AND a.chinese_dis_id IN ("
                     close=")">
                #{chineseDisId}
            </foreach>
        </if>

        <if test="null != patientName and patientName != '' ">
            AND (a.patient_name like CONCAT('%',trim(#{patientName}),'%') or a.patient_card_number like
            CONCAT('%',trim(#{patientName}),'%') )
        </if>

        <if test="null != monitorStartDateStr and monitorStartDateStr != '' and null != monitorEndDateStr and monitorEndDateStr != '' ">
            AND a.create_date BETWEEN #{monitorStartDateStr} and #{monitorEndDateStr}
        </if>

        <if test="null != closedStatus and closedStatus != '' ">
            AND a.closed_status = #{closedStatus}
        </if>

        GROUP BY a.closed_no

    </select>

    <select id="selectOneByCondition" resultMap="BaseResultMap"
            parameterType="com.cbkj.diagnosis.beans.health.MedicalRecords">
        select
        <include refid="Base_Column_List"/>
        from medical_records
        <where>
            app_id = #{appId}
            and ins_code = #{insCode}
            and VISIT_NO = #{visitNo}

        </where>


        limit 1
    </select>
    <select id="getOneBypatientId" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from medical_records
        <where>
            patient_id = #{patientId}
        </where>


        limit 1
    </select>

    <select id="getMedicalRecordByRecordId" resultMap="BaseResultMap">
        SELECT *
        FROM medical_records
        WHERE records_id = #{recordId}
    </select>
    <select id="getNoEcrpy" parameterType="com.cbkj.diagnosis.beans.business.requestvo.NoEcrpy"
            resultMap="BaseResultMapNoEn">
        select
        <include refid="Base_Column_List"/>
        from medical_records
        <where>
            <if test="startDate != null and startDate != ''">
                and record_time >= #{startDate}

            </if>

            <if test="endDate != null and endDate != ''">
                and record_time <![CDATA[ <= ]]>   #{endDate}

            </if>
            and CHAR_LENGTH(patient_name) <![CDATA[ < ]]> 5

        </where>
    </select>


    <update id="updateClosedByPatientId" parameterType="String">
        update medical_records
        set closed_status = '1',
            closed_no= #{closedNo},
            closed_time=now()
        where patient_id = #{patientId}
          and closed_status = '0'
    </update>

    <update id="updateClosedById" parameterType="String">
        update medical_records
        set closed_status = '1',
            closed_no= #{closedNo},
            closed_time=now()
        where records_id = #{id}
          and closed_status = '0'
    </update>

    <update id="updateClosedByclosdNo" parameterType="String">
        update medical_records
        set closed_status = #{closedStatus},
            closed_time=now()
        where closed_no = #{closedNo}
    </update>


    <select id="getcloseNo" parameterType="String" resultType="com.cbkj.diagnosis.beans.business.TDiseaseNo">
        SELECT c.dis_id as disId,c.dis_name as disName ,c.dis_no as disNo,c.ins_name as insName
        FROM `t_record` a
        JOIN `t_pre_diagnosis_dis_mapping` b ON a.dia_id = b.dia_id AND b.dis_type = '1' AND a.STATUS='0'
        JOIN `t_disease_no` c ON c.dis_id = b.dis_id
        <where>
            <if test="patientId != null and patientId != ''">
                and a.patient_id = #{patientId}
            </if>
            <if test="id != null and id != ''">
                and a.rec_id = #{id}
            </if>
        </where>
        ORDER BY create_date LIMIT 1
    </select>

    <update id="updateTDiseaseNo" parameterType="com.cbkj.diagnosis.beans.business.TDiseaseNo">
        update t_disease_no
        set dis_no = #{disNo} + 1
        where dis_id = #{disId}
          and dis_no = #{disNo}
    </update>
</mapper>