<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.home.SysHomeShowUrlMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.home.SysHomeShowUrl">
        <id column="show_url_id" jdbcType="INTEGER"  property="showUrlId" />
        <result column="label" jdbcType="VARCHAR" property="label" />
        <result column="route" jdbcType="VARCHAR" property="route" />
    </resultMap>


    <sql id="Base_Column_List">
    show_url_id,label,route
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.home.SysHomeShowUrl">
        delete from sys_home_show_url where show_url_id = #{ showUrlId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_home_show_url where show_url_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.home.SysHomeShowUrl">
        insert into sys_home_show_url (<include refid="Base_Column_List" />) values
        (#{showUrlId},#{label},#{route})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_home_show_url (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.showUrlId},#{item.label},#{item.route})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.home.SysHomeShowUrl">
        update sys_home_show_url
        <set>
             <if test="label != null">
                label = #{ label },
             </if>
             <if test="route != null">
                route = #{ route },
             </if>
        </set>
        where show_url_id = #{ showUrlId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_home_show_url where show_url_id = #{ showUrlId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.home.SysHomeShowUrl" resultMap="BaseResultMap">
        SELECT show_url_id,label,route
        from sys_home_show_url
        <where>
            <if test=" label != null and label!='' ">
                and label like CONCAT('%',trim(#{label}),'%')
            </if>
        </where>
order by sort asc
    </select>

</mapper>