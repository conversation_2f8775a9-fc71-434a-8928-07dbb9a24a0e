<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.SysLogInterfaceMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.SysLogInterface">
        <id column="ID" jdbcType="VARCHAR"  property="id" />
        <result column="APP_ID" jdbcType="VARCHAR" property="appId" />
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode" />
        <result column="DOCTOR_ID" jdbcType="VARCHAR" property="doctorId" />
        <result column="DOCTOR_NAME" jdbcType="VARCHAR" property="doctorName" />
        <result column="PATIENT_ID" jdbcType="VARCHAR" property="patientId" />
        <result column="PATIENT_NAME" jdbcType="VARCHAR" property="patientName" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="INTERFACE_NAME" jdbcType="VARCHAR" property="interfaceName" />
        <result column="INTERFACE_DESC" jdbcType="VARCHAR" property="interfaceDesc" />
        <result column="INTERFACE_TOKEN" jdbcType="VARCHAR" property="interfaceToken" />
        <result column="INTERFACE_PARAMS" jdbcType="VARCHAR" property="interfaceParams" />
        <result column="RESULT_STATUS" jdbcType="VARCHAR" property="resultStatus" />
        <result column="RESULT_MESEAGE" jdbcType="VARCHAR" property="resultMeseage" />
        <result column="RESULT_DATA" jdbcType="VARCHAR" property="resultData" />
    </resultMap>


    <sql id="Base_Column_List">
    ID,APP_ID,INS_CODE,DOCTOR_ID,DOCTOR_NAME,PATIENT_ID,PATIENT_NAME,CREATE_TIME,INTERFACE_NAME,INTERFACE_DESC,INTERFACE_TOKEN,INTERFACE_PARAMS,RESULT_STATUS,RESULT_MESEAGE,RESULT_DATA
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.SysLogInterface">
        delete from sys_log_interface where ID = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_log_interface where ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.SysLogInterface">
        insert into sys_log_interface (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{insCode},#{doctorId},#{doctorName},#{patientId},#{patientName},#{createTime},#{interfaceName},#{interfaceDesc},#{interfaceToken},#{interfaceParams},#{resultStatus},#{resultMeseage},#{resultData})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_log_interface (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.insCode},#{item.doctorId},#{item.doctorName},#{item.patientId},#{item.patientName},#{item.createTime},#{item.interfaceName},#{item.interfaceDesc},#{item.interfaceToken},#{item.interfaceParams},#{item.resultStatus},#{item.resultMeseage},#{item.resultData})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.SysLogInterface">
        update sys_log_interface
        <set>
             <if test="appId != null">
                APP_ID = #{ appId },
             </if>
             <if test="insCode != null">
                INS_CODE = #{ insCode },
             </if>
             <if test="doctorId != null">
                DOCTOR_ID = #{ doctorId },
             </if>
             <if test="doctorName != null">
                DOCTOR_NAME = #{ doctorName },
             </if>
             <if test="patientId != null">
                PATIENT_ID = #{ patientId },
             </if>
             <if test="patientName != null">
                PATIENT_NAME = #{ patientName },
             </if>
             <if test="createTime != null">
                CREATE_TIME = #{ createTime },
             </if>
             <if test="interfaceName != null">
                INTERFACE_NAME = #{ interfaceName },
             </if>
             <if test="interfaceDesc != null">
                INTERFACE_DESC = #{ interfaceDesc },
             </if>
             <if test="interfaceToken != null">
                INTERFACE_TOKEN = #{ interfaceToken },
             </if>
             <if test="interfaceParams != null">
                INTERFACE_PARAMS = #{ interfaceParams },
             </if>
             <if test="resultStatus != null">
                RESULT_STATUS = #{ resultStatus },
             </if>
             <if test="resultMeseage != null">
                RESULT_MESEAGE = #{ resultMeseage },
             </if>
             <if test="resultData != null">
                RESULT_DATA = #{ resultData },
             </if>
        </set>
        where ID = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_log_interface where ID = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.SysLogInterface" resultMap="BaseResultMap">
        SELECT ID,APP_ID,INS_CODE,DOCTOR_ID,DOCTOR_NAME,PATIENT_ID,PATIENT_NAME,CREATE_TIME,INTERFACE_NAME,INTERFACE_DESC,INTERFACE_TOKEN,RESULT_STATUS,RESULT_MESEAGE
        from sys_log_interface
        <where>
            <if test=" interfaceDesc != null and interfaceDesc!='' ">
                and INTERFACE_DESC like CONCAT('%',trim(#{interfaceDesc}),'%')
            </if>
            <if test=" beginTime != null">
                and CREATE_TIME >= #{beginTime}
            </if>
            <if test=" endTime!=null ">
                and CREATE_TIME &lt;= #{endTime}
            </if>
            <if test=" interfaceParams!=null and interfaceParams!=''">
                and INTERFACE_PARAMS like CONCAT('%',trim(#{interfaceParams}),'%')
            </if>
        </where>
        order by CREATE_TIME desc
    </select>

</mapper>
