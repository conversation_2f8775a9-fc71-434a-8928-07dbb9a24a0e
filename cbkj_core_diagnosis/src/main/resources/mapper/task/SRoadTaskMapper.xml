<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.task.SRoadTaskMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.task.SRoadTask">
        <id column="s_road_task_id" jdbcType="VARCHAR" property="sRoadTaskId"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="s_road_id" jdbcType="VARCHAR" property="sRoadId"/>
        <result column="s_road_task_content" jdbcType="VARCHAR" property="sRoadTaskContent"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="patient_num" jdbcType="INTEGER" property="patientNum"/>
        <result column="s_road_group_way" jdbcType="VARCHAR" property="sRoadGroupWay"/>
        <result column="s_road_name" jdbcType="VARCHAR" property="sRoadName"/>

        <result column="record_start_time" jdbcType="TIMESTAMP" property="recordStartTime"/>
        <result column="record_end_time" jdbcType="TIMESTAMP" property="recordEndTime"/>
        <result column="limit_diagnosis_days_info" jdbcType="INTEGER" property="limitDiagnosisDaysInfo"/>
        <result column="limit_repeat_record" jdbcType="INTEGER" property="limitRepeatRecord"/>

        <result column="limit_diagnosis_days_info_check" jdbcType="INTEGER" property="limitDiagnosisDaysInfoCheck"/>
        <result column="limit_repeat_record_check" jdbcType="INTEGER" property="limitRepeatRecordCheck"/>
        <result column="join_road_task" jdbcType="INTEGER" property="joinRoadTask"/>
    </resultMap>

    <resultMap id="BaseResultMapResponse" type="com.cbkj.diagnosis.beans.task.SRoadTaskResponse">
        <id column="s_road_task_id" jdbcType="VARCHAR" property="sRoadTaskId"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="s_road_id" jdbcType="VARCHAR" property="sRoadId"/>
        <result column="s_road_task_content" jdbcType="VARCHAR" property="sRoadTaskContent"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="patient_num" jdbcType="INTEGER" property="patientNum"/>
        <result column="s_road_group_way" jdbcType="VARCHAR" property="sRoadGroupWay"/>
        <result column="s_road_name" jdbcType="VARCHAR" property="sRoadName"/>


    </resultMap>


    <sql id="Base_Column_List">
        s_road_task_id,task_name,s_road_id,s_road_task_content,create_time,create_user_id,create_user_name,status,patient_num,s_road_group_way,
            record_start_time,record_end_time,limit_diagnosis_days_info,limit_repeat_record,join_road_task,
            limit_diagnosis_days_info_check,limit_repeat_record_check
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadTask">
        delete
        from s_road_task
        where s_road_task_id = #{ sRoadTaskId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road_task where s_road_task_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.cbkj.diagnosis.beans.task.SRoadTask">
        insert into s_road_task (<include refid="Base_Column_List"/>) values
        (#{sRoadTaskId},#{taskName},#{sRoadId},#{sRoadTaskContent},#{createTime},#{createUserId},
        #{createUserName},#{status},#{patientNum},#{sRoadGroupWay},
            #{recordStartTime},#{recordEndTime},#{limitDiagnosisDaysInfo},#{limitRepeatRecord},#{joinRoadTask},#{limitDiagnosisDaysInfoCheck},#{limitRepeatRecordCheck})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into s_road_task (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.sRoadTaskId},#{item.taskName},#{item.sRoadId},#{item.sRoadTaskContent},#{item.createTime},#{item.createUserId},
            #{item.createUserName},#{item.status},#{item.patientNum},#{item.sRoadGroupWay},
                #{item.recordStartTime},#{item.recordEndTime},#{item.limitDiagnosisDaysInfo},#{item.limitRepeatRecord},#{item.joinRoadTask},
            #{item.limitDiagnosisDaysInfoCheck},#{item.limitRepeatRecordCheck})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadTask">
        update s_road_task
        <set>
            <if test="taskName != null">
                task_name = #{ taskName },
            </if>
            <if test="sRoadId != null">
                s_road_id = #{ sRoadId },
            </if>
            <if test="sRoadTaskContent != null">
                s_road_task_content = #{ sRoadTaskContent },
            </if>
            <if test="createTime != null">
                create_time = #{ createTime },
            </if>
            <if test="createUserId != null">
                create_user_id = #{ createUserId },
            </if>
            <if test="createUserName != null">
                create_user_name = #{ createUserName },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="patientNum != null">
                patient_num = #{ patientNum },
            </if>
            <if test="sRoadGroupWay != null">
                s_road_group_way = #{ sRoadGroupWay },
            </if>

            <if test="sRoadName != null">
                s_road_name = #{sRoadName},
            </if>

            <if test="recordStartTime != null">
                record_start_time = #{recordStartTime},
            </if>
            <if test="recordEndTime != null">
                record_end_time = #{recordEndTime},
            </if>
            <if test="limitDiagnosisDaysInfo != null">
                limit_diagnosis_days_info = #{limitDiagnosisDaysInfo},
            </if>
            <if test="limitRepeatRecord != null">
                limit_repeat_record = #{limitRepeatRecord},
            </if>

            <if test="limitDiagnosisDaysInfoCheck != null">
                limit_diagnosis_days_info_check = #{limitDiagnosisDaysInfoCheck},
            </if>
            <if test="limitRepeatRecordCheck != null">
                limit_repeat_record_check = #{limitRepeatRecordCheck},
            </if>
            <if test="joinRoadTask != null">
                join_road_task = #{joinRoadTask},
            </if>

        </set>
        where s_road_task_id = #{ sRoadTaskId }
    </update>

    <update id="updateStatusDeleteByPrimaryKey" parameterType="String">
        update s_road_task
        <set>
            <if test="status != null">
                status = '1',
            </if>
        </set>
        where s_road_task_id = #{sRoadTaskId}

    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from s_road_task where s_road_task_id = #{ sRoadTaskId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.task.SRoadTask" resultMap="BaseResultMap">
        SELECT a.s_road_task_id,
        a.task_name,
        a.s_road_id,
        a.s_road_task_content,
        a.create_time,
        a.create_user_id,
        a.create_user_name,
        a.status,
            (select count(*) from s_road_task_patients_mapping srpm where srpm.s_road_task_id = a.s_road_task_id )  patient_num,
        a.s_road_group_way,
        b.s_road_name
        from s_road_task a join s_road b on(a.s_road_id = b.s_road_id)
        <where>
                                               a.status in ('0','2')
            <if test=" taskName != null and taskName!='' ">
                and a.task_name like CONCAT('%',trim(#{taskName}),'%')
            </if>
            <if test=" status != null and status!='' ">
                and a.status =#{status}
            </if>
            <if test=" sRoadGroupWay != null and sRoadGroupWay!='' ">
                and a.s_road_group_way = #{sRoadGroupWay}
            </if>
        </where>
order by  a.create_time desc
    </select>

    <select id="getPageListByObjResponse" parameterType="Map" resultMap="BaseResultMapResponse">
        SELECT a.s_road_task_id,
        a.task_name,
        a.s_road_id,
        a.s_road_task_content,
        a.create_time,
        a.create_user_id,
        a.create_user_name,
        a.status,
        (select count(*) from s_road_task_patients_mapping srpm where srpm.s_road_task_id = a.s_road_task_id )  patient_num,
        a.s_road_group_way,
        b.s_road_name
        from s_road_task a join s_road b on(a.s_road_id = b.s_road_id)
        <where>
            a.status in ('0','2')
            <if test=" taskName != null and taskName!='' ">
                and a.task_name like CONCAT('%',trim(#{taskName}),'%')
            </if>
            <if test="diaId != null and diaId!='' ">
                and a.task_name = #{diaId}
            </if>
            <if test=" status != null and status!='' ">
                and a.status =#{status}
            </if>
            <if test=" sRoadGroupWay != null and sRoadGroupWay!='' ">
                and a.s_road_group_way = #{sRoadGroupWay}
            </if>
        <if test="createUserId != null and createUserId !='' ">
            and
            (a.create_user_id = #{createUserId}
            or
            <!-- 医生在随访成员小组里面-->
            (select count(*) from s_road_task_ex as srte where srte.s_road_task_id = a.s_road_task_id and srte.ex_type =
            2 and srte.diagnosis_doctor_id = #{createUserId} ) > 0
            )
        </if>
        </where>
        order by  a.create_time desc
    </select>

    <select id="getRoadTaskConditons" resultType="com.cbkj.diagnosis.beans.task.RoadTaskConditonsSQLResult" parameterType="com.cbkj.diagnosis.beans.task.RoadTaskConditonsQuery">
        SELECT

                taskEx.`dept_id` deptId,
                taskEx.`dept_name` deptName,
                taskEx.`diagnosis_doctor_id` diagnosisDoctorId,
                taskEx.`record_doctor_name` recordDoctorName,
                taskEx.`record_doctor_id` recordDoctorId,
                taskEx.`diagnosis_doctor_name` diagnosisDoctorName

        FROM   `s_road_task_ex` AS taskEx
        WHERE taskEx.`s_road_task_id`
         = #{sRoadTaskId} and taskEx.ex_type = #{exType}

    </select>
    <select id="getAllList" resultType="com.cbkj.diagnosis.beans.task.SRoadTask">
        select
        <include refid="Base_Column_List"/>
        from s_road_task where  status = '0' and s_road_group_way = '2' and s_road_task_id = '1909420350993076224'
    </select>
    <select id="getListByObj" resultType="com.cbkj.diagnosis.beans.task.SRoadTask" parameterType="com.cbkj.diagnosis.beans.task.SRoadTask">
        select
        <include refid="Base_Column_List"/>
        from s_road_task where  status = '0' and s_road_id = #{sRoadId}

    </select>


</mapper>