<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.task.SRoadExecuteMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.task.SRoadExecute">
        <id column="road_execute_id" jdbcType="INTEGER"  property="roadExecuteId" />
        <result column="road_execute_event" jdbcType="VARCHAR" property="roadExecuteEvent" />
        <result column="road_execute_time" jdbcType="VARCHAR" property="roadExecuteTime" />
        <result column="road_execute_event_unit" jdbcType="VARCHAR" property="roadExecuteEventUnit" />
        <result column="road_execute_event_way" jdbcType="VARCHAR" property="roadExecuteEventWay" />
        <result column="road_execute_event_type" jdbcType="VARCHAR" property="roadExecuteEventType" />
        <result column="s_road_id" jdbcType="VARCHAR" property="sRoadId" />
        <result column="s_road_sort" jdbcType="INTEGER" property="sRoadSort" />
    </resultMap>


    <sql id="Base_Column_List">
    road_execute_id,road_execute_event,road_execute_time,road_execute_event_unit,road_execute_event_way,road_execute_event_type,s_road_id,s_road_sort
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadExecute">
        delete from s_road_execute where road_execute_id = #{ roadExecuteId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road_execute where road_execute_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.task.SRoadExecute" useGeneratedKeys="true" keyColumn="road_execute_id" keyProperty="roadExecuteId">
        insert into s_road_execute (<include refid="Base_Column_List" />) values
        (#{roadExecuteId},#{roadExecuteEvent},#{roadExecuteTime},#{roadExecuteEventUnit},#{roadExecuteEventWay},#{roadExecuteEventType},#{sRoadId},#{sRoadSort})
    </insert>

    <insert id="insertList" parameterType="List" useGeneratedKeys="true" keyColumn="road_execute_id" keyProperty="roadExecuteId">
        insert into s_road_execute (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.roadExecuteId},#{item.roadExecuteEvent},#{item.roadExecuteTime},#{item.roadExecuteEventUnit},#{item.roadExecuteEventWay},#{item.roadExecuteEventType},#{item.sRoadId},#{item.sRoadSort})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadExecute">
        update s_road_execute
        <set>
             <if test="roadExecuteEvent != null">
                road_execute_event = #{ roadExecuteEvent },
             </if>
             <if test="roadExecuteTime != null">
                road_execute_time = #{ roadExecuteTime },
             </if>
             <if test="roadExecuteEventUnit != null">
                road_execute_event_unit = #{ roadExecuteEventUnit },
             </if>
             <if test="roadExecuteEventWay != null">
                road_execute_event_way = #{ roadExecuteEventWay },
             </if>
             <if test="roadExecuteEventType != null">
                road_execute_event_type = #{ roadExecuteEventType },
             </if>
             <if test="sRoadId != null">
                s_road_id = #{ sRoadId },
             </if>
             <if test="sRoadSort != null">
                s_road_sort = #{ sRoadSort },
             </if>
        </set>
        where road_execute_id = #{ roadExecuteId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from s_road_execute where road_execute_id = #{ roadExecuteId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.task.SRoadExecute" resultMap="BaseResultMap">
        SELECT road_execute_id,road_execute_event,road_execute_time,road_execute_event_unit,road_execute_event_way,road_execute_event_type,s_road_id,s_road_sort
        from s_road_execute
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>


    <resultMap id="sRoadExecuteList" type="com.cbkj.diagnosis.beans.task.SRoadExecute">
        <id column="road_execute_id" jdbcType="INTEGER"  property="roadExecuteId" />
        <result column="road_execute_event" jdbcType="VARCHAR" property="roadExecuteEvent" />
        <result column="road_execute_time" jdbcType="VARCHAR" property="roadExecuteTime" />
        <result column="road_execute_event_unit" jdbcType="VARCHAR" property="roadExecuteEventUnit" />
        <result column="road_execute_event_way" jdbcType="VARCHAR" property="roadExecuteEventWay" />
        <result column="road_execute_event_type" jdbcType="VARCHAR" property="roadExecuteEventType" />
        <result column="s_road_id" jdbcType="VARCHAR" property="sRoadId" />
        <result column="s_road_sort" jdbcType="INTEGER" property="sRoadSort" />
        <collection property="sRoadExecuteContentsList" ofType="com.cbkj.diagnosis.beans.task.SRoadExecuteContents">
            <result column="road_execute_contents_id" jdbcType="INTEGER"  property="roadExecuteContentsId" />
            <result column="road_execute_id" jdbcType="INTEGER" property="roadExecuteId" />
            <result column="road_execute_event_type" jdbcType="VARCHAR" property="roadExecuteEventType" />
            <result column="road_execute_event_content_id" jdbcType="VARCHAR" property="roadExecuteEventContentId" />
            <result column="road_execute_event_content_name" jdbcType="VARCHAR" property="roadExecuteEventContentName" />
        </collection>
    </resultMap>


    <select id="selectsRoadExecuteList" resultMap="sRoadExecuteList" parameterType="String">
        SELECT a.road_execute_id,
               a.road_execute_event,
               a.road_execute_time,
               a.road_execute_event_unit,
               a.road_execute_event_way,
               a.road_execute_event_type,
               a.s_road_id,
               a.s_road_sort,

               b.road_execute_contents_id,
               b.road_execute_id,
               b.road_execute_event_type,
               b.road_execute_event_content_id,
               b.road_execute_event_content_name

        FROM s_road_execute a
                 join s_road_execute_contents b on (a.road_execute_id = b.road_execute_id)
        where s_road_id = #{sRoadId}

    </select>

    <resultMap id="recordAndPatientByConditionsResultMap" type="com.cbkj.diagnosis.service.webapi.business.vo.GetRecordAndPatientByConditions">
        <result column="records_id" jdbcType="VARCHAR" property="recordsId" />
        <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
        <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
        <result column="patient_sex" jdbcType="VARCHAR" property="patientSex" />
        <result column="patient_age" jdbcType="VARCHAR" property="patientAge" />
        <result column="patient_card_number" jdbcType="VARCHAR" property="patientCardNumber" />

        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName" />
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_id" jdbcType="VARCHAR" property="insId" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId" />
        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName" />
    </resultMap>


    <select id="getRecordAndPatientByConditions" resultMap="recordAndPatientByConditionsResultMap" parameterType="com.cbkj.diagnosis.service.webapi.business.vo.RecordAndPatientByConditions">


select * from (
        SELECT
            a.`USER_ID` patient_id,
            a.`USER_NAME` patient_name,
            a.`SEX` patient_sex,
            a.`AGE` patient_age,
            a.`card_number` patient_card_number,
            b.record_time,
            b.records_id records_id,


            b.app_id,
            b.ins_id,
            b.ins_name,
            b.ins_code,
            b.dept_code,
            b.dept_id,
            b.dept_name,
            b.doctor_id,
            b.doctor_name,
        a.`last_records_id`

        FROM
            `t_admin_info` a
                JOIN `medical_records` b
                     ON (
        <choose>
            <when test="recordsId != null and recordsId != ''">
                b.`records_id` = #{recordsId} and
            </when>
             <otherwise>
              <!--   a.`last_records_id` = b.`records_id` and -->
                1=1 and
             </otherwise>
        </choose>

                              a.`USER_ID` = b.`patient_id`
                         )

        <where>
            <!-- 判断 s_road_task_patients 表 中任务 road_execute_event_content_id 字段 task_excute_time 在字段 不能重 除了road_execute_event_type不等于3 这个要另外判断 -->
      <!--  <if test="roadTaskConditionList != null">

            <foreach collection="roadTaskConditionList"  item="item" index="index" separator="or">
                (c.`road_execute_event_content_id` = #{item.roadExecuteEventContentId}
                and c.road_execute_event_time = #{item.roadExecuteEventTime}
                and c.road_execute_event_unit = #{item.roadExecuteEventUnit}
                and c.records_id = b.`records_id`
                and c.patient_id = a.`USER_ID`
                and c.road_execute_event_type = #{item.roadExecuteEventType})
            </foreach>


        </if>
-->
        <if test="joinRoadTask != null and joinRoadTask == 0 ">
            and a.`join_road_task` != '1'
        </if>

        <if test="recordStartTime != null">
                AND b.`record_time`<![CDATA[ >= ]]> #{recordStartTime}
        </if>
        <if test="recordEndTime != null">
            AND b.`record_time`   <![CDATA[ <= ]]> #{recordEndTime}
        </if>

<!--         <if test="joinRoadTask != null and joinRoadTask == 0 ">-->
<!--            AND a.`join_road_task` = '0'-->
<!--         </if>-->


<if test="tChineseDiseaseId != null">
     and b.`chinese_dis_id` in
            <foreach
                collection="tChineseDiseaseId" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>

            <if test="tWestDiseaseId != null">

        and         b.`west_dis_id` in

             <foreach item="item" index="index" collection="tWestDiseaseId" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </if>

            <if test="sSymptomId != null">
            and
                 b.`sym_id` in

             <foreach item="item" index="index" collection="sSymptomId" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>


            <if test="doctorId != null">
                AND b.`doctor_id` in
             <foreach item="item" index="index" collection="doctorId" open="(" separator="," close=")">

                    #{item}
                </foreach>
            </if>
            <if test="sysDeptId != null">
                AND b.`dept_id` in
             <foreach item="item" index="index" collection="sysDeptId" open="(" separator="," close=")">

                    #{item}
                </foreach>
            </if>


        </where>


) as a
<where>
<!--  几天内没有重复就诊患者 不纳-->
     <if test="limitRepeatRecord != null and limitRepeatRecord != 0">

   and
         IFNULL(
         (DATEDIFF(   a.`record_time`,(SELECT aa.record_time FROM `medical_records` AS aa WHERE aa.patient_id = a.`patient_id`  AND aa.records_id != a.`last_records_id`  ORDER BY aa.record_time DESC LIMIT 0,1)))

         ,(#{limitRepeatRecord}+1)

         )

         <![CDATA[ > ]]> #{limitRepeatRecord}


         </if>
<!-- 从就诊开始时间 往前推 __ 天 有预诊信息患者 纳入-->
     <if test="limitDiagnosisDaysInfo != null and limitDiagnosisDaysInfo != 0">
   AND (select count(*) from t_record as b where b.patient_id = a.patient_id and datediff(a.record_time,b.create_date) <![CDATA[<= ]]> #{limitDiagnosisDaysInfo})>0
     </if>
     </where>


    </select>
    <select id="getRoadEventTwoDetailById" resultMap="sRoadExecuteList" parameterType="String">
        SELECT a.road_execute_id,
               a.road_execute_event,
               a.road_execute_time,
               a.road_execute_event_unit,
               a.road_execute_event_way,
               a.road_execute_event_type,
               a.s_road_id,
               a.s_road_sort,

               b.road_execute_contents_id,
               b.road_execute_id,
               b.road_execute_event_type,
               b.road_execute_event_content_id,
               b.road_execute_event_content_name

        FROM s_road_execute a
                 join s_road_execute_contents b on (a.road_execute_id = b.road_execute_id)
        where s_road_id = #{sRoadId} and a.road_execute_event = '2'
    </select>

</mapper>