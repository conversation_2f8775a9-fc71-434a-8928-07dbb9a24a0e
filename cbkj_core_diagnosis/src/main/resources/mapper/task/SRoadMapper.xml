<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.task.SRoadMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.task.SRoad">
        <id column="s_road_id" jdbcType="VARCHAR"  property="sRoadId" />
        <result column="s_road_name" jdbcType="VARCHAR" property="sRoadName" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="del_time" jdbcType="TIMESTAMP" property="delTime" />
        <result column="del_user_id" jdbcType="VARCHAR" property="delUserId" />
        <result column="del_user_name" jdbcType="VARCHAR" property="delUserName" />
        <result column="s_road_group_way" jdbcType="VARCHAR" property="sRoadGroupWay" />
    </resultMap>


    <sql id="Base_Column_List">
    s_road_id,s_road_name,status,create_time,create_user_id,create_user_name,del_time,del_user_id,del_user_name,s_road_group_way
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoad">
        delete from s_road where s_road_id = #{ sRoadId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road where s_road_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.task.SRoad">
        insert into s_road (<include refid="Base_Column_List" />) values
        (#{sRoadId},#{sRoadName},#{status},#{createTime},#{createUserId},#{createUserName},#{delTime},#{delUserId},#{delUserName},#{sRoadGroupWay})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into s_road (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.sRoadId},#{item.sRoadName},#{item.status},#{item.createTime},#{item.createUserId},#{item.createUserName},#{item.delTime},#{item.delUserId},#{item.delUserName},#{item.sRoadGroupWay})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoad">
        update s_road
        <set>
             <if test="sRoadName != null">
                s_road_name = #{ sRoadName },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
             <if test="createUserId != null">
                create_user_id = #{ createUserId },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="delTime != null">
                del_time = #{ delTime },
             </if>
             <if test="delUserId != null">
                del_user_id = #{ delUserId },
             </if>
             <if test="delUserName != null">
                del_user_name = #{ delUserName },
             </if>
             <if test="sRoadGroupWay != null">
                s_road_group_way = #{ sRoadGroupWay },
             </if>
        </set>
        where s_road_id = #{ sRoadId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from s_road where s_road_id = #{ sRoadId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.task.SRoad" resultMap="BaseResultMap">
        SELECT s_road_id,s_road_name,status,create_time,create_user_id,create_user_name,del_time,del_user_id,del_user_name,s_road_group_way
        from s_road
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="getPageRoadListBySRoad" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.beans.task.SRoad">
        select
        <include refid="Base_Column_List"/>
        from s_road
        <where>
        status in('0','3')
            <if test="sRoadId != null and sRoadId != ''">
                and s_road_id = #{ sRoadId }
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="sRoadName != null and sRoadName != ''">
                and s_road_name like CONCAT('%',trim(#{sRoadName}),'%')
            </if>
        <if test="createUserId != null and createUserId != ''">
            and create_user_id  = #{createUserId}
        </if>
        </where>
order by create_time desc
    </select>

</mapper>