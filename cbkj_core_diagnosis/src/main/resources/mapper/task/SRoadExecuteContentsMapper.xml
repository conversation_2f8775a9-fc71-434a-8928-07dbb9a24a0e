<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.task.SRoadExecuteContentsMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.task.SRoadExecuteContents">
        <id column="road_execute_contents_id" jdbcType="INTEGER"  property="roadExecuteContentsId" />
        <result column="road_execute_id" jdbcType="INTEGER" property="roadExecuteId" />
        <result column="road_execute_event_type" jdbcType="VARCHAR" property="roadExecuteEventType" />
        <result column="road_execute_event_content_id" jdbcType="VARCHAR" property="roadExecuteEventContentId" />
        <result column="road_execute_event_content_name" jdbcType="VARCHAR" property="roadExecuteEventContentName" />
    </resultMap>


    <sql id="Base_Column_List">
    road_execute_contents_id,road_execute_id,road_execute_event_type,road_execute_event_content_id,road_execute_event_content_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadExecuteContents">
        delete from s_road_execute_contents where road_execute_contents_id = #{ roadExecuteContentsId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road_execute_contents where road_execute_contents_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.task.SRoadExecuteContents" useGeneratedKeys="true"  keyColumn="road_execute_contents_id" keyProperty="roadExecuteContentsId">
        insert into s_road_execute_contents (<include refid="Base_Column_List" />) values
        (#{roadExecuteContentsId},#{roadExecuteId},#{roadExecuteEventType},#{roadExecuteEventContentId},#{roadExecuteEventContentName})
    </insert>

    <insert id="insertList" parameterType="List" useGeneratedKeys="true"  keyColumn="road_execute_contents_id" keyProperty="roadExecuteContentsId">
        insert into s_road_execute_contents (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.roadExecuteContentsId},#{item.roadExecuteId},#{item.roadExecuteEventType},#{item.roadExecuteEventContentId},#{item.roadExecuteEventContentName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadExecuteContents">
        update s_road_execute_contents
        <set>
             <if test="roadExecuteId != null">
                road_execute_id = #{ roadExecuteId },
             </if>
             <if test="roadExecuteEventType != null">
                road_execute_event_type = #{ roadExecuteEventType },
             </if>
             <if test="roadExecuteEventContentId != null">
                road_execute_event_content_id = #{ roadExecuteEventContentId },
             </if>
            <if test="roadExecuteEventContentName != null">
                road_execute_event_content_name = #{ roadExecuteEventContentName },
            </if>
        </set>
        where road_execute_contents_id = #{ roadExecuteContentsId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from s_road_execute_contents where road_execute_contents_id = #{ roadExecuteContentsId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.task.SRoadExecuteContents" resultMap="BaseResultMap">
        SELECT road_execute_contents_id,road_execute_id,road_execute_event_type,road_execute_event_content_id,road_execute_event_content_name
        from s_road_execute_contents
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>