<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.task.SRoadConditionsMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.task.SRoadConditions">
        <id column="road_conditions_id" jdbcType="INTEGER"  property="roadConditionsId" />
        <result column="s_road_id" jdbcType="VARCHAR" property="sRoadId" />
        <result column="s_road_name" jdbcType="VARCHAR" property="sRoadName" />
        <result column="t_west_disease_id" jdbcType="VARCHAR" property="tWestDiseaseId" />
        <result column="t_west_disease_name" jdbcType="VARCHAR" property="tWestDiseaseName" />
        <result column="t_chinese_disease_name" jdbcType="VARCHAR" property="tChineseDiseaseName" />
        <result column="t_chinese_disease_id" jdbcType="VARCHAR" property="tChineseDiseaseId" />
        <result column="s_symptom_id" jdbcType="VARCHAR" property="sSymptomId" />
        <result column="s_symptom_name" jdbcType="VARCHAR" property="sSymptomName" />
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId" />
        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName" />
        <result column="sys_dept_id" jdbcType="VARCHAR" property="sysDeptId" />
        <result column="sys_dept_name" jdbcType="VARCHAR" property="sysDeptName" />
        <result column="record_type" jdbcType="VARCHAR" property="recordType" />
    </resultMap>


    <sql id="Base_Column_List">
    road_conditions_id,s_road_id,s_road_name,t_west_disease_id,t_west_disease_name,t_chinese_disease_name,
        t_chinese_disease_id,s_symptom_id,doctor_id,doctor_name,sys_dept_id,sys_dept_name,record_type,s_symptom_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadConditions">
        delete from s_road_conditions where road_conditions_id = #{ roadConditionsId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road_conditions where road_conditions_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.task.SRoadConditions" useGeneratedKeys="true"  keyColumn="road_conditions_id" keyProperty="roadConditionsId">
        insert into s_road_conditions (<include refid="Base_Column_List" />) values
        (#{roadConditionsId},#{sRoadId},#{sRoadName},#{tWestDiseaseId},#{tWestDiseaseName},#{tChineseDiseaseName},#{tChineseDiseaseId},
         #{sSymptomId},#{doctorId},#{doctorName},#{sysDeptId},#{sysDeptName},#{recordType},#{sSymptomName})
    </insert>

    <insert id="insertList" parameterType="List" useGeneratedKeys="true"  keyColumn="road_conditions_id" keyProperty="roadConditionsId">
        insert into s_road_conditions (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.roadConditionsId},#{item.sRoadId},#{item.sRoadName},#{item.tWestDiseaseId},#{item.tWestDiseaseName},#{item.tChineseDiseaseName},
             #{item.tChineseDiseaseId},#{item.sSymptomId},#{item.doctorId},#{item.doctorName},#{item.sysDeptId},#{item.sysDeptName},#{item.recordType},#{item.sSymptomName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadConditions">
        update s_road_conditions
        <set>
             <if test="sRoadId != null">
                s_road_id = #{ sRoadId },
             </if>
             <if test="sRoadName != null">
                s_road_name = #{ sRoadName },
             </if>
             <if test="tWestDiseaseId != null">
                t_west_disease_id = #{ tWestDiseaseId },
             </if>
             <if test="tWestDiseaseName != null">
                t_west_disease_name = #{ tWestDiseaseName },
             </if>
             <if test="tChineseDiseaseName != null">
                t_chinese_disease_name = #{ tChineseDiseaseName },
             </if>
             <if test="tChineseDiseaseId != null">
                t_chinese_disease_id = #{ tChineseDiseaseId },
             </if>
             <if test="sSymptomId != null">
                s_symptom_id = #{ sSymptomId },
             </if>
             <if test="doctorId != null">
                doctor_id = #{ doctorId },
             </if>
             <if test="doctorName != null">
                doctor_name = #{ doctorName },
             </if>
             <if test="sysDeptId != null">
                sys_dept_id = #{ sysDeptId },
             </if>
             <if test="sysDeptName != null">
                sys_dept_name = #{ sysDeptName },
             </if>
            <if test="recordType != null">
                record_type = #{recordType},
             </if>
            <if test="sSymptomName != null">
                s_symptom_name = #{sSymptomName},
             </if>
        </set>
        where road_conditions_id = #{ roadConditionsId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from s_road_conditions where road_conditions_id = #{ roadConditionsId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.task.SRoadConditions" resultMap="BaseResultMap">
        SELECT road_conditions_id,s_road_id,s_road_name,t_west_disease_id,t_west_disease_name,t_chinese_disease_name,t_chinese_disease_id,s_symptom_id,
               doctor_id,doctor_name,sys_dept_id,sys_dept_name,record_type,s_symptom_name
        from s_road_conditions
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="getAllListByObj" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.beans.task.SRoadConditions">

        select
        <include refid="Base_Column_List"/>
        from s_road_conditions where s_road_id = #{sRoadId}


    </select>

</mapper>