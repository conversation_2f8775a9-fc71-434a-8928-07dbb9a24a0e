<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayExpensesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayExpenses">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="app_id" property="appId"/>
        <result column="ins_id" property="insId"/>
        <result column="ins_code" property="insCode"/>
        <result column="ins_name" property="insName"/>
        <result column="dis_id" property="disId"/>
        <result column="dis_name" property="disName"/>
        <result column="dis_code" property="disCode"/>
        <result column="num" property="num"/>
        <result column="type" property="type"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, app_id, ins_id, ins_code, ins_name, dis_id, dis_name, dis_code, num, type
    </sql>
    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.beans.statistics.BlockFiveDetail">

    </resultMap>
    <select id="getBlockFiveList" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.beans.statistics.BlockFiveDetail">
        SELECT
        a.*
        FROM
        (SELECT
        SUM(sede.num) y,
   <!--     CASE
        WHEN type = 1 THEN '中药处方'
        WHEN type = 2 THEN '中成药'
        WHEN type = 3 THEN '现代医学检查检查'
        WHEN type = 4 THEN '中医适宜技术'
        ELSE '未知类型'
        END AS x
-->
        type as x
        FROM
        `statistics_erver_day_expenses` as sede
        <if test="userId != null and userId != ''">
            join sys_admin_info_dis_mapping as saidm on(saidm.user_id = #{userId} and saidm.dis_id = sede.dis_id)
        </if>
        <where>

            <if test="startDate != null and startDate != ''">
                and sede.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND sede.create_time &lt;= #{endDate}
            </if>
            <if test="insName != null and insName != ''">
                AND sede.ins_name = #{insName}
            </if>
            <if test="disCode != null and disCode != '' ">
                AND sede.dis_code =#{disCode}
            </if>

        </where>
        GROUP BY sede.type) a
        ORDER BY a.y desc
    </select>

</mapper>
