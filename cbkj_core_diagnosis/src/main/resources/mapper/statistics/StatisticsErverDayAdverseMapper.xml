<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayAdverseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayAdverse">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="app_id" property="appId" />
        <result column="ins_id" property="insId" />
        <result column="ins_code" property="insCode" />
        <result column="ins_name" property="insName" />
        <result column="dis_id" property="disId" />
        <result column="dis_name" property="disName" />
        <result column="dis_code" property="disCode" />
        <result column="num" property="num" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, app_id, ins_id, ins_code, ins_name, dis_id, dis_name, dis_code, num
    </sql>

    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.beans.statistics.BlockFourDetail">

    </resultMap>
    <select id="getBlockFourList" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo">
        SELECT
        a.*
        FROM
        (SELECT
        SUM(seda.num) y,
        seda.create_time AS x
        FROM
        `statistics_erver_day_adverse` seda
        <if test="userId != null and userId != ''">
            join sys_admin_info_dis_mapping as saidm on(saidm.user_id = #{userId} and saidm.dis_id = seda.dis_id)
        </if>
        <where>

            <if test="startDate != null and startDate != ''">
                and seda.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND seda.create_time &lt;= #{endDate}
            </if>
            <if test="insName != null and insName != ''">
                AND seda.ins_name = #{insName}
            </if>
            <if test="disCode != null and disCode != '' ">
                AND seda.dis_code =#{disCode}
            </if>

        </where>
        GROUP BY seda.create_time) a
        ORDER BY a.x asc
    </select>
    <select id="listJoinDic" resultType="com.cbkj.diagnosis.beans.business.TRecordEvent" parameterType="com.cbkj.diagnosis.beans.statistics.vo.ListJoinDic">
        select  a.dic_id, a.dic_code, a.dic_name from t_record_event as a join sys_dic as b
        where a.dic_id = b.dic_id and b.dic_code  = 'follow-up-event_unusual' and a.rec_id = #{recId}
    </select>

</mapper>
