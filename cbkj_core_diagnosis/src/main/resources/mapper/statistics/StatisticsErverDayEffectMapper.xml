<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayEffectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayEffect">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="app_id" property="appId" />
        <result column="ins_id" property="insId" />
        <result column="ins_code" property="insCode" />
        <result column="ins_name" property="insName" />
        <result column="dis_id" property="disId" />
        <result column="dis_name" property="disName" />
        <result column="dis_code" property="disCode" />
        <result column="num" property="num" />
        <result column="type" property="type" />
        <result column="dic_code" property="dicCode" />
        <result column="dic_id" property="dicId" />
        <result column="dic_name" property="dicName" />
        <result column="patient_id" property="patientId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, app_id, ins_id, ins_code, ins_name, dis_id, dis_name, dis_code, num, type,dic_code,dic_id,dic_name,patient_id
    </sql>

    <resultMap id="BaseResultMap3" type="com.cbkj.diagnosis.beans.statistics.BlockThreeDetail">

    </resultMap>
    <select id="getBlockThreeList" resultMap="BaseResultMap3" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo">
        SELECT
        a.*
        FROM
        (SELECT
        SUM(sede.num) y,
        CASE
        WHEN sede.type = 1 THEN '有效'
        WHEN sede.type = 2 THEN '显效'
        WHEN sede.type = 3 THEN '痊愈'
        WHEN sede.type = 4 THEN '无效'
        ELSE '未知类型'
        END AS x
        FROM
        `statistics_erver_day_effect` as sede
        <if test="userId != null and userId != ''">
            join sys_admin_info_dis_mapping as saidm on(saidm.user_id = #{userId} and saidm.dis_id = sede.dis_id)
        </if>
        <where>

            <if test="startDate != null and startDate != ''">
                and sede.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND sede.create_time &lt;= #{endDate}
            </if>
            <if test="insName != null and insName != ''">
                AND sede.ins_name = #{insName}
            </if>
            <if test="disCode != null and disCode != '' ">
                AND sede.dis_code =#{disCode}
            </if>

        </where>
        GROUP BY sede.type) a
        ORDER BY a.y desc
    </select>

    <select id="listJoinDic" resultType="com.cbkj.diagnosis.beans.business.TRecordEvent" parameterType="com.cbkj.diagnosis.beans.statistics.vo.ListJoinDic">
        select  a.dic_id, a.dic_code, a.dic_name from t_record_event as a join sys_dic as b
        where a.dic_id = b.dic_id and b.dic_id  in ('follow-up-event-effective','follow-up-event-ineffective','follow-up-event-recovery','follow-up-event-significant')
        and a.rec_id = #{recId}
    </select>
</mapper>
