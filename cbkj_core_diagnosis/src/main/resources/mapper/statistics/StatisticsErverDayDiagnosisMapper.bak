<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayDiagnosisMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDiagnosis">
        <id column="id" jdbcType="BIGINT"  property="id" />
        <result column="create_time" jdbcType="DATE" property="createTime" />
        <result column="num" jdbcType="INTEGER" property="num" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_id" jdbcType="VARCHAR" property="insId" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dis_id" jdbcType="VARCHAR" property="disId" />
        <result column="dis_name" jdbcType="VARCHAR" property="disName" />
        <result column="dis_code" jdbcType="VARCHAR" property="disCode" />
    </resultMap>


    <sql id="Base_Column_List">
        id,create_time,num,app_id,ins_code,ins_id,ins_name,dis_id,dis_name,dis_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDiagnosis">
        delete from statistics_erver_day_diagnosis where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from statistics_erver_day_diagnosis where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDiagnosis">
        insert into statistics_erver_day_diagnosis (<include refid="Base_Column_List" />) values
        (#{id},#{createTime},#{num},#{appId},#{insCode},#{insId},#{insName},#{disId},#{disName},#{disCode})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into statistics_erver_day_diagnosis (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.createTime},#{item.num},#{item.appId},#{item.insCode},#{item.insId},#{item.insName},#{item.disId},#{item.disName},#{item.disCode})
        </foreach>
    </insert>
    <insert id="insertStatisticsEverDayData" parameterType="com.cbkj.diagnosis.service.vo.PastSevenDaysRe">
        INSERT INTO statistics_erver_day_follow (
            create_time,
            num,
            ins_code,
            doctor_id,app_id,ins_name,ins_id,
            dis_id,dis_name,dis_code

        )

        SELECT
            #{startDate},
            COUNT(*) AS num,
            a.`ins_code`,
            a.doctor_id,
            a.app_id,
            a.ins_name,
            a.ins_id,
            dism.`dis_id`,
            dism.`dis_name`,
            dism.`dis_code`

        FROM
            `s_road_task_patients` a
                join t_record as b on(b.rec_id = a.rec_id)
                JOIN t_pre_diagnosis_dis_mapping AS dism ON(dism.`dia_id`=b.`dia_id`)
        WHERE
            b.create_date > #{startDate2}
          and b.create_date &lt;= #{endDate}
          and  b.form_type = '1'

        GROUP BY a.app_id,a.ins_code


    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDiagnosis">
        update statistics_erver_day_diagnosis
        <set>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
             <if test="num != null">
                num = #{ num },
             </if>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insId != null">
                ins_id = #{ insId },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
            <if test="disId != null">
                dis_id = #{ disId },
            </if>
            <if test="disName != null">
                dis_name = #{ disName },
            </if>
            <if test="disCode != null">
                dis_code = #{ disCode },
            </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from statistics_erver_day_diagnosis where id = #{ id }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDiagnosis" resultMap="BaseResultMap">
        SELECT id,create_time,num,app_id,ins_code,ins_id,ins_name
        from statistics_erver_day_diagnosis
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>