<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayHealthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth">
        <id column="id" property="id" />
        <result column="num" property="num" />
        <result column="create_time" property="createTime" />
        <result column="app_id" property="appId" />
        <result column="ins_code" property="insCode" />
        <result column="ins_id" property="insId" />
        <result column="ins_name" property="insName" />
        <result column="dis_id" property="disId" />
        <result column="dis_name" property="disName" />
        <result column="dis_code" property="disCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, num, create_time, app_id, ins_code, ins_id, ins_name, dis_id, dis_name, dis_code
    </sql>
    <resultMap id="BaseResultMap3" type="com.cbkj.diagnosis.beans.statistics.BlockOneDetail">

    </resultMap>
    <select id="getBlockOneList" resultMap="BaseResultMap3" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo">
        SELECT
        a.*
        FROM
        (SELECT
        SUM(sedh.num) y,sedh.create_time x
        FROM
        `statistics_erver_day_health` as sedh
        <if test="userId != null and userId != ''">
            join sys_admin_info_dis_mapping as saidm on(saidm.user_id = #{userId} and saidm.dis_id = sedh.dis_id)
        </if>
        <where>

            <if test="startDate != null and startDate != ''">
                and sedh.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND sedh.create_time &lt;= #{endDate}
            </if>
            <if test="insName != null and insName != ''">
                AND sedh.ins_name = #{insName}
            </if>
            <if test="disCode != null and disCode != '' ">
                AND sedh.dis_code =#{disCode}
            </if>

        </where>
        GROUP BY sedh.create_time) a
        ORDER BY a.x ASC
    </select>
    <select id="getBlockTwoList" resultType="com.cbkj.diagnosis.beans.statistics.BlockTwoDetail" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo">
        SELECT
        a.*
        FROM
        (SELECT
        SUM(num) y,ins_name x
        FROM
        `statistics_erver_day_health`
        <where>

            <if test="startDate != null and startDate != ''">
                and create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND create_time &lt;= #{endDate}
            </if>
            <if test="insName != null and insName != ''">
                AND ins_name = #{insName}
            </if>
            <if test="disCode != null and disCode != '' ">
                AND dis_code =#{disCode}
            </if>

        </where>
        GROUP BY ins_name) a
        ORDER BY a.y desc
    </select>
    <select id="staticsHistoryHealth"
            resultType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData">
        SELECT COUNT(*) AS num,a.`app_id` appId,a.`ins_code` insCode,a.`insert_date` as createTime,b.dis_id disId,b.dis_name disName,b.dis_code disCode FROM `s_road_task_patients` AS a
        JOIN t_propaganda_edu_dis_mapping AS b ON(a.road_execute_event_content_id = b.t_propaganda_edu_id)
        WHERE a.`road_execute_event_type` = '1' AND a.`task_excute_time` >= #{startDate} AND a.`task_excute_time` <![CDATA[ <= ]]>  #{endDate}
        GROUP BY a.`app_id`,a.`ins_code`,a.insert_date,b.dis_id
    </select>

</mapper>
