<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayDisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis">
        <id column="id" property="id" />
        <result column="dis_id" property="disId" />
        <result column="dis_code" property="disCode" />
        <result column="dis_name" property="disName" />
        <result column="follow_num" property="followNum" />
        <result column="diagnosis_num" property="diagnosisNum" />
        <result column="total_num" property="totalNum" />
        <result column="app_id" property="appId" />
        <result column="ins_id" property="insId" />
        <result column="ins_code" property="insCode" />
        <result column="ins_name" property="insName" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dis_id, dis_code, dis_name, follow_num, diagnosis_num, total_num, app_id, ins_id, ins_code, ins_name, create_time
    </sql>
    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.beans.statistics.BlockDis">
        <result column="diagnosis_num" property="diaNum" />
        <result column="follow_num" property="flowNum" />
        <result column="records_num" property="recordsNum" />
        <result column="dis_name" property="name" />
        <result column="heath_num" property="healthNum" />
    </resultMap>

    <select id="getBlockDisList" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo">

        SELECT
        a.*
        FROM
        (SELECT
        SUM(diagnosis_num) diagnosis_num,
        SUM(follow_num) follow_num,
        SUM(records_num) records_num,
     <!--   SUM(total_num) total_num, -->
        IFNULL(
        (
        SELECT SUM(num) FROM `statistics_erver_day_health` AS a WHERE a.dis_code = b.dis_code
        <if test="startDate != null and startDate != ''">
            and a.create_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND a.create_time &lt;= #{endDate}
        </if>
        <if test="insName != null and insName != ''">
            AND a.ins_name = #{insName}
        </if>
        <if test="disCode != null and disCode != '' ">
            AND a.dis_code =#{disCode}
        </if>
        )
        ,0 ) AS heath_num,
        ANY_VALUE(b.dis_name) as dis_name,b.dis_code
        FROM
        `statistics_erver_day_dis` as b
        <if test="userId != null and userId != ''">
        join sys_admin_info_dis_mapping as saidm on(saidm.user_id = #{userId} and saidm.dis_id = b.dis_id)
        </if>
        <where>
            <if test="startDate != null and startDate != ''">
                and b.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND b.create_time &lt;= #{endDate}
            </if>
            <if test="insName != null and insName != ''">
                AND b.ins_name = #{insName}
            </if>
            <if test="disCode != null and disCode != '' ">
                AND b.dis_code =#{disCode}
            </if>
        </where>
        GROUP BY b.dis_code) a
    <!--    ORDER BY a.total_num DESC -->

    </select>


    <resultMap id="BaseResultMap3" type="com.cbkj.diagnosis.beans.statistics.BlockOneDetail">

    </resultMap>
    <select id="getBlockOneList" resultMap="BaseResultMap3" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo">


        SELECT
        a.*
        FROM
        (SELECT
        SUM(diagnosis_num) y,sedd.create_time x
        FROM
        `statistics_erver_day_dis` as sedd
        <if test="userId != null and userId != ''">
            join sys_admin_info_dis_mapping as saidm on(saidm.user_id = #{userId} and saidm.dis_id = sedd.dis_id)
        </if>
        <where>

            <if test="startDate != null and startDate != ''">
                and sedd.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND sedd.create_time &lt;= #{endDate}
            </if>
            <if test="insName != null and insName != ''">
                AND sedd.ins_name = #{insName}
            </if>
            <if test="disCode != null and disCode != '' ">
                AND sedd.dis_code =#{disCode}
            </if>

        </where>
        GROUP BY create_time) a
        ORDER BY a.x ASC
    </select>
    <select id="getBlockOneList2" resultMap="BaseResultMap3" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo">
        SELECT
        a.*
        FROM
        (SELECT
        SUM(follow_num) y,sedd.create_time x
        FROM
        `statistics_erver_day_dis` as sedd
        <if test="userId != null and userId != ''">
            join sys_admin_info_dis_mapping as saidm on(saidm.user_id = #{userId} and saidm.dis_id = sedd.dis_id)
        </if>
        <where>

            <if test="startDate != null and startDate != ''">
                and sedd.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND sedd.create_time &lt;= #{endDate}
            </if>
            <if test="insName != null and insName != ''">
                AND sedd.ins_name = #{insName}
            </if>
            <if test="disCode != null and disCode != '' ">
                AND sedd.dis_code =#{disCode}
            </if>

        </where>
        GROUP BY sedd.create_time) a
        ORDER BY a.x ASC
    </select>

    <resultMap id="BaseResultMap4" type="com.cbkj.diagnosis.beans.statistics.BlockTwoDetail">

    </resultMap>


    <select id="getBlockTwoList" resultMap="BaseResultMap4" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo">
        SELECT
        a.*
        FROM
        (SELECT
        SUM(sedd.diagnosis_num) y,sedd.ins_name x
        FROM
        `statistics_erver_day_dis` as sedd
        <if test="userId != null and userId != ''">
            join sys_admin_info_dis_mapping as saidm on(saidm.user_id = #{userId} and saidm.dis_id = sedd.dis_id)
        </if>
        <where>

            <if test="startDate != null and startDate != ''">
                and sedd.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND sedd.create_time &lt;= #{endDate}
            </if>
            <if test="insName != null and insName != ''">
                AND sedd.ins_name = #{insName}
            </if>
            <if test="disCode != null and disCode != '' ">
                AND sedd.dis_code =#{disCode}
            </if>

        </where>
        GROUP BY sedd.ins_name) a
        ORDER BY a.y desc
    </select>

    <select id="getBlockTwoListFlow" resultMap="BaseResultMap4" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StatisticsVo">
        SELECT
        a.*
        FROM
        (SELECT
        SUM(follow_num) y,ins_name x
        FROM
        `statistics_erver_day_dis`
        <where>

            <if test="startDate != null and startDate != ''">
                and create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND create_time &lt;= #{endDate}
            </if>
            <if test="insName != null and insName != ''">
                AND ins_name = #{insName}
            </if>
            <if test="disCode != null and disCode != '' ">
                AND dis_code =#{disCode}
            </if>

        </where>
        GROUP BY ins_name) a
        ORDER BY a.y desc
    </select>
    <select id="staticsHistoryYZ" resultType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis" parameterType="com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData">
        SELECT c.`id`,COUNT(*) AS currentNum, c.follow_num  followNum,a.`insert_date` as createTime ,b.dis_id disId,b.dis_name disName , b.dis_code disCode FROM `t_record` AS a
        JOIN t_pre_diagnosis_dis_mapping AS b ON(a.`dia_id` = b.`dia_id` and b.dis_type = '1') LEFT JOIN `statistics_erver_day_dis` AS c ON(c.`dis_code` = b.`dis_code` AND c.`create_time` = a.`insert_date`)
        WHERE a.`form_type` = '1' AND a.`create_date` >= #{startDate} AND a.`create_date`  <![CDATA[ <= ]]>  #{endDate}
        GROUP BY a.`insert_date`,b.`dis_id`
    </select>
    <select id="staticsHistorySF" resultType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayDis"  parameterType="com.cbkj.diagnosis.beans.statistics.vo.StaticsHistoryData">
        SELECT b.`id`,COUNT(*) AS currentNum, b.diagnosis_num as diagnosisNum ,a.`insert_date` as createTime,aa.dis_id disId,aa.dis_name disName , aa.dis_code disCode FROM `t_record` AS a
join t_pre_diagnosis_dis_mapping as aa on aa.dia_id = a.dia_id and aa.dis_type = '1'
        LEFT JOIN statistics_erver_day_dis AS b ON(b.`create_time` = a.`insert_date`)
        WHERE a.`form_type` = '2' AND a.`create_date` >= #{startDate} AND a.`create_date` <![CDATA[ <= ]]>  #{endDate}
        GROUP BY a.`insert_date`,aa.dis_id
    </select>

</mapper>
