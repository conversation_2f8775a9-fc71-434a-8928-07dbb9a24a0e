<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayFollowMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayFollow">
        <id column="follow_up_id" jdbcType="BIGINT"  property="followUpId" />
        <result column="create_time" jdbcType="DATE" property="createTime" />
        <result column="num" jdbcType="INTEGER" property="num" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId" />

        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="dis_id" jdbcType="VARCHAR" property="disId" />
        <result column="dis_name" jdbcType="VARCHAR" property="disName" />
        <result column="dis_code" jdbcType="VARCHAR" property="disCode" />
    </resultMap>


    <sql id="Base_Column_List">
        follow_up_id,create_time,num,ins_code,doctor_id,app_id,ins_name,type,dis_id,dis_name,dis_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayFollow">
        delete from statistics_erver_day_follow where follow_up_id = #{ followUpId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from statistics_erver_day_follow where follow_up_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>


    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayFollow">
        insert into statistics_erver_day_follow (<include refid="Base_Column_List" />) values
        (#{followUpId},#{createTime},#{num},#{insCode},#{doctorId},#{appId},#{insName},#{type},#{disId},#{disName},#{disCode})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into statistics_erver_day_follow (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.followUpId},#{item.createTime},#{item.num},#{item.insCode},#{item.doctorId},#{item.appId},#{item.insName},#{item.type},#{item.disId},#{item.disName},#{item.disCode})
        </foreach>
    </insert>
    <insert id="insertStatisticsEverDayFollowData" parameterType="com.cbkj.diagnosis.beans.statistics.PastSevenDaysRe">
        INSERT INTO statistics_erver_day_follow (
            create_time,
            num,
            ins_code,
            doctor_id,app_id,ins_name,type,
            dis_id,dis_name,dis_code

        )

        SELECT
            #{startDate},
            COUNT(*) AS num,
            b.`ins_code`,
            a.doctor_id,
            b.app_id,
            b.ins_name,
            1,
            dism.`dis_id`,
            dism.`dis_name`,
            dism.`dis_code`
        FROM
            `s_road_task_patients_phone` a
                JOIN `medical_records` b
                     ON (a.records_id = b.records_id)
                JOIN t_pre_diagnosis_dis_mapping AS dism ON(dism.`dia_id`=b.`dia_id`)
        WHERE a.sui_fang_time = #{startDate}
          AND a.phone_status = 8
        GROUP BY a.doctor_id,
                 b.`ins_code`


    </insert>
    <insert id="insertStatisticsEverDayFollowDataPage" parameterType="com.cbkj.diagnosis.beans.statistics.PastSevenDaysRe">
        INSERT INTO statistics_erver_day_follow (
            create_time,
            num,
            ins_code,
            doctor_id,app_id,ins_name,type,dis_id,dis_name,dis_code


        )

        SELECT
            #{startDate},
            COUNT(*) AS num,
            a.`ins_code`,
            a.doctor_id,
            a.app_id,
            a.ins_name,
            2,
            dism.`dis_id`,
            dism.`dis_name`,
            dism.`dis_code`
        FROM
            `s_road_task_patients` a
join t_record as b on(b.rec_id = a.rec_id)
JOIN t_pre_diagnosis_dis_mapping AS dism ON(dism.`dia_id`=b.`dia_id`)
        WHERE
            b.create_date > #{startDate2}
          and b.create_date &lt;= #{endDate}
           and  b.form_type = '2'

        GROUP BY a.app_id,a.ins_code,dism.`dis_id`


    </insert>


    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayFollow">
        update statistics_erver_day_follow
        <set>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
             <if test="num != null">
                num = #{ num },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
        </set>
        where follow_up_id = #{ followUpId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from statistics_erver_day_follow where follow_up_id = #{ followUpId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayFollow" resultMap="BaseResultMap">
        SELECT follow_up_id,create_time,num,ins_code
        from statistics_erver_day_follow
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="getPastSevenDays" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.beans.statistics.PastSevenDaysRe">
        select

        a.create_time,
        a.num
        from statistics_erver_day_follow a
        where a.create_time > #{startDate}
        and a.create_time &lt;= #{endDate}
        <if test="doctorId != null and doctorId != ''">
            and doctor_id=#{doctorId}
        </if>
        <if test="insCode != null and insCode != ''">
            and ins_code=#{insCode}
        </if>
    </select>
    <select id="getYesterDayNumber" resultType="java.lang.Long" parameterType="com.cbkj.diagnosis.beans.statistics.PastSevenDaysRe">

        select

        sum( a.num )
        from statistics_erver_day_follow a
        where a.create_time >= #{startDate}
        and a.create_time &lt;= #{endDate}
          and a.type = 1
        <if test="doctorId != null and doctorId != ''">
            and doctor_id=#{doctorId}
        </if>
        <if test="insCode != null and insCode != ''">
            and ins_code=#{insCode}
        </if>

    </select>

</mapper>