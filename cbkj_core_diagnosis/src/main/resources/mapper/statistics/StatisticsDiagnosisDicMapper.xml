<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.statistics.StatisticsDiagnosisDicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.statistics.StatisticsDiagnosisDic">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="app_id" property="appId" />
        <result column="ins_id" property="insId" />
        <result column="ins_code" property="insCode" />
        <result column="ins_name" property="insName" />
        <result column="dis_id" property="disId" />
        <result column="dis_code" property="disCode" />
        <result column="dis_name" property="disName" />
        <result column="num" property="num" />
        <result column="dic_id" property="dicId" />
        <result column="dic_name" property="dicName" />
        <result column="dic_code" property="dicCode" />
        <result column="patient_id" property="patientId" />
        <result column="insert_time" property="insertTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, app_id, ins_id, ins_code, ins_name, dis_id, dis_code, dis_name, num, dic_id, dic_name, dic_code, patient_id,insert_time
    </sql>

</mapper>
