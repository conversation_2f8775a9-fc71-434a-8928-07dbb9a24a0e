<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.statistics.StatisticsErverDayHealthMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth">
        <id column="id" jdbcType="BIGINT"  property="id" />
        <result column="num" jdbcType="INTEGER" property="num" />
        <result column="create_time" jdbcType="DATE" property="createTime" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_id" jdbcType="VARCHAR" property="insId" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dis_id" jdbcType="VARCHAR" property="disId" />
        <result column="dis_name" jdbcType="VARCHAR" property="disName" />
        <result column="dis_code" jdbcType="VARCHAR" property="disCode" />

    </resultMap>


    <sql id="Base_Column_List">
    id,num,create_time,app_id,ins_code,ins_id,ins_name,dis_id,dis_name,dis_code

    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth">
        delete from statistics_erver_day_health where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from statistics_erver_day_health where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth">
        insert into statistics_erver_day_health (<include refid="Base_Column_List" />) values
        (#{id},#{num},#{createTime},#{appId},#{insCode},#{insId},#{insName},#{disId},#{disName},#{disCode})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into statistics_erver_day_health (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.num},#{item.createTime},#{item.appId},#{item.insCode},#{item.insId},#{item.insName},#{item.disId},#{item.disName},#{item.disCode})
        </foreach>
    </insert>

    <insert id="insertStatisticsEverDayData"  parameterType="com.cbkj.diagnosis.service.vo.PastSevenDaysRe">
        INSERT INTO statistics_erver_day_follow (
            create_time,
            num,
            ins_code,
            doctor_id,app_id,ins_name,ins_id

        )

        SELECT
            #{startDate},
            COUNT(*) AS num,
            a.`ins_code`,
            a.doctor_id,
            a.app_id,
            a.ins_name,
            a.ins_id
        FROM
            `s_road_task_patients` a
        WHERE
            a.task_excute_time > #{startDate2}
          AND a.task_excute_time &lt;= #{endDate}
          AND  a.road_execute_event_type = '1'
        GROUP BY a.app_id,a.ins_code
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth">
        update statistics_erver_day_health
        <set>
             <if test="num != null">
                num = #{ num },
             </if>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insId != null">
                ins_id = #{ insId },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from statistics_erver_day_health where id = #{ id }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.statistics.StatisticsErverDayHealth" resultMap="BaseResultMap">
        SELECT id,num,create_time,app_id,ins_code,ins_id,ins_name
        from statistics_erver_day_health
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>