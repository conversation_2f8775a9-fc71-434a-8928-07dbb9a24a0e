<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TSysParamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TSysParam">
        <id column="PAR_ID" property="parId" />
        <result column="PAR_CODE" property="parCode" />
        <result column="PAR_NAME" property="parName" />
        <result column="PAR_VALUES" property="parValues" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_USER" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="status" property="status" />
        <result column="param_desc" property="paramDesc" />
        <result column="sort" property="sort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        PAR_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name, status, param_desc, sort
    </sql>

</mapper>
