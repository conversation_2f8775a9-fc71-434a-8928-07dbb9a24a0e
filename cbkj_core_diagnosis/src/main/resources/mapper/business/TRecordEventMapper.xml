<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TRecordEventMapper">

    <!-- 通用查询映射结果 -->

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TRecordEvent">
        <result column="rec_id" property="recId" />
        <result column="dic_id" property="dicId" />
        <result column="dic_code" property="dicCode" />
        <result column="dic_name" property="dicName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        rec_id, dic_id, dic_code, dic_name
    </sql>
    <select id="listJoinDic" resultType="com.cbkj.diagnosis.beans.business.TRecordEvent" parameterType="com.cbkj.diagnosis.beans.statistics.vo.ListJoinDic">
        select a.dic_id, a.dic_code, a.dic_name
        from t_record_event as a
                 join sys_dic as b
        where a.dic_id = b.dic_id
     <!--     and b.dic_id not in ('follow-up-event-archive-closure') -->
          and a.rec_id = #{recId}
    </select>

</mapper>
