<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionRangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestionRange">
        <id column="question_range_id" property="questionRangeId" />
        <result column="question_id" property="questionId" />
        <result column="question_range_title" property="questionRangeTitle" />
        <result column="question_range_min" property="questionRangeMin" />
        <result column="question_range_max" property="questionRangeMax" />
        <result column="question_range_decimal" property="questionRangeDecimal" />
        <result column="question_range_default" property="questionRangeDefault" />
        <result column="question_range_unit" property="questionRangeUnit" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        question_range_id, question_id, question_range_title, question_range_min, question_range_max, question_range_decimal, question_range_default, question_range_unit
    </sql>

</mapper>
