<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPreDiagnosisSkipMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisSkip">
        <id column="skip_id" jdbcType="INTEGER"  property="skipId" />
        <result column="option_id" jdbcType="INTEGER" property="optionId" />
        <result column="option_name" jdbcType="VARCHAR" property="optionName" />
        <result column="skip_question_id" jdbcType="INTEGER" property="skipQuestionId" />
    </resultMap>


    <sql id="Base_Column_List">
    skip_id,option_id,option_name,skip_question_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisSkip">
        delete from t_pre_diagnosis_skip where skip_id = #{ skipId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_pre_diagnosis_skip where skip_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisSkip" useGeneratedKeys="true" keyColumn="skip_id" keyProperty="skipId">
        insert into t_pre_diagnosis_skip (<include refid="Base_Column_List" />) values
        (#{skipId},#{optionId},#{optionName},#{skipQuestionId})
    </insert>

    <insert id="insertList" parameterType="List"  useGeneratedKeys="true" keyColumn="skip_id" keyProperty="skipId">
        insert into t_pre_diagnosis_skip (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.skipId},#{item.optionId},#{item.optionName},#{item.skipQuestionId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisSkip">
        update t_pre_diagnosis_skip
        <set>
             <if test="optionId != null">
                option_id = #{ optionId },
             </if>
             <if test="optionName != null">
                option_name = #{ optionName },
             </if>
             <if test="skipQuestionId != null">
                skip_question_id = #{ skipQuestionId },
             </if>
        </set>
        where skip_id = #{ skipId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_pre_diagnosis_skip where skip_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisSkip" resultMap="BaseResultMap">
        SELECT skip_id,option_id,option_name,skip_question_id
        from t_pre_diagnosis_skip
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>