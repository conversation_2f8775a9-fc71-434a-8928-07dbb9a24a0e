<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPreDiagnosisChildMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisChild">
        <id column="option_id" jdbcType="INTEGER"  property="optionId" />
        <result column="option_name" jdbcType="VARCHAR" property="optionName" />
        <result column="dia_id" jdbcType="VARCHAR" property="diaId" />
        <result column="child_question_id" jdbcType="INTEGER" property="childQuestionId" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
        <result column="master_question_id" jdbcType="INTEGER" property="masterQuestionId" />
    </resultMap>


    <sql id="Base_Column_List">
    option_id,option_name,child_question_id,sort,master_question_id,dia_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisChild">
        delete from t_pre_diagnosis_child where option_id = #{ optionId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_pre_diagnosis_child where option_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisChild">
        insert into t_pre_diagnosis_child (<include refid="Base_Column_List" />) values
        (#{optionId},#{optionName},#{childQuestionId},#{sort},#{masterQuestionId},#{diaId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_pre_diagnosis_child (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.optionId},#{item.optionName},#{item.childQuestionId},#{item.sort},#{item.masterQuestionId},#{item.diaId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisChild">
        update t_pre_diagnosis_child
        <set>
             <if test="optionName != null">
                option_name = #{ optionName },
             </if>
             <if test="childQuestionId != null">
                child_question_id = #{ childQuestionId },
             </if>
             <if test="sort != null">
                sort = #{ sort },
             </if>
        </set>
        where option_id = #{ optionId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_pre_diagnosis_child where option_id = #{ optionId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisChild" resultMap="BaseResultMap">
        SELECT option_id,option_name,child_question_id,sort,master_question_id,dia_id
        from t_pre_diagnosis_child
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>