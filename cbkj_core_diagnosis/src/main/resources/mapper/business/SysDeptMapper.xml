<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.SysDeptMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.SysDept">
        <id column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_id" jdbcType="VARCHAR" property="insId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="ins_name" jdbcType="VARCHAR" property="insName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
    </resultMap>


    <sql id="Base_Column_List">
        dept_id,dept_code,dept_name,app_id,ins_id,ins_code,ins_name,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SysDept">
        delete
        from sys_dept
        where dept_id = #{ deptId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_dept where dept_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.cbkj.diagnosis.beans.business.SysDept">
        insert into sys_dept (<include refid="Base_Column_List"/>) values
        (#{deptId},#{deptCode},#{deptName},#{appId},#{insId},#{insCode},#{insName},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_dept (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.deptId},#{item.deptCode},#{item.deptName},#{item.appId},#{item.insId},#{item.insCode},#{item.insName},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SysDept">
        update sys_dept
        <set>
            <if test="deptCode != null">
                dept_code = #{ deptCode },
            </if>
            <if test="deptName != null">
                dept_name = #{ deptName },
            </if>
            <if test="appId != null">
                app_id = #{ appId },
            </if>
            <if test="insId != null">
                ins_id = #{ insId },
            </if>
            <if test="insCode != null">
                ins_code = #{ insCode },
            </if>
            <if test="insName != null">
                ins_name = #{ insName },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
        </set>
        where dept_id = #{ deptId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from sys_dept where dept_id = #{ deptId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.SysDept" resultMap="BaseResultMap">
        SELECT dept_id,dept_code,dept_name,app_id,ins_id,ins_code,ins_name,status
        from sys_dept
        <where>
            status = '0'
            <if test=" deptName != null and deptName!='' ">
                and dept_name like CONCAT('%',trim(#{deptName}),'%')
            </if>

        </where>
    </select>

    <select id="selectOneByObj" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.beans.business.SysDept">
        select
        <include refid="Base_Column_List"/>
        from sys_dept
        <where>
            <if test="appId != null ">
                and app_id = #{appId}
            </if>
            <if test="insCode != null">
                and ins_code = #{insCode}
            </if>
            <if test="deptCode != null">
                and dept_code = #{deptCode}
            </if>
        </where>
    </select>

</mapper>