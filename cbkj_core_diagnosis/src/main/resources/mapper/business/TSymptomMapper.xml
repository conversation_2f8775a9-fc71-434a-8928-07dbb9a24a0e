<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TSymptomMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TSymptom">
        <id column="symptom_id" jdbcType="VARCHAR" property="symptomId"/>
        <result column="symptom_name" jdbcType="VARCHAR" property="symptomName"/>
        <result column="symptom_code" jdbcType="VARCHAR" property="symptomCode"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="del_date" jdbcType="TIMESTAMP" property="delDate"/>
        <result column="del_user" jdbcType="VARCHAR" property="delUser"/>
        <result column="del_username" jdbcType="VARCHAR" property="delUsername"/>
        <result column="sort" jdbcType="VARCHAR" property="sort"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="symptom_class_id" jdbcType="VARCHAR" property="symptomClassId"/>
        <result column="symptom_class_name" jdbcType="VARCHAR" property="symptomClassName"/>
    </resultMap>


    <sql id="Base_Column_List">
        symptom_id,symptom_name,symptom_code,create_date,create_user,create_username,update_date,update_user,update_username,del_date,del_user,del_username,sort,status
        ,symptom_class_id,symptom_class_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TSymptom">
        delete
        from t_symptom
        where symptom_id = #{ symptomId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_symptom where symptom_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.cbkj.diagnosis.beans.business.TSymptom">
        insert into t_symptom (<include refid="Base_Column_List"/>) values
        (#{symptomId},#{symptomName},#{symptomCode},#{createDate},#{createUser},#{createUsername},#{updateDate},#{updateUser},#{updateUsername},
        #{delDate},#{delUser},#{delUsername},#{sort},#{status},#{symptomClassId},#{symptomClassName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_symptom (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.symptomId},#{item.symptomName},#{item.symptomCode},#{item.createDate},#{item.createUser},#{item.createUsername},#{item.updateDate},#{item.updateUser},
            #{item.updateUsername},#{item.delDate},#{item.delUser},#{item.delUsername},#{item.sort},#{item.status},
            #{item.symptomClassId},#{item.symptomClassName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TSymptom">
        update t_symptom
        <set>
            <if test="symptomName != null">
                symptom_name = #{ symptomName },
            </if>
            <if test="symptomCode != null">
                symptom_code = #{ symptomCode },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
            <if test="createUser != null">
                create_user = #{ createUser },
            </if>
            <if test="createUsername != null">
                create_username = #{ createUsername },
            </if>
            <if test="updateDate != null">
                update_date = #{ updateDate },
            </if>
            <if test="updateUser != null">
                update_user = #{ updateUser },
            </if>
            <if test="updateUsername != null">
                update_username = #{ updateUsername },
            </if>
            <if test="delDate != null">
                del_date = #{ delDate },
            </if>
            <if test="delUser != null">
                del_user = #{ delUser },
            </if>
            <if test="delUsername != null">
                del_username = #{ delUsername },
            </if>
            <if test="sort != null">
                sort = #{ sort },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="symptomClassId != null">
                symptom_class_id = #{symptomClassId},
            </if>
            <if test="symptomClassName != null">
                symptom_class_name = #{symptomClassName},
            </if>
        </set>
        where symptom_id = #{ symptomId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_symptom where symptom_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.TSymptom" resultMap="BaseResultMap">
        SELECT
        symptom_id,symptom_name,symptom_code,create_date,create_user,create_username,update_date,update_user,update_username,del_date,del_user,del_username,sort,status,
        symptom_class_id,symptom_class_name
        from t_symptom
        <where>
            <if test=" symptomName != null and symptomName!='' ">
                and symptom_name like CONCAT('%',trim(#{symptomName}),'%')
            </if>
            <if test=" symptomCode != null and symptomCode!='' ">
                and symptom_code like CONCAT('%',trim(#{symptomCode}),'%')
            </if>
        </where>
    </select>
    <select id="getPageListByObjBySelf" resultMap="BaseResultMap"
            parameterType="com.cbkj.diagnosis.service.webapi.business.vo.WebTSymptomReVo">
        SELECT symptom_id,symptom_name,symptom_code,sort,symptom_class_id,symptom_class_name
        from t_symptom
        <where>
            <if test=" key != null and key!='' ">
                and
                (
                symptom_name like CONCAT('%',trim(#{key}),'%')
                or
                symptom_code like CONCAT('%',trim(#{key}),'%')
                )
            </if>
        </where>
        order by sort
    </select>

    <select id="getSymptomPageList" parameterType="com.cbkj.diagnosis.service.webapi.business.vo.GetSymptomPageListRe"
            resultMap="BaseResultMap">
        SELECT a.symptom_id,a.symptom_name,a.symptom_code,a.sort,a.symptom_class_id,a.symptom_class_name
        from t_symptom a left join t_symptom_class b on (a.symptom_class_id = b.symptom_class_id)
        <where>
              a.status='0'
            <if test=" symptomName != null and symptomName!='' ">
                and
                (
                a.symptom_name like CONCAT('%',trim(#{symptomName}),'%')

                )
            </if>
            <if test="symptomClassId != null and symptomClassId !=''">
                and b.symptom_class_id = #{symptomClassId}
            </if>
        </where>
        order by a.sort

    </select>
    <resultMap id="getSymptomClassPageList" type="com.cbkj.diagnosis.beans.business.TSymptomClass">
        <id column="symptom_class_id" jdbcType="INTEGER" property="symptomClassId"/>
        <result column="symptom_class_name" jdbcType="VARCHAR" property="symptomClassName"/>
        <result column="class_hierarchy" jdbcType="INTEGER" property="classHierarchy"/>
        <result column="parent_class_id" jdbcType="INTEGER" property="parentClassId"/>
    </resultMap>
    <select id="getSymptomClassPageList" resultMap="getSymptomClassPageList" parameterType="Integer">
        select *
        from t_symptom_class where status = 0 and class_hierarchy = #{classHierarchy}



    </select>

</mapper>