<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.SysInsMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.SysIns">
        <id column="ins_id" jdbcType="VARCHAR"  property="insId" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
    </resultMap>


    <sql id="Base_Column_List">
    ins_id,ins_name,ins_code,status,app_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SysIns">
        delete from sys_ins where ins_id = #{ insId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_ins where ins_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.SysIns">
        insert into sys_ins (<include refid="Base_Column_List" />) values
        (#{insId},#{insName},#{insCode},#{status} ,#{appId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_ins (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.insId},#{item.insName},#{item.insCode},#{item.status},#{item.appId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SysIns">
        update sys_ins
        <set>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
        </set>
        where ins_id = #{ insId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_ins where ins_id = #{ insId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.SysIns" resultMap="BaseResultMap">
        SELECT ins_id,ins_name,ins_code,status,app_id
        from sys_ins
        <where>
        status = '0'
            <if test=" insName != null and insName!='' ">
                and ins_name like CONCAT('%',trim(#{insName}),'%')
            </if>
        </where>
    </select>
    <select id="getCountByIns" parameterType="com.cbkj.diagnosis.beans.business.SysIns" resultMap="BaseResultMap">
select <include refid="Base_Column_List" /> from sys_ins where
                                 status ='0'
        <if test="insCode != null">
           and  ins_code = #{ insCode }
        </if>
        <if test="appId != null">
            and  app_id = #{appId}
        </if>
    </select>

</mapper>