<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TDiseaseMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TDisease">
        <id column="dis_id" jdbcType="VARCHAR"  property="disId" />
        <result column="dis_name" jdbcType="VARCHAR" property="disName" />
        <result column="dis_code" jdbcType="VARCHAR" property="disCode" />
        <result column="dis_type" jdbcType="VARCHAR" property="disType" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername" />
        <result column="del_date" jdbcType="TIMESTAMP" property="delDate" />
        <result column="del_user" jdbcType="VARCHAR" property="delUser" />
        <result column="del_username" jdbcType="VARCHAR" property="delUsername" />
        <result column="sort" jdbcType="VARCHAR" property="sort" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    dis_id,dis_name,dis_code,dis_type,create_date,create_user,create_username,update_date,update_user,update_username,del_date,del_user,del_username,sort,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TDisease">
        delete from t_disease where dis_id = #{ disId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_disease where dis_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.TDisease">
        insert into t_disease (<include refid="Base_Column_List" />) values
        (#{disId},#{disName},#{disCode},#{disType},#{createDate},#{createUser},#{createUsername},#{updateDate},#{updateUser},#{updateUsername},#{delDate},#{delUser},#{delUsername},#{sort},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_disease (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.disId},#{item.disName},#{item.disCode},#{item.disType},#{item.createDate},#{item.createUser},#{item.createUsername},#{item.updateDate},#{item.updateUser},#{item.updateUsername},#{item.delDate},#{item.delUser},#{item.delUsername},#{item.sort},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TDisease">
        update t_disease
        <set>
             <if test="disName != null">
                dis_name = #{ disName },
             </if>
             <if test="disCode != null">
                dis_code = #{ disCode },
             </if>
             <if test="disType != null">
                dis_type = #{ disType },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="createUsername != null">
                create_username = #{ createUsername },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
             <if test="updateUser != null">
                update_user = #{ updateUser },
             </if>
             <if test="updateUsername != null">
                update_username = #{ updateUsername },
             </if>
             <if test="delDate != null">
                del_date = #{ delDate },
             </if>
             <if test="delUser != null">
                del_user = #{ delUser },
             </if>
             <if test="delUsername != null">
                del_username = #{ delUsername },
             </if>
             <if test="sort != null">
                sort = #{ sort },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where dis_id = #{ disId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_disease where dis_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.TDisease" resultMap="BaseResultMap">
        SELECT dis_id,dis_name,dis_code,dis_type,create_date,create_user,create_username,update_date,update_user,update_username,del_date,del_user,del_username,sort,status
        from t_disease
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>



    <select id="getListByVo" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.service.common.vo.DiseaseVo">
        select
        a.dis_id,
        a.dis_name,
        a.dis_code,
        a.dis_type
        from t_disease a
        <where>
            <if test="queryAll == '0' ">
                a.dis_id
                not in (
                select c.dis_id from t_pre_diagnosis_dis_mapping c join   t_pre_diagnosis_form d on (c.dia_id = d.dia_id)
                where d.status != 1
                )

            </if>

            <if test="disType != null and disType != '' ">
                and a.dis_type = #{disType}
            </if>
            <if test="diaName != null and diaName != '' ">
                and a.dis_name like CONCAT('%',trim(#{diaName}),'%')
            </if>
        </where>
    </select>



    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.service.common.strategy.record.impl.TDiseaseRes">
        <result column="dis_id" jdbcType="VARCHAR"  property="disId" />
        <result column="dia_id" jdbcType="VARCHAR"  property="diaId" />
        <result column="dis_name" jdbcType="VARCHAR" property="disName" />
        <result column="dis_code" jdbcType="VARCHAR" property="disCode" />
        <result column="dis_summary" jdbcType="VARCHAR" property="disSummary" />

    </resultMap>
    <select id="getListByTDisease" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.beans.business.TDisease">
        select  a.dis_name,a.dis_code,a.dis_id,b.dia_id,
        IFNULL(b.dis_summary,(SELECT d.dis_summary FROM t_disease d WHERE a.dis_code = d.dis_code)) AS dis_summary
                from t_disease a

        join t_pre_diagnosis_dis_mapping b on a.dis_id=b.dis_id

                       join t_pre_diagnosis_form as c on c.dia_id=b.dia_id and c.status='0' and c.show_status=1 and c.form_type = '1'
        where a.dis_type = #{disType}
<if test="disName != null and disName != ''">
    and a.dis_name  like CONCAT('%',trim(#{disName}),'%')
</if>
    </select>

    <select id="getListByDept" resultType="com.cbkj.diagnosis.service.common.strategy.record.impl.TDeptRes" parameterType="com.cbkj.diagnosis.beans.business.TDisease">
        SELECT
        dia_id as diaId,
        dept_dia_name as deptDiaName,
        SUBSTRING_INDEX(SUBSTRING_INDEX(dept_ids, ',', n), ',', -1) AS deptId,
        (SELECT dept_name FROM `sys_dept` WHERE dept_code = SUBSTRING_INDEX(SUBSTRING_INDEX(dept_ids, ',', n), ',', -1)) AS deptName
        FROM `t_pre_diagnosis_form`
        JOIN (
        SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4
        ) numbers ON CHAR_LENGTH(dept_ids) - CHAR_LENGTH(REPLACE(dept_ids, ',', '')) >= n - 1

        WHERE
        where dis_type = #{disType}  and status='0' and show_status=1 and form_type = '1'
        <if test="disName != null and disName != ''">
            and dept_dia_name like CONCAT('%',trim(#{disName}),'%')
        </if>

        ORDER BY dept_id;
    </select>

    <select id="getListByVo2" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.service.common.vo.DiseaseVo2">
        select
        a.dis_id,
        a.dis_name,
        a.dis_code,
        a.dis_type
        from t_disease a
        <where>
            <if test="disType != null and disType != '' ">
                and a.dis_type = #{disType}
            </if>
            <if test="diaName != null and diaName != '' ">
                and a.dis_name like CONCAT('%',trim(#{diaName}),'%')
            </if>
        </where>
    </select>
    <select id="getOneByDisCode" resultType="java.lang.String" parameterType="com.cbkj.diagnosis.beans.business.TDisease">
        select
            a.dis_id

        from t_disease a where a.dis_code = #{disCode} and a.dis_type = #{disType} limit 1
    </select>
    <select id="getAdminDisList" resultType="com.cbkj.diagnosis.beans.business.AdminDisList" parameterType="String">
        SELECT '-1' AS disId,'全科' AS disName FROM DUAL
                                             <where>
                                                 <if test="disName != null and disName != ''">
                                                     '全科'  like CONCAT('%',trim(#{disName}),'%')
                                                 </if>
                                             </where>
        UNION
    select a.dis_id disId,a.dis_name disName from t_disease a where dis_type='1'
    <if test="disName != null and disName != ''">
        and a.dis_name  like CONCAT('%',trim(#{disName}),'%')
    </if>

    </select>
    <select id="getMySelfDisList" resultType="com.cbkj.diagnosis.beans.business.AdminDisList" parameterType="com.cbkj.diagnosis.service.webapi.business.vo.MySelfDisList">
        SELECT a.dis_id as disId,a.dis_name as disName FROM `sys_admin_info_dis_mapping` AS a
        <where>
            <if test="userId != null and userId != ''">
                and a.user_id = #{userId}
            </if>
            <if test="disName != null and disName != ''">
                and a.dis_name like CONCAT('%',trim(#{disName}),'%')
            </if>
        </where>
        order by a.sort asc
    </select>
    <select id="getDiseaseDiagnosisMappingList" resultType="com.cbkj.diagnosis.beans.business.TDisease" >
        SELECT
            DISTINCT tpddm.`dis_id`,tpddm.`dis_name`
        FROM `t_pre_diagnosis_dis_mapping` AS tpddm JOIN `t_pre_diagnosis_form` AS tpdf
                                                         ON(
                                                                     tpddm.`dia_id` = tpdf.`dia_id` AND tpdf.`status`='0' AND tpdf.dia_type = '1' AND tpddm.`dis_type`='1'
                                                             )
    </select>


    <!--    <select id="getListByTDisease" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.beans.business.TDisease">-->
<!--        select a.dis_name,a.dis_code,a.dis_id from t_disease a-->
<!--        where a.dis_type = #{disType}-->
<!--        <if test="disName != null and disName != ''">-->
<!--            and a.dis_name  like CONCAT('%',trim(#{disName}),'%')-->
<!--        </if>-->
<!--    </select>-->


</mapper>