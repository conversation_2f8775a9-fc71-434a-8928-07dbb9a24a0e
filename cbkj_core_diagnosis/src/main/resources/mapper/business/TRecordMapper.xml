<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TRecordMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TRecord">
        <id column="rec_id" jdbcType="VARCHAR"  property="recId" />
        <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
        <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
        <result column="patient_idcard" jdbcType="VARCHAR" property="patientIdcard" />
        <result column="patient_sex" jdbcType="VARCHAR" property="patientSex" />
        <result column="patient_age" jdbcType="VARCHAR" property="patientAge" />
        <result column="dia_content" jdbcType="VARCHAR" property="diaContent" />
        <result column="dia_id" jdbcType="VARCHAR" property="diaId" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername" />
        <result column="del_date" jdbcType="TIMESTAMP" property="delDate" />
        <result column="del_user" jdbcType="VARCHAR" property="delUser" />
        <result column="del_username" jdbcType="VARCHAR" property="delUsername" />
        <result column="status" jdbcType="VARCHAR" property="status" />

        <result column="record_resource" jdbcType="VARCHAR" property="recordResource" />
        <result column="form_type" jdbcType="VARCHAR" property="formType" />
        <result column="reg_plan_id" jdbcType="VARCHAR" property="regPlanId" />
    </resultMap>


    <sql id="Base_Column_List">
    rec_id,patient_id,patient_name,patient_idcard,patient_sex,patient_age,dia_content,dia_id,create_date,create_user,create_username,
        update_date,update_user,update_username,del_date,del_user,del_username,status,record_resource,form_type,reg_plan_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TRecord">
        delete from t_record where rec_id = #{ recId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record where rec_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.TRecord">
        insert into t_record (<include refid="Base_Column_List" />) values
        (#{recId},#{patientId},#{patientName},#{patientIdcard},#{patientSex},#{patientAge},#{diaContent},#{diaId},#{createDate},
         #{createUser},#{createUsername},#{updateDate},#{updateUser},#{updateUsername},#{delDate},#{delUser},#{delUsername},#{status},#{recordResource},#{formType}
        ,#{regPlanId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.recId},#{item.patientId},#{item.patientName},#{item.patientIdcard},#{item.patientSex},#{item.patientAge},#{item.diaContent},#{item.diaId},
             #{item.createDate},#{item.createUser},#{item.createUsername},#{item.updateDate},#{item.updateUser},#{item.updateUsername},#{item.delDate},#{item.delUser},
             #{item.delUsername},#{item.status},#{item.recordResource},#{item.formType},
             #{item.regPlanId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TRecord">
        update t_record
        <set>
             <if test="patientId != null">
                patient_id = #{ patientId },
             </if>
             <if test="patientName != null">
                patient_name = #{ patientName },
             </if>
             <if test="patientIdcard != null">
                patient_idcard = #{ patientIdcard },
             </if>
             <if test="patientSex != null">
                patient_sex = #{ patientSex },
             </if>
             <if test="patientAge != null">
                patient_age = #{ patientAge },
             </if>
             <if test="diaContent != null">
                dia_content = #{ diaContent },
             </if>
             <if test="diaId != null">
                dia_id = #{ diaId },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="createUsername != null">
                create_username = #{ createUsername },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
             <if test="updateUser != null">
                update_user = #{ updateUser },
             </if>
             <if test="updateUsername != null">
                update_username = #{ updateUsername },
             </if>
             <if test="delDate != null">
                del_date = #{ delDate },
             </if>
             <if test="delUser != null">
                del_user = #{ delUser },
             </if>
             <if test="delUsername != null">
                del_username = #{ delUsername },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="regPlanId != null and regPlanId != ''">
                 reg_plan_id = #{regPlanId},
             </if>
            <if test="decryptContent != null and decryptContent != ''">
                decrypt_content = #{decryptContent},
            </if>
        </set>
        where rec_id = #{ recId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record where rec_id = #{id}
    </select>

    <select id="getPageListByObjNew" parameterType="com.cbkj.diagnosis.service.common.vo.PreListReVoDAO" resultMap="BaseResultMap">
        SELECT
        tr.rec_id,
        tr.patient_id,
        tr.patient_name,
        tr.patient_idcard,
        tr.patient_sex,
        tr.patient_age,
        tr.dia_content,
        tr.dia_id,
        tr.create_date,
        tr.create_user,
        tr.create_username,
        tr.update_date,
        tr.update_user,
        tr.update_username,
        tr.del_date,
        tr.del_user,
        tr.del_username,
        tr.STATUS,
        tr.record_resource,
        tr.form_type,
        tr.reg_plan_id
        FROM
        t_record AS tr join t_pre_diagnosis_form tpdf on(tpdf.dia_id = tr.dia_id)
        <where>
            EXISTS
            (SELECT
            1
            FROM
            t_pre_diagnosis_dis_mapping AS tpddm,
            sys_admin_info_dis_mapping AS saidm
            where tr.dia_id = tpddm.dia_id
            AND tpddm.`dis_id` = saidm.`dis_id`
              <if test="disId != null and disId != '' ">
                  and tpddm.`dis_id` = #{disId}
              </if>
            AND saidm.`user_id` = #{userId})

            and tr.form_type = '1'
<!--
            <choose>
                <when test="diaTypeBool">
                    and tpdf.dia_type in ('1','2')
                </when>
                <otherwise>
                    and tpdf.dia_type = '1'
                </otherwise>
            </choose>
-->
            <if test=" key != null and key!='' ">
                and (tr.patient_name like CONCAT('%',trim(#{key}),'%') or tr.patient_idcard=#{key})
            </if>
            <if test="startDate != null">
                and tr.create_date >= #{startDate}
            </if>
            <if test="endDate != null">
                and tr.create_date &lt;= #{endDate}
            </if>
            <if test="patientId !=null and patientId !=''">
                and tr.patient_id = #{patientId}
            </if>
            <if test="diaId !=null and diaId !=''">
                and tr.dia_id = #{diaId}
            </if>

        </where>
        order by tr.create_date desc
    </select>

    <select id="getOneByRecId" parameterType="String" resultMap="BaseResultMap">
        SELECT
        a.rec_id,
        a.patient_id,
        a.patient_name,
        a.patient_idcard,
        a.patient_sex,
        a.patient_age,
        a.dia_content,
        a.dia_id,
        a.create_date,
        a.create_user,
        a.create_username,
        a.update_date,
        a.update_user,
        a.update_username,
        a.del_date,
        a.del_user,
        a.del_username,
        a.status,
        a.record_resource,
        a.form_type,
        a.reg_plan_id,
        b.cue_word_status
        from t_record as a  join t_pre_diagnosis_form as b on(a.dia_id = b.dia_id)
        <where>
            a.form_type = '1'
            and a.rec_id = #{recId}
        </where>

    </select>


    <resultMap id="BasePageListByObjNew2" type="com.cbkj.diagnosis.beans.business.TRecordHis">
    <id column="rec_id" jdbcType="VARCHAR"  property="recId" />
    <id column="patientIdCard" jdbcType="VARCHAR"  property="patientIdCard" />
    <id column="patient_name" jdbcType="VARCHAR"  property="patientName" />
    <id column="dia_content" jdbcType="VARCHAR"  property="diaContent" />
    <id column="dia_id" jdbcType="VARCHAR"  property="diaId" />
    <id column="create_date" jdbcType="VARCHAR"  property="createDate" />
    <id column="patient_card_type" jdbcType="VARCHAR"  property="patientCardType" />
    <id column="health_card_num" jdbcType="VARCHAR"  property="healthCardNum" />
    <id column="form_code" jdbcType="VARCHAR"  property="formCode" />
    <id column="reg_plan_id" jdbcType="VARCHAR"  property="regPlanId" />
    </resultMap>
    <select id="getPageListByObjNew2" resultMap="BasePageListByObjNew2" parameterType="com.cbkj.diagnosis.beans.his.GetPageListByObjNew2">
        SELECT a.rec_id,a.patient_name,b.card_number as patientIdCard ,a.dia_content,a.dia_id,c.form_code,a.create_date,b.patient_card_type,
               b.health_card_num,a.reg_plan_id
        from t_record a join  t_admin_info b on(a.patient_id=b.USER_ID)
join t_pre_diagnosis_form c on(a.dia_id=c.dia_id)
        <where>
            <if test="startDate != null">
                and a.create_date >= #{startDate}
            </if>
            <if test="recId != null and recId != ''">
                and a.rec_id =  #{recId}
            </if>
            <if test="endDate != null">
                and  a.create_date &lt;= #{endDate}
            </if>
            <if test="mobile !=null and mobile !=''">
                and b.MOBILE = #{mobile}
            </if>
            <if test="cardType !=null and cardType !=''">
                and b.patient_card_type = #{cardType}
            </if>
            <if test="healthCardNum !=null and healthCardNum !=''">
                and b.health_card_num = #{healthCardNum}
            </if>
            <if test="cardNumber !=null and cardNumber !=''">
                and b.card_number = #{cardNumber}
            </if>
        </where>
limit 1
    </select>
    <select id="getPageListByObj2" parameterType="com.cbkj.diagnosis.beans.business.TRecord" resultMap="BaseResultMap">

        SELECT rec_id,patient_id,patient_name,patient_idcard,patient_sex,patient_age,dia_content,dia_id,create_date,create_user,create_username,update_date,
               update_user,update_username,del_date,del_user,del_username,status,record_resource,form_type,reg_plan_id
        from t_record
        <where>
            <if test="patientId !=null and patientId !=''">
                and patient_id = #{patientId}
            </if>
            <if test="diaId !=null and diaId !=''">
                and dia_id = #{diaId}
            </if>
        </where>
        order by create_date desc

    </select>
    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.service.mobileapi.vo.FurtherConsultationRecordListVo">
        <id column="rec_id" jdbcType="BIGINT" property="recId"/>
        <result column="form_name" jdbcType="VARCHAR" property="formName"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>

    </resultMap>
    <select id="getFurtherRecordList" parameterType="com.cbkj.diagnosis.service.mobileapi.vo.GetFurtherConsultationList"
            resultMap="BaseResultMap2">
        SELECT
            a.rec_id,
            a.`create_date`,
            b.`form_name`,
            c.ins_name AS insName
        FROM `t_record` AS a JOIN `t_pre_diagnosis_form` AS b ON(a.`dia_id`=b.`dia_id`)
        join s_road_task_patients as c on(c.rec_id = a.`rec_id`)
        WHERE a.`patient_id`=#{patientId} AND a.`form_type`=#{formType} and c.road_execute_event_way = '1' ORDER BY a.`create_date` DESC

    </select>

    <select id="getFurtherRecordList2" parameterType="com.cbkj.diagnosis.service.mobileapi.vo.GetFurtherConsultationList"
            resultMap="BaseResultMap2">
        SELECT
            a.rec_id,
            a.`create_date`,
            concat(b.chinese_dis_name,b.west_dis_name) as form_name,
            c.ins_name AS insName
        FROM `t_record` AS a JOIN `t_pre_diagnosis_form` AS b ON(a.`dia_id`=b.`dia_id`)
                             left join s_road_task_patients as c on(c.rec_id = a.`rec_id`)
        WHERE a.`patient_id`=#{patientId} AND a.`form_type`='1' ORDER BY a.`create_date` DESC

    </select>

    <resultMap id="questionClassTypeInfo" type="com.cbkj.diagnosis.service.webapi.business.vo.GetQuestionClassTypeInfo">
        <result column="content" jdbcType="BIGINT" property="content"/>
        <result column="question_class_type" jdbcType="VARCHAR" property="questionClassType"/>
        <result column="date_unit" jdbcType="VARCHAR" property="dateUnit"/>
        <result column="question_type" jdbcType="VARCHAR" property="questionType"/>

    </resultMap>
    <select id="getQuestionClassTypeInfo"
            resultMap="questionClassTypeInfo" parameterType="String">
        SELECT
            a.`question_number`,
            a.`question_stem`,
            a.`question_class_type`,
            a.`date_unit`,
            a.question_type,

                    CONCAT(
                            CASE WHEN a.`question_stem` IS NOT NULL AND a.`question_stem` != '' THEN CONCAT(a.`question_stem`, '#') ELSE CONCAT(' ', '#')    END,
                            a.`content`,
                            CASE WHEN a.`question_type` =4 THEN CONCAT('-', a.`date_unit`) ELSE '' END
                        )
                AS content
        FROM
            (
                SELECT
                    a.`content`,
                    b.`question_number`,
                    b.`question_stem`,
                    b.`question_class_type`,
                    b.`date_unit`,
                    b.question_type,
                    b.question_name
                FROM `t_record_dia` AS a
                         JOIN `t_pre_diagnosis_question` AS b ON a.`question_id` = b.`question_id`
                WHERE  a.`rec_id`=#{recId} and b.question_class_type is not null and b.question_class_type != ''

                ORDER BY b.`question_number` ASC

            ) AS a


    </select>
    <select id="getCurrentSecondByRecId" resultType="java.lang.String" parameterType="com.cbkj.diagnosis.beans.business.TRecord" >
        SELECT aaa.rec_id FROM (
        SELECT a.`rec_id` FROM `t_record` AS a WHERE  a.`dia_id` IN (


        SELECT tp.`dia_id` FROM `t_pre_diagnosis_form` AS tp JOIN t_pre_diagnosis_dis_mapping tpdm ON(tp.`dia_id` = tpdm.`dia_id`) WHERE tpdm.`dis_id` IN (
        SELECT b.`dis_id` FROM `t_record` AS a
        JOIN `t_pre_diagnosis_dis_mapping` AS b
        ON(a.`dia_id` = b.`dia_id` AND b.`dis_type` = '1') WHERE a.`rec_id` = #{recId} AND a.`patient_id`=#{patientId}
        ) and tp.form_type = '1'


        ) AND  a.`create_date` <![CDATA[ < ]]> #{createDate}  AND a.`patient_id`=#{patientId} ORDER BY a.`create_date` DESC

        ) AS aaa LIMIT 1,1

    </select>
    <resultMap id="getStatisticsDicByRecId" type="com.cbkj.diagnosis.beans.statistics.StatisticsDic">

    </resultMap>
    <select id="getStatisticsDicByRecId" resultMap="getStatisticsDicByRecId" parameterType="String">

    </select>


    <select id="getDecryptContent" parameterType="String" resultType="com.cbkj.diagnosis.beans.business.TRecord">
        select
        rec_id as recId,
        patient_idcard as decryptContent
        from t_record
        <where>
            <if test="stime != null and stime != ''">
                and create_date >=  #{stime}
            </if>
            <if test="etime != null and etime != ''">
                and create_date <![CDATA[ <= ]]>   #{etime}
            </if>
        </where>
    </select>

    <update id="updateClosedByPatientId" parameterType="String">
        update t_record set closed_status = '1',closed_no= #{closedNo},closed_time=now() where patient_id = #{patientId} and closed_status = '0'
    </update>

    <update id="updateClosedById" parameterType="String">
        update t_record set closed_status = '1',closed_no= #{closedNo},closed_time=now() where rec_id = #{id} and closed_status = '0'
    </update>

    <update id="updateClosedByclosdNo" parameterType="String">
        update t_record set closed_status = #{closedStatus}, closed_time=now() where closed_no = #{closedNo}
    </update>




</mapper>