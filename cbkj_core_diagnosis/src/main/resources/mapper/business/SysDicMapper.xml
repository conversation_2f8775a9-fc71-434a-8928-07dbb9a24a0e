<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.SysDicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.SysDic">
        <id column="dic_id" property="dicId" />
        <result column="dic_name" property="dicName" />
        <result column="dic_shot_name" property="dicShotName" />
        <result column="dic_code" property="dicCode" />
        <result column="dic_value" property="dicValue" />
        <result column="dic_sort" property="dicSort" />
        <result column="status" property="status" />
        <result column="parent_id" property="parentId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        dic_id, dic_name,dic_shot_name, dic_code, dic_value, dic_sort, status, parent_id
    </sql>
    <select id="getDetailsByDicCodeList" resultType="java.util.HashMap" parameterType="List">
        select dic_id dicId,dic_code dicCode,dic_name dicName,dic_shot_name dicShotName from sys_dic where dic_code in
       <foreach collection="list" item="item" open="(" separator="," close=")">
           #{item}
       </foreach>
    </select>

</mapper>
