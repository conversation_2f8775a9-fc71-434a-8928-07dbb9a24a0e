<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion">
        <id column="question_id" jdbcType="INTEGER"  property="questionId" />
        <result column="dia_id" jdbcType="VARCHAR" property="diaId" />
        <result column="question_code" jdbcType="VARCHAR" property="questionCode" />
        <result column="question_name" jdbcType="VARCHAR" property="questionName" />
        <result column="question_stem" jdbcType="VARCHAR" property="questionStem" />
        <result column="question_type" jdbcType="VARCHAR" property="questionType" />
        <result column="question_number" jdbcType="INTEGER" property="questionNumber" />
        <result column="date_unit" jdbcType="VARCHAR" property="dateUnit" />
        <result column="question_class_type" jdbcType="VARCHAR" property="questionClassType" />
        <result column="question_unit" jdbcType="VARCHAR" property="questionUnit" />
        <result column="question_option_groups" jdbcType="VARCHAR" property="questionOptionGroups" />
        <result column="follow_up_class_type_code" jdbcType="VARCHAR" property="followUpClassTypeCode" />
        <result column="question_img" jdbcType="VARCHAR" property="questionImg" />
        <result column="question_img_max_num" jdbcType="VARCHAR" property="questionImgMaxNum" />
        <result column="question_dimension_code" jdbcType="VARCHAR" property="questionDimensionCode" />
        <result column="question_dimension_name" jdbcType="VARCHAR" property="questionDimensionName" />
    </resultMap>


    <sql id="Base_Column_List">
    question_id,dia_id,question_name,question_stem,question_type,question_number,date_unit,question_class_type,question_unit,question_option_groups,question_code,follow_up_class_type_code
    ,question_img,question_img_max_num,question_dimension_code,question_dimension_name</sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion">
        delete from t_pre_diagnosis_question where question_id = #{ questionId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_pre_diagnosis_question where question_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteByDiaId">
        delete from t_pre_diagnosis_question where dia_id = #{diaId}
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion"  useGeneratedKeys="true" keyColumn="question_id" keyProperty="questionId">
        insert into t_pre_diagnosis_question (<include refid="Base_Column_List" />) values
        (#{questionId},#{diaId},#{questionName},#{questionStem},#{questionType},#{questionNumber},#{dateUnit},#{questionClassType},#{questionUnit},
         #{questionOptionGroups},#{questionCode},#{followUpClassTypeCode},#{questionImg},#{questionImgMaxNum},#{questionDimensionCode},#{questionDimensionName} )
    </insert>

    <insert id="insertList" parameterType="List" useGeneratedKeys="true" keyColumn="question_id" keyProperty="questionId">
        insert into t_pre_diagnosis_question (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.questionId},#{item.diaId},#{item.questionName},#{item.questionStem},#{item.questionType},#{item.questionNumber},#{item.dateUnit},
             #{item.questionClassType},#{item.questionUnit},#{item.questionOptionGroups},#{item.questionCode},#{item.followUpClassTypeCode},
             #{item.questionImg},#{item.questionImgMaxNum},#{item.questionDimensionCode},#{item.questionDimensionName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion">
        update t_pre_diagnosis_question
        <set>
             <if test="diaId != null">
                dia_id = #{ diaId },
             </if>
             <if test="questionName != null">
                question_name = #{ questionName },
             </if>
             <if test="questionStem != null">
                question_stem = #{ questionStem },
             </if>
             <if test="questionType != null">
                question_type = #{ questionType },
             </if>
             <if test="questionNumber != null">
                question_number = #{ questionNumber },
             </if>
             <if test="dateUnit != null">
                date_unit = #{ dateUnit },
             </if>
            <if test="questionClassType != null">
                question_class_type = #{questionClassType},
             </if>
            <if test="questionUnit != null and questionUnit !=''">
                question_unit = #{questionUnit},
            </if>
            <if test="questionOptionGroups != null and questionOptionGroups !=''">
                question_option_groups = #{questionOptionGroups},
            </if>
            <if test="followUpClassTypeCode != null and followUpClassTypeCode !=''">
                follow_up_class_type_code = #{followUpClassTypeCode},
            </if>
            <if test="questionImg != null and questionImg !=''">
                question_img = #{questionImg},
            </if>
            <if test="questionImgMaxNum != null">
                question_img_max_num = #{questionImgMaxNum},
            </if>
            <if test="questionDimensionCode != null and questionDimensionCode !=''">
                question_dimension_code = #{questionDimensionCode},
            </if>
            <if test="questionDimensionName != null and questionDimensionName !=''">
                question_dimension_name = #{questionDimensionName},
            </if>
        </set>
        where question_id = #{ questionId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_pre_diagnosis_question where question_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_pre_diagnosis_question
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
            <if test=" diaId != null and diaId!='' ">
                and dia_id = #{diaId}
            </if>
        </where>
    </select>


    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.beans.business.TRecordDia">
        <id column="question_id" jdbcType="INTEGER"  property="questionId" />
        <id column="dia_id" jdbcType="VARCHAR"  property="diaId" />
        <result column="question_name" jdbcType="VARCHAR" property="questionName" />
        <result column="question_number" jdbcType="INTEGER" property="questionNumber" />
        <result column="question_type" jdbcType="VARCHAR" property="questionType" />
        <result column="date_unit" jdbcType="VARCHAR" property="dateUnit" />
        <result column="question_unit" jdbcType="VARCHAR" property="questionUnit" />
        <result column="question_option_groups" jdbcType="VARCHAR" property="questionOptionGroups" />
    </resultMap>

    <select id="getFirstQ" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.service.common.vo.QuestionMobile">


        SELECT
             b.question_id,
             b.dia_id,
             b.question_name,
             b.question_number,
             b.question_type,
             b.date_unit,
             b.question_unit
        FROM
        `t_pre_diagnosis_form` c
        JOIN   `t_pre_diagnosis_question` b ON (c.`dia_id`=b.`dia_id` AND c.status =0)
   <!--         left join `t_pre_diagnosis_dis_mapping` a on (a.`dia_id`=b.`dia_id`
                <if test="disId != null and disId !=''">
                    a.`dis_id`=#{disId}
                </if>
                )
                -->
        <where>
            <if test="diaId != null">
                and c.dia_id = #{diaId}
            </if>
        </where>



        order by b.`question_number` asc
        limit 1

    </select>
    <select id="getChildQ" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.service.common.vo.NextQVo">
        SELECT
            d.question_id,
            d.dia_id,
            d.question_name,
            d.question_number,
            d.question_type,
            d.date_unit,
            d.question_unit
        FROM `t_pre_diagnosis_question` d join t_pre_diagnosis_child c on(c.child_question_id = d.question_id)

        WHERE  d.`dia_id`=#{diaId}

        <if test="questionIds != null">

            and d.`question_id` not in
            (<foreach collection="questionIds" item="item" index="index" separator=",">
            #{item}
        </foreach>)

        </if>

        <if test="masterId != null">

          and c.master_question_id = #{masterId}
        </if>

        <if test="optionIds != null ">
            and c.option_id in
            (<foreach collection="optionIds" item="item" index="index" separator=",">
            #{item}
        </foreach>)
        </if>



        order by sort asc limit 1
    </select>



    <select id="getNextQ" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.service.common.vo.NextQVo">
        SELECT
        d.question_id,
        d.dia_id,
        d.question_name,
        d.question_number,
        d.question_type,
        d.date_unit,
        d.question_unit
        FROM `t_pre_diagnosis_question` d

        left JOIN `t_pre_diagnosis_option` f ON d.`question_id`=f.`question_id`

        JOIN `t_pre_diagnosis_form` c ON c.`dia_id`=d.`dia_id` AND c.status =0
        WHERE d.`dia_id`=#{diaId}

        <if test="optionIds != null and optionIds.size() != 0">
            AND d.`question_id` NOT IN (


            SELECT c.`skip_question_id` from

            t_pre_diagnosis_skip c
            WHERE
            <if test="optionIds != null and optionIds.size() != 0">
                c.`option_id`

                in(
                <foreach collection="optionIds" item="item" index="index" separator=",">
                    #{item}
                </foreach>)

            </if>


            )

        </if>

        <if test="questionIds != null and questionIds.size() != 0">

            and d.`question_id` not in
            (<foreach collection="questionIds" item="item" index="index" separator=",">
            #{item}
        </foreach>)

        </if>
<!-- 排除没被选择的子题 -->
        and d.`question_id` not in (
            select child_question_id from t_pre_diagnosis_child where dia_id=#{diaId}
        )


        ORDER BY d.`question_number` ASC LIMIT 1

    </select>
    <select id="getFirstQByQuan" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.service.common.vo.QuestionMobile">
        SELECT
            b.question_id,
            b.dia_id,
            b.question_name,
            b.question_number,
            b.question_type,
            b.date_unit,
            b.question_unit
        FROM `t_pre_diagnosis_question` b
                                             JOIN     `t_pre_diagnosis_form` c ON c.`dia_id`=b.`dia_id` AND c.status =0

        WHERE
            <!-- c.`dia_type`=#{diaType} -->
        <if test="diaId != null">
            and c.dia_id = #{diaId}
        </if>
        order by b.`question_number` asc
        limit 1
    </select>
    <select id="getQuestionStem" resultType="java.lang.String" parameterType="Integer">
        select question_stem from t_pre_diagnosis_question where question_id = #{questionId}
    </select>
    <select id="getFirstQSuiFang" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.service.common.vo.QuestionMobile">
        SELECT
        b.question_id,
        b.dia_id,
        b.question_name,
        b.question_number,
        b.question_type,
        b.date_unit,
        b.question_unit
        FROM `t_pre_diagnosis_question` b
        JOIN `t_pre_diagnosis_form` c ON c.`dia_id`=b.`dia_id`

        <where>
            <if test="diaId != null">
                and c.dia_id = #{diaId}
            </if>
 <!--           <if test="diaType != null and diaType != ''">
                and c.`dia_type`=#{diaType}
            </if>
            -->
        </where>
        order by b.`question_number` asc
        limit 1
    </select>
    <select id="getAllQuestionListByDiaId"
            resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_pre_diagnosis_question where dia_id = #{diaId}
        order by question_number asc
    </select>

    <!-- 批量查询多个问卷的问题数据 - 性能优化 -->
    <select id="getAllQuestionsByDiaIds" resultMap="BaseResultMap" parameterType="List">
        select <include refid="Base_Column_List" />
        from t_pre_diagnosis_question
        where dia_id in
        <foreach collection="list" item="diaId" open="(" separator="," close=")">
            #{diaId}
        </foreach>
        order by question_number asc
    </select>
    <select id="selectDimensionScoreByList" resultType="java.util.HashMap">
        select
        a.question_dimension_code as questionDimensionCode,
        a.question_dimension_name as questionDimensionName,
        b.option_dimension_score as optionDimensionScore
        from t_pre_diagnosis_question as a join option_id as b on a.question_id = b.question_id and b.option_dimension_score_switch = 1
        where b.option_id in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>