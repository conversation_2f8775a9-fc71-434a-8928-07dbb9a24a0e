<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TRecordDiaDimensionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TRecordDiaDimension">
        <result column="id" property="id" />
        <result column="t_record" property="tRecord" />
        <result column="question_dimension_code" property="questionDimensionCode" />
        <result column="question_dimension_name" property="questionDimensionName" />
        <result column="question_dimension_total_score" property="questionDimensionTotalScore" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, t_record, question_dimension_code, question_dimension_name, question_dimension_total_score
    </sql>

</mapper>
