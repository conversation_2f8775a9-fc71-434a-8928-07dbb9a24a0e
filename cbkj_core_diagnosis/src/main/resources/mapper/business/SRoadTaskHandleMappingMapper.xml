<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.SRoadTaskHandleMappingMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.SRoadTaskHandleMapping">
        <result column="handle_record_id" jdbcType="VARCHAR"  property="handleRecordId" />
        <result column="s_road_task_id" jdbcType="VARCHAR" property="sRoadTaskId" />
        <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
    </resultMap>


    <sql id="Base_Column_List">
    handle_record_id,s_road_task_id,patient_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskHandleMapping">
        delete from s_road_task_handle_mapping where handle_record_id = #{ handleRecordId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road_task_handle_mapping where handle_record_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskHandleMapping">
        insert into s_road_task_handle_mapping (<include refid="Base_Column_List" />) values
        (#{handleRecordId},#{sRoadTaskId},#{patientId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into s_road_task_handle_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.handleRecordId},#{item.sRoadTaskId},#{item.patientId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskHandleMapping">
        update s_road_task_handle_mapping
        <set>
             <if test="sRoadTaskId != null">
                s_road_task_id = #{ sRoadTaskId },
             </if>
             <if test="patientId != null">
                patient_id = #{ patientId },
             </if>
        </set>
        where handle_record_id = #{ handleRecordId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from s_road_task_handle_mapping where handle_record_id = #{ handleRecordId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskHandleMapping" resultMap="BaseResultMap">
        SELECT handle_record_id,s_road_task_id,patient_id
        from s_road_task_handle_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>