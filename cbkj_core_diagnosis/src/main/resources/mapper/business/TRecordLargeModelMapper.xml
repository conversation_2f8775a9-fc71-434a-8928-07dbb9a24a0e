<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TRecordLargeModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TRecordLargeModel">
        <id column="record_model_id" property="recordModelId" />
        <result column="rec_id" property="recId" />
        <result column="dia_id" property="diaId" />
        <result column="request_text" property="requestText" />
        <result column="response_text" property="responseText" />
        <result column="cue_word_text" property="cueWordText" />
        <result column="cuw_word_trans" property="cuwWordTrans" />
        <result column="record_model_status" property="recordModelStatus" />
        <result column="record_model_fail_msg" property="recordModelFailMsg" />
        <result column="insert_time" property="insertTime" />
        <result column="re_call_times" property="reCallTimes" />
        <result column="last_re_call_time" property="lastReCallTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        record_model_id, rec_id, dia_id, request_text, response_text, cue_word_text, cuw_word_trans, record_model_status, record_model_fail_msg, insert_time, re_call_times, last_re_call_time
    </sql>

</mapper>
