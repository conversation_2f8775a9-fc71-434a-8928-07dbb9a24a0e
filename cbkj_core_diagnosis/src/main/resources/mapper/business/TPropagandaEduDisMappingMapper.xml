<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPropagandaEduDisMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPropagandaEduDisMapping">
        <id column="t_propaganda_edu_id" property="tPropagandaEduId" />
        <id column="dis_id" property="disId" />
        <result column="dis_name" property="disName" />
        <result column="dis_type" property="disType" />
        <result column="dis_code" property="disCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t_propaganda_edu_id, dis_id, dis_name, dis_type, dis_code
    </sql>
    <select id="listByList" resultType="com.cbkj.diagnosis.beans.business.TPropagandaEduDisMapping" parameterType="collection">
        select
        <include refid="Base_Column_List" />
        from t_propaganda_edu_dis_mapping
        where t_propaganda_edu_id in
        <foreach collection="staticsEduEduId" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
