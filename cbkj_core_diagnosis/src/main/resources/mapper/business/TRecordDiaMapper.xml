<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TRecordDiaMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TRecordDia">
        <id column="dia_rec_id" jdbcType="INTEGER"  property="diaRecId" />
        <result column="rec_id" jdbcType="VARCHAR" property="recId" />
        <result column="question_id" jdbcType="INTEGER" property="questionId" />
        <result column="question_name" jdbcType="VARCHAR" property="questionName" />
        <result column="question_number" jdbcType="INTEGER" property="questionNumber" />
        <result column="question_type" jdbcType="VARCHAR" property="questionType" />
        <result column="option_ids" jdbcType="VARCHAR" property="optionIds" />
        <result column="option_names" jdbcType="VARCHAR" property="optionNames" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="year" jdbcType="VARCHAR" property="year" />
        <result column="month" jdbcType="VARCHAR" property="month" />
        <result column="day" jdbcType="VARCHAR" property="day" />
        <result column="hour" jdbcType="VARCHAR" property="hour" />
        <result column="date_unit" jdbcType="VARCHAR" property="dateUnit" />
        <result column="question_unit" jdbcType="VARCHAR" property="questionUnit" />
        <result column="question_code" jdbcType="VARCHAR" property="questionCode" />
        <result column="week" jdbcType="VARCHAR" property="week" />
    </resultMap>


    <sql id="Base_Column_List">
    dia_rec_id,rec_id,question_id,question_name,question_number,question_type,option_ids,option_names,content,year,month,day,hour,date_unit,week
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TRecordDia">
        delete from t_record_dia where dia_rec_id = #{ diaRecId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record_dia where dia_rec_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.TRecordDia" useGeneratedKeys="true" keyColumn="dia_rec_id" keyProperty="diaRecId">
        insert into t_record_dia (<include refid="Base_Column_List" />) values
        (#{diaRecId},#{recId},#{questionId},#{questionName},#{questionNumber},#{questionType},
         #{optionIds},#{optionNames},#{content},#{year},#{month},#{day},#{hour},#{dateUnit},#{week})
    </insert>

    <insert id="insertList" parameterType="List" useGeneratedKeys="true" keyColumn="dia_rec_id" keyProperty="diaRecId">
        insert into t_record_dia (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.diaRecId},#{item.recId},#{item.questionId},#{item.questionName},#{item.questionNumber},#{item.questionType},#{item.optionIds},#{item.optionNames}
            ,#{item.content},#{item.year},#{item.month},#{item.day},#{item.hour},#{item.dateUnit},#{item.week})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TRecordDia">
        update t_record_dia
        <set>
             <if test="recId != null">
                rec_id = #{ recId },
             </if>
             <if test="questionId != null">
                question_id = #{ questionId },
             </if>
             <if test="questionName != null">
                question_name = #{ questionName },
             </if>
             <if test="questionNumber != null">
                question_number = #{ questionNumber },
             </if>
             <if test="questionType != null">
                question_type = #{ questionType },
             </if>
             <if test="optionIds != null">
                option_ids = #{ optionIds },
             </if>
             <if test="optionNames != null">
                option_names = #{ optionNames },
             </if>
             <if test="content != null">
                content = #{ content },
             </if>
             <if test="year != null">
                year = #{ year },
             </if>
             <if test="month != null">
                month = #{ month },
             </if>
             <if test="day != null">
                day = #{ day },
             </if>
            <if test="hour != null">
                hour = #{hour},
             </if>
            <if test="week != null">
                week = #{week},
            </if>
        </set>
        where dia_rec_id = #{ diaRecId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record_dia where dia_rec_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.TRecordDia" resultMap="BaseResultMap">
        SELECT dia_rec_id,rec_id,question_id,question_name,question_number,question_type,option_ids,option_names,content,year,month,day,hour,date_unit,week
        from t_record_dia
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="getObjectByRecId" parameterType="String" resultMap="BaseResultMap">
    select
        a.dia_rec_id,
        a.rec_id,
        c.question_id,
        c.question_name,
        c.question_number,
        c.question_type,
        a.option_ids,
        a.option_names,
        a.content,
        a.year,
        a.month,
        a.day,
        a.week,
        a.hour,
        c.date_unit,
        b.dia_id as diaId,
        c.question_unit,
        c.question_code
        from t_record b
        join t_record_dia a on(a.rec_id=b.rec_id and  a.rec_id = #{recId})
        join t_pre_diagnosis_question as c on (c.question_id = a.question_id)
        order by c.question_number asc
    </select>




    <select id="getFaceList" resultType="com.cbkj.diagnosis.service.webapi.business.vo.FaceListVo"
    parameterType="com.cbkj.diagnosis.service.common.vo.SuiFangPreListReVo">
        SELECT
            a.`rec_id` recId,
            a.`patient_id` patientId,
            a.`patient_name` patientName,
            a.`patient_idcard` patientIdcard,
            a.`patient_sex` patientSex,
            a.`patient_age` patientAge,
            a.`dia_content` diaContent,
            b.`form_name` formName,
            a.create_date createDate,
            a.`create_username` createUsername,
            a.`create_user` createUser
        FROM
            `t_record` AS a
                JOIN `t_pre_diagnosis_form` AS b
                     ON (a.`dia_id` = b.`dia_id`)
        WHERE a.`record_resource` = '2'


        <if test="startDate != null and startDate != ''">

            and a.create_date >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">

            and a.create_date &lt;= #{endDate}
        </if>

        <if test="patientName != null and patientName != ''">

            and patient_name &lt;= #{patientName}
        </if>

        <if test="formType != null and formType != ''">

            and a.form_type = #{formType}
        </if>
        <if test="doctorName != null and doctorName != ''">

            and a.create_username like  CONCAT('%',trim(#{doctorName}),'%')
        </if>

    </select>
    <select id="getRecordDiaByRecIdAndQuestionId" resultType="com.cbkj.diagnosis.beans.business.TRecordDia" parameterType="Map">

        select
        <include refid="Base_Column_List"/>
        from t_record_dia where rec_id = #{recId} and question_id = #{questionId}
    </select>

</mapper>