<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPreDiagnosisStructureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisStructure">
        <id column="diagnosis_structure_id" property="diagnosisStructureId"/>
        <result column="dia_id" property="diaId"/>
        <result column="emr_type" property="emrType"/>
        <result column="type_code" property="typeCode"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        diagnosis_structure_id, dia_id, emr_type, type_code,sort
    </sql>







</mapper>
