<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPropagandaEduMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPropagandaEdu">
        <id column="t_propaganda_edu_id" jdbcType="INTEGER" property="tPropagandaEduId"/>
        <result column="edu_title" jdbcType="VARCHAR" property="eduTitle"/>
        <result column="edu_cover_image" jdbcType="VARCHAR" property="eduCoverImage"/>
        <result column="edu_cover_video" jdbcType="VARCHAR" property="eduCoverVideo"/>
        <result column="edu_content" jdbcType="VARCHAR" property="eduContent"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="edu_type" jdbcType="VARCHAR" property="eduType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="ins_name" jdbcType="VARCHAR" property="insName"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_id" jdbcType="VARCHAR" property="insId"/>
        <result column="edu_abstract" jdbcType="VARCHAR" property="eduAbstract"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="show_status" property="showStatus"/>
        <result column="chinese_name" jdbcType="VARCHAR" property="chineseName"/>
    </resultMap>


    <sql id="Base_Column_List">
        t_propaganda_edu_id,edu_title,edu_cover_image,edu_cover_video,edu_content,status,create_user_id,create_user_name,edu_type,create_time,
        ins_name,ins_code,app_id,ins_id,edu_abstract,update_date,update_user_id,update_user_name,show_status,chinese_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPropagandaEdu">
        delete
        from t_propaganda_edu
        where t_propaganda_edu_id = #{ tPropagandaEduId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_propaganda_edu where t_propaganda_edu_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.cbkj.diagnosis.beans.business.TPropagandaEdu" useGeneratedKeys="true"
            keyColumn="t_propaganda_edu_id" keyProperty="tPropagandaEduId">
        insert into t_propaganda_edu (<include refid="Base_Column_List"/>) values
        (#{tPropagandaEduId},#{eduTitle},#{eduCoverImage},#{eduCoverVideo},#{eduContent},#{status},#{createUserId},#{createUserName},#{eduType},#{createTime},
        #{insName},#{insCode},#{appId},#{insId},#{eduAbstract},
        #{updateDate},#{updateUserId},#{updateUserName},#{showStatus},#{chineseName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_propaganda_edu (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.tPropagandaEduId},#{item.eduTitle},#{item.eduCoverImage},#{item.eduCoverVideo},#{item.eduContent},#{item.status},
            #{item.createUserId},#{item.createUserName},,#{item.eduType},#{item.createTime},
            #{item.insName},#{item.insCode},#{item.appId},#{item.insId},#{item.eduAbstract},
            #{item.updateDate},#{item.updateUserId},#{item.updateUserName},#{item.showStatus},#{item.chineseName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPropagandaEdu">
        update t_propaganda_edu
        <set>
            <if test="eduTitle != null">
                edu_title = #{ eduTitle },
            </if>
            <if test="eduCoverImage != null">
                edu_cover_image = #{ eduCoverImage },
            </if>
            <if test="eduCoverVideo != null">
                edu_cover_video = #{ eduCoverVideo },
            </if>
            <if test="eduContent != null">
                edu_content = #{ eduContent },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="insName != null">
                ins_name = #{ insName },
            </if>
            <if test="insCode != null">
                ins_Code = #{ insCode },
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="insId != null">
                ins_id = #{insId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="eduAbstract != null and eduAbstract != ''">
                edu_abstract = #{eduAbstract},
            </if>

            <if test="updateDate != null ">
                update_date = #{updateDate},
            </if>

            <if test="updateUserId != null and updateUserId != ''">
                update_user_id = #{updateUserId},
            </if>

            <if test="updateUserName != null and updateUserName != ''">
                update_user_name = #{updateUserName},
            </if>

            <if test="showStatus != null">
                show_status = #{showStatus},
            </if>
            <if test="chineseName != null and chineseName != ''">
                chinese_name = #{chineseName},
            </if>
            <if test="eduType != null">
                edu_type = #{eduType},
            </if>
        </set>
        where t_propaganda_edu_id = #{ tPropagandaEduId }
        <!--         <if test="createUserId != null and createUserId !=''">
                         and create_user_id = #{ createUserId }
                 </if>
                 -->
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_propaganda_edu where t_propaganda_edu_id = #{tPropagandaEduId} order by update_date desc,create_time desc
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.TPropagandaEdu"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from t_propaganda_edu as a
        <where>
            <if test="userId != null and userId != '' ">
                (
                (
                SELECT COUNT(*) FROM  t_propaganda_edu_dis_mapping AS tpedm

                WHERE a.t_propaganda_edu_id = tpedm.t_propaganda_edu_id  AND EXISTS  ( SELECT 1 FROM sys_admin_info_dis_mapping AS sdidm WHERE sdidm.user_id = #{userId} AND sdidm.dis_id = tpedm.dis_id )
                )>0 or a.create_user_id = #{userId}

                )  and
            </if>
            status != '1'
            <if test=" eduTitle != null and eduTitle!='' ">
                and edu_title like CONCAT('%',trim(#{eduTitle}),'%')
            </if>
                <if test="showStatus != null">
                    and a.show_status = #{showStatus}
                </if>
        </where>
        order by update_date desc ,create_time desc
    </select>
    <select id="getObjectByHealthEducationDetails"
            resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.beans.his.HealthEducationDetails">
        SELECT tpe.t_propaganda_edu_id,
               tpe.edu_title,
               tpe.edu_cover_image,
               tpe.edu_cover_video,
               tpe.edu_content,
               tpe.edu_type,
               tpe.chinese_name,
               tpe.ins_name,
               tpe.edu_abstract,
               srtp.task_excute_time as create_time
        from t_propaganda_edu as tpe
                 join s_road_task_patients as srtp on (srtp.road_execute_event_content_id = tpe.t_propaganda_edu_id)

        where tpe.t_propaganda_edu_id = #{roadExecuteEventContentId}
          and srtp.task_patients_id = #{taskPatientsId}
    </select>

</mapper>