<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TBusinessProposalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TBusinessProposal">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_name" property="appName" />
        <result column="ins_code" property="insCode" />
        <result column="ins_name" property="insName" />
        <result column="proposal_title" property="proposalTitle" />
        <result column="proposal_content" property="proposalContent" />
        <result column="proposal_liaison" property="proposalLiaison" />
        <result column="proposal_type" property="proposalType" />
        <result column="proposal_receive_state" property="proposalReceiveState" />
        <result column="proposal_receive_opinion" property="proposalReceiveOpinion" />
        <result column="proposal_receive_id" property="proposalReceiveId" />
        <result column="proposal_receive_name" property="proposalReceiveName" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_user_id" property="deleteUserId" />
        <result column="is_del" property="isDel" />
        <result column="source_from" property="sourceFrom" />
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.beans.business.adviceVo.GetAdviceListRes">
        <id column="id" property="id" />
        <result column="proposal_title" property="proposalTitle" />
        <result column="proposal_content" property="proposalContent" />
        <result column="proposal_receive_opinion" property="proposalReceiveOpinion" />
        <result column="proposal_type" property="proposalType" />
        <result column="proposal_receive_state" property="proposalReceiveState" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="proposal_liaison" property="proposalLiaison" />
        <result column="source_from" property="sourceFrom" />
        <collection property="adviceImages" ofType="com.cbkj.diagnosis.beans.business.adviceVo.ImageUploadRes">
            <result column="urlId" property="urlId" />
            <result column="url" property="url" />
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, app_name, ins_code, ins_name, proposal_title, proposal_content, proposal_liaison, proposal_type, proposal_receive_state, proposal_receive_opinion, proposal_receive_id, proposal_receive_name, create_time, create_user_id, create_user_name, update_time, update_user_id, delete_time, delete_user_id, is_del
    </sql>
    <select id="getAdviceList" resultMap="BaseResultMap2" parameterType="com.cbkj.diagnosis.beans.business.TBusinessProposal">
        select
        a.id,
        a.proposal_title,
        a.proposal_content,
        a.proposal_liaison,
        a.proposal_type,
        a.proposal_receive_state,
        a.proposal_receive_opinion,
        a.proposal_receive_id,
        a.proposal_receive_name,
        a.proposal_receive_opinion,
        a.create_time,
        a.create_user_id,
        a.create_user_name,
        a.proposal_liaison,
        a.source_from
        from t_business_proposal as a
        <where>
                                                                             a.is_del = 0
            <if test="createUserId != null and createUserId != ''">
                and a.create_user_id = #{createUserId}
            </if>
            <if test="proposalReceiveState != null ">
                and a.proposal_receive_state = #{proposalReceiveState}
            </if>
        </where>
order by a.create_time desc
    </select>
    <select id="getAdviceImagesList" resultType="com.cbkj.diagnosis.beans.business.adviceVo.ImageUploadRes" parameterType="String">
        select
        b.id as urlId,
        b.annex_path as url
        from  t_business_annex as b
where b.annex_foreign_id = #{id} and b.is_del = 0
    </select>

</mapper>
