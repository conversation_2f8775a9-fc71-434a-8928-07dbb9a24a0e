<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPreDiagnosisFormMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisForm">
        <id column="dia_id" jdbcType="VARCHAR"  property="diaId" />
        <result column="chinese_dis_name" jdbcType="VARCHAR" property="chineseDisName" />
        <result column="west_dis_name" jdbcType="VARCHAR" property="westDisName" />
        <result column="total_question_num" jdbcType="INTEGER" property="totalQuestionNum" />
        <result column="longest_question_num" jdbcType="INTEGER" property="longestQuestionNum" />
        <result column="shortest_question_num" jdbcType="INTEGER" property="shortestQuestionNum" />
        <result column="dia_type" jdbcType="VARCHAR" property="diaType" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername" />
        <result column="del_date" jdbcType="TIMESTAMP" property="delDate" />
        <result column="del_user" jdbcType="VARCHAR" property="delUser" />
        <result column="del_username" jdbcType="VARCHAR" property="delUsername" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="form_type" jdbcType="VARCHAR" property="formType" />
        <result column="form_name" jdbcType="VARCHAR" property="formName" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_id" jdbcType="VARCHAR" property="insId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="form_code" jdbcType="VARCHAR" property="formCode" />
        <result column="show_status" property="showStatus" />
        <result column="cue_word_status" property="cueWordStatus" />
        <result column="cue_word_text" property="cueWordText" />
        <result column="cue_word_trans" property="cueWordTrans" />
    </resultMap>


    <sql id="Base_Column_List">
    dia_id,chinese_dis_name,west_dis_name,total_question_num,longest_question_num,shortest_question_num,dia_type,
        create_date,create_user,create_username,update_date,update_user,update_username,del_date,del_user,del_username,
        status,form_type,form_name,app_id,ins_id,ins_code,form_code,show_status,cue_word_status,cue_word_text,cue_word_trans
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisForm">
        delete from t_pre_diagnosis_form where dia_id = #{ diaId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_pre_diagnosis_form where dia_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteFormMapping">
        delete from t_pre_diagnosis_dis_mapping where dia_id = #{diaId}
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisForm" >
        insert into t_pre_diagnosis_form (<include refid="Base_Column_List" />)
            select
        #{diaId},#{chineseDisName},#{westDisName},#{totalQuestionNum},#{longestQuestionNum},#{shortestQuestionNum},#{diaType},#{createDate},#{createUser},#{createUsername},
         #{updateDate},#{updateUser},#{updateUsername},#{delDate},#{delUser},#{delUsername},#{status},#{formType},#{formName},#{appId},#{insId},#{insCode},
         #{formCode},#{showStatus}
        ,#{cueWordStatus},#{cueWordText},#{cueWordTrans}
        from dual where not exists (select 1 from t_pre_diagnosis_form where form_code = #{formCode} and (status='0' or status='2' ))
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_pre_diagnosis_form (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.diaId},#{item.chineseDisName},#{item.westDisName},#{item.totalQuestionNum},#{item.longestQuestionNum},#{item.shortestQuestionNum},#{item.diaType},#{item.createDate},
             #{item.createUser},#{item.createUsername},#{item.updateDate},#{item.updateUser},
             #{item.updateUsername},#{item.delDate},#{item.delUser},#{item.delUsername},#{item.status},#{item.formType},#{item.formName},#{item.appId},#{item.insId},
             #{item.insCode},#{item.formCode},#{item.showStatus}
            ,#{item.cueWordStatus},#{item.cueWordText},#{item.cueWordTrans})
        </foreach>
    </insert>

    <insert id="insertPaperMapping" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMappingV">
        insert into `t_pre_diagnosis_dis_mapping` (`dia_id`, `dis_id`,dis_name,dis_type,dis_code) values (#{diaId}, #{disId},#{disName},#{disType},#{disCode})
    </insert>
    <insert id="insertRe" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisForm" >
        replace into t_pre_diagnosis_form (<include refid="Base_Column_List" />) values
        (#{diaId},#{chineseDisName},#{westDisName},#{totalQuestionNum},#{longestQuestionNum},#{shortestQuestionNum},#{diaType},#{createDate},#{createUser},#{createUsername},
        #{updateDate},#{updateUser},#{updateUsername},#{delDate},#{delUser},#{delUsername}
        ,#{status},#{formType},#{formName},#{appId},#{insId},#{insCode},
         #{formCode},#{showStatus},
        #{cueWordStatus},#{cueWordText},#{cueWordTrans}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisForm">
        update t_pre_diagnosis_form
        <set>
             <if test="chineseDisName != null">
                chinese_dis_name = #{ chineseDisName },
             </if>
             <if test="westDisName != null">
                west_dis_name = #{ westDisName },
             </if>
             <if test="totalQuestionNum != null">
                total_question_num = #{ totalQuestionNum },
             </if>
             <if test="longestQuestionNum != null">
                longest_question_num = #{ longestQuestionNum },
             </if>
             <if test="shortestQuestionNum != null">
                shortest_question_num = #{ shortestQuestionNum },
             </if>
             <if test="diaType != null">
                dia_type = #{ diaType },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>

             <if test="createUsername != null">
                create_username = #{ createUsername },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
             <if test="updateUser != null">
                update_user = #{ updateUser },
             </if>
             <if test="updateUsername != null">
                update_username = #{ updateUsername },
             </if>
             <if test="delDate != null">
                del_date = #{ delDate },
             </if>
             <if test="delUser != null">
                del_user = #{ delUser },
             </if>
             <if test="delUsername != null">
                del_username = #{ delUsername },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
            <if test="formName != null">
                form_name = #{formName},
            </if>
            <if test="formType != null">
                form_type = #{formType},
            </if>
            <if test="showStatus != null">
                show_status = #{showStatus},
            </if>
            <if test="cueWordStatus != null">
                cue_word_status = #{cueWordStatus},
            </if>
            <if test="cueWordText != null">
                cue_word_text = #{cueWordText},
            </if>
            <if test="cueWordTrans != null">
                cue_word_trans = #{cueWordTrans},
            </if>
            <if test="deptIds != null">
                dept_ids = #{deptIds},
            </if>
            <if test="deptDiaName != null">
                dept_dia_name = #{deptDiaName},
            </if>
        </set>
        where dia_id = #{ diaId }
        <if test="createUser != null and createUser != ''">
                and create_user = #{ createUser }
        </if>
    </update>
    <update id="updateStatusToDeleteByDiaId" parameterType="String">
        update t_pre_diagnosis_form set status = '1' ,cue_word_status = 1 where dia_id = #{ diaId }
    </update>
    <update id="updateFormCodeIfNull" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisForm">
        update t_pre_diagnosis_form set form_code = #{formCode} where dia_id = #{ diaId } and form_code is null
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_pre_diagnosis_form where dia_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisForm" resultMap="BaseResultMap">
        SELECT dia_id,chinese_dis_name,west_dis_name,total_question_num,longest_question_num,
               shortest_question_num,dia_type,create_date,create_user,create_username,update_date,update_user,
               update_username,del_date,del_user,del_username,status,form_type,form_name,app_id,ins_id,ins_code,form_code,show_status
        from t_pre_diagnosis_form
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getobjectByDiagnosisForm" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisForm">
        SELECT <include refid="Base_Column_List" />

        from t_pre_diagnosis_form
        <where>
            status = '0'
            <if test=" chineseDisName != null and chineseDisName!='' ">
                and (
                chinese_dis_name like CONCAT('%',trim(#{chineseDisName}),'%')

                )
            </if>
            <if test=" diaType != null and diaType!='' ">
                and dia_type = #{diaType}
            </if>
            <if test=" formName != null and formName!='' ">
                and form_name = #{formName}
            </if>
            <if test=" formType != null and formType!='' ">
                and form_type = #{formType}
            </if>
        </where>
limit 1
    </select>


    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.service.webapi.business.vo.LookPaperRecive">
        <id column="dia_id" jdbcType="VARCHAR" property="diaId" />
        <result column="chinese_dis_name" jdbcType="VARCHAR" property="chineseDisName" />
        <result column="west_dis_name" jdbcType="VARCHAR" property="westDisName" />
        <result column="dia_type" jdbcType="VARCHAR" property="diaType" />
        <result column="form_type" jdbcType="VARCHAR" property="formType" />
        <result column="form_name" jdbcType="VARCHAR" property="formName" />
        <result column="form_code" jdbcType="VARCHAR" property="formCode" />
        <collection property="questionMobilesList" ofType="com.cbkj.diagnosis.service.webapi.business.vo.LookPaperQuestionMain">
            <result column="question_id" jdbcType="INTEGER" property="questionId" />
            <result column="question_name" jdbcType="VARCHAR" property="questionName" />
            <result column="question_number" jdbcType="INTEGER" property="questionNumber" />
            <result column="question_type" jdbcType="VARCHAR" property="questionType" />
            <result column="date_unit" jdbcType="VARCHAR" property="dateUnit" />
            <result column="question_stem" jdbcType="VARCHAR" property="questionStem" />
            <result column="question_class_type" jdbcType="VARCHAR" property="questionClassType" />
            <result column="follow_up_class_type_code" jdbcType="VARCHAR" property="followUpClassTypeCode" />
            <result column="question_option_groups" jdbcType="VARCHAR" property="questionOptionGroups" />
            <result column="question_unit" jdbcType="VARCHAR" property="questionUnit" />
            <result column="question_code" jdbcType="VARCHAR" property="questionCode" />
            <result column="question_img" jdbcType="VARCHAR" property="questionImg" />
            <result column="question_img_max_num" jdbcType="VARCHAR" property="questionImgMaxNum" />
            <result column="question_dimension_code" jdbcType="VARCHAR" property="questionDimensionCode" />
            <result column="question_dimension_name" jdbcType="VARCHAR" property="questionDimensionName" />
            <collection property="questionRangeList" ofType="com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestionRange">
                <result column="question_range_title" jdbcType="VARCHAR" property="questionRangeTitle" />
                <result column="question_range_id" jdbcType="INTEGER" property="questionRangeId" />
                <result column="question_range_min" jdbcType="DECIMAL" property="questionRangeMin" />
                <result column="question_range_max" jdbcType="DECIMAL" property="questionRangeMax" />
                <result column="question_range_decimal" jdbcType="INTEGER" property="questionRangeDecimal" />
                <result column="question_range_default" jdbcType="DECIMAL" property="questionRangeDefault" />
                <result column="question_range_unit" jdbcType="VARCHAR" property="questionRangeUnit" />
            </collection>
            <collection property="questionOptionSaveList" ofType="com.cbkj.diagnosis.service.webapi.business.vo.QuestionOptionSave">
                <result column="option_id" jdbcType="VARCHAR" property="optionId" />
                <result column="option_name" jdbcType="VARCHAR" property="optionName" />
                <result column="option_sort" jdbcType="VARCHAR" property="optionSort" />
                <result column="symptom_id" jdbcType="VARCHAR" property="symptomId" />
                <result column="question_id" jdbcType="VARCHAR" property="questionId" />
                <result column="option_type" jdbcType="VARCHAR" property="optionType" />
                <result column="option_content" jdbcType="VARCHAR" property="optionContent" />
                <result column="option_image" jdbcType="VARCHAR" property="optionImage" />
                <result column="option_fill_blank" jdbcType="VARCHAR" property="optionFillBlank" />
                <result column="option_fill_check" jdbcType="VARCHAR" property="optionFillCheck" />
                <result column="option_code" jdbcType="VARCHAR" property="optionCode" />
                <result column="option_structure_value" jdbcType="VARCHAR" property="optionStructureValue" />
                <result column="option_dimension_score" jdbcType="DOUBLE" property="optionDimensionScore" />
                <result column="option_dimension_score_switch" jdbcType="INTEGER" property="optionDimensionScoreSwitch" />
                <result column="follow_up_visit_status" jdbcType="INTEGER" property="followUpVisitStatus" />
                <result column="follow_up_visit_id" jdbcType="INTEGER" property="followUpVisitId" />
              <!--  <result column="option_follow_event_code" jdbcType="VARCHAR" property="optionFollowEventCode" />-->
                <collection property="questionSkipSaveList" ofType="String" javaType="List" >
                    <result column="skip_question_id"/>
                </collection>
                <collection property="questionChildSaveList" ofType="String" javaType="List" >
                    <result column="child_question_id"/>
                </collection>
                <collection property="optionMutexList" ofType="String" javaType="List" >
                    <result column="mutex_option_id"/>
                </collection>
                <collection property="optionFollowEventCode" ofType="String" javaType="List" >
                    <result column="dic_code"/>
                </collection>
            </collection>
        </collection>
    </resultMap>

    <select id="getLookPaper" resultMap="BaseResultMap2" parameterType="String">
        select
        a.dia_id ,
        a.chinese_dis_name ,
        a.west_dis_name ,
        a.dia_type ,
        a.form_type,
        a.form_name,
        a.form_code,
        b.question_id,
        b.question_name,
        b.question_number,
        b.question_type,
        b.date_unit,
        b.question_stem,
        b.question_class_type,
        b.follow_up_class_type_code,
        b.question_option_groups ,
        b.question_unit,
        b.question_code,
        a2.option_content,
        a2.option_image,
        a2.option_id,
        a2.question_id,
        a2.option_name,
        a2.option_sort,
        a2.symptom_id,
        a2.option_type,
        a2.option_fill_blank,
        a2.option_fill_check,
        a2.option_code,
        a2.option_structure_value,
        b.question_img,
        a2.option_dimension_score,
        a2.option_dimension_score_switch,
        b.question_img_max_num,
        b.question_dimension_code,
        b.question_dimension_name,
       <!-- a2.option_follow_event_code, -->
        b2.skip_question_id,
        c2.child_question_id,
        d2.mutex_option_id,
        e2.dic_code,
        a2.follow_up_visit_status,
        a2.follow_up_visit_id,
        tpdqr.question_range_id,
        tpdqr.question_range_title,
        tpdqr.question_range_min,
        tpdqr.question_range_max,
        tpdqr.question_range_decimal,
        tpdqr.question_range_default,
        tpdqr.question_range_unit
        from t_pre_diagnosis_form as a join t_pre_diagnosis_question as b on (a.dia_id = b.dia_id)
            left join t_pre_diagnosis_question_range as tpdqr on(tpdqr.question_id = b.question_id)
            left join  t_pre_diagnosis_option a2 on(a2.question_id = b.question_id)


            left  join  t_pre_diagnosis_skip b2 on(a2.option_id = b2.option_id)

            left join   t_pre_diagnosis_child c2 on(c2.option_id = a2.option_id)

            left join   t_pre_diagnosis_option_mutex d2 on(d2.option_id = a2.option_id)
            left join   t_pre_diagnosis_option_event as e2 on(e2.option_id = a2.option_id)


        where a.dia_id = #{diaId} <!-- and a.status in ('0','2') -->
    </select>

    <resultMap id="BaseResultMap3" type="com.cbkj.diagnosis.service.webapi.business.vo.QuestionOptionSave">
        <id column="option_id" jdbcType="VARCHAR" property="optionId" />
        <result column="option_name" jdbcType="VARCHAR" property="optionName" />
        <result column="option_sort" jdbcType="VARCHAR" property="optionSort" />
        <result column="symptom_id" jdbcType="VARCHAR" property="symptomId" />
        <result column="question_id" jdbcType="VARCHAR" property="questionId" />
        <result column="option_type" jdbcType="VARCHAR" property="optionType" />
        <collection property="questionSkipSaveList" ofType="String" javaType="List" >
            <result column="skip_question_id"/>
        </collection>
    </resultMap>

    <select id="getQuestionOptionSaveList" resultMap="BaseResultMap3" parameterType="String">
        select
a.option_id,
a.question_id,
a.option_name,
a.option_sort,
a.symptom_id,
a.option_type,
b.skip_question_id
            from t_pre_diagnosis_option a left  join  t_pre_diagnosis_skip b on(a.option_id = b.option_id)
where a.question_id = #{questionId}

    </select>

    <select id="getPaperList" resultMap="BaseResultMap" parameterType="Map">
        select
        a.dia_id,
        a.chinese_dis_name,
        a.west_dis_name,
        a.total_question_num,
        a.longest_question_num,
        a.shortest_question_num,
        a.dia_type,
        a.form_type,
        a.form_code,a.update_date,a.update_user,a.update_username,a.show_status,
        a.cue_word_status,a.cue_word_text,a.cue_word_trans
        from t_pre_diagnosis_form a
            <if test="disId != null and disId != ''">
                join t_pre_diagnosis_dis_mapping b on(a.dia_id = b.dia_id and b.dis_id = #{disId})
            </if>
        where
            <if test="userId != null and userId != '' ">
            (
                (
                SELECT COUNT(*) FROM  t_pre_diagnosis_dis_mapping AS tpddm

                WHERE a.dia_id = tpddm.dia_id  AND EXISTS  ( SELECT 1 FROM sys_admin_info_dis_mapping AS sdidm WHERE sdidm.user_id = #{userId} AND sdidm.dis_id = tpddm.dis_id )
                )>0 or a.create_user = #{userId}

                )     and
            </if>
        a.status !='1' and a.form_type = '1'
        order by a.update_date DESC,a.create_date DESC
    </select>

    <select id="getSuiFangPaperList" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.service.webapi.business.vo.GetSuiFangPaperList">
        select
        a.dia_id,
        a.chinese_dis_name,
        a.west_dis_name,
        a.total_question_num,
        a.longest_question_num,
        a.shortest_question_num,
        a.dia_type,
        a.form_name,
        a.form_type,
        a.form_code,a.update_date,a.update_user,a.update_username,a.show_status,
        a.cue_word_status,a.cue_word_text,a.cue_word_trans
        from t_pre_diagnosis_form a

       <where>
        a.status !='1'  and form_type = '2'
        <if test="sroadIds != null">
            AND
            EXISTS
            (

            SELECT 1 FROM `s_road_task` AS a1 JOIN `s_road` AS a2 ON (a1.`s_road_id` = a2.`s_road_id`)

            JOIN s_road_execute AS a3 ON (a3.`s_road_id` = a2.`s_road_id`) JOIN `s_road_execute_contents` AS a4 ON
            (a4.`road_execute_id` = a3.`road_execute_id`)

            WHERE a4.`road_execute_event_content_id` = a.`dia_id` AND a1.`s_road_task_id` IN(
            <foreach collection="sroadIds" item="sroadId" separator=",">
                #{sroadId}
            </foreach>
            )
            )
        </if>
        <if test="userId != null and userId != '' ">
            and(
            (
            SELECT COUNT(*) FROM  t_pre_diagnosis_dis_mapping AS tpddm

            WHERE a.dia_id = tpddm.dia_id  AND EXISTS  ( SELECT 1 FROM sys_admin_info_dis_mapping AS sdidm WHERE sdidm.user_id = #{userId} AND sdidm.dis_id = tpddm.dis_id )
            )>0 or a.create_user = #{userId}

            )
        </if>

          <if test="formName != null and formName != ''">
            and  form_name like concat('%',#{formName},'%')
        </if>
        <if test="diaType != null and diaType != ''">
            and  dia_type =#{diaType}
        </if>
        <if test="showStatus != null">
            and  show_status =#{showStatus}
        </if>
       </where>
        order by  a.update_date DESC,a.create_date DESC
    </select>

    <resultMap id="getPaperMappingListByDiaId" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMappingV">
        <id column="dia_id" jdbcType="VARCHAR" property="diaId" />
        <id column="dis_id" jdbcType="VARCHAR" property="disId" />
        <id column="dis_name" jdbcType="VARCHAR" property="disName" />
        <id column="dis_type" jdbcType="VARCHAR" property="disType" />
    </resultMap>
    <select id="getPaperMappingListByDiaId"
            resultMap="getPaperMappingListByDiaId" >

select dia_id,dis_id,dis_name,dis_type from t_pre_diagnosis_dis_mapping where dia_id = #{diaId}

    </select>
    <select id="getLastSuiFangFormDiaId" resultType="java.lang.String">
        select dia_id from t_pre_diagnosis_form where dia_type in ('3','4') order by create_date desc limit 1;
    </select>
    <select id="getCountComputer" resultType="java.lang.Integer" parameterType="com.cbkj.diagnosis.service.mobileapi.vo.CountComputer">

        SELECT count(*)
        FROM `t_pre_diagnosis_child`
        WHERE dia_id = #{diaId}
          AND master_question_id = #{masterQuestionId}
          AND child_question_id > #{questionId}
    </select>
    <select id="getOneByDisId" resultMap="BaseResultMap" parameterType="String">
        SELECT a.dia_id
        from t_pre_diagnosis_form a
                 join t_pre_diagnosis_dis_mapping b on (a.dia_id = b.dia_id and a.form_type='1' and b.dis_type = '1')
        where b.dis_id = #{disId} AND a.status = 0 limit 1
    </select>
    <select id="selctCountFormCode" resultType="java.lang.Integer" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisForm">
        select count(*) from t_pre_diagnosis_form where form_code = #{formCode} and (status = '0' or status = '2')
    </select>
    <select id="getObjectByFormCode"  resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
            from t_pre_diagnosis_form where form_code = #{formCode} and status = '0' limit 1
    </select>
    <select id="checkDisMappingIsMu" resultType="java.lang.Integer" parameterType="com.cbkj.diagnosis.beans.dao.CheckDisMappingIsMu">

        SELECT COUNT(*) FROM `t_pre_diagnosis_dis_mapping` AS mapping ,`t_pre_diagnosis_form` AS form

        WHERE mapping.`dia_id`=form.`dia_id` AND form.`status`='0'
        <if test="diaId != null and diaId != ''">
            AND mapping.`dia_id` != #{diaId}
        </if>
        <if test="disId != null and disId != ''">
            AND mapping.`dis_id` = #{disId}
        </if>
        <if test="formType != null and formType != ''">
            AND form.`form_type` = #{formType}
        </if>

    </select>

    <select id="getDiaIdentification" parameterType="String" resultType="com.cbkj.diagnosis.beans.business.requestvo.DiaIdentificationVo">
        SELECT
            dia_id AS diaId,
            dept_ids AS deptIds,
            dept_dia_name AS deptDiaName
        FROM `t_pre_diagnosis_form` WHERE dia_id = #{diaId}
    </select>
    <select id="getDiaIdentificationItemList" parameterType="String" resultType="com.cbkj.diagnosis.beans.business.TPreDiagnosisDisMapping">
        SELECT
            dia_id AS diaId,
            dis_id AS disId,
            dis_name AS disName,
            dis_code AS disCode,
            IFNULL(dis_summary,(SELECT b.dis_summary FROM t_disease b WHERE a.dis_code = b.dis_code)) AS disSummary,
            is_show_summary AS isShowSummary
        FROM t_pre_diagnosis_dis_mapping a
        WHERE a.dia_id = #{diaId} AND a.dis_type= '1'
    </select>


</mapper>