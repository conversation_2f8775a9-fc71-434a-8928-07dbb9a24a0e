<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisOption">
        <id column="option_id" jdbcType="INTEGER"  property="optionId" />
        <result column="question_id" jdbcType="INTEGER" property="questionId" />
        <result column="option_type" jdbcType="VARCHAR" property="optionType" />
        <result column="option_code" jdbcType="VARCHAR" property="optionCode" />
        <result column="option_name" jdbcType="VARCHAR" property="optionName" />
        <result column="symptom_id" jdbcType="VARCHAR" property="symptomId" />
        <result column="option_content" jdbcType="VARCHAR" property="optionContent" />
        <result column="option_image" jdbcType="VARCHAR" property="optionImage" />
        <result column="option_sort" jdbcType="INTEGER" property="optionSort" />
        <result column="option_fill_blank" jdbcType="VARCHAR" property="optionFillBlank" />
        <result column="option_fill_check" jdbcType="INTEGER" property="optionFillCheck" />
        <result column="option_structure_value" jdbcType="VARCHAR" property="optionStructureValue" />
        <result column="option_structure_save_blank" jdbcType="INTEGER" property="optionStructureSaveBlank" />
        <result column="option_structure_save_patient" jdbcType="INTEGER" property="optionStructureSavePatient" />
        <result column="option_dimension_score" jdbcType="DOUBLE" property="optionDimensionScore" />
        <result column="option_dimension_score_switch" jdbcType="INTEGER" property="optionDimensionScoreSwitch" />
        <result column="follow_up_visit_status" jdbcType="INTEGER" property="followUpVisitStatus" />
        <result column="follow_up_visit_id" jdbcType="INTEGER" property="followUpVisitId" />

    </resultMap>


    <sql id="Base_Column_List">
    option_id,question_id,option_type,option_name,symptom_id,option_sort,option_content,option_image,
        option_fill_blank,option_fill_check,option_code,option_structure_value,option_structure_save_blank,option_structure_save_patient,
    option_dimension_score,option_dimension_score_switch,follow_up_visit_status,follow_up_visit_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisOption">
        delete from t_pre_diagnosis_option where option_id = #{ optionId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_pre_diagnosis_option where option_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteByQuestionId">
        delete from t_pre_diagnosis_option where question_id = #{questionId}
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisOption" useGeneratedKeys="true" keyColumn="option_id" keyProperty="optionId">
        insert into t_pre_diagnosis_option (<include refid="Base_Column_List" />) values
        (#{optionId},#{questionId},#{optionType},#{optionName},#{symptomId},#{optionSort},#{optionContent},
         #{optionImage},#{optionFillBlank},#{optionFillCheck},
         #{optionCode},#{optionStructureValue},#{optionStructureSaveBlank},#{optionStructureSavePatient},
         #{optionDimensionScore},#{optionDimensionScoreSwitch},#{followUpVisitStatus},#{followUpVisitId}
         )
    </insert>

    <insert id="insertList" parameterType="List" useGeneratedKeys="true" keyColumn="option_id" keyProperty="optionId">
        insert into t_pre_diagnosis_option (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.optionId},#{item.questionId},#{item.optionType},#{item.optionName},#{item.symptomId},#{item.optionSort},,#{item.optionContent},#{item.optionImage},
            #{item.optionFillBlank},#{item.optionFillCheck},#{item.optionCode},#{item.optionStructureValue},
            #{item.optionStructureSaveBlank},#{item.optionStructureSavePatient},#{item.optionDimensionScore},#{item.optionDimensionScoreSwitch},
             #{item.followUpVisitStatus},#{item.followUpVisitId}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisOption">
        update t_pre_diagnosis_option
        <set>
             <if test="questionId != null">
                question_id = #{ questionId },
             </if>
             <if test="optionType != null and optionType != '' ">
                option_type = #{ optionType },
             </if>
             <if test="optionName != null and optionName != ''">
                option_name = #{ optionName },
             </if>
             <if test="symptomId != null and symptomId != ''">
                symptom_id = #{ symptomId },
             </if>
             <if test="optionSort != null">
                option_sort = #{ optionSort },
             </if>
            <if test="optionContent != null and optionContent != '' ">
                option_content = #{optionContent},
            </if>
            <if test="optionImage != null and optionImage != ''">
                option_image = #{optionImage},
            </if>
            <if test="optionFillBlank != null and optionFillBlank != ''">
                option_fill_blank = #{optionFillBlank},
            </if>
            <if test="optionFillCheck != null">
                option_fill_check = #{optionFillCheck},
            </if>
            <if test="optionCode != null and optionCode != ''">
                option_code = #{optionCode},
            </if>
            <if test="optionStructureValue != null ">
                option_structure_value = #{optionStructureValue},
            </if>
            <if test="optionStructureSaveBlank != null">
                option_structure_save_blank = #{optionStructureSaveBlank},
            </if>
            <if test="optionStructureSavePatient != null">
                option_structure_save_patient = #{optionStructureSavePatient},
            </if>
            <if test="optionDimensionScore != null">
                option_dimension_score = #{optionDimensionScore},
            </if>
            <if test="optionDimensionScoreSwitch != null">
                option_dimension_score_switch = #{optionDimensionScoreSwitch},
            </if>
            <if test="followUpVisitStatus != null">
                follow_up_visit_status = #{followUpVisitStatus},
            </if>
            <if test="followUpVisitId != null">
                follow_up_visit_id = #{followUpVisitId},
            </if>
        </set>
        where option_id = #{ optionId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_pre_diagnosis_option where option_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisOption" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_pre_diagnosis_option
        <where>
            <if test=" optionName != null and optionName!='' ">
                and option_name like CONCAT('%',trim(#{optionName}),'%')
            </if>
        <if test="questionId != null and questionId != ''">
            and question_id = #{questionId}
        </if>
        </where>
    </select>

    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.service.common.vo.QuestionOption">


        <result column="option_name" jdbcType="VARCHAR" property="optionName" />
        <result column="option_id" jdbcType="VARCHAR" property="optionId" />
        <result column="option_sort" jdbcType="VARCHAR" property="optionSort" />
        <result column="symptom_id" jdbcType="VARCHAR" property="symptomId" />
        <result column="option_type" jdbcType="VARCHAR" property="optionType" />
        <result column="option_content" jdbcType="VARCHAR" property="optionContent" />
        <result column="option_image" jdbcType="VARCHAR" property="optionImage" />
        <result column="option_fill_blank" jdbcType="VARCHAR" property="optionFillBlank" />
        <result column="option_fill_check" jdbcType="INTEGER" property="optionFillCheck" />
        <result column="option_code" jdbcType="VARCHAR" property="optionCode" />
        <result column="option_structure_value" jdbcType="VARCHAR" property="optionStructureValue" />
        <collection property="todayPatientEventList" ofType="com.cbkj.diagnosis.service.common.vo.TodayPatientEvent">
            <result column="dic_id" jdbcType="VARCHAR" property="dicId"/>
            <result column="dic_code" jdbcType="VARCHAR" property="dicCode"/>
            <result column="dic_name" jdbcType="VARCHAR" property="dicName"/>
            <result column="dic_shot_name" jdbcType="VARCHAR" property="simpleLabel"/>
            <result column="style" jdbcType="VARCHAR" property="style"/>
        </collection>
    </resultMap>

    <select id="getObjectByQuestionId" parameterType="Integer" resultMap="BaseResultMap2">
    select
        a.option_name,
        a.option_id,
        a.option_sort,
        a.symptom_id,
        a.option_type,
        a.option_content,
        a.option_image,
        a.option_fill_blank,
        a.option_fill_check,
        a.option_code,
        a.option_structure_value,
        sd.dic_id,
        sd.dic_code,
        sd.dic_name,
        sd.dic_shot_name,
        sd.dic_value as style
    from t_pre_diagnosis_option as a
             LEFT JOIN `t_pre_diagnosis_option_event` AS tpdoe ON(tpdoe.option_id = a.option_id )
             left join sys_dic as sd on(sd.dic_id = tpdoe.dic_id)
    where a.question_id = #{questionId}
    </select>
    <select id="getDiagnosisEventCode" resultType="HashMap" parameterType="collection">
        SELECT dic_id dicId,dic_code dicCode,dic_name dicName from `t_pre_diagnosis_option_event`
        where <foreach collection="array" item="item" index="index" open="option_id in (" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>