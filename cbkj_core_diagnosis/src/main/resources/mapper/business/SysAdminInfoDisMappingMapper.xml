<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.SysAdminInfoDisMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.SysAdminInfoDisMapping">
        <result column="user_id" property="userId" />
        <result column="dis_id" property="disId" />
        <result column="dis_name" property="disName" />
        <result column="sort" property="sort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, dis_id, dis_name, sort
    </sql>
    <select id="selectDisCodeList" resultType="com.cbkj.diagnosis.beans.DoctorDocDisCode" parameterType="com.cbkj.diagnosis.beans.business.statictis.UserDisQuery">
        select td.dis_code as disCode,td.dis_name as disName,td.dis_id as disId from sys_admin_info_dis_mapping as saidm  join t_disease as td on(td.dis_id=saidm.dis_id)
        <where>
            <if test="disName != null and disName != ''">
                and saidm like CONCAT('%',trim(#{disName}),'%')
            </if>
            <if test="userId != null and userId != ''">
                and saidm.user_id = #{userId}
            </if>
        </where>
    </select>
    <select id="getDisFromGroupBy" resultType="com.cbkj.diagnosis.beans.DoctorDocDisCode" parameterType="String">
        select td.dis_id as disId,td.dis_code as disCode,td.dis_name as disName from sys_admin_info_dis_mapping as saidm  join t_disease as td on(td.dis_id=saidm.dis_id)
        <where>
            <if test="disName != null and disName != ''">
                and saidm.dis_name like CONCAT('%',trim(#{disName}),'%')
            </if>

        </where>
        group by td.dis_id
    </select>

</mapper>
