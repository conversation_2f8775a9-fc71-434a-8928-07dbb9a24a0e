<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.TAdminInfoMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.TAdminInfo">
        <id column="USER_ID" jdbcType="VARCHAR"  property="userId" />
        <result column="SEX" jdbcType="VARCHAR" property="sex" />
        <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
        <result column="AGE" jdbcType="VARCHAR" property="age" />
        <result column="OPEN_ID" jdbcType="VARCHAR" property="openId" />
        <result column="WX_NICE_NAME" jdbcType="VARCHAR" property="wxNiceName" />
        <result column="WX_HEAD_IMG" jdbcType="VARCHAR" property="wxHeadImg" />
        <result column="IDENTITY" jdbcType="VARCHAR" property="identity" />
        <result column="SHARE_NUM" jdbcType="INTEGER" property="shareNum" />
        <result column="SHARE_CLICK_NUM" jdbcType="INTEGER" property="shareClickNum" />
        <result column="SHARE_USER_ID" jdbcType="VARCHAR" property="shareUserId" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="EXPERTISE" jdbcType="VARCHAR" property="expertise" />
        <result column="EXPECT" jdbcType="VARCHAR" property="expect" />
        <result column="EXPECTXH" jdbcType="VARCHAR" property="expectxh" />
        <result column="TOKEN_ID" jdbcType="VARCHAR" property="tokenId" />
        <result column="CIRCLE_ID" jdbcType="VARCHAR" property="circleId" />
        <result column="EXPIRESIN" jdbcType="BIGINT" property="expiresin" />
        <result column="PASSWORD" jdbcType="VARCHAR" property="password" />
        <result column="card_number" jdbcType="VARCHAR" property="cardNumber" />
        <result column="weight" jdbcType="VARCHAR" property="weight" />
        <result column="height" jdbcType="VARCHAR" property="weight" />
        <result column="last_records_id" jdbcType="VARCHAR" property="lastRecordsId" />
        <result column="last_chinese_dis_name" jdbcType="VARCHAR" property="lastChineseDisName" />
        <result column="last_west_dis_name" jdbcType="VARCHAR" property="lastWestDisName" />
        <result column="last_sym_name" jdbcType="VARCHAR" property="lastSymName" />
        <result column="master_user_id" jdbcType="VARCHAR" property="masterUserId" />
        <result column="patient_card_type" jdbcType="VARCHAR" property="patientCardType" />
        <result column="health_card_num" jdbcType="VARCHAR" property="healthCardNum" />
        <result column="health_files_num" jdbcType="VARCHAR" property="healthFilesNum" />
        <result column="marital_status" jdbcType="VARCHAR" property="maritalStatus" />
        <result column="education_code" jdbcType="VARCHAR" property="educationCode" />
        <result column="contacts_name" jdbcType="VARCHAR" property="contactsName" />
        <result column="contacts_phone" jdbcType="VARCHAR" property="contactsPhone" />
        <result column="contacts_relationship" jdbcType="VARCHAR" property="contactsRelationship" />
        <result column="last_dept_name" jdbcType="VARCHAR" property="lastDeptName" />
        <result column="last_doctor_id" jdbcType="VARCHAR" property="lastDoctorId" />
        <result column="last_doctor_name" jdbcType="VARCHAR" property="lastDoctorName" />
        <result column="last_records_time" jdbcType="TIMESTAMP" property="lastRecordsTime" />
        <result column="NATION" jdbcType="TIMESTAMP" property="nation" />
        <result column="nationality" jdbcType="TIMESTAMP" property="nationality" />
        <result column="medical_card" jdbcType="VARCHAR" property="medicalCard" />
    </resultMap>

    <sql id="Base_Column_List">
        weight,height,card_number,USER_ID,SEX,USER_NAME,MOBILE,
        AGE,OPEN_ID,WX_NICE_NAME,WX_HEAD_IMG,IDENTITY,SHARE_NUM,
        SHARE_CLICK_NUM,SHARE_USER_ID,CREATE_TIME,EXPERTISE,EXPECT,EXPECTXH,PASSWORD,last_records_id,last_chinese_dis_name,last_west_dis_name,last_sym_name,master_user_id,
            patient_card_type,health_card_num,health_files_num,marital_status,education_code,contacts_name,contacts_phone,
            contacts_relationship,last_dept_name,
            last_doctor_id,last_doctor_name,last_records_time,NATION,nationality,medical_card
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.TAdminInfo">
      update t_admin_info set
      is_del = '1',
      DEL_USERNAME = #{ delUsername },
      DEL_DATE = #{ delDate }
      where USER_ID = #{ userId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_admin_info where USER_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.TAdminInfo">
        insert into t_admin_info (USER_ID,SEX,USER_NAME,MOBILE,AGE,OPEN_ID,WX_NICE_NAME,WX_HEAD_IMG,IDENTITY,SHARE_NUM,SHARE_CLICK_NUM,SHARE_USER_ID,CREATE_TIME,EXPERTISE,EXPECT,EXPECTXH,CIRCLE_ID,PASSWORD,card_number,master_user_id,
                                  patient_card_type,health_card_num,health_files_num,marital_status,education_code,contacts_name,contacts_phone,
                                  contacts_relationship,last_dept_name,
                                  last_records_id,last_chinese_dis_name,last_west_dis_name,last_sym_name,
                                  last_doctor_name,last_doctor_id,last_records_time,NATION,nationality,medical_card

                                  ) values
        (#{userId},#{sex},#{userName},#{mobile},#{age},#{openId},#{wxNiceName},#{wxHeadImg},#{identity},#{shareNum},
         #{shareClickNum},#{shareUserId},#{createTime},#{expertise},#{expect},#{expectxh},#{circleId},#{password},#{cardNumber},#{masterUserId},

         #{patientCardType},#{healthCardNum},#{healthFilesNum},#{maritalStatus},#{educationCode},#{contactsName},#{contactsPhone},
         #{contactsRelationship},#{lastDeptName},
         #{lastRecordsId},#{lastChineseDisName},#{lastWestDisName},#{lastSymName},#{lastDoctorName},#{lastDoctorId},#{lastRecordsTime},#{nation},#{nationality},#{medicalCard}
         )
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_admin_info (USER_ID,SEX,USER_NAME,MOBILE,AGE,OPEN_ID,WX_NICE_NAME,WX_HEAD_IMG,IDENTITY,SHARE_NUM,SHARE_CLICK_NUM,SHARE_USER_ID,CREATE_TIME,EXPERTISE,EXPECT,EXPECTXH,card_number,master_user_id,
        patient_card_type,health_card_num,health_files_num,marital_status,education_code,contacts_name,contacts_phone,
        contacts_relationship,last_dept_name,

        last_records_id,last_chinese_dis_name,last_west_dis_name,last_sym_name,
        last_doctor_name,last_doctor_id,last_records_time,NATION,nationality
                                  ) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.userId},#{item.sex},#{item.userName},#{item.mobile},#{item.age},#{item.openId},#{item.wxNiceName},#{item.wxHeadImg},#{item.identity},#{item.shareNum},#{item.shareClickNum},#{item.shareUserId},
             #{item.createTime},#{item.expertise},#{item.expect},#{item.expectxh},#{item.cardNumber},#{item.masterUserId},
            #{item.patientCardType},#{item.healthCardNum},#{item.healthFilesNum},#{item.maritalStatus},#{item.educationCode},
             #{item.contactsName},#{item.contactsPhone},#{item.contactsRelationship},#{item.lastDeptName},
            #{item.lastRecordsId},#{item.lastChineseDisName},#{item.lastWestDisName},#{item.lastSymName},
            #{item.lastDoctorName},#{item.lastDoctorId},#{item.lastRecordsTime},#{item.nation},#{item.nationality},#{item.medicalCard}
             )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.TAdminInfo">
        update t_admin_info
        <set >
            <if test="sex != null" >
                SEX = #{ sex },
            </if>
            <if test="userName != null" >
                USER_NAME = #{ userName },
            </if>
            <if test="mobile != null" >
                MOBILE = #{ mobile },
            </if>
            <if test="age != null" >
                AGE = #{ age },
            </if>
            <if test="openId != null" >
                OPEN_ID = #{ openId },
            </if>
            <if test="wxNiceName != null" >
                WX_NICE_NAME = #{ wxNiceName },
            </if>
            <if test="wxHeadImg != null" >
                WX_HEAD_IMG = #{ wxHeadImg },
            </if>
            <if test="identity != null" >
                IDENTITY = #{ identity },
            </if>
            <if test="shareNum != null" >
                SHARE_NUM = #{ shareNum },
            </if>
            <if test="shareClickNum != null" >
                SHARE_CLICK_NUM = #{ shareClickNum },
            </if>
            <if test="shareUserId != null" >
                SHARE_USER_ID = #{ shareUserId },
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{ createTime },
            </if>
            <if test="expertise != null" >
                EXPERTISE = #{ expertise },
            </if>
            <if test="expect != null" >
                EXPECT = #{ expect },
            </if>
            <if test="expectxh != null" >
                EXPECTXH = #{ expectxh },
            </if>
            <if test="circleId != null" >
                CIRCLE_ID = #{ circleId },
            </if>
            <if test="tokenId != null" >
                TOKEN_ID = #{ tokenId },
            </if>
            <if test="expiresin != null" >
                EXPIRESIN = #{ expiresin },
            </if>
            <if test="password != null" >
                PASSWORD = #{ password },
            </if>
            <if test="cardNumber != null" >
                card_number = #{ cardNumber },
            </if>
            <if test="height != null" >
                height = #{height},
            </if>
            <if test="weight != null" >
                weight = #{weight},
            </if>
            <if test="patientCardType != null">
                patient_card_type = #{ patientCardType },
            </if>
            <if test="healthCardNum != null">
                health_card_num = #{ healthCardNum },
            </if>
            <if test="healthFilesNum != null">
                health_files_num = #{ healthFilesNum },
            </if>
            <if test="maritalStatus != null">
                marital_status = #{ maritalStatus },
            </if>
            <if test="educationCode != null">
                education_code = #{ educationCode },
            </if>
            <if test="contactsName != null">
                contacts_name = #{ contactsName },
            </if>
            <if test="contactsPhone != null">
                contacts_phone = #{ contactsPhone },
            </if>
            <if test="contactsRelationship != null">
                contacts_relationship = #{ contactsRelationship },
            </if>
            <if test="lastDeptName != null">
                last_dept_name = #{lastDeptName},
            </if>

            <if test="lastRecordsId != null">
                last_records_id = #{lastRecordsId},
            </if>
            <if test="lastChineseDisName != null">
                last_chinese_dis_name = #{lastChineseDisName},
            </if>

            <if test="lastWestDisName != null">
                last_west_dis_name = #{lastWestDisName},
            </if>

            <if test="lastSymName != null">
                last_sym_name = #{lastSymName},
            </if>

            <if test="lastDoctorName != null">
                last_doctor_name = #{lastDoctorName},
            </if>

            <if test="lastDoctorId != null">
                last_doctor_id = #{lastDoctorId},
            </if>

            <if test="lastRecordsTime != null">
                last_records_time = #{lastRecordsTime},
            </if>

            <if test="nation != null">
                nation = #{nation},
            </if>

            <if test="nationality != null">
                nationality = #{nationality},
            </if>
            <if test="medicalCard != null and medicalCard != ''">
                medical_card = #{medicalCard},
            </if>








        </set>
        where USER_ID = #{ userId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update t_admin_info set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <update id="setPatientMark" parameterType="com.cbkj.diagnosis.service.webapi.business.vo.SetPatientMarkVo">
        update t_admin_info set join_road_task = #{joinRoadTask} where USER_ID = #{patientId}
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_admin_info where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_admin_info where USER_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_admin_info where USER_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="com.cbkj.diagnosis.beans.TAdminInfo" resultType="Map">
        SELECT USER_ID,SEX,USER_NAME,MOBILE,AGE,OPEN_ID,WX_NICE_NAME,WX_HEAD_IMG,IDENTITY,SHARE_NUM,SHARE_CLICK_NUM,SHARE_USER_ID,CREATE_TIME  from t_admin_info
        where is_del='0'
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.TAdminInfo" resultMap="BaseResultMap">
        SELECT USER_ID,SEX,USER_NAME,MOBILE,AGE,OPEN_ID,WX_NICE_NAME,WX_HEAD_IMG,IDENTITY,SHARE_NUM,SHARE_CLICK_NUM,SHARE_USER_ID,CREATE_TIME
        from t_admin_info
        where is_del='0'
        <!--条件-->
        <if test="name != null and name!='' ">
        and name like CONCAT('%',trim(#{name}),'%')
        </if>

    </select>
    <select id="getUserInfoByOpenId" parameterType="String" resultMap="BaseResultMap">
        SELECT *
         from t_admin_info
         where OPEN_ID = #{openId}
    </select>
    <select id="getCheckUserId" parameterType="String" resultType="Integer">
        SELECT count(*)
         from t_admin_info
         where USER_ID = #{userId}
    </select>

    <select id="getUserInfoByUserIdAndToken"  parameterType="com.cbkj.diagnosis.beans.TAdminInfo" resultMap="BaseResultMap">
        SELECT *
         from t_admin_info
         where USER_ID = #{userId}
    </select>

    <select id="getUserInfoByToken"  parameterType="com.cbkj.diagnosis.beans.TAdminInfo" resultMap="BaseResultMap">
        SELECT *
        from t_admin_info
        where TOKEN_ID = #{tokenId}
    </select>

    <select id="getUserInfoByUserPassword"  parameterType="com.cbkj.diagnosis.beans.TAdminInfo" resultMap="BaseResultMap">
        SELECT *
        from t_admin_info
        where MOBILE =#{mobile}
        order by CREATE_TIME desc
        limit 1
    </select>


    <select id="getUserInfoByCardNumber"  parameterType="com.cbkj.diagnosis.beans.TAdminInfo2" resultMap="BaseResultMap">
        SELECT *
        from t_admin_info
        where card_number = #{idCard}
           <if test="healthCardNum != null and healthCardNum != ''">
               or health_card_num = #{healthCardNum}
           </if>

        order by CREATE_TIME desc
        limit 1
    </select>
    <select id="getTAdminInfoById" resultType="com.cbkj.diagnosis.beans.TAdminInfo" resultMap="BaseResultMap" parameterType="String">
        SELECT
            <include refid="Base_Column_List"/>
        from t_admin_info where USER_ID =#{patientId}
    </select>
    <select id="getListByIds" resultMap="BaseResultMap" parameterType="List">

        select
        <include refid="Base_Column_List"/>
        from t_admin_info
        <where>
            USER_ID in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item.userId}
            </foreach>
        </where>

    </select>
    <select id="getListByIds2" resultMap="BaseResultMap" parameterType="List">

        select
        <include refid="Base_Column_List"/>
        from t_admin_info
        <where>
            USER_ID in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>

    </select>


    <select id="checkPatientEx" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.beans.TAdminInfo2">
        SELECT  <include refid="Base_Column_List"/>
        from t_admin_info
        where card_number = #{idCard} limit 1
    </select>


    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.beans.AssociationUserInfo" >
    <id column="userId" jdbcType="VARCHAR"  property="userId" />
    <result column="userName" jdbcType="VARCHAR" property="userName" />
    <result column="userSex" jdbcType="VARCHAR" property="userSex" />
    </resultMap>

    <select id="getaAssociation" resultMap="BaseResultMap2" parameterType="String">
        select
        b.user_id as userId,b.USER_NAME as userName,b.sex as userSex,b.user_id as userId
        from  t_admin_info b
        <where>
            <if test="currentUserId != null and currentUserId !=''">
                b.master_user_id = #{currentUserId}
            </if>
        </where>
        UNION ALL
        select
        b.user_id as userId,b.USER_NAME as userName,b.sex as userSex,b.user_id as userId
        from  t_admin_info b where b.user_id = #{currentUserId}
    </select>
    <select id="getTAdminInfoByMobile" parameterType="String" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List"/>
        from t_admin_info
        where MOBILE = #{mobile} limit 1

    </select>
    <select id="checkPatientJoinThisTask" resultType="java.lang.Integer" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping">
        select count(*) from s_road_task_patients_mapping where s_road_task_id = #{sRoadTaskId} and patient_id = #{patientId} and records_id = #{recordsId}
    </select>
    <select id="getOnePatientAllRecords" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping" resultMap="BaseResultMap">


        SELECT
        b.`records_id` recordsId,b.doctor_id doctorId,b.doctor_name doctorName,a.*
        FROM t_admin_info a JOIN medical_records b ON(a.USER_ID = b.patient_id)
        WHERE NOT EXISTS(
        SELECT 1 FROM s_road_task_patients_mapping srtpm WHERE  srtpm.s_road_task_id = '0'  AND srtpm.patient_id = #{patientId}
        AND srtpm.records_id = b.records_id
        ) AND a.`USER_ID`=#{patientId}

    </select>
    <select id="getUserByMobile" resultMap="BaseResultMap" parameterType="com.cbkj.diagnosis.beans.TAdminInfo2">
        select <include refid="Base_Column_List" />
        from t_admin_info where MOBILE = #{mobile} limit 1
    </select>
    <select id="getHisLoginCheckUserInfo" parameterType="com.cbkj.diagnosis.beans.TAdminInfo2" resultMap="BaseResultMap">
        SELECT *
        from t_admin_info
        where
            MOBILE = #{mobile}
        order by CREATE_TIME desc
        limit 1
    </select>

    <select id="getHisLoginCheckUserInfoByIdCard" parameterType="com.cbkj.diagnosis.beans.TAdminInfo2" resultMap="BaseResultMap">
        SELECT *
        from t_admin_info
        where
            card_number = #{idCard} and patient_card_type = #{patientCardType}
        order by CREATE_TIME desc
        limit 1
    </select>


</mapper>