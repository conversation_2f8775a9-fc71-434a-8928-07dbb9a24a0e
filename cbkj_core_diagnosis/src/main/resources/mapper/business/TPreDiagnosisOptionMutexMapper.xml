<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPreDiagnosisOptionMutexMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisOptionMutex">
        <id column="mutex_id" jdbcType="INTEGER"  property="mutexId" />
        <result column="option_id" jdbcType="INTEGER" property="optionId" />
        <result column="option_name" jdbcType="VARCHAR" property="optionName" />
        <result column="mutex_option_id" jdbcType="INTEGER" property="mutexOptionId" />
    </resultMap>


    <sql id="Base_Column_List">
    mutex_id,option_id,option_name,mutex_option_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisOptionMutex">
        delete from t_pre_diagnosis_option_mutex where mutex_id = #{ mutexId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_pre_diagnosis_option_mutex where mutex_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisOptionMutex">
        insert into t_pre_diagnosis_option_mutex (<include refid="Base_Column_List" />) values
        (#{mutexId},#{optionId},#{optionName},#{mutexOptionId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_pre_diagnosis_option_mutex (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.mutexId},#{item.optionId},#{item.optionName},#{item.mutexOptionId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisOptionMutex">
        update t_pre_diagnosis_option_mutex
        <set>
             <if test="optionId != null">
                option_id = #{ optionId },
             </if>
             <if test="optionName != null">
                option_name = #{ optionName },
             </if>
             <if test="mutexOptionId != null">
                mutex_option_id = #{ mutexOptionId },
             </if>
        </set>
        where mutex_id = #{ mutexId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_pre_diagnosis_option_mutex where mutex_id = #{ mutexId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.TPreDiagnosisOptionMutex" resultMap="BaseResultMap">
        SELECT mutex_id,option_id,option_name,mutex_option_id
        from t_pre_diagnosis_option_mutex
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>