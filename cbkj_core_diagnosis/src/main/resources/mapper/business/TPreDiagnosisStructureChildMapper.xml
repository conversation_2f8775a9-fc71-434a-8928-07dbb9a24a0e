<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPreDiagnosisStructureChildMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisStructureChild">
        <id column="structure_child" property="structureChild" />
        <result column="content" property="content" />
        <result column="content_type" property="contentType" />
        <result column="structure_content_id" property="structureContentId" />
        <result column="paragraph_sort" property="paragraphSort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        structure_child, content, content_type, structure_content_id, paragraph_sort
    </sql>
    <delete id="deleteByDiaId" parameterType="String">

        DELETE FROM t_pre_diagnosis_structure_child
        WHERE structure_content_id IN (
            SELECT structure_content_id FROM t_pre_diagnosis_structure_content
            WHERE diagnosis_structure_id IN (
                SELECT diagnosis_structure_id FROM t_pre_diagnosis_structure WHERE dia_id = #{diaId}
            )
        )

    </delete>

</mapper>
