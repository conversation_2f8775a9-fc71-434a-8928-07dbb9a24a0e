<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TPreDiagnosisStructureContentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TPreDiagnosisStructureContent">
        <id column="structure_content_id" property="structureContentId" />
        <result column="diagnosis_structure_id" property="diagnosisStructureId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        structure_content_id, diagnosis_structure_id
    </sql>
    <delete id="deleteByDiaId" parameterType="String">
        DELETE FROM t_pre_diagnosis_structure_content
        WHERE diagnosis_structure_id IN (
            SELECT diagnosis_structure_id FROM t_pre_diagnosis_structure WHERE dia_id = #{diaId}
        )


    </delete>

</mapper>
