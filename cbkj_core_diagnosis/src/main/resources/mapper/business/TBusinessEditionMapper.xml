<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TBusinessEditionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TBusinessEdition">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_user_id" property="deleteUserId" />
        <result column="edition_num" property="editionNum" />
        <result column="edition_content" property="editionContent" />
        <result column="edition_time" property="editionTime" />
        <result column="is_del" property="isDel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, create_user_id, create_user_name, update_time, update_user_id, delete_time, delete_user_id, edition_num, edition_content, edition_time, is_del
    </sql>

</mapper>
