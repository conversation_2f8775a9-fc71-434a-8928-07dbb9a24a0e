<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.TBusinessAnnexMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.TBusinessAnnex">
        <id column="id" property="id" />
        <result column="insert_user_id" property="insertUserId" />
        <result column="annex_foreign_id" property="annexForeignId" />
        <result column="annex_name" property="annexName" />
        <result column="annex_Original_name" property="annexOriginalName" />
        <result column="annex_suffix_name" property="annexSuffixName" />
        <result column="annex_size" property="annexSize" />
        <result column="annex_path" property="annexPath" />
        <result column="annex_type" property="annexType" />
        <result column="is_del" property="isDel" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, insert_user_id, annex_foreign_id, annex_name, annex_Original_name, annex_suffix_name, annex_size, annex_path, annex_type, is_del,create_time
    </sql>
    <select id="getImagesListByProposalId"
            resultType="com.cbkj.diagnosis.beans.business.adviceVo.ImageUploadRes" parameterType="String">
        select id as urlId,annex_path as url from t_business_annex where annex_foreign_id=#{id}

    </select>

</mapper>
