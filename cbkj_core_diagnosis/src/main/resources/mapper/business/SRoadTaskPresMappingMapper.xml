<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.SRoadTaskPresMappingMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.SRoadTaskPresMapping">
        <result column="prescriptions_id" jdbcType="VARCHAR"  property="prescriptionsId" />
        <result column="s_road_task_id" jdbcType="VARCHAR" property="sRoadTaskId" />
        <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
    </resultMap>


    <sql id="Base_Column_List">
    prescriptions_id,s_road_task_id,patient_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPresMapping">
        delete from s_road_task_pres_mapping where prescriptions_id = #{ prescriptionsId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road_task_pres_mapping where prescriptions_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPresMapping">
        insert into s_road_task_pres_mapping (<include refid="Base_Column_List" />) values
        (#{prescriptionsId},#{sRoadTaskId},#{patientId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into s_road_task_pres_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.prescriptionsId},#{item.sRoadTaskId},#{item.patientId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPresMapping">
        update s_road_task_pres_mapping
        <set>
             <if test="sRoadTaskId != null">
                s_road_task_id = #{ sRoadTaskId },
             </if>
             <if test="patientId != null">
                patient_id = #{ patientId },
             </if>
        </set>
        where prescriptions_id = #{ prescriptionsId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from s_road_task_pres_mapping where prescriptions_id = #{ prescriptionsId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPresMapping" resultMap="BaseResultMap">
        SELECT prescriptions_id,s_road_task_id,patient_id
        from s_road_task_pres_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>