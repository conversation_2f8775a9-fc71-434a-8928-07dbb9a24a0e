<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.SSymptomMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.SSymptom">
        <id column="s_symptom_id" jdbcType="VARCHAR"  property="sSymptomId" />
        <result column="s_symptom_name" jdbcType="VARCHAR" property="sSymptomName" />
        <result column="s_symptom_code" jdbcType="VARCHAR" property="sSymptomCode" />
    </resultMap>


    <sql id="Base_Column_List">
    s_symptom_id,s_symptom_name,s_symptom_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SSymptom">
        delete from s_symptom where s_symptom_id = #{ sSymptomId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_symptom where s_symptom_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.SSymptom">
        insert into s_symptom (<include refid="Base_Column_List" />) values
        (#{sSymptomId},#{sSymptomName},#{sSymptomCode})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into s_symptom (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.sSymptomId},#{item.sSymptomName},#{item.sSymptomCode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SSymptom">
        update s_symptom
        <set>
             <if test="sSymptomName != null">
                s_symptom_name = #{ sSymptomName },
             </if>
             <if test="sSymptomCode != null">
                s_symptom_code = #{ sSymptomCode },
             </if>
        </set>
        where s_symptom_id = #{ sSymptomId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from s_symptom where s_symptom_id = #{ sSymptomId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.SSymptom" resultMap="BaseResultMap">
        SELECT s_symptom_id,s_symptom_name,s_symptom_code
        from s_symptom
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="selectListByKeyWord" parameterType="String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from s_symptom

<where>
    <if test="keyWord != null and keyWord != ''">
        and
        (
            s_symptom_code like CONCAT('%',trim(#{keyWord}),'%')
            or
            s_symptom_name like CONCAT('%',trim(#{keyWord}),'%')
        )
    </if>
</where>
    </select>
    <select id="getOneBySymCode" resultType="java.lang.String" parameterType="com.cbkj.diagnosis.beans.business.SSymptom">
        select s_symptom_id
        from s_symptom
        where s_symptom_code = #{sSymptomCode}
    </select>

</mapper>