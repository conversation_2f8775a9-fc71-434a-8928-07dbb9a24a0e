<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsPhoneMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone">
        <id column="id" jdbcType="BIGINT" property="sRoadTaskPatientsPhoneId"/>
        <result column="task_patients_id" jdbcType="BIGINT" property="taskPatientsId"/>
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId"/>
        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName"/>
        <result column="allot_task_status" jdbcType="VARCHAR" property="allotTaskStatus"/>
        <result column="sui_fang_time" jdbcType="TIMESTAMP" property="suiFangTime"/>
        <result column="patient_id" jdbcType="VARCHAR" property="patientId"/>
        <result column="phone_status" jdbcType="INTEGER" property="phoneStatus"/>
        <result column="road_execute_id" jdbcType="INTEGER" property="roadExecuteId"/>
        <result column="s_road_task_id" jdbcType="VARCHAR" property="sRoadTaskId"/>
        <result column="records_id" jdbcType="VARCHAR" property="recordsId"/>
        <result column="allot_time" jdbcType="TIMESTAMP" property="allotTime"/>
        <result column="rec_id" jdbcType="VARCHAR" property="recId"/>
        <result column="sui_fang_finish_time" jdbcType="TIMESTAMP" property="suiFangFinishTime"/>
        <result column="task_patients_node" jdbcType="VARCHAR" property="taskPatientsNode"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,task_patients_id,doctor_id,doctor_name,allot_task_status,sui_fang_time,patient_id,phone_status,road_execute_id
            ,s_road_task_id,records_id,allot_time,rec_id,sui_fang_finish_time,task_patients_node
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone">
        delete
        from s_road_task_patients_phone
        where id = #{sRoadTaskPatientsPhoneId}
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road_task_patients_phone where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone">
        insert into s_road_task_patients_phone (<include refid="Base_Column_List"/>) values
        (#{sRoadTaskPatientsPhoneId},#{taskPatientsId},#{doctorId},#{doctorName},#{allotTaskStatus},#{suiFangTime},
        #{patientId},#{phoneStatus},#{roadExecuteId},#{sRoadTaskId},#{recordsId},#{allotTime},#{recId},#{suiFangFinishTime},#{taskPatientsNode})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into s_road_task_patients_phone (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.sRoadTaskPatientsPhoneId},#{item.taskPatientsId},#{item.doctorId},#{item.doctorName},#{item.allotTaskStatus},
            #{item.suiFangTime},#{item.patientId},#{item.phoneStatus},#{item.roadExecuteId},#{item.sRoadTaskId},
            #{item.recordsId},#{item.allotTime},#{item.recId},#{item.suiFangFinishTime},#{item.taskPatientsNode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone">
        update s_road_task_patients_phone
        <set>
            <if test="taskPatientsId != null">
                task_patients_id = #{ taskPatientsId },
            </if>
            <if test="doctorId != null and doctorId != ''">
                doctor_id = #{ doctorId },
            </if>
            <if test="doctorName != null and doctorName != ''">
                doctor_name = #{ doctorName },
            </if>
            <if test="allotTaskStatus != null">
                allot_task_status = #{ allotTaskStatus },
            </if>
            <if test="suiFangTime != null">
                sui_fang_time = #{suiFangTime},
            </if>
            <if test="patientId != null and patientId != ''">
                patient_id = #{patientId},
            </if>
            <if test="phoneStatus != null ">
                phone_status = #{phoneStatus},
            </if>

            <if test="roadExecuteId != null">
                road_execute_id = #{roadExecuteId},
            </if>
            <if test="allotTime != null">
                allot_time = #{allotTime},
            </if>
            <if test="sRoadTaskId != null and sRoadTaskId != ''">
                s_road_task_id = #{sRoadTaskId},
            </if>
            <if test="recordsId != null and recordsId != ''">
                records_id = #{recordsId},
            </if>
            <if test="recId != null and recId != ''">
                rec_id = #{recId},
            </if>
            <if test="suiFangFinishTime != null ">
                sui_fang_finish_time = #{suiFangFinishTime},
            </if>
            <if test="taskPatientsNode != null ">
                task_patients_node = #{taskPatientsNode},
            </if>
        </set>
        where id = #{sRoadTaskPatientsPhoneId}
    </update>
    <update id="updatePatientTaskStatusByPAndR"
            parameterType="com.cbkj.diagnosis.service.webapi.business.vo.PatientTaskDelete">
        update s_road_task_patients_phone
        <set>

            phone_status = #{taskPatientStatus}

        </set>
        where s_road_task_id = #{sRoadTaskId} and patient_id = #{patientId} and phone_status = #{taskPatientStatusPast}
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from s_road_task_patients_phone where id = #{ id }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone"
            resultMap="BaseResultMap">
        SELECT
        a.id,
        a.task_patients_id,
        a.doctor_id,
        a.doctor_name,
        a.allot_task_status,
        a.sui_fang_time,
        a.sui_fang_finish_time,
        a.allot_time,
        a.patient_id,
        a.phone_status,
        a.s_road_task_id,
        a.road_execute_id,
        a.records_id,
        a.rec_id,
        a.task_patients_node,
        b.sex patientSex,
        b.USER_NAME patientName,
        b.card_number patientCardNumber
        from s_road_task_patients_phone as a join t_admin_info as b on(a.patient_id = b.user_id)
        <where>
            <if test=" doctorName != null and doctorName!='' ">
                and a.doctor_name like CONCAT('%',trim(#{doctorName}),'%')
            </if>
            <if test="allotTaskStatus != null and allotTaskStatus != ''">
                and a.allot_task_status = #{allotTaskStatus}
            </if>
        </where>
    </select>
    <select id="getPageListByObj2" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone"
            resultMap="BaseResultMap">
        select t1.* from ( SELECT
        a.id,
        a.task_patients_id,
        a.doctor_id,
        a.doctor_name,
        a.allot_task_status,
        a.sui_fang_time,
        a.allot_time,
        a.sui_fang_finish_time,
        a.patient_id,
        a.phone_status,
        a.s_road_task_id,
        a.road_execute_id,
        a.records_id,
        a.rec_id,
        b.sex patientSex,
        b.USER_NAME patientName,
        b.card_number patientCardNumber
        from s_road_task_patients_phone as a join t_admin_info as b on(a.patient_id = b.user_id)
        <where>
            <if test=" doctorName != null and doctorName!='' ">
                and a.doctor_name like CONCAT('%',trim(#{doctorName}),'%')
            </if>
            <if test="allotTaskStatus != null and allotTaskStatus != ''">
                and a.allot_task_status = #{allotTaskStatus}
            </if>
        </where>
        order by a.sui_fang_time ) t1 group by
        t1.s_road_task_id,t1.road_execute_id,t1.patient_id
        <!--    t1.records_id -->
    </select>

    <select id="getOneByTaskPatientsId" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from s_road_task_patients_phone where task_patients_id = #{taskPatientsId} limit 1

    </select>
    <select id="getListByRecordsId" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from s_road_task_patients_phone where task_patients_id = #{taskPatientsId}
        and phone_status in(1,2)
    </select>


    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.service.common.vo.TodayPatientList">
        <id column="taskPatientsId" jdbcType="BIGINT" property="taskPatientsId"/>
        <result column="sRoadTaskPatientsPhoneId"  property="sRoadTaskPatientsPhoneId"/>
        <result column="taskPatientsId"  property="taskPatientsId"/>
        <result column="doctorId"  property="doctorId"/>
        <result column="doctorName"  property="doctorName"/>
        <result column="allotTaskStatus"  property="allotTaskStatus"/>
        <result column="allotTime"  property="allotTime"/>
        <result column="patientId"  property="patientId"/>
        <result column="taskExcuteStatus"  property="taskExcuteStatus"/>
        <result column="sRoadTaskId"  property="sRoadTaskId"/>
        <result column="roadExecuteId"  property="roadExecuteId"/>
        <result column="recordsId"  property="recordsId"/>
        <result column="recId"  property="recId"/>
        <result column="patientSex"  property="patientSex"/>
        <result column="patientName"  property="patientName"/>
        <result column="patientCardNumber"  property="patientCardNumber"/>
        <result column="patientAge"  property="patientAge"/>
        <result column="patientSex"  property="patientSex"/>
        <result column="taskName"  property="taskName"/>
        <result column="remark"  property="remark"/>
        <result column="lastChineseDisName"  property="lastChineseDisName"/>
        <result column="lastWestDisName"  property="lastWestDisName"/>
        <result column="taskExcuteTime"  property="taskExcuteTime"/>
        <collection property="todayPatientEventList" ofType="com.cbkj.diagnosis.service.common.vo.TodayPatientEvent">
            <result column="dic_id" jdbcType="VARCHAR" property="dicId"/>
            <result column="dic_code" jdbcType="VARCHAR" property="dicCode"/>
            <result column="dic_name" jdbcType="VARCHAR" property="dicName"/>
            <result column="dic_shot_name" jdbcType="VARCHAR" property="simpleLabel"/>
            <result column="style" jdbcType="VARCHAR" property="style"/>
        </collection>
    </resultMap>

    <select id="countTodayPatientList" parameterType="com.cbkj.diagnosis.beans.business.requestvo.GetTodayPatientList"
            resultType="Integer">
        select count(1) from (
        select tt.* from (
        SELECT t1.*
        FROM ( SELECT
        NULL AS sRoadTaskPatientsPhoneId,
        a.task_patients_id taskPatientsId,
        a.doctor_id doctorId,
        a.doctor_name doctorName,
        a.allot_task_status allotTaskStatus,
        <!--     a.sui_fang_time suiFangTime,-->
        a.allot_time allotTime,
        a.patient_id patientId,
        a.task_excute_status taskExcuteStatus,
        <!--     a.sui_fang_finish_time suiFangFinishTime,-->
        <!--       a.phone_status phoneStatus,-->
        a.s_road_task_id sRoadTaskId,
        a.road_execute_id roadExecuteId,
        a.records_id recordsId,
        a.rec_id recId,
        b.sex patientSex,
        b.USER_NAME patientName,
        b.card_number patientCardNumber,
        b.AGE patientAge,
        b.remark,
        srt.task_name taskName,
        b.last_chinese_dis_name lastChineseDisName,
        b.last_west_dis_name lastWestDisName,
        a.task_excute_time as taskExcuteTime

        FROM
        t_admin_info AS b
        JOIN s_road_task_patients AS a ON( a.road_execute_event_way != '3' AND a.road_execute_event_type IN ('2','4')
        AND a.status = '0' AND b.`USER_ID` = a.`patient_id` )
        JOIN s_road_task AS srt ON(srt.s_road_task_id = a.s_road_task_id )
    <!--    LEFT JOIN `t_record_event` AS tre ON(tre.rec_id = a.rec_id )
        left join sys_dic as sd on(sd.dic_id = tre.dic_id) -->
        <!-- join t_admin_info as b on(b.USER_ID = a.patient_id) -->
        <where>
            <if test="eventCodesList != null">
                EXISTS ( select 1 from t_record_event as aa where aa.rec_id =  a.rec_id and aa.dic_code in
                <foreach collection="eventCodesList" item="item" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
                )
            </if>
            <!--
                <if test="phoneStatus != null">
                    and a.phone_status =#{phoneStatus}
                </if>
                -->
            <if test="toDayTaskStatus != null and toDayTaskStatus == -1">
                <!-- 过期 ，今日之前的时间 -->
                and a.task_excute_status in(1,2,5,6,7)
                and a.task_excute_time <![CDATA[ < ]]> (CONCAT(CURDATE(), ' 23:59:59') )
            </if>
            <if test="toDayTaskStatus != null and toDayTaskStatus == 0">
                <!-- 完成 -->
                and a.task_excute_status in(8)
            </if>

            <if test="toDayTaskStatus != null and toDayTaskStatus == 2">
                <!-- 计划中，今日之后的时间 -->
                and a.task_excute_status in(1,2)
                and a.task_excute_time > (CONCAT(CURDATE(), ' 23:59:59') )
            </if>
            <if test="sRoadTaskId != null">
                and a.s_road_task_id =#{sRoadTaskId}
            </if>
            <if test="toDayTaskStatus != null and toDayTaskStatus != 2">
                <if test="startDate != null">
                    AND a.task_excute_time >= #{startDate}
                </if>
                <if test="endDate != null">
                    AND a.task_excute_time <![CDATA[<=]]> #{endDate}
                </if>
            </if>
            <if test=" patientName != null and patientName!='' ">
                AND b.USER_NAME LIKE CONCAT('%',TRIM(#{patientName}),'%')
            </if>
            <!--        <if test=" patientCardNumber != null and patientCardNumber!='' ">
                        AND b.card_number = #{patientCardNumber}
                    </if>
                    -->
            <if test=" patientIdcard != null and patientIdcard!='' ">
                AND b.card_number = #{patientIdcard}
            </if>
            <if test="diseaseName != null and diseaseName != ''">
                AND (
                b.`last_west_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
                OR
                b.`last_chinese_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
                )
            </if>
            <if test="userId != null and userId != ''">
                AND (
                srt.create_user_id = #{userId}
                or exists
                ( select 1 from s_road_task_ex as srte where srte.s_road_task_id = a.s_road_task_id and srte.ex_type = 2
                and srte.diagnosis_doctor_id = #{userId} )
                )
            </if>
        </where>
        <choose>
            <when test="toDayTaskStatus == 2 ">
                ORDER BY a.task_excute_time
            </when>
            <otherwise>
                ORDER BY a.task_excute_time desc
            </otherwise>
        </choose>
        ) t1

        union all
        SELECT t1.*
        FROM ( SELECT
        srtpp.id sRoadTaskPatientsPhoneId,
        a.task_patients_id taskPatientsId,
        a.doctor_id doctorId,
        a.doctor_name doctorName,
        srtpp.allot_task_status allotTaskStatus,
        srtpp.allot_time allotTime,
        a.patient_id patientId,
        srtpp.phone_status taskExcuteStatus,
        a.s_road_task_id sRoadTaskId,
        a.road_execute_id roadExecuteId,
        a.records_id recordsId,
        srtpp.rec_id recId,
        b.sex patientSex,
        b.USER_NAME patientName,
        b.card_number patientCardNumber,
        b.AGE patientAge,
        b.remark,
        srt.task_name taskName,
        b.last_chinese_dis_name lastChineseDisName,
        b.last_west_dis_name lastWestDisName,
        srtpp.sui_fang_time as taskExcuteTime
        FROM t_admin_info AS b
        join s_road_task_patients AS a ON (a.road_execute_event_way = '3'
        and a.road_execute_event_type IN ('2','4') and a.status = '0' and a.patient_id = b.user_id )
        join s_road_task as srt on(srt.s_road_task_id = a.s_road_task_id)
        JOIN s_road_task_patients_phone as srtpp on( srtpp.task_patients_id = a.task_patients_id )
    <!--    LEFT JOIN `t_record_event` AS tre ON(tre.rec_id = a.rec_id )
        left join sys_dic as sd on(sd.dic_id = tre.dic_id) -->
        <where>
            <if test="eventCodesList != null">
                EXISTS ( select 1 from t_record_event as aa where aa.rec_id =  a.rec_id and aa.dic_code in
                <foreach collection="eventCodesList" item="item" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
                )
            </if>
            <if test="toDayTaskStatus != null and toDayTaskStatus == -1">
                <!-- 过期 ，今日之前的时间 -->
                and srtpp.phone_status in(1,2,5,6,7)
                and srtpp.sui_fang_time <![CDATA[ < ]]>  (CONCAT(CURDATE(), ' 23:59:59') )
            </if>
            <if test="toDayTaskStatus != null and toDayTaskStatus == 0">
                <!-- 完成 -->
                and srtpp.phone_status in(8)
            </if>

            <if test="toDayTaskStatus != null and toDayTaskStatus == 2">
                <!-- 计划中，今日之后的时间 -->
                and srtpp.phone_status in(1,2)
                and srtpp.sui_fang_time > (CONCAT(CURDATE(), ' 23:59:59') )
            </if>
            <if test="sRoadTaskId != null">
                and a.s_road_task_id =#{sRoadTaskId}
            </if>
            <if test="toDayTaskStatus != null and toDayTaskStatus != 2">
                <if test="startDate != null">
                    AND srtpp.sui_fang_time >= #{startDate}
                </if>
                <if test="endDate != null">
                    AND srtpp.sui_fang_time <![CDATA[<=]]> #{endDate}
                </if>
            </if>
            <if test=" patientName != null and patientName!='' ">
                AND b.USER_NAME LIKE CONCAT('%',TRIM(#{patientName}),'%')
            </if>
            <!--          <if test=" patientCardNumber != null and patientCardNumber!='' ">
                          AND b.card_number = #{patientCardNumber}
                      </if> -->
            <if test=" patientIdcard != null and patientIdcard!='' ">
                AND b.card_number = #{patientIdcard}
            </if>
            <if test="diseaseName != null and diseaseName != ''">
                AND (
                b.`last_west_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
                OR
                b.`last_chinese_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
                )
            </if>
            <if test="userId != null and userId != ''">
                AND (srt.create_user_id = #{userId}
                or exists
                ( select 1 from s_road_task_ex as srte where srte.s_road_task_id = a.s_road_task_id and srte.ex_type = 2
                and srte.diagnosis_doctor_id = #{userId} )

                )
            </if>
        </where>
        <choose>
            <when test="toDayTaskStatus == 2 ">
                ORDER BY srtpp.sui_fang_time
            </when>
            <otherwise>
                ORDER BY srtpp.sui_fang_time desc
            </otherwise>
        </choose>
        ) t1


        ) as tt

        ) t3


    </select>
    <select id="getTodayPatientList" parameterType="com.cbkj.diagnosis.beans.business.requestvo.GetTodayPatientList"
            resultMap="BaseResultMap2">
        select tt.* from (
        SELECT t1.*
        FROM ( SELECT
        NULL AS sRoadTaskPatientsPhoneId,
        a.task_patients_id taskPatientsId,
        a.doctor_id doctorId,
        a.doctor_name doctorName,
        a.allot_task_status allotTaskStatus,
        <!--     a.sui_fang_time suiFangTime,-->
        a.allot_time allotTime,
        a.patient_id patientId,
        a.task_excute_status taskExcuteStatus,
        <!--     a.sui_fang_finish_time suiFangFinishTime,-->
        <!--       a.phone_status phoneStatus,-->
        a.s_road_task_id sRoadTaskId,
        a.road_execute_id roadExecuteId,
        a.records_id recordsId,
        a.rec_id recId,
        b.sex patientSex,
        b.USER_NAME patientName,
        b.card_number patientCardNumber,
        b.AGE patientAge,
        b.remark,
        srt.task_name taskName,
        b.last_chinese_dis_name lastChineseDisName,
        b.last_west_dis_name lastWestDisName,
        a.task_excute_time as taskExcuteTime,
        tre.dic_code,
        tre.dic_id,
        tre.dic_name,
        sd.dic_shot_name,
        sd.dic_value as style
        FROM
        t_admin_info AS b
        JOIN s_road_task_patients AS a ON( a.road_execute_event_way != '3' AND a.road_execute_event_type IN ('2','4')
        AND a.status = '0' AND b.`USER_ID` = a.`patient_id` )
        JOIN s_road_task AS srt ON(srt.s_road_task_id = a.s_road_task_id )
        LEFT JOIN `t_record_event` AS tre ON(tre.rec_id = a.rec_id )
        left join sys_dic as sd on(sd.dic_id = tre.dic_id)
        <!-- join t_admin_info as b on(b.USER_ID = a.patient_id) -->
        <where>
            <if test="eventCodesList != null">
                EXISTS ( select 1 from t_record_event as aa where aa.rec_id =  a.rec_id and aa.dic_code in
                   <foreach collection="eventCodesList" item="item" separator="," open="(" close=")" index="index">
                       #{item}
                  </foreach>
                )
            </if>
            <!--
                <if test="phoneStatus != null">
                    and a.phone_status =#{phoneStatus}
                </if>
                -->
            <if test="toDayTaskStatus != null and toDayTaskStatus == -1">
                <!-- 过期 ，今日之前的时间 -->
                and a.task_excute_status in(1,2,5,6,7)
                and a.task_excute_time <![CDATA[ < ]]> (CONCAT(CURDATE(), ' 23:59:59') )
            </if>
            <if test="toDayTaskStatus != null and toDayTaskStatus == 0">
                <!-- 完成 -->
                and a.task_excute_status in(8)
            </if>

            <if test="toDayTaskStatus != null and toDayTaskStatus == 2">
                <!-- 计划中，今日之后的时间 -->
                and a.task_excute_status in(1,2)
                and a.task_excute_time > (CONCAT(CURDATE(), ' 23:59:59') )
            </if>
            <if test="sRoadTaskId != null">
                and a.s_road_task_id =#{sRoadTaskId}
            </if>
            <if test="toDayTaskStatus != null and toDayTaskStatus != 2">
                <if test="startDate != null">
                    AND a.task_excute_time >= #{startDate}
                </if>
                <if test="endDate != null">
                    AND a.task_excute_time <![CDATA[<=]]> #{endDate}
                </if>
            </if>
            <if test=" patientName != null and patientName!='' ">
                AND b.USER_NAME LIKE CONCAT('%',TRIM(#{patientName}),'%')
            </if>
            <!--        <if test=" patientCardNumber != null and patientCardNumber!='' ">
                        AND b.card_number = #{patientCardNumber}
                    </if>
                    -->
            <if test=" patientIdcard != null and patientIdcard!='' ">
                AND b.card_number = #{patientIdcard}
            </if>
            <if test="diseaseName != null and diseaseName != ''">
                AND (
                b.`last_west_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
                OR
                b.`last_chinese_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
                )
            </if>
            <if test="userId != null and userId != ''">
                AND (
                srt.create_user_id = #{userId}
                or exists
                ( select 1 from s_road_task_ex as srte where srte.s_road_task_id = a.s_road_task_id and srte.ex_type = 2
                and srte.diagnosis_doctor_id = #{userId} )
                )
            </if>
        </where>
        <choose>
            <when test="toDayTaskStatus == 2 ">
                ORDER BY a.task_excute_time
            </when>
            <otherwise>
                ORDER BY a.task_excute_time desc
            </otherwise>
        </choose>
        ) t1

        union all
        SELECT t1.*
        FROM ( SELECT
        srtpp.id sRoadTaskPatientsPhoneId,
        a.task_patients_id taskPatientsId,
        a.doctor_id doctorId,
        a.doctor_name doctorName,
        srtpp.allot_task_status allotTaskStatus,
        srtpp.allot_time allotTime,
        a.patient_id patientId,
        srtpp.phone_status taskExcuteStatus,
        a.s_road_task_id sRoadTaskId,
        a.road_execute_id roadExecuteId,
        a.records_id recordsId,
        srtpp.rec_id recId,
        b.sex patientSex,
        b.USER_NAME patientName,
        b.card_number patientCardNumber,
        b.AGE patientAge,
        b.remark,
        srt.task_name taskName,
        b.last_chinese_dis_name lastChineseDisName,
        b.last_west_dis_name lastWestDisName,
        srtpp.sui_fang_time as taskExcuteTime,
        tre.dic_code,
        tre.dic_id,
        tre.dic_name,
        sd.dic_shot_name,
        sd.dic_value as style
        FROM t_admin_info AS b
        join s_road_task_patients AS a ON (a.road_execute_event_way = '3'
        and a.road_execute_event_type IN ('2','4') and a.status = '0' and a.patient_id = b.user_id )
        join s_road_task as srt on(srt.s_road_task_id = a.s_road_task_id)
        JOIN s_road_task_patients_phone as srtpp on( srtpp.task_patients_id = a.task_patients_id )
        LEFT JOIN `t_record_event` AS tre ON(tre.rec_id = a.rec_id )
        left join sys_dic as sd on(sd.dic_id = tre.dic_id)
        <where>
            <if test="eventCodesList != null">
                EXISTS ( select 1 from t_record_event as aa where aa.rec_id =  a.rec_id and aa.dic_code in
                <foreach collection="eventCodesList" item="item" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
                )
            </if>
            <if test="toDayTaskStatus != null and toDayTaskStatus == -1">
                <!-- 过期 ，今日之前的时间 -->
                and srtpp.phone_status in(1,2,5,6,7)
                and srtpp.sui_fang_time <![CDATA[ < ]]>  (CONCAT(CURDATE(), ' 23:59:59') )
            </if>
            <if test="toDayTaskStatus != null and toDayTaskStatus == 0">
                <!-- 完成 -->
                and srtpp.phone_status in(8)
            </if>

            <if test="toDayTaskStatus != null and toDayTaskStatus == 2">
                <!-- 计划中，今日之后的时间 -->
                and srtpp.phone_status in(1,2)
                and srtpp.sui_fang_time > (CONCAT(CURDATE(), ' 23:59:59') )
            </if>
            <if test="sRoadTaskId != null">
                and a.s_road_task_id =#{sRoadTaskId}
            </if>
            <if test="toDayTaskStatus != null and toDayTaskStatus != 2">
                <if test="startDate != null">
                    AND srtpp.sui_fang_time >= #{startDate}
                </if>
                <if test="endDate != null">
                    AND srtpp.sui_fang_time <![CDATA[<=]]> #{endDate}
                </if>
            </if>
            <if test=" patientName != null and patientName!='' ">
                AND b.USER_NAME LIKE CONCAT('%',TRIM(#{patientName}),'%')
            </if>
            <!--          <if test=" patientCardNumber != null and patientCardNumber!='' ">
                          AND b.card_number = #{patientCardNumber}
                      </if> -->
            <if test=" patientIdcard != null and patientIdcard!='' ">
                AND b.card_number = #{patientIdcard}
            </if>
            <if test="diseaseName != null and diseaseName != ''">
                AND (
                b.`last_west_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
                OR
                b.`last_chinese_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
                )
            </if>
            <if test="userId != null and userId != ''">
                AND (srt.create_user_id = #{userId}
                or exists
                ( select 1 from s_road_task_ex as srte where srte.s_road_task_id = a.s_road_task_id and srte.ex_type = 2
                and srte.diagnosis_doctor_id = #{userId} )

                )
            </if>
        </where>
        <choose>
            <when test="toDayTaskStatus == 2 ">
                ORDER BY srtpp.sui_fang_time
            </when>
            <otherwise>
                ORDER BY srtpp.sui_fang_time desc
            </otherwise>
        </choose>
        ) t1


        ) as tt
        <choose>
            <when test="toDayTaskStatus == 2 ">
                order by tt.taskExcuteTime
            </when>
            <otherwise>
                order by tt.taskExcuteTime desc
            </otherwise>
        </choose>
        limit #{limit} offset #{page}
        <!--   GROUP BY t1.taskPatientsId -->

    </select>
    <select id="getStartSuiFangInfo" resultMap="BaseResultMap2" parameterType="Long">
        SELECT a.id                    sRoadTaskPatientsPhoneId,
               a.task_patients_id      taskPatientsId,
               a.doctor_id             doctorId,
               a.doctor_name           doctorName,
               a.allot_task_status     allotTaskStatus,
               a.sui_fang_time         suiFangTime,
               a.allot_time            allotTime,
               a.patient_id            patientId,
               a.phone_status          phoneStatus,
               a.rec_id                recId,
               a.s_road_task_id        SRoadTaskId,
               a.road_execute_id       roadExecuteId,
               a.task_patients_node    taskPatientsNode,
               a.records_id            recordsId,
               b.last_chinese_dis_name lastChineseDisName,
               b.last_west_dis_name    lastWestDisName,

               b.sex                   patientSex,
               b.USER_NAME             patientName,
               b.AGE                   patientAge,
               b.card_number           patientCardNumber,
               b.MOBILE                patientMobile,
               b.remark,
               c.task_name as          taskName
        FROM s_road_task_patients_phone AS a
                 JOIN t_admin_info AS b ON (a.patient_id = b.user_id)
                 left join s_road_task c on (c.s_road_task_id = a.s_road_task_id)
        where a.id = #{sRoadTaskPatientsPhoneId}
          and a.phone_status not in ('1', '3', '8')
        ORDER BY a.sui_fang_time asc
        limit 1
    </select>
    <select id="getSuiFangInfo" resultType="com.cbkj.diagnosis.service.common.vo.SuiFangInfoPatientList"
            parameterType="com.cbkj.diagnosis.service.common.vo.GetStartSuiFangInfo">
        <choose>
            <when test="sroadTaskPatientsPhoneId != null">
                SELECT
                a.task_patients_id taskPatientsId,
                c.`id` as sRoadTaskPatientsPhoneId,
                a.`patient_name` patientName,
                a.`patient_age` patientAge,
                b.`chinese_dis_name` chineseDisName,
                b.`west_dis_name` westDisName,
                a.`task_name` taskName,
                c.`task_patients_node` taskPatientsNode,
                b.`patient_phone` patientMobile,
                c.`rec_id` recId,
                c.phone_status taskExcuteStatus,
                a.patient_id patientId
                FROM `s_road_task_patients` AS a LEFT JOIN `medical_records` AS b ON(
                a.`records_id` = b.`records_id`
                )
                join s_road_task_patients_phone as c on(c.task_patients_id = a.task_patients_id)

                where c.`task_patients_id` = #{taskPatientsId} and c.`id` = #{sroadTaskPatientsPhoneId}
                and a.road_execute_event_way = '3'
            </when>
            <otherwise>
                SELECT
                a.task_patients_id taskPatientsId,
                null as sRoadTaskPatientsPhoneId,
                a.`patient_name` patientName,
                a.`patient_age` patientAge,
                b.`chinese_dis_name` chineseDisName,
                b.`west_dis_name` westDisName,
                a.`task_name` taskName,
                a.`task_patients_node` taskPatientsNode,
                b.`patient_phone` patientMobile,
                a.`rec_id` recId,
                a.task_excute_status taskExcuteStatus,
                a.patient_id patientId
                FROM `s_road_task_patients` AS a LEFT JOIN `medical_records` AS b ON(
                a.`records_id` = b.`records_id`
                ) where a.`task_patients_id` = #{taskPatientsId} and a.road_execute_event_way != '3'
            </otherwise>
        </choose>

    </select>


    <resultMap id="BaseResultMap3" type="com.cbkj.diagnosis.service.common.vo.TodayPatientDetail">
        <id column="sRoadTaskPatientsPhoneId" jdbcType="BIGINT" property="sRoadTaskPatientsPhoneId"/>
    </resultMap>

    <select id="getTodayPatientDetail" resultMap="BaseResultMap3"
            parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone">
        select a.id                              sRoadTaskPatientsPhoneId,
               a.sui_fang_time                   suiFangTime,
               a.allot_time                      allotTime,
               a.rec_id                          recId,
               a.phone_status                    phoneStatus,
               b.task_name                       taskName,
               c.road_execute_event_content_id   roadExecuteEventContentId,
               c.road_execute_event_content_name roadExecuteEventContentName
        from s_road_task_patients_phone a
                 join s_road_task b on (b.s_road_task_id = a.s_road_task_id)
                 join s_road_task_patients c on (c.task_patients_id = a.task_patients_id)
        where a.records_id = #{recordsId}
          and a.s_road_task_id = #{sRoadTaskId}
          and a.road_execute_id = #{roadExecuteId}
        order by a.sui_fang_time asc
    </select>


    <resultMap id="BaseResultMap4" type="com.cbkj.diagnosis.service.common.vo.SuiFangPhoneRecordListRes">
        <id column="sRoadTaskPatientsPhoneId" jdbcType="BIGINT" property="sRoadTaskPatientsPhoneId"/>
    </resultMap>
    <!-- 进行随访-获取患者所有电话随访记录 -->
    <select id="getSuiFangPhoneRecordList"
            resultMap="BaseResultMap4">
        select a.id            sRoadTaskPatientsPhoneId,
               a.sui_fang_time suiFangTime,
               a.phone_status  phoneStatus,
               a.allot_time    allotTime,
               a.rec_id        recId,
               a.doctor_id     doctorId,
               a.doctor_name   doctorName,
               b.task_name     taskName
        from s_road_task_patients_phone a
                 join s_road_task b on (a.s_road_task_id = b.s_road_task_id)
        where a.patient_id = #{patientId}

    </select>

    <resultMap id="BaseResultMap5" type="com.cbkj.diagnosis.service.common.vo.StartSuiFangInfoDetailsRes">
        <id column="sRoadTaskPatientsPhoneId" jdbcType="BIGINT" property="sRoadTaskPatientsPhoneId"/>
    </resultMap>

    <select id="getStartSuiFangInfoDetails"
            resultMap="BaseResultMap5" parameterType="Long">

        select a.id                              sRoadTaskPatientsPhoneId,
               a.sui_fang_time                   suiFangTime,
               a.task_patients_node              taskPatientsNode,
               a.allot_time                      allotTime,
               a.doctor_id                       doctorId,
               a.doctor_name                     doctorName,
               a.sui_fang_finish_time            suiFangFinishTime,
               IF(NULL,b.task_name,'手动任务')                     taskName,
               c.road_execute_event_content_name roadExecuteEventContentName,
               c.road_execute_event_content_id   roadExecuteEventContentId,
               tai.MOBILE as                     patientMobile
        from s_road_task_patients_phone a
                 left join s_road_task b on (a.s_road_task_id = b.s_road_task_id)
                 join s_road_task_patients c on (c.task_patients_id = a.task_patients_id)
                 join t_admin_info as tai on (tai.USER_ID = a.patient_id)
        where a.id = #{sRoadTaskPatientsPhoneId}
    </select>

    <select id="getListByTaskPatientsId"
            resultMap="BaseResultMap3"
            parameterType="com.cbkj.diagnosis.beans.business.requestvo.ListByTaskPatientsIdSearch">
        select a.id sRoadTaskPatientsPhoneId,
        a.sui_fang_time suiFangTime,
        a.allot_time allotTime,
        a.rec_id recId,
        a.phone_status phoneStatus,
        b.task_name taskName,
        c.road_execute_event_content_id roadExecuteEventContentId,
        c.road_execute_event_content_name roadExecuteEventContentName
        from s_road_task_patients_phone a
        join s_road_task b on (b.s_road_task_id = a.s_road_task_id)
        join s_road_task_patients c on (c.task_patients_id = a.task_patients_id)
        where a.patient_id = #{patientId} and a.s_road_task_id = #{sRoadTaskId}
        <if test="startDate != null and startDate != ''">

            and a.sui_fang_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != '' ">

            and a.sui_fang_time &lt;= #{endDate}
        </if>

    </select>

    <resultMap id="getIncludePhoneListByTaskPatientsIdMap" type="com.cbkj.diagnosis.service.common.vo.SuiFangPatientDetail">
        <result column="taskPatientsId" property="taskPatientsId"/>
        <result column="sRoadTaskPatientsPhoneId" property="sRoadTaskPatientsPhoneId"/>
        <result column="taskExcuteTime" property="taskExcuteTime"/>
        <result column="recId" property="recId"/>
        <result column="taskExcuteStatus" property="taskExcuteStatus"/>
        <result column="taskName" property="taskName"/>
        <result column="roadExecuteEventContentId" property="roadExecuteEventContentId"/>
        <result column="roadExecuteEventContentName" property="roadExecuteEventContentName"/>
        <result column="roadExecuteEventWay" property="roadExecuteEventWay"/>
        <result column="taskPatientsNode" property="taskPatientsNode"/>
        <collection property="todayPatientEventList" ofType="com.cbkj.diagnosis.service.common.vo.TodayPatientEvent">
            <result column="dic_id" property="dicId"/>
            <result column="dic_code" property="dicCode"/>
            <result column="dic_name" property="dicName"/>
        </collection>
    </resultMap>
    <select id="getIncludePhoneListByTaskPatientsId"
            resultMap="getIncludePhoneListByTaskPatientsIdMap"
            parameterType="com.cbkj.diagnosis.beans.business.requestvo.ListByTaskPatientsIdSearch">

        select a.*
        from (SELECT c.task_patients_id taskPatientsId,
        a.id sRoadTaskPatientsPhoneId,
        a.sui_fang_time taskExcuteTime,
        a.rec_id recId,
        a.phone_status taskExcuteStatus,
        c.task_name taskName,
        c.road_execute_event_content_id roadExecuteEventContentId,
        c.road_execute_event_content_name roadExecuteEventContentName,
        c.road_execute_event_way roadExecuteEventWay,
        a.task_patients_node taskPatientsNode,
        tre.dic_id dic_id,
        tre.dic_code dic_code,
        tre.dic_name dic_name
        FROM s_road_task_patients_phone a
        JOIN s_road_task_patients c ON (c.task_patients_id = a.task_patients_id and a.phone_status != 3)
        JOIN t_admin_info AS b ON(c.patient_id = b.user_id )
     <!--   join s_road_task as srt on(srt.s_road_task_id = c.s_road_task_id) -->
        LEFT JOIN `t_record_event` AS tre ON(tre.rec_id = a.rec_id )
        WHERE c.road_execute_event_way = '3'
        and c.road_execute_event_type in ('2', '4')
        and a.patient_id = #{patientId}


  <!--      <if test="userId != null and userId != ''">
            AND srt.create_user_id = #{userId}
        </if> -->
        <if test="userId != null and userId != ''">
            AND
            (
            SELECT COUNT(*) FROM  t_pre_diagnosis_dis_mapping AS tpddm

            WHERE c.`road_execute_event_content_id` = tpddm.`dia_id`  AND EXISTS  ( SELECT 1 FROM sys_admin_info_dis_mapping AS sdidm WHERE sdidm.user_id = #{userId} AND sdidm.dis_id = tpddm.`dis_id` )
            )>0
        </if>
        UNION ALL
        SELECT a.task_patients_id taskPatientsId,
        NULL AS sRoadTaskPatientsPhoneId,
        a.task_excute_time taskExcuteTime,
        a.rec_id recId,
        a.task_excute_status taskExcuteStatus,
        a.task_name taskName,
        a.road_execute_event_content_id roadExecuteEventContentId,
        a.road_execute_event_content_name roadExecuteEventContentName,
        a.road_execute_event_way roadExecuteEventWay,
        a.task_patients_node taskPatientsNode,
        tre.dic_id dic_id,
        tre.dic_code dic_code,
        tre.dic_name dic_name
        FROM `s_road_task_patients` AS a
        JOIN t_admin_info AS b ON(a.patient_id = b.user_id and a.status = '0')
      <!--  join s_road_task as srt on(srt.s_road_task_id = a.s_road_task_id) -->
        LEFT JOIN `t_record_event` AS tre ON(tre.rec_id = a.rec_id )
        WHERE a.road_execute_event_way != '3'
        and a.patient_id = #{patientId}
        and a.road_execute_event_type in ('2', '4')

    <!--    <if test="userId != null and userId != ''">
            AND srt.create_user_id = #{userId}
        </if>
        -->
        <if test="userId != null and userId != ''">
            AND
            (
            SELECT COUNT(*) FROM  t_pre_diagnosis_dis_mapping AS tpddm

            WHERE a.`road_execute_event_content_id` = tpddm.`dia_id`  AND EXISTS  ( SELECT 1 FROM sys_admin_info_dis_mapping AS sdidm WHERE sdidm.user_id = #{userId} AND sdidm.dis_id = tpddm.`dis_id` )
            )>0
        </if>
        ) as a order by a.taskExcuteTime desc
    </select>

    <resultMap id="BaseResultMap6" type="com.cbkj.diagnosis.service.common.vo.StartSuiFangDiaForm">
        <id column="roadExecuteEventContentId" jdbcType="VARCHAR" property="roadExecuteEventContentId"/>
    </resultMap>

    <select id="getDiaFormListBySRoadTaskPatientsPhoneId"
            resultMap="BaseResultMap6" parameterType="Long">
        <!--
                select b.road_execute_event_content_id   roadExecuteEventContentId,
                       b.road_execute_event_content_name roadExecuteEventContentName
                from s_road_task_patients_phone as a
                         join s_road_task_patients as b
                              on (a.task_patients_id = b.task_patients_id)
                where a.id = #{sRoadTaskPatientsPhoneId}
        -->

        select a.road_execute_event_content_id roadExecuteEventContentId,
        a.road_execute_event_content_name roadExecuteEventContentName
        from s_road_task_patients as a
        where a.task_patients_id = #{sRoadTaskPatientsPhoneId}

    </select>
    <select id="getYesterDaySuiFangFinishPeopleNumb" resultType="java.lang.Long"
            parameterType="com.cbkj.diagnosis.service.webapi.business.vo.YesterDaySuiFangFinishPeopleNumbSearch">
        select count(*) from s_road_task_patients_phone a
        <where>
            <if test=" doctorId != null and doctorId !=''">
                and a.doctor_id = #{doctorId}
            </if>
            <if test="startDate != null and startDate != ''">
                and a.sui_fang_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != '' ">
                and a.sui_fang_time &lt; #{endDate}
            </if>
            <if test="phoneStatus != null">
                and a.phone_status in(2,4,5,6,8)
            </if>

        </where>
        <!-- group by a.road_execute_id -->

    </select>
    <select id="getYesterDaySuiFangFinishPeopleNumb2" resultType="java.lang.Long"
            parameterType="com.cbkj.diagnosis.service.webapi.business.vo.YesterDaySuiFangFinishPeopleNumbSearch">

        select count(a.b) from
        (select count(*) b from s_road_task_patients_phone a
        <where>
            <if test=" doctorId != null and doctorId !=''">
                and a.doctor_id = #{doctorId}
            </if>
            <if test="startDate != null and startDate != ''">
                and a.sui_fang_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != '' ">
                and a.sui_fang_time &lt; #{endDate}
            </if>
            <if test="phoneStatus != null">
                and a.phone_status = #{phoneStatus}
            </if>

        </where>
        GROUP BY a.patient_id
        ) a
        <!-- group by a.road_execute_id -->

    </select>
    <select id="getTodayDaySuiFangTotalPeopleNumb" resultType="java.lang.Long"
            parameterType="com.cbkj.diagnosis.service.webapi.business.vo.YesterDaySuiFangFinishPeopleNumbSearch">

        select count(*) from s_road_task_patients_phone a
        <where>
            <if test=" doctorId != null and doctorId !=''">
                and a.doctor_id = #{doctorId}
            </if>
            <if test="startDate != null and startDate != ''">
                and a.sui_fang_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != '' ">
                and a.sui_fang_time &lt; #{endDate}
            </if>
            and a.phone_status in (2)
        </where>


    </select>
    <select id="getXiaoShanPhoneSuiFangStatisticsTopRight" resultType="java.lang.Long"
            parameterType="com.cbkj.diagnosis.service.webapi.business.vo.YesterDaySuiFangFinishPeopleNumbSearch">

        SELECT COUNT(a.patient_id) from s_road_task_patients_phone a
        <where>
            <if test=" doctorId != null and doctorId !=''">
                and a.doctor_id = #{doctorId}
            </if>

            and a.phone_status = 8
        </where>

    </select>
    <select id="getXiaoShanPhoneSuiFangStatisticsTopRight2" resultType="java.lang.Long"
            parameterType="com.cbkj.diagnosis.service.webapi.business.vo.YesterDaySuiFangFinishPeopleNumbSearch">
        select count(*) from s_road_task_patients_phone a
        <where>
            <if test=" doctorId != null and doctorId !=''">
                and a.doctor_id = #{doctorId}
            </if>
            and a.phone_status = 8
        </where>

    </select>
    <select id="getLastestSRoadTaskPatientsId"
            resultType="com.cbkj.diagnosis.service.common.vo.SuiFangPatientDetail">

        SELECT a.task_patients_id as taskPatientsId, a.sRoadTaskPatientsPhoneId
        FROM ((SELECT a.task_patients_id, NULL AS sRoadTaskPatientsPhoneId, a.task_excute_time
               FROM `s_road_task_patients` AS a

               WHERE a.`patient_id` = #{patientId}
                 AND a.road_execute_event_way != '3'

               ORDER BY a.task_excute_time DESC
               LIMIT 1)
              UNION ALL
              (SELECT a.task_patients_id, a.id AS sRoadTaskPatientsPhoneId, b.task_excute_time
               FROM `s_road_task_patients_phone` AS a

                        JOIN s_road_task_patients AS b ON (a.`task_patients_id` = b.task_patients_id)

               WHERE a.`patient_id` = #{patientId}
                 AND b.road_execute_event_way = '3'

               ORDER BY a.id DESC
               LIMIT 1)) AS a
        ORDER BY a.task_excute_time DESC
        LIMIT 1

    </select>
    <select id="getTodayPatientIdsList" resultType="java.lang.String"
            parameterType="com.cbkj.diagnosis.beans.business.requestvo.GetTodayPatientList">
        SELECT a.* FROM (
        (select t.* from (SELECT a.patient_id FROM `s_road_task_patients` AS a
        JOIN t_admin_info AS b ON(a.patient_id = b.user_id )
        join s_road_task as srt on(srt.s_road_task_id = a.s_road_task_id)
        WHERE a.road_execute_event_way != '3' AND a.road_execute_event_type IN ('2','4') AND a.`status` = '0'
        <if test="toDayTaskStatus != null and toDayTaskStatus == -1">
            <!-- 过期 ，今日之前的时间 -->
            and a.task_excute_status in(1,2,5,6,7)
            and a.task_excute_time <![CDATA[ < ]]> (CONCAT(CURDATE(), ' 23:59:59') )
        </if>
        <if test="toDayTaskStatus != null and toDayTaskStatus == 0">
            <!-- 完成 -->
            and a.task_excute_status in(8)
        </if>
        <if test="toDayTaskStatus != null and toDayTaskStatus == 2">
            <!-- 计划中，今日之后的时间 -->
            and a.task_excute_status in(1,2)
            and a.task_excute_time > (CONCAT(CURDATE(), ' 23:59:59') )
        </if>
        <if test="startDate != null">
            AND a.task_excute_time >= #{startDate}
        </if>
        <if test="sRoadTaskId != null">
            and a.s_road_task_id =#{sRoadTaskId}
        </if>
        <if test="endDate != null">
            AND a.task_excute_time <![CDATA[<]]> #{endDate}
        </if>
        <if test=" patientName != null and patientName!='' ">
            AND b.USER_NAME LIKE CONCAT('%',TRIM(#{patientName}),'%')
        </if>
        <if test=" patientIdcard != null and patientIdcard!='' ">
            AND b.card_number = #{patientIdcard}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            AND (
            b.`last_west_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
            OR
            b.`last_chinese_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
            )
        </if>

        <if test="userId != null and userId != ''">
            AND (
            srt.create_user_id = #{userId}
            or exists
            ( select 1 from s_road_task_ex as srte where srte.s_road_task_id = a.s_road_task_id and srte.ex_type = 2 and
            srte.diagnosis_doctor_id = #{userId} )
            )
        </if>
        <choose>
            <when test="toDayTaskStatus == 2 ">
                ORDER BY a.task_excute_time
            </when>
            <otherwise>
                ORDER BY a.task_excute_time desc
            </otherwise>
        </choose>
        ) t
        GROUP BY t.patient_id


        )
        UNION ALL
        (
        select t.* from ( SELECT srtpp.patient_id FROM
        s_road_task_patients AS a
        JOIN t_admin_info AS b ON(a.patient_id = b.user_id)
        join s_road_task as srt on(srt.s_road_task_id = a.s_road_task_id)
        join s_road_task_patients_phone as srtpp on( srtpp.task_patients_id = a.task_patients_id )
        WHERE a.road_execute_event_way = '3' AND a.road_execute_event_type IN ('2','4') AND a.`status` = '0'
        <if test="toDayTaskStatus != null and toDayTaskStatus == -1">
            <!-- 过期 ，今日之前的时间 -->
            and srtpp.phone_status in(1,2,5,6,7)
            and srtpp.sui_fang_time <![CDATA[ < ]]> (CONCAT(CURDATE(), ' 23:59:59') )
        </if>
        <if test="toDayTaskStatus != null and toDayTaskStatus == 0">
            <!-- 完成 -->
            and srtpp.phone_status in(8)
        </if>
        <if test="sRoadTaskId != null">
            and a.s_road_task_id =#{sRoadTaskId}
        </if>
        <if test="toDayTaskStatus != null and toDayTaskStatus == 2">
            <!-- 计划中，今日之后的时间 -->
            and srtpp.phone_status in(1,2)
            and srtpp.sui_fang_time > (CONCAT(CURDATE(), ' 23:59:59') )
        </if>
        <if test="sRoadTaskId != null">
            and a.s_road_task_id =#{sRoadTaskId}
        </if>

        <if test="startDate != null">
            AND srtpp.sui_fang_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND srtpp.sui_fang_time <![CDATA[<]]> #{endDate}
        </if>

        <if test=" patientName != null and patientName!='' ">
            AND b.USER_NAME LIKE CONCAT('%',TRIM(#{patientName}),'%')
        </if>
        <!--          <if test=" patientCardNumber != null and patientCardNumber!='' ">
                      AND b.card_number = #{patientCardNumber}
                  </if> -->
        <if test=" patientIdcard != null and patientIdcard!='' ">
            AND b.card_number = #{patientIdcard}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            AND (
            b.`last_west_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
            OR
            b.`last_chinese_dis_name` LIKE CONCAT('%',TRIM(#{diseaseName}),'%')
            )
        </if>
        <if test="userId != null and userId != ''">
            AND (
            srt.create_user_id = #{userId}
            or exists
            ( select 1 from s_road_task_ex as srte where srte.s_road_task_id = a.s_road_task_id and srte.ex_type = 2 and
            srte.diagnosis_doctor_id = #{userId} )
            )
        </if>
        <choose>
            <when test="toDayTaskStatus == 2 ">
                ORDER BY srtpp.sui_fang_time
            </when>
            <otherwise>
                ORDER BY srtpp.sui_fang_time desc
            </otherwise>
        </choose>
        ) t GROUP BY t.patient_id )
        ) AS a GROUP BY a.patient_id limit #{limit} offset #{page}
    </select>


</mapper>