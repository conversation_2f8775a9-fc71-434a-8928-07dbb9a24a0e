<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.SRoadTaskWestMappingMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.SRoadTaskWestMapping">
        <result column="west_prescriptions_id" jdbcType="VARCHAR"  property="westPrescriptionsId" />
        <result column="s_road_task_id" jdbcType="VARCHAR" property="sRoadTaskId" />
        <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
    </resultMap>


    <sql id="Base_Column_List">
    west_prescriptions_id,s_road_task_id,patient_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskWestMapping">
        delete from s_road_task_west_mapping where west_prescriptions_id = #{ westPrescriptionsId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road_task_west_mapping where west_prescriptions_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskWestMapping">
        insert into s_road_task_west_mapping (<include refid="Base_Column_List" />) values
        (#{westPrescriptionsId},#{sRoadTaskId},#{patientId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into s_road_task_west_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.westPrescriptionsId},#{item.sRoadTaskId},#{item.patientId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskWestMapping">
        update s_road_task_west_mapping
        <set>
             <if test="sRoadTaskId != null">
                s_road_task_id = #{ sRoadTaskId },
             </if>
             <if test="patientId != null">
                patient_id = #{ patientId },
             </if>
        </set>
        where west_prescriptions_id = #{ westPrescriptionsId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from s_road_task_west_mapping where west_prescriptions_id = #{ westPrescriptionsId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskWestMapping" resultMap="BaseResultMap">
        SELECT west_prescriptions_id,s_road_task_id,patient_id
        from s_road_task_west_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>