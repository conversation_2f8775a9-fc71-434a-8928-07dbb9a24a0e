<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.business.SRoadTaskPatientsMappingMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping">
        <id column="s_road_task_id" jdbcType="VARCHAR" property="sRoadTaskId"/>
        <result column="patient_id" jdbcType="VARCHAR" property="patientId"/>
        <result column="records_id" jdbcType="VARCHAR" property="recordsId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>


    <sql id="Base_Column_List">
        s_road_task_id,patient_id,status,records_id,create_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping">
        delete
        from s_road_task_patients_mapping
        where s_road_task_id = #{ sRoadTaskId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road_task_patients_mapping where s_road_task_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping">
        replace into s_road_task_patients_mapping (<include refid="Base_Column_List"/>) values
        (#{sRoadTaskId},#{patientId},#{status},#{recordsId},#{createTime})
    </insert>

    <insert id="insertList" parameterType="List">
        replace into s_road_task_patients_mapping (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.sRoadTaskId},#{item.patientId},#{item.status},#{item.recordsId},#{item.createTime})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping">
        update s_road_task_patients_mapping
        <set>
            <if test="patientId != null">
                patient_id = #{ patientId },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
        </set>
        where s_road_task_id = #{ sRoadTaskId } and patient_id = #{patientId} and records_id=#{recordsId}
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from s_road_task_patients_mapping where s_road_task_id = #{ sRoadTaskId } limit 1
    </select>
    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.service.webapi.business.vo.RoadEventTaskSchedulerRes">

    </resultMap>
    <select id="getRoadEventTaskSchedulerHandlePreList" resultMap="BaseResultMap2">
        SELECT
            c.`USER_ID` patientId,
            c.`AGE` patientAge,
            c.`SEX` patientSex,
            c.`MOBILE` patientMobile,
            c.`card_number` patientCardNumber,
            c.`USER_NAME` patientName,
            a.s_road_task_id sRoadTaskId,
            a.records_id recordsId,
            d.s_road_id sRoadId,
            d.task_name taskName,
            b.`handle_record_id` handleRecordId,
            b.`records_id` recordsId
        FROM
            `s_road_task_patients_mapping` a
                JOIN `medical_basic_handle_record` b
                     ON (a.`records_id` = b.`records_id`)
                JOIN `t_admin_info` c
                     ON (c.`user_id` = a.`patient_id`)
                join s_road_task as d on (d.s_road_task_id = a.s_road_task_id)
        WHERE NOT EXISTS (
                select 1 from s_road_task_handle_mapping srtpm where srtpm.patient_id = a.patient_id and srtpm.handle_record_id=b.handle_record_id and
                    srtpm.s_road_task_id = a.s_road_task_id
            )
    </select>
    <select id="getRoadEventTaskSchedulerWestPreList" resultMap="BaseResultMap2">
        SELECT
            c.`USER_ID` patientId,
            c.`AGE` patientAge,
            c.`SEX` patientSex,
            c.`MOBILE` patientMobile,
            c.`card_number` patientCardNumber,
            c.`USER_NAME` patientName,
            a.s_road_task_id sRoadTaskId,
            a.records_id recordsId,
            d.s_road_id sRoadId,
            d.task_name taskName,
            b.`west_prescriptions_id` westPrescriptionsId,
            b.`records_id` recordsId
        FROM
            `s_road_task_patients_mapping` a
                JOIN `medical_west_prescriptions` b
                     ON (a.`records_id` = b.`records_id`)
                JOIN `t_admin_info` c
                     ON (c.`user_id` = a.`patient_id`)
                join s_road_task as d on (d.s_road_task_id = a.s_road_task_id)
        WHERE NOT EXISTS (
                select 1 from s_road_task_west_mapping srtpm where srtpm.patient_id = a.patient_id and srtpm.west_prescriptions_id=b.west_prescriptions_id and
                    srtpm.s_road_task_id = a.s_road_task_id
            )
    </select>
    <select id="getRoadEventTaskSchedulerList" resultMap="BaseResultMap2">
        SELECT
            c.`USER_ID` patientId,
            c.`AGE` patientAge,
            c.`SEX` patientSex,
            c.`MOBILE` patientMobile,
            c.`card_number` patientCardNumber,
            c.`USER_NAME` patientName,
            a.s_road_task_id sRoadTaskId,
            a.records_id recordsId,
            d.s_road_id sRoadId,
            d.task_name taskName,
            b.`prescriptions_id` prescriptionsId,
            b.`records_id` recordsId,
            b.prescription_num as prescriptionNum,
            mr2.app_id appId,
            mr2.ins_id insId,
            mr2.ins_code insCode,
            mr2.ins_name insName,
            mr2.dept_code deptCode,
            mr2.dept_id deptId,
            mr2.dept_name deptName,
            d.create_user_id createUserId,
            d.create_user_name createUserName
        FROM
            `s_road_task_patients_mapping` a
                JOIN `medical_records_prescriptions` b
                     ON (a.`records_id` = b.`records_id`)
                JOIN `t_admin_info` c
                     ON (c.`user_id` = a.`patient_id`)
join s_road_task as d on (d.s_road_task_id = a.s_road_task_id)
        join medical_records mr2 on(mr2.records_id = a.records_id)
        WHERE NOT EXISTS (
            select 1 from s_road_task_pres_mapping srtpm where srtpm.patient_id = a.patient_id and srtpm.prescriptions_id=b.prescriptions_id and
                                                               srtpm.s_road_task_id = a.s_road_task_id
            )
    </select>

    <!-- <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsMapping" resultMap="BaseResultMap">
         SELECT s_road_task_id,patient_id,status,records_id,create_time
         from s_road_task_patients_mapping
         <where>
             <if test=" name != null and name!='' ">
                 and name like CONCAT('%',trim(#{name}),'%')
             </if>
         </where>
     </select>
 -->
</mapper>