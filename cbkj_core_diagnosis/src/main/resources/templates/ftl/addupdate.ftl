<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="pragma" content="no-cache"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <meta name="renderer" content="webkit"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <title th:replace="../templates/common :: commonHeand('添加或者修改${desc}')"></title>
    <link rel="stylesheet" type="text/css" media="all" th:href="@{/css/addUpdate.css}"/>
    <script type="text/javascript" charset="UTF-8" th:src="@{/sys/${entityName?uncap_first}/addupdate.js}"></script>

</head>
<body>
<form class="layui-form" action="" lay-filter="example">
    <!-- 防止重复提交 必须字段 -->
    <input type="hidden" name="token" th:value="${r"${token}"}"/>

    <input type="hidden" name="${entityPrimary2}" th:value="${r"${"}${entityPrimary}${r"}"}"/>

 <#list maps?keys as key>
     <#if key != primary>
    <div class="layui-form-item">
        <label class="layui-form-label">${maps[key]}</label>
        <div class="layui-input-inline" style="width:275px;">
             <input type="text" maxlength="${lengthM[key]}" name="${maps[key]}" lay-verify="title" autocomplete="off" placeholder="请输入" class="layui-input">
        </div>
    </div>
     </#if>
 </#list>
</form>
</body>

</html>