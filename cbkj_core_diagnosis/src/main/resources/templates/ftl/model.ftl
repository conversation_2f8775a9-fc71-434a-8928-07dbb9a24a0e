package com.cbkj.diagnosis.beans${businessPath};

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Schema
public class ${entityName} implements Serializable{

<#list maps?keys as key>
    @Schema(description =  "${comments[key_index]}")
    private ${javaType[key]} ${maps[key]};

</#list>

}
