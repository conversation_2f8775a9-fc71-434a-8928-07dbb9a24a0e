<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cbkj.diagnosis</groupId>
        <artifactId>cbkj_v5</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>cbkj_core_diagnosis</artifactId>
    <groupId>cbkj_v5</groupId>
    <version>1.0</version>


    <name>cbkj_core_diagnosis</name>
    <url>http://maven.apache.org</url>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>

        <!--    <dependency>-->
        <!--        <groupId>org.bouncycastle</groupId>-->
        <!--        <artifactId>bcprov-jdk16</artifactId>-->
        <!--        <version>1.46</version>-->
        <!--    </dependency>-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.68</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.bouncycastle</groupId>-->
<!--            <artifactId>bcprov-jdk15on</artifactId>-->
<!--            <version>1.70</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>cbkj_v5</groupId>
            <artifactId>cbkj_common</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.1.3</version>
        </dependency>
        <!--    &lt;!&ndash; MP 核心库 &ndash;&gt;-->
<!--            <dependency>-->
<!--                <groupId>com.baomidou</groupId>-->
<!--                <artifactId>mybatis-plus</artifactId>-->
<!--                <version>3.4.0</version>-->
<!--            </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.baomidou</groupId>-->
<!--            <artifactId>mybatis-plus-extension</artifactId>-->
<!--            <version>3.5.1</version>-->
<!--        </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.baomidou</groupId>-->
<!--                <artifactId>mybatis-plus-generator</artifactId>-->
<!--                <version>3.4.0</version>-->
<!--            </dependency>-->
        <!--    &lt;!&ndash; shiro+redis缓存插件 &ndash;&gt;-->
        <!--    <dependency>-->
        <!--        <groupId>org.crazycake</groupId>-->
        <!--        <artifactId>shiro-redis</artifactId>-->
        <!--        <version>2.4.14</version>-->
        <!--    </dependency>-->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>8.2.0</version>
            <!--        <scope>6.0.6</scope>-->
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.16</version>
        </dependency>


        <!--    <dependency>-->
        <!--        <groupId>com.oracle.database.jdbc</groupId>-->
        <!--        <artifactId>ojdbc6</artifactId>-->
        <!--        <version>11.2.0.4</version>-->
        <!--    </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>


        <!-- jedis客户端 -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>lettuce-core</artifactId>
                    <groupId>io.lettuce</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.8.0</version>  <!-- 1.x 最新稳定版 -->
        </dependency>
        <!-- OpenFeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>3.1.5</version>
        </dependency>

        <!-- Hystrix -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
            <version>2.2.10.RELEASE</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>io.projectreactor</groupId>-->
<!--            <artifactId>reactor-core</artifactId>-->
<!--            <version>3.5.0</version> &lt;!&ndash; 建议使用最新稳定版 &ndash;&gt;-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
            <version>12.5</version>
        </dependency>
    </dependencies>
</project>